from dao.get.mysql.iter_info import get_biz_test_iter, get_dev_pipeline_id_info, get_db_group_name_and_br_name
from db_mgt.db_dump_ser import get_dump_archive_branch_name_by_app_name
from job.jenkins.test_data_dev.make_dump_service import trans_date
from settings import logger
from test_publish_aio.test_publish_aio_models.test_publish_ser import get_db_archive_branch_list


class DbDumpMgt:

    # @staticmethod
    # def get_new_after_dump_file_biz_iter(dump_pipline_id, current_pipline_id, app_list, biz_code, opt_type):
    #     """
    #     获取dump文件对应的业务迭代
    #     :param dump_pipline_id:
    #     :param current_pipline_id:
    #     :param app_list:
    #     :param biz_code:
    #     :param opt_type:
    #     :return:
    #     """
    #     start_time = None
    #     if dump_pipline_id:
    #         start_time = get_biz_test_iter(dump_pipline_id)[0].get('br_end_time')
    #     # 空dump无开始时间，current_pipline_id是否有结束时间，取决于是否关闭的业务分支
    #     end_time = None
    #     if current_pipline_id:
    #         end_time = get_biz_test_iter(current_pipline_id)[0].get('br_end_time')
    #     if opt_type == 1:
    #         if not start_time and not end_time:
    #             return None
    #     if opt_type == 2:
    #         if not end_time:
    #             raise Exception(
    #                 '制作dump,dump_pipline_id:{},current_pipline_id{},结束时间不能为空'.format(dump_pipline_id,
    #                                                                                            current_pipline_id))
    #     if opt_type == 3:
    #         if not start_time and not end_time:
    #             raise Exception(
    #                 '检查dump,dump_pipline_id:{},current_pipline_id{},开始时间和结束时间都不能同时为空'.format(
    #                     dump_pipline_id,
    #                     current_pipline_id))
    #     biz_iter_dict = get_new_dump_archive_iter_id(biz_code, start_time, end_time, app_list)
    #     logger.info(
    #         "test_pipline_id(业务迭代分支): {},开始时间：{}, 结束时间：{}, result:{}".format(current_pipline_id,
    #                                                                                        start_time,
    #                                                                                        end_time, biz_iter_dict))
    #     # 没有绑定的 dump文件
    #     if len(biz_iter_dict) == 0:
    #         return None
    #     available_biz_br_dict = {}
    #     for db_group in biz_iter_dict:
    #         br_name_list = biz_iter_dict[db_group]
    #         if not dump_pipline_id:
    #             br_name_list.append('0.0.1')
    #         if br_name_list:
    #             name_list = list(set(br_name_list))
    #             # name_list = []
    #             # for name in br_name_list:
    #             #     if br_name_list.count(name) == 1 and name not in name_list:
    #             #         name_list.append(name)
    #             available_biz_br_dict.setdefault(db_group, []).extend(name_list)
    #
    #     return available_biz_br_dict

    # @staticmethod
    # def get_after_dump_file_biz_iter(dump_biz_code, app_list):
    #     """
    #     获取dump文件对应的业务迭代
    #     :param dump_biz_code:
    #     :param app_list:
    #     :return:
    #     """
    #
    #     biz_iter_dict = get_dump_archive_iter_id(app_list, dump_biz_code, 'biz')
    #     # 没有绑定的 dump文件
    #     if len(biz_iter_dict) == 0:
    #         return None
    #     available_biz_br_dict = {}
    #     for db_group in biz_iter_dict:
    #         for biz_iter in biz_iter_dict[db_group]:
    #             available_br = get_archive_biz_br_by_gt_biz_iter_id(biz_iter)
    #             if db_group not in available_biz_br_dict:
    #                 available_biz_br_dict[db_group] = []
    #             available_biz_br_dict[db_group].extend(available_br)
    #
    #     return available_biz_br_dict

    @staticmethod
    def get_new_after_dump_file_dev_iter(dump_pipline_id, current_pipline_id, app_list, opt_type):
        """
        获取dump文件对应的开发迭代
        :param dump_pipline_id:
        :param current_pipline_id:
        :param app_list:
        :param opt_type:
        :return:
        """
        start_time = None
        if dump_pipline_id:
            start_time = get_biz_test_iter(dump_pipline_id)[0].get('br_end_time')
        end_time = None
        if current_pipline_id:
            end_time = get_biz_test_iter(current_pipline_id)[0].get('br_end_time')
        if opt_type == 1:
            logger.info("数据初始化时，查询开发分支的开始时间是：{}, 结束时间：{}".format(start_time, end_time))
        if opt_type == 2:
            if not end_time:
                raise Exception(
                    '制作dump,dump_pipline_id:{},current_pipline_id{},结束时间不能为空'.format(dump_pipline_id,
                                                                                               current_pipline_id))
        if opt_type == 3:
            if not start_time and not end_time:
                raise Exception(
                    '检查dump,dump_pipline_id:{},current_pipline_id{},开始时间和结束时间都不能同时为空'.format(
                        dump_pipline_id,
                        current_pipline_id))
        new_end_time = trans_date(end_time)
        new_start_time = trans_date(start_time)
        dev_iter_dict = get_dump_archive_branch_name_by_app_name(app_list, new_start_time, new_end_time)
        logger.info(
            "dev_pipline_id(开发迭代分支): {},开始时间：{}, 结束时间：{}, result:{}".format(current_pipline_id,
                                                                                          new_start_time,
                                                                                          new_end_time, dev_iter_dict))

        return dev_iter_dict

    @staticmethod
    def get_new_after_dump_file_dev_iter_2(dump_pipeline_id_list, biz_iter_id, app_list):
        """
        获取dump文件对应的开发迭代
        :param dump_pipeline_id_list:
        :param biz_iter_id:
        :param app_list:
        :return:
        """
        # [{'br_name': '20240511', 'db_group_name': 'ZHZX'}, {'br_name': '3.3.3', 'db_group_name': 'ZHZX_HK'}, {'br_name': '2.0.16', 'db_group_name': 'PAY'}]
        db_group_name_and_br_name_list = get_db_group_name_and_br_name(dump_pipeline_id_list, app_list)
        dev_iter_dict = {}
        # 在不考虑其它业务的情况下，直接修复语法的错误。用来解决应用不需要数据库时获取的数据为「None」的问题。zt@2025-01-14
        if db_group_name_and_br_name_list:
            for item in db_group_name_and_br_name_list:
                if item.get("db_group_name"):
                    if item.get("db_group_name") not in dev_iter_dict:
                        dev_iter_dict[item.get("db_group_name")] = get_db_archive_branch_list(item.get("db_group_name"),
                                                                                              item.get("br_name"))
                    else:
                        # 合并去重
                        existing_list = dev_iter_dict[item.get("db_group_name")]
                        new_list = get_db_archive_branch_list(item.get("db_group_name"), item.get("br_name"))
                        # 同db分组的可能记录不同的研发迭代分支，所以需要合并取交集
                        # 比如，分支总list[1,2,3,4,5]，通过记录2，找到 [3,4,5],通过记录3，找到 [4,5]
                        # 最后分拣的逻辑是取交集，只要取[4,5]的dml
                        combined_list = list(set(existing_list) & set(new_list))
                        dev_iter_dict[item.get("db_group_name")] = combined_list
            logger.info("业务迭代 {} 需要分拣的研发应用对应的sql分支为： {}".format(biz_iter_id, dev_iter_dict))
        return dev_iter_dict


if __name__ == '__main__':
    # biz_iter_dict = {"ZHZX": ["GONGMU-ORDER-PLAN_test2024022102"]}
    # available_biz_iter_list = DbDumpMgt.get_after_dump_file_dev_iter('it100', ['pdc-online-web'])
    # print(available_biz_iter_list)
    # available_dev_iter_dict = DbDumpMgt.get_after_dump_file_biz_iter('it100', ['pdc-online-web'])
    # print(available_dev_iter_dict)

    list_a = [1,2,3]
    list_b = [1,2,3,4]
    print(list(set(list_a) & set(list_b)))