from peewee import *
from dao.base_model import SpiderBaseModels, BaseModel

# coding: utf-8
from sqlalchemy import Column, String, TIMESTAMP, JSON, DateTime
from sqlalchemy.dialects.mysql import BIGINT, INTEGER
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class DbMgtArcheryCheckTestEnv(SpiderBaseModels):
    """
    用户指定SQL验证的环境表
    """
    module_name = CharField(verbose_name='应用名', max_length=100)
    check_suite_code = CharField(verbose_name='sqlcheck指定的环境', max_length=20)
    create_user = CharField(verbose_name='创建人', max_length=50)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length=50)
    update_time = DateTimeField(verbose_name='更新时间')
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'db_mgt_archery_check_test_env'
        verbose_name = 'SQL验证的环境表'


class DbMgtExecSqlFileHistory(SpiderBaseModels):
    iteration_id = CharField(verbose_name='迭代id', max_length=100)
    batch_no = CharField(verbose_name='执行批次号', max_length=100)
    suite_code = CharField(verbose_name='环境', max_length=100)
    db_name = CharField(verbose_name='数据库名', max_length=100)
    sql_ver_name = CharField(verbose_name='sql脚本名称', max_length=1000)
    sql_file_hash = CharField(verbose_name='sql脚本文件的hash值', max_length=1000)
    create_user = CharField(verbose_name='创建人', max_length=100)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length=100)
    update_time = DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'db_mgt_exec_sql_file_history'
        verbose_name = '数据库脚本执行历史'


class IterMgtSqlCheckDetail(SpiderBaseModels):
    iteration_id = CharField(verbose_name='迭代id', max_length=100)
    module_name = CharField(verbose_name='应用名', max_length=100)
    check_detail = TextField(verbose_name='sqlcheck的详细信息')
    create_user = CharField(verbose_name='创建人', max_length=100)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length=100)
    update_time = DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'iter_mgt_sql_check_detail'
        verbose_name = 'sqlcheck的详细信息'


class EnvMgtDbDeployInfo(SpiderBaseModels):
    db_info_id = BigIntegerField(verbose_name='数据库信息id')
    suite_code = CharField(verbose_name='环境', max_length=100)
    pipeline_id = CharField(verbose_name='迭代id', max_length=500)
    copy_br_name_record = TextField(verbose_name='拷贝的版本记录')
    update_time = DateTimeField(verbose_name='更新时间')
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'env_mgt_db_deploy_info'
        verbose_name = '数据库部署信息'


class IterMgtSqlHandleLog(Base):
    __tablename__ = 'iter_mgt_sql_handle_log'

    id = Column(BIGINT(20), primary_key=True)
    archery_bind_id = Column(BIGINT(20), nullable=False, comment='archery_bind_id')
    module_name = Column(String(100), nullable=False, comment='应用名')
    suite_code = Column(String(100), nullable=False, comment='环境套名称')
    archery_url = Column(String(500), comment='请求地址')
    request_type = Column(String(50), comment='请求类型')
    request_header = Column(JSON, comment='请求头')
    request_param = Column(JSON, comment='请求参数')
    response_json = Column(JSON, comment='请求返回消息')
    iteration_id = Column(String(200), nullable=False, comment='迭代id')
    create_time = Column(TIMESTAMP)
    update_time = Column(TIMESTAMP)
    create_user = Column(String(100))
    update_user = Column(String(100))
    stamp = Column(BIGINT(11))


class DbMgtArcheryAuthInfo(Base):
    __tablename__ = 'db_mgt_archery_auth_info'

    id = Column(BIGINT(20), primary_key=True)
    username = Column(String(50), comment='用户名')
    archery_id = Column(BIGINT(11), comment='archery_id')
    token = Column(String(1000), comment='access_token')
    last_login_time = Column(DateTime, comment='最后登录时间')


class PipelineLogOpt(Base):
    __tablename__ = 'pipeline_log_opt'

    sid = Column(INTEGER(11), primary_key=True)
    opt_user = Column(String(50), comment='操作用户')
    job_name = Column(String(100), comment='job名称')
    opt_type = Column(String(50), comment='操作类型')
    opt_time = Column(DateTime, comment='操作时间')
    pipeline_id = Column(String(100), comment='迭代id')
    opt_info_str = Column(String(200), comment='操作信息')
