#!/usr/bin/env python3
# coding=utf-8
# ================
# 从「vcs」中生成SQL制品 zt@2023-02-13
# 第1版 zt@2023-02-13
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/workspaces/PycharmProjects/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/create_sql_from_vcs/
# Jenkins配置示例：
#   sh 'export PYTHONPATH=/home/<USER>/be-scripts/be-scripts && python3.x XXX/create_sql_from_vcs.py "STEP_PULL_SQL" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
import asyncio
# ==== 1、环境变量 ====
import os
import re
import sys
import time
import logging
import traceback
import json
import sqlparse

from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
from common.common_tool import common_tool
from dao.get.mysql.db_mgt_info import get_db_type_by_module_name
from dao.get.mysql.iter_mgt_iter_app_info import get_iter_app_info_for_archive
from qc_mgt.modles import GuardSwitchInfo
from test_mgt.models import DbMgtSql
from utils.compile.mvn.compiler import Compiler

print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
from logging.handlers import TimedRotatingFileHandler
from test_publish_aio.test_publish_aio_exec import test_publish_aio_util
from dao.get.mysql import db_mgt_bind_view
from settings import INTERFACE_URL, logger, SQL_CONFIG
from db_mgt.creat_sql_migrate.models import DbMgtArcheryCheckTestEnv, PipelineLogOpt, IterMgtSqlCheckDetail
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "create_sql_from_vcs"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "create_sql_from_vcs.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
from datetime import datetime
from enum import unique, Enum
from ci_pipeline.pipeline_record.pipeline_record import PipelineStatus, PipelineRecorder
from db_mgt.sql_create_bo.sql_migrate_bo import SqlMigrateBo
from dao.get.mysql import db_mgt_info
from common.ext_cmd.svn.svn_cmd import SvnCmd
from common.ztst_utils.file_hash import file_sha256
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd, CmdException

CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None
ZONE_CACHE_SUITE_DICT = {}


@unique
class StepEnum(Enum):
    STEP_PRINT_INFO = ('print_info', "信息打印")
    STEP_PULL_SQL = ('pull_sql', "拉取SQL")
    STEP_MAKE_SQL = ('make_sql', "SQL版本化")
    STEP_PUSH_SQL = ('push_sql', "SQL制品上传")
    STEP_CHECK_SQL = ('check_sql', "SQL检查")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class VcsTypeEnum(Enum):
    VCS_SVN = ('svn', "svn")
    VCS_GIT = ('git', "git")
    VCS_GITLAB = ('gitlab', "gitlab")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


def check_sql_name(sql_file_str):
    if " " in sql_file_str:
        raise ValueError("文件名「{}」中不能包含空格。".format(sql_file_str))


class SqlMigrateBusiness:

    @staticmethod
    def dict_fetchall(cursor):
        return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]

    @staticmethod
    def get_cost_time(st, et=None):
        """获取耗时，单位「秒」zt@2022-11-09"""
        ct = 0
        if st:
            if not et:
                et = datetime.now()
            ct = round((et - st).total_seconds(), 3)
        return ct

    def recode_sql_check_detail(func):
        def wrapper(*args, **kwargs):
            sql_migrate_bo = args[0]
            result, check_detail = func(*args, **kwargs)
            if "成功" not in check_detail:
                IterMgtSqlCheckDetail.create(iteration_id=sql_migrate_bo.iteration_id,
                                             module_name=sql_migrate_bo.module_name,
                                             check_detail=check_detail,
                                             create_user='howbuyscm',
                                             create_time=datetime.now())
            return result, check_detail

        return wrapper

    @staticmethod
    @recode_sql_check_detail
    def __pull_sql(sql_migrate_bo: SqlMigrateBo) -> tuple[str, str]:
        logger.info(">>>> sql_migrate_bo.flag_file_dir：{}".format(sql_migrate_bo.flag_file_dir))
        logger.info(">>>> sql_migrate_bo.iteration_id：{}".format(sql_migrate_bo.iteration_id))
        logger.info(">>>> sql_migrate_bo.module_name：{}".format(sql_migrate_bo.module_name))
        logger.info(">>>> sql_migrate_bo.app_name：{}".format(sql_migrate_bo.app_name))
        logger.info(">>>> sql_migrate_bo.workspace：{}".format(sql_migrate_bo.workspace))
        logger.info(">>>> sql_migrate_bo.sid：{}".format(sql_migrate_bo.sid))
        logger.info(">>>> sql_migrate_bo.git_group：{}".format(sql_migrate_bo.git_group))
        logger.info(">>>> sql_migrate_bo.br_name：{}".format(sql_migrate_bo.br_name))

        result = "sql拉取成功"
        # 检查sql记录是否需要清理（为了后续重编）。zt@2025-05-26
        # SqlMigrateBusiness.__check_sql_need_all_rebuild(sql_migrate_bo.iteration_id)
        iteration_id = sql_migrate_bo.iteration_id
        module_name = sql_migrate_bo.app_name
        SqlMigrateBusiness.__check_sql_need_clear_and_rebuild(iteration_id, module_name)

        # 从数据库获取sql制品的存放信息
        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(sql_migrate_bo.module_name)
        # 先警告没有绑定，暂时不强制。zt@2023-02-20
        if vcs_info_list and len(vcs_info_list) > 0:
            vcs_info_obj = vcs_info_list[0]
            # 拉取sql文件
            sql_file_dict = SqlMigrateBusiness.__get_devops_sql(vcs_info_obj, sql_migrate_bo)
            logger.info('sql_file_dict======================={}'.format(sql_file_dict))
            # 版本化sql文件
            db_name_list, db_name_group_dict, db_name_app_dict = SqlMigrateBusiness.__get_db_name_list(sql_migrate_bo)
            logger.info('db_name_list======================={}'.format(db_name_list))
            logger.info('db_name_app_dict======================={}'.format(db_name_app_dict))
            # 新增sql文件md5对比逻辑。zt@2024-09-09
            #   1、小心本次SQL没有文件，而没有清理的问题。
            #   2、SQL文件的切分逻辑不变。
            #   3、新传业务后，所有SQL重新编译的逻辑不再需要。
            #   4、
            if sql_file_dict:
                # 去除同迭代跟应用没绑定的库sql文件
                new_sql_file_dict = {}
                sql_db_dict = {}
                for k, v in sql_file_dict.items():
                    logger.info('===========db_vcs_type============{}'.format(vcs_info_obj.get('db_vcs_type')))
                    logger.info('===========VcsTypeEnum============{}'.format(VcsTypeEnum.VCS_GITLAB))
                    if vcs_info_obj.get('db_vcs_type') == 'gitlab':
                        db_name = k.split("/")[3]
                        if db_name in db_name_list:
                            logger.info('=============3')
                            new_sql_file_dict[k] = v
                            db_group = db_name_group_dict.get(db_name)
                            sql_db_dict[k] = {"db_name": db_name, "db_group": db_group}
                    else:
                        db_name = k.split("/")[2]
                        if db_name in db_name_list:
                            logger.info('=============2')
                            new_sql_file_dict[k] = v
                            db_group = db_name_group_dict.get(db_name)
                            sql_db_dict[k] = {"db_name": db_name, "db_group": db_group}
                logger.info("new_sql_file_dict==={}".format(new_sql_file_dict))
                # SqlMigrateBusiness.__ins_or_upd_sql_file(sql_migrate_bo, new_sql_file_dict)
                SqlMigrateBusiness.__ins_or_upd_sql_file_by_md5(sql_migrate_bo, new_sql_file_dict, sql_db_dict, db_name_app_dict)
        else:
            result = ">>>> 警告：应用「{}」在pull_sql阶段检测到有绑定「SQL制品」信息，db_mgt_app_bind表中没有配置。".format(
                sql_migrate_bo.module_name)
            log.warning(result)

        return PipelineStatus.success, result

    @staticmethod
    @recode_sql_check_detail
    def __make_sql(sql_migrate_bo: SqlMigrateBo) -> tuple[str, str]:

        result = "sql版本化成功"

        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(sql_migrate_bo.module_name)
        if vcs_info_list and len(vcs_info_list) > 0:
            # 获取DB中的SQL文件
            iteration_id = sql_migrate_bo.iteration_id
            db_mgt_sql_list = db_mgt_info.get_db_mgt_sql(iteration_id)
            # 组字典
            db_group_dict = SqlMigrateBusiness.__get_db_group_dict(sql_migrate_bo)
            logger.info("db_mgt_sql_list===={}".format(db_mgt_sql_list))
            if db_mgt_sql_list:
                curr_time = datetime.now()
                db_ver_prefix = curr_time.strftime('%Y.%m.%d.%H.%M.%S')

                sql_ver_file_tuple_list = []
                db_name_cache = None
                logger.info("=============================sql-info==========================")
                logger.info(db_mgt_sql_list)
                db_mgt_sql_list.sort(key=lambda item: item.get('sql_file_path'), reverse=False)
                logger.info(db_mgt_sql_list)
                for tmp_dict in db_mgt_sql_list:
                    # 增量版本化
                    sql_ver_name = tmp_dict.get('sql_ver_name')
                    # 前端传入业务类型标识库要做一次初始化，则sql需要全部重新编译一次 20230227 by fwm
                    # 经过讨论，确定移除传入业务类型，就全量重新编译的逻辑。zt@2024-09-11
                    # if not sql_ver_name or biz_base_db:
                    if not sql_ver_name:
                        db_id = tmp_dict.get('id')
                        br_name = tmp_dict.get('br_name')
                        sql_file_name = tmp_dict.get('sql_file_name')
                        sql_src_type = tmp_dict.get('sql_src_type')
                        sql_file_path = tmp_dict.get('sql_file_path')
                        # 解析库和分组名
                        db_name, db_group_name = SqlMigrateBusiness.__parse_db_info_with_path(db_group_dict,
                                                                                              br_name,
                                                                                              sql_src_type,
                                                                                              sql_file_path)
                        if not db_name:
                            raise ValueError("格式错误：文件「{}」数据库名解析失败。".format(sql_file_path))
                        if not db_group_name:
                            raise ValueError("数据错误：应用「{}」和数据库「{}」没有绑定。".format(tmp_dict.get('module_name'), db_name))
                        # 版本化
                        if db_name_cache and db_name == db_name_cache:
                            i = i + 1
                        else:
                            i = 1
                            db_name_cache = db_name
                        sql_file_idx = '%03d' % i
                        sql_ver_str = 'V{}.{}__{}'.format(db_ver_prefix, sql_file_idx, sql_file_name)
                        # 其它更新数据
                        update_user = 'howbuyscm'
                        update_time = curr_time
                        sql_ver_upload_status = False
                        # 更新列表
                        sql_ver_file_tuple = (
                            update_user, update_time, sql_ver_str, sql_ver_upload_status, db_id)
                        sql_ver_file_tuple_list.append(sql_ver_file_tuple)
                        logger.info("sql更新列表为===={}".format(sql_ver_file_tuple_list))
                # 如果有新增sql文件，则更新。
                if sql_ver_file_tuple_list:
                    logger.info(sql_ver_file_tuple_list)
                    db_mgt_info.upd_db_mgt_sql_for_make(sql_ver_file_tuple_list)
                else:
                    logger.info("没有新增SQL需要更新")
        else:
            result = ">>>> 警告：应用「{}」在make_sql阶段检测到有绑定「SQL制品」信息，db_mgt_app_bind表中没有配置。". \
                format(sql_migrate_bo.module_name)
            log.warning(result)

        return PipelineStatus.success, result

    @staticmethod
    @recode_sql_check_detail
    def __check_sql(sql_migrate_bo: SqlMigrateBo) -> tuple[str, str]:
        logger.info('sql_migrate_bo============================{}'.format(sql_migrate_bo))
        check_result = "sql检查成功"
        iteration_id = sql_migrate_bo.iteration_id
        module_name_list = []
        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(sql_migrate_bo.module_name)
        # 先警告没有绑定，暂时不强制。zt@2023-02-20
        if not vcs_info_list:
            result = ">>>> 警告：应用「{}」在check_sql阶段检测到有绑定「SQL制品」信息，db_mgt_app_bind表中没有配置。".format(
                sql_migrate_bo.module_name)
            log.warning(result)
            return PipelineStatus.success, "sql检查执行成功"
        app_dict = get_iter_app_info_for_archive(iteration_id)
        if app_dict:
            module_name_list = app_dict['app_list']
        check_failure = []
        # 根据应用和迭代找到sql内容，并按库区分，一个应用可能多个库
        sql_content_list = SqlMigrateBusiness.__get_sql_content_list(sql_migrate_bo)
        logger.info('===================================sql_content_list:{}'.format(sql_content_list))
        if not sql_content_list:
            logger.info("没有可检测的sql语句")
            return PipelineStatus.success, "sql检查执行成功"
        db_name_list = []
        # 查询当前流水线最后一次操作人
        job_name = sql_migrate_bo.iteration_id + '_' + sql_migrate_bo.app_name
        opt_user = ''
        with DBConnectionManagerForSqlalchemy() as db:
            plo = db.session.query(PipelineLogOpt).filter(PipelineLogOpt.job_name == job_name).order_by(
                PipelineLogOpt.sid.desc()).first()
            if plo:
                opt_user = plo.opt_user
        for module_name in module_name_list:
            sql_migrate_bo.set_app_name(module_name)
            status, msg = SqlMigrateBusiness.__start_check_sql(sql_migrate_bo, sql_content_list, db_name_list, opt_user)
            if status == PipelineStatus.failure:
                check_failure.append(msg)
        if check_failure:
            return PipelineStatus.failure, ','.join(check_failure)
        else:
            return PipelineStatus.success, check_result

    @staticmethod
    def __start_check_sql(sql_migrate_bo, sql_content_list, db_name_list, opt_user):
        logger.info('__start_check_sql============================{}'.format(sql_migrate_bo))
        db_type = get_db_type_by_module_name(sql_migrate_bo.module_name)
        check_result = "sql检查成功"
        if db_type != "mysql":
            check_result = "非mysql的数据库跳过检测"
            logger.info(check_result)
            return PipelineStatus.success, check_result
        check_suite_code = sql_migrate_bo.check_suite_code
        if not check_suite_code:
            check_result = "db_mgt_archery_check_test_env表没有配置sql检测对应的环境，跳过检测"
            logger.info(check_result)
            return PipelineStatus.success, check_result

        # 根据应用和环境找到库信息和库对应的archery信息
        archery_info_list = SqlMigrateBusiness.__get_archery_info(sql_migrate_bo)
        # 会暴露密码，暂不打印
        # logger.info('===================================archery_info_list:{}'.format(archery_info_list))
        guard_switch = GuardSwitchInfo.get(GuardSwitchInfo.guard_name == 'check_sql').guard_switch
        if not archery_info_list:
            check_result = "没有找到数据库对应的archery绑定信息，db_mgt_archery_bind表没有配置！"

            if guard_switch:
                log.error(check_result)
                return PipelineStatus.failure, check_result
            else:
                log.warning(check_result)
                return PipelineStatus.success, check_result

        for sql_content in sql_content_list:
            for archery_info in archery_info_list:
                db_name = sql_content.get("db_name")
                if db_name == archery_info.get("db_name") and db_name not in db_name_list:
                    db_name_list.append(db_name)
                    archery_id = archery_info.get("archery_id")

                    access_token = common_tool.get_user_archery_access_token(archery_id, opt_user)

                    archery_host_url = 'http://' + archery_info.get("archery_srv_hosts") \
                                       + ':' + str(archery_info.get("archery_srv_port")) if archery_info.get(
                        "archery_srv_port") else 'http://' + archery_info.get("archery_srv_hosts")
                    request_address = archery_host_url + INTERFACE_URL['sql_check_api_path']
                    param = {"request_address": request_address, "access_token": access_token,
                             "db_name": sql_content.get('db_name'), "sql_content": sql_content.get("sql_content"),
                             "instance_id": archery_info.get("instance_id"),
                             "archery_bind_id": archery_info.get("archery_bind_id"),
                             "module_name": sql_migrate_bo.module_name, "suite_code": sql_migrate_bo.check_suite_code,
                             "iteration_id": sql_migrate_bo.iteration_id, "opt_user": opt_user}
                    r_status, result = test_publish_aio_util.sql_check(param)

                    if result.get("code") == 'token_not_valid':
                        raise ValueError("archery token过期了，请重新登录平台解决！")

                    if r_status and result.get("error_count") < 1:
                        logger.info("sql在archery检测通过")
                    else:
                        err_info = []
                        for item in result.get("rows"):
                            if item.get("errlevel") > 0:
                                err_info.append(
                                    {"本次检测错误个数": item.get("errlevel"), "错误信息": item.get("errormessage"),
                                     "sql内容": item.get("sql")})
                        log.error('=========》sql检查错误信息：{}'.format(err_info))
                        if guard_switch:
                            asyncio.run(Compiler.request_ai(
                                '这一步是SQL检测时打印的日志。日志中有没有报出问题？主要是什么问题？报文如下',
                                "这是请求参数：{}".format(param) + "这是sql校验结果信息： {}".format(result)))
                            raise ValueError("sql校验不通过")
                        else:
                            log.warning("强制检测未打开，暂不拦截！")

        return PipelineStatus.success, check_result

    @staticmethod
    @recode_sql_check_detail
    def __push_sql(sql_migrate_bo: SqlMigrateBo) -> tuple[str, str]:
        result = "sql制品上传成功"
        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(sql_migrate_bo.module_name)
        if vcs_info_list and len(vcs_info_list) > 0:
            # 获取DB中的SQL文件
            module_name = sql_migrate_bo.module_name
            iteration_id = sql_migrate_bo.iteration_id
            biz_base_db = sql_migrate_bo.biz_base_db
            workspace = sql_migrate_bo.workspace
            br_name = sql_migrate_bo.br_name
            # 因比对md5，没有变化的文件也需要全部获取。zt@2024-09-25
            # db_mgt_sql_list = db_mgt_info.get_db_mgt_sql(module_name, iteration_id)
            db_mgt_sql_list = db_mgt_info.get_all_db_mgt_sql_only_iter(iteration_id)
            # 按库分组
            if db_mgt_sql_list:
                curr_time = datetime.now()

                db_mgt_sql_list_dict = {}
                for tmp_dict in db_mgt_sql_list:
                    sql_ver_group = tmp_dict.get('sql_ver_group')
                    tmp_list = db_mgt_sql_list_dict.get(sql_ver_group)
                    if not tmp_list:
                        tmp_list = [tmp_dict]
                        db_mgt_sql_list_dict[sql_ver_group] = tmp_list
                    else:
                        tmp_list.append(tmp_dict)

                # 一个应用可能有多个仓库
                if db_mgt_sql_list_dict and len(db_mgt_sql_list_dict) > 0:
                    logger.info(">>>> db_mgt_sql_list_dict: {}".format(db_mgt_sql_list_dict))
                    # 循环上传制品（针对一个仓库）
                    all_upd_tuple_list = []
                    sql_ver_db = None
                    logger.info("=========================db_mgt_sql_list_dict================".format(
                        db_mgt_sql_list_dict.items()))
                    for k, v in db_mgt_sql_list_dict.items():
                        SqlMigrateBusiness.__ck_gitlab(k, br_name)
                        for sql_dict in v:
                            logger.info("清空本地制品库")
                            SqlMigrateBusiness.__clean_gitlab(workspace, k, sql_dict.get('sql_ver_db'))
                        # 复制sql制品文件
                        upd_tuple_list = []
                        for sql_dict in v:
                            sql_ver_db = sql_dict.get('sql_ver_db')

                            db_id = sql_dict.get('id')
                            sql_ver_name = sql_dict.get('sql_ver_name')
                            sql_file_path = sql_dict.get('sql_file_path')

                            SqlMigrateBusiness.__cp_gitlab(k, sql_ver_db, sql_file_path, sql_ver_name)
                            # 数据更新
                            # 其它更新数据
                            update_user = 'howbuyscm'
                            update_time = curr_time
                            sql_ver_upload_status = True
                            sql_ver_upload_time = curr_time
                            # 更新列表
                            upd_tuple = (
                                update_user, update_time, sql_ver_upload_status, sql_ver_upload_time, db_id)
                            upd_tuple_list.append(upd_tuple)

                        # 一个库多条语句，一次提交。
                        if upd_tuple_list and len(upd_tuple_list) > 0:
                            commit_msg = "add: 应用「{}」迭代「{}」添加sql文件。".format(module_name, iteration_id)
                            SqlMigrateBusiness.__add_commit_push_gitlab(k, sql_ver_db, commit_msg, br_name)
                            all_upd_tuple_list.extend(upd_tuple_list)
                            logger.info(">>>> all_upd_tuple_list: {}".format(all_upd_tuple_list))
                    # 所有库（一个应用可能多个库），更新sql制品上传状态。
                    if all_upd_tuple_list and len(all_upd_tuple_list) > 0:
                        db_mgt_info.upd_db_mgt_sql_for_upload(all_upd_tuple_list)
        else:
            result = ">>>> 警告：应用「{}」在push_sql阶段检测到有绑定「SQL制品」信息，db_mgt_app_bind表中没有配置。". \
                format(sql_migrate_bo.module_name)
            log.warning(result)

        return PipelineStatus.success, result

    @staticmethod
    def __check_sql_need_all_rebuild(iteration_id):
        # iteration_id = sql_migrate_bo.iteration_id

        obj = DbMgtSql.select().where(DbMgtSql.iteration_id == iteration_id).order_by(DbMgtSql.create_time).first()
        if obj:
            sql_min_create_time = obj.create_time
            log.info("sql_min_create_time==={}".format(sql_min_create_time))

            obj = BranchesModel.select().where(BranchesModel.pipeline_id == iteration_id).get_or_none()
            log.info("obj==={}".format(obj))
            project_group = obj.project_group
            log.info("project_group==={}".format(project_group))
            obj = BranchesModel.select().where(BranchesModel.project_group == project_group,
                                               BranchesModel.br_status == 'close').order_by(
                BranchesModel.br_end_date.desc()).first()
            log.info("obj==={}".format(obj))
            br_end_date = obj.br_end_date
            log.info("br_end_date==={}".format(br_end_date))
            # 字符串转时间
            if br_end_date:
                br_end_date = datetime.strptime(br_end_date, '%Y年%m月%d日 %H时%M分')
                log.info("br_end_date==={}".format(br_end_date))

            # 如果最新归档时间大于sq构建的最小时间，则需要清空全部重新编译
            if br_end_date > sql_min_create_time:
                log.info("需要清空全部重新编译")
                DbMgtSql.delete().where(DbMgtSql.iteration_id == iteration_id).execute()
            else:
                log.info("不需要清空全部重新编译")

    @staticmethod
    def __check_sql_need_clear_and_rebuild(iteration_id, module_name):
        # 找到迭代的sql构建时间：只找关联了库的应用（核心优化①）
        sql_file_dict_list = db_mgt_info.get_min_sql_time_for_clear(iteration_id, module_name)
        if sql_file_dict_list:
            sql_file_dict_first = sql_file_dict_list[0]
            sql_min_time = sql_file_dict_first.get('create_time')
            log.info(">>>> sql_min_time：{}".format(sql_min_time))

            if sql_min_time:
                # 计算分组的最后归档时间：优化只计算有SQL的迭代（核心优化②）！
                archive_time_dict = db_mgt_info.get_archive_time_for_group(iteration_id)
                log.info(">>>> archive_time_dict：{}".format(archive_time_dict))
                if archive_time_dict:
                    group_archive_time_str = archive_time_dict.get('archive_time')
                    # 字符串转时间
                    if group_archive_time_str:
                        log.info(">>>> group_archive_time_str：{}".format(group_archive_time_str))
                        group_archive_time = datetime.strptime(group_archive_time_str, '%Y年%m月%d日 %H时%M分')
                        log.info(">>>> group_archive_time：{}".format(group_archive_time))

                        # 对比 & 全清
                        if group_archive_time > sql_min_time:
                            log.info(">>>> 有带SQL的迭代归档，归档时间为：{}。本迭代SQL需要先清理后，再全量重编。".format(group_archive_time_str))
                            DbMgtSql.delete().where(DbMgtSql.iteration_id == iteration_id).execute()

    @staticmethod
    def __get_archery_info(sql_migrate_bo):
        db_info_result = db_mgt_bind_view.get(module_name=sql_migrate_bo.module_name,
                                              suite_code=sql_migrate_bo.check_suite_code)
        # 会暴露密码，暂不打印
        # logger.info("db_info_result==={}".format(db_info_result))
        if not db_info_result:
            archery_info_list = []
        else:
            archery_info_list = db_mgt_info.get_archery_info_by_db_info(db_info_result)
        return archery_info_list

    @staticmethod
    def __get_sql_content_list(sql_migrate_bo):
        content_sql_dict = {}
        sql_file_name_list = []
        # db_mgt_sql_list = db_mgt_info.get_db_mgt_sql(sql_migrate_bo.module_name, sql_migrate_bo.iteration_id)
        db_mgt_sql_list = db_mgt_info.get_sql_file_by_module_and_iter(sql_migrate_bo.iteration_id)

        if db_mgt_sql_list:
            for tmp_dict in db_mgt_sql_list:
                logger.info(tmp_dict)
                if tmp_dict.get('sql_ver_name') not in sql_file_name_list:
                    sql_file_name_list.append(tmp_dict.get('sql_ver_name'))
                    read_spl_path = os.path.join(sql_migrate_bo.workspace, tmp_dict.get('sql_file_path'))
                    logger.info('====================================read_spl_path:{}'.format(read_spl_path))
                    with open(read_spl_path, encoding='utf-8') as r:
                        content_sql = r.read()
                        if tmp_dict.get('sql_ver_db') not in content_sql_dict:
                            content_sql_dict[tmp_dict.get('sql_ver_db')] = content_sql
                        else:
                            content_sql_dict[tmp_dict.get('sql_ver_db')] += content_sql
        content_sql_list = []
        for k, v in content_sql_dict.items():
            content_sql_list.append({"db_name": k, "sql_content": v})
        return content_sql_list

    @staticmethod
    def __parse_db_info_with_path(db_group_dict, br_name, sql_src_type, sql_file_path):
        # vcs类型枚举
        vcs_enum = SqlMigrateBusiness.__get_vcs_type_enum(sql_src_type)
        logger.info("db_group_dict===={}".format(db_group_dict))
        logger.info("sql_file_path===={}".format(sql_file_path))

        if vcs_enum == VcsTypeEnum.VCS_SVN:
            db_name, db_group_name = SqlMigrateBusiness.__get_db_name_group_from_svn(br_name,
                                                                                     sql_file_path,
                                                                                     db_group_dict)
        elif vcs_enum == VcsTypeEnum.VCS_GIT:
            db_name, db_group_name = SqlMigrateBusiness.__get_db_name_group_from_git(sql_file_path)
        else:
            db_name, db_group_name = SqlMigrateBusiness.__get_db_name_group_from_gitlab(br_name,
                                                                                        sql_file_path,
                                                                                        db_group_dict)

        return db_name, db_group_name

    @staticmethod
    def __get_db_name_group_from_svn(br_name, sql_file_path, db_group_dict):
        # 库名解析正则
        br_path = 'devops-sql/{}'.format(br_name)
        db_name_pattern = r'{}/(\w+).*'.format(br_path)
        logger.info("db_name_pattern===={}".format(db_name_pattern))
        logger.info("sql_file_path===={}".format(sql_file_path))
        # 匹配解析
        search_obj = re.search(db_name_pattern, sql_file_path)
        if search_obj:
            db_name = search_obj.group(1)
            db_group_name = db_group_dict.get(db_name)
        else:
            log.warning(">>>> 格式错误：文件「{}」无法解析数据库名，跳过。".format(sql_file_path))
        return db_name, db_group_name

    @staticmethod
    def __get_db_name_group_from_git(sql_src_path):
        pass

    @staticmethod
    def __get_db_name_group_from_gitlab(br_name, sql_file_path, db_group_dict):
        # 库名解析正则
        br_path = '{}/{}/{}'.format(sql_file_path.split('/')[0], br_name, 'db')
        # br_path = 'devops-sql/{}'.format(br_name)
        db_name_pattern = r'{}/(\w+).*'.format(br_path)
        logger.info("db_name_pattern===={}".format(db_name_pattern))
        logger.info("sql_file_path===={}".format(sql_file_path))
        # 匹配解析
        search_obj = re.search(db_name_pattern, sql_file_path)
        logger.info('==================search_obj============{}'.format(search_obj))
        if search_obj:
            db_name = search_obj.group(1)
            db_group_name = db_group_dict.get(db_name)
        else:
            log.warning(">>>> 格式错误：文件「{}」无法解析数据库名，跳过。".format(sql_file_path))
        return db_name, db_group_name

    @staticmethod
    def __get_vcs_type_enum(db_vcs_type):
        if not db_vcs_type:
            raise ValueError("数据异常「SQL制品」：绑定信息没有「db_vcs_type」。")

        db_vcs_type_enum = None
        for vcs_enum in VcsTypeEnum:
            if vcs_enum.type_name == db_vcs_type:
                db_vcs_type_enum = vcs_enum
        if not db_vcs_type_enum:
            raise ValueError("数据异常「SQL制品」：不支持的「db_vcs_type」。")

        return db_vcs_type_enum

    @staticmethod
    def __get_devops_sql(vcs_info_dict, sql_migrate_bo):
        # vcs类型枚举
        db_vcs_type = vcs_info_dict.get('db_vcs_type')
        db_vcs_type_enum = SqlMigrateBusiness.__get_vcs_type_enum(db_vcs_type)

        vcs_root_url = SqlMigrateBusiness.__get_vsc_url(db_vcs_type_enum, vcs_info_dict)
        git_group = sql_migrate_bo.git_group
        br_name = sql_migrate_bo.br_name
        if db_vcs_type_enum == VcsTypeEnum.VCS_SVN:
            vcs_iter_url = '{}/{}/{}'.format(vcs_root_url, git_group, br_name)
        elif db_vcs_type_enum == VcsTypeEnum.VCS_GITLAB:
            vcs_iter_url = '{}{}/{}.git'.format(vcs_root_url, git_group, 'devops-std')

        sql_migrate_bo.set_vcs_iter_url(vcs_iter_url)
        sql_migrate_bo.set_db_vcs_type(db_vcs_type)

        return SqlMigrateBusiness.__download_devops_sql(db_vcs_type_enum, sql_migrate_bo)

    @staticmethod
    def __download_devops_sql(db_vcs_type_enum, sql_migrate_bo):
        if db_vcs_type_enum == VcsTypeEnum.VCS_SVN:
            sql_file_dict = SqlMigrateBusiness.__download_devops_sql_from_svn(sql_migrate_bo)
        elif db_vcs_type_enum == VcsTypeEnum.VCS_GIT:
            sql_file_dict = SqlMigrateBusiness.__download_devops_sql_from_git(sql_migrate_bo)
        else:
            sql_file_dict = SqlMigrateBusiness.__download_devops_sql_from_gitlab(sql_migrate_bo)
        return sql_file_dict

    @staticmethod
    def __download_devops_sql_from_svn(sql_migrate_bo):
        sql_file_dict = None

        vcs_iter_url = sql_migrate_bo.vcs_iter_url
        svn_cmd = SvnCmd()
        sql_dir_list = svn_cmd.svn_list(vcs_iter_url)
        # logger.info(">>>> sql_dir_list：{}".format(sql_dir_list))
        if sql_dir_list and len(sql_dir_list) > 1:
            # 定义目录
            sql_src_dir_name = 'devops-sql'
            # 清理
            cmd = 'rm -rf {}'.format(sql_src_dir_name)
            os.system(cmd)
            # 检出
            svn_cmd.svn_checkout(vcs_iter_url, sql_src_dir_name)
            sql_file_dict = SqlMigrateBusiness.__get_sql_file_dict(sql_src_dir_name)
        else:
            logger.info("======== 没找到sql文件 ========")
            logger.info("== 应用:「{}」".format(sql_migrate_bo.module_name))
            logger.info("== 迭代:「{}」".format(sql_migrate_bo.iteration_id))
            logger.info("== 路径:「{}」".format(vcs_iter_url))
            logger.info("================================")

        return sql_file_dict

    @staticmethod
    def __get_sql_file_dict(sql_src_dir_name):
        # cmd = "find {} | grep -iE '.*.sql'".format(sql_src_dir_name)
        cmd = '''find {} -type f \( -path "*/DDL/*.sql" -o -path "*/DML/*.sql" \)'''.format(sql_src_dir_name)
        logger.info(cmd)
        ret_obj = None
        sql_file_list = None
        sql_file_dict = None
        try:
            ret_obj = exec_local_cmd(cmd)
        except CmdException as e:
            logger.info("devops-sql下是空目录，没找到任务sql文件")
        logger.info("==============tree -fi====={}".format(cmd))
        if ret_obj:
            sql_file_list = bytes.decode(ret_obj.stdout, encoding='utf-8').split(os.linesep)
        logger.info("sql_file_list====={}".format(sql_file_list))
        if sql_file_list and len(sql_file_list) > 1:
            sql_file_list.sort()
            sql_file_dict = {}
            for sql_file_str in sql_file_list:
                if sql_file_str.endswith(".sql"):
                    # 判断sql_file_str中有没有空格，
                    check_sql_name(sql_file_str)
                    # 切分sql文件
                    new_file_seq, sql_count = split_sql_file_to_seq(sql_file_str)
                    if '/DML/' in sql_file_str and sql_count > int(SQL_CONFIG.get("dml_limit")):
                        raise ValueError(">>>> DML文件超过限制{}条，可能涉及信息保密，请通过其他途径提交DML！".format(
                            int(SQL_CONFIG.get("dml_limit"))))
                    for new_file in new_file_seq:
                        sql_file_sha256 = file_sha256(new_file)
                        sql_file_dict[new_file] = sql_file_sha256
        logger.info("sql_file_dict===={}".format(sql_file_dict))
        return sql_file_dict

    @staticmethod
    def __download_devops_sql_from_git(sql_migrate_bo):
        sql_file_dict = None
        return sql_file_dict

    @staticmethod
    def __download_devops_sql_from_gitlab(sql_migrate_bo):
        sql_file_dict = None

        vcs_iter_url = sql_migrate_bo.vcs_iter_url
        br_name = sql_migrate_bo.br_name
        # 定义目录
        sql_src_dir_name = 'devops-std'
        # 清理
        cmd = 'rm -rf {}'.format(sql_src_dir_name)
        os.system(cmd)

        cmd = 'mkdir -p {}'.format(sql_src_dir_name)
        os.system(cmd)
        logger.info(cmd)
        SqlMigrateBusiness.__ck_gitlab_code(vcs_iter_url, br_name, sql_src_dir_name)

        sql_file_dict = SqlMigrateBusiness.__get_sql_file_dict(sql_src_dir_name)

        return sql_file_dict

    @staticmethod
    def __get_vsc_url(db_vcs_type_enum, vcs_info_dict):
        # vcs_root_url = None
        # 公用信息验证：
        db_vcs_protocol = vcs_info_dict.get('db_vcs_protocol')
        db_vcs_domain = vcs_info_dict.get('db_vcs_domain')
        vcs_proj_name = vcs_info_dict.get('vcs_proj_name')
        if not db_vcs_protocol:
            raise ValueError("数据异常「SQL制品」：绑定信息没有「db_vcs_protocol」。")
        if not db_vcs_domain:
            raise ValueError("数据异常「SQL制品」：绑定信息没有「db_vcs_domain」。")
        if not vcs_proj_name:
            raise ValueError("数据异常「SQL制品」：绑定信息没有「vcs_proj_name」。")

        vcs_root_path = vcs_info_dict.get('vcs_root_path')
        db_vcs_username = vcs_info_dict.get('db_vcs_username')

        if db_vcs_type_enum == VcsTypeEnum.VCS_SVN:
            db_vcs_context = vcs_info_dict.get('db_vcs_context')
            if not db_vcs_context:
                raise ValueError("数据异常「SQL制品」：绑定信息没有「db_vcs_context」。")
            if not vcs_root_path:
                raise ValueError("数据异常「SQL制品」：绑定信息没有「vcs_root_path」。")
            vcs_root_url = '{}://{}/{}{}/{}'.format(db_vcs_protocol,
                                                    db_vcs_domain,
                                                    db_vcs_context,
                                                    vcs_root_path,
                                                    vcs_proj_name)
        elif db_vcs_type_enum == VcsTypeEnum.VCS_GIT:
            if not db_vcs_username:
                raise ValueError("数据异常「SQL制品」：绑定信息没有「db_vcs_username」。")
            vcs_root_url = '{}@{}:{}/{}'.format(db_vcs_protocol,
                                                db_vcs_domain,
                                                vcs_root_path,
                                                vcs_proj_name)
        else:
            vcs_root_url = '{}@{}:'.format(db_vcs_protocol,
                                           db_vcs_domain)
        return vcs_root_url

    @staticmethod
    def __ins_or_upd_sql_file(sql_migrate_bo, sql_file_dict):
        if sql_file_dict:
            module_name = sql_migrate_bo.module_name
            iteration_id = sql_migrate_bo.iteration_id
            # 获取DB中的SQL文件
            db_mgt_info.del_db_mgt_sql(module_name, iteration_id)
            # print(db_mgt_info.get_db_mgt_sql(module_name, iteration_id))
            db_mgt_sql_dict = {}
            # 遍历VCS中SQL文件，插入新增文件
            db_ins_tuple_list = []
            curr_time = datetime.now()
            file_name_list = []
            logger.info(sql_file_dict)
            sql_file_name_list = []
            for k, v in sql_file_dict.items():
                file_name = os.path.basename(k)
                logger.info("file_name")
                logger.info("迭代{}的{}文件不存在，需要添加！".format(iteration_id, file_name))
                if file_name in file_name_list:
                    raise ValueError("文件异常：存在同名文件「{}」(不同目录下的文件也不能同名）！".format(file_name))
                else:
                    file_name_list.append(file_name)

                db_dict = db_mgt_sql_dict.get(k)
                if db_dict:
                    sql_file_hash = db_dict.get('sql_file_hash')
                    if v != sql_file_hash:
                        log.warning(">>>> 环境异常：「{}」文件内容有变动，执行「migrate」时将校验失败。".format(k))
                else:
                    create_user = 'howbuyscm'
                    create_time = curr_time
                    update_user = 'howbuyscm'
                    update_time = curr_time
                    stamp = 0
                    # module_name = module_name
                    # iteration_id = iteration_id
                    git_group = sql_migrate_bo.git_group
                    br_name = sql_migrate_bo.br_name
                    sql_file_path = k
                    sql_file_name = file_name
                    sql_file_hash = v
                    sql_src_type = sql_migrate_bo.db_vcs_type
                    sql_src_path = sql_migrate_bo.vcs_iter_url
                    sql_ver_name = None
                    sql_ver_db = None
                    sql_ver_group = None
                    sql_ver_tgt = None
                    sql_ver_upload_status = False
                    sql_ver_upload_time = None
                    sql_file_desc = None

                    db_ins_tuple = (
                        create_user,
                        create_time,
                        update_user,
                        update_time,
                        stamp,
                        module_name,
                        iteration_id,
                        git_group,
                        br_name,
                        sql_file_path,
                        sql_file_name,
                        sql_file_hash,
                        sql_src_type,
                        sql_src_path,
                        sql_ver_db,
                        sql_ver_group,
                        sql_ver_name,
                        sql_ver_tgt,
                        sql_ver_upload_status,
                        sql_ver_upload_time,
                        sql_file_desc,
                    )
                    db_ins_tuple_list.append(db_ins_tuple)
                    sql_file_name_list.append(sql_file_name)
                    logger.info("待插入的sql数据元组为： {}".format(db_ins_tuple))
            db_mgt_info.del_db_mgt_sql_by_file_name_iteration_id(iteration_id, sql_file_name_list)
            db_mgt_info.ins_db_mgt_sql(db_ins_tuple_list)

    @staticmethod
    def __ins_or_upd_sql_file_by_md5(sql_migrate_bo, sql_file_dict, sql_db_dict, db_name_app_dict):
        """优化sql的处理基于md5对比。zt@2024-09-09"""
        if not sql_migrate_bo:
            raise ValueError(">>>> 数据异常：应用的SQL处理对象为空！")

        iteration_id = sql_migrate_bo.iteration_id
        if not iteration_id:
            raise ValueError(">>>> 数据异常：SQL处理对应的「迭代名」为空！")

        opt_user = "howbuyscm"
        curr_time = datetime.now()
        # 是否整个迭代SQL重新编译
        sql_file_dict_list = db_mgt_info.get_sql_file_by_module_and_iter(iteration_id)
        db_sql_file_dist = {}
        if sql_file_dict_list:
            db_sql_file_dist = {
                obj.get("sql_file_name"): obj for obj in sql_file_dict_list if obj and obj.get("sql_file_name")}

        # 2、再解析开发上传的SQL文件信息：
        dev_sql_file_dist = {}
        if sql_file_dict:
            for file_name, file_hash in sql_file_dict.items():
                dev_sql_file_name = os.path.basename(file_name)
                db_dict = sql_db_dict.get(file_name)
                if dev_sql_file_dist.get(dev_sql_file_name) is not None:
                    raise ValueError(
                        ">>>文件异常：存在同名文件「{}」(不同目录下的文件也不能同名）！".format(dev_sql_file_name))
                db_name = db_dict.get('db_name')
                module_name = db_name_app_dict.get(db_name)
                dev_sql_obj = {
                    "sql_file_name": dev_sql_file_name,
                    "sql_file_hash": file_hash,
                    # bo中的属性填充：
                    "module_name": module_name,
                    "iteration_id": iteration_id,
                    "git_group": sql_migrate_bo.git_group,
                    "br_name": sql_migrate_bo.br_name,
                    "sql_ver_db": db_dict.get('db_name'),
                    "sql_ver_group": db_dict.get('db_group'),
                    "sql_file_path": file_name,
                    "sql_src_type": sql_migrate_bo.db_vcs_type,
                    "sql_src_path": sql_migrate_bo.vcs_iter_url,
                }
                dev_sql_file_dist[dev_sql_file_name] = dev_sql_obj

        # 3、忽略文件名，完全基于hash对比：
        #   1）新增文件  --> 新增 --> 编译
        #   2）删除文件  --> 删除 --> 不处理
        #   3）修改文件内容 --> 删除 & 新增 --> 编译
        #   4）重命名   --> 删除 & 新增 --> 编译（就算只修改了文件名，flyway验证一样过不去，只能重新编译）
        #   5）重命名 & 修改内容 --> 删除 & 新增 --> 编译
        #   6）文件名 & 内容都不变 --> 不处理 --> 不处理
        file_comp_st = datetime.now()
        db_add_sql_file_list, db_del_sql_file_list = SqlMigrateBusiness.__comp_sql_file_dict(db_sql_file_dist,
                                                                                             dev_sql_file_dist)
        file_comp_et = datetime.now()
        file_comp_cost_time = SqlMigrateBusiness.get_cost_time(file_comp_st, file_comp_et)
        logger.info(">>>> sql文件比对耗时（秒）：{}".format(file_comp_cost_time))
        logger.info(">>>> sql文件比对结果，删除列表：db_del_sql_file_list = {}".format(db_del_sql_file_list))
        logger.info(">>>> sql文件比对结果，新增列表：db_add_sql_file_list = {}".format(db_add_sql_file_list))
        # 有增有减，hash排除：经过讨论，暂时不需要实现。
        # 批量处理：
        del_count = 0
        ins_count = 0
        if db_del_sql_file_list:
            del_count = SqlMigrateBusiness.__del_sql_file_from_db_with_batch(db_del_sql_file_list)

        if db_add_sql_file_list:
            ins_count = SqlMigrateBusiness.__ins_sql_file_to_db_with_batch(db_add_sql_file_list, opt_user, curr_time)

        logger.info(">>>> sql文件处理结果，删除数量：{}，新增数量：{}".format(del_count, ins_count))

    @staticmethod
    def __comp_sql_file_dict(db_sql_file_dict, dev_sql_file_dict):
        """比对两个文件列表，计算出变化的部分。zt@2024-09-10"""
        logger.info(">>>> db_sql_file_dict={}".format(db_sql_file_dict))
        logger.info(">>>> dev_sql_file_dict={}".format(dev_sql_file_dict))
        # 解析
        db_add_sql_file_list = []
        db_del_sql_file_list = []
        if not db_sql_file_dict:
            # db中没有，全量新增：
            db_add_sql_file_list = [item for item in dev_sql_file_dict.values()]
        elif not dev_sql_file_dict:
            # 再次编译时清空，全量删除：
            db_del_sql_file_list = [item for item in db_sql_file_dict.values()]
        else:
            # 对比处理：嵌套非全量循环，效率最高可提高两个数量级：N*N --> 1+1。
            # db_dict = dict(sorted(db_sql_file_dict.items()))
            # dev_dict = dict(sorted(dev_sql_file_dict.items()))
            # 已知风险：
            #   1、DDL和DML混装排序，顺序可能会DML在前。
            #   2、如果两次修改DDL，可能造成DDL放在最后。
            db_key_list = sorted(db_sql_file_dict)
            dev_key_list = sorted(dev_sql_file_dict)
            db_count = len(db_key_list)
            dev_count = len(dev_key_list)
            logger.info(">>>> db_key_list={}".format(db_key_list))
            logger.info(">>>> dev_key_list={}".format(dev_key_list))
            logger.info(">>>> db_count={}, dev_count={}".format(db_count, dev_count))

            i = 0
            j = 0
            while i < db_count:
                db_file_name = db_key_list[i]

                # 右尽左删：
                if j == dev_count:
                    logger.info(">>>> 右尽左删: i={}, j={}, db_file_name={}".format(i, j, db_file_name))
                    i += 1
                    db_file_obj = db_sql_file_dict[db_file_name]
                    db_del_sql_file_list.append(db_file_obj)

                while j < dev_count:
                    dev_file_name = dev_key_list[j]
                    # 算法调试打印
                    logger.info(">>>> i={}, j={}".format(i, j))
                    logger.info(">>>> db_file_name={}, dev_file_name={}".format(db_file_name, dev_file_name))

                    # 右进一：
                    if db_file_name > dev_file_name:
                        j += 1
                        dev_file_obj = dev_sql_file_dict[dev_file_name]
                        db_add_sql_file_list.append(dev_file_obj)
                    # 左进一：
                    else:
                        i += 1
                        if db_file_name < dev_file_name:
                            db_file_obj = db_sql_file_dict[db_file_name]
                            db_del_sql_file_list.append(db_file_obj)
                        else:
                            # 左右进一：
                            j += 1
                            # hash对比：
                            db_file_obj = db_sql_file_dict[db_file_name]
                            db_file_hash = db_file_obj.get("sql_file_hash") if db_file_obj else None
                            dev_file_obj = dev_sql_file_dict[dev_file_name]
                            dev_file_hash = dev_file_obj.get("sql_file_hash") if dev_file_obj else None
                            if db_file_hash != dev_file_hash:
                                db_del_sql_file_list.append(db_file_obj)
                                db_add_sql_file_list.append(dev_file_obj)
                        # 左尽右增：
                        if i == db_count:
                            for idx in range(j, dev_count):
                                dev_file_name = dev_key_list[idx]
                                logger.info(">>>> 左尽右增: i = {}, j -> idx = {}, dev_file_name = {}".format(
                                    i, idx, dev_file_name))
                                dev_file_obj = dev_sql_file_dict[dev_file_name]
                                db_add_sql_file_list.append(dev_file_obj)
                        # 算法难点：
                        break

        return db_add_sql_file_list, db_del_sql_file_list

    @staticmethod
    def __del_sql_file_from_db_with_batch(db_del_sql_file_list):
        """批量处理sql文件：删除。zt@2024-09-10"""
        del_count = 0
        if db_del_sql_file_list:
            del_id_str = ",".join(str(obj.get("id")) for obj in db_del_sql_file_list if obj and obj.get("id"))
            del_count = db_mgt_info.del_db_mgt_sql_with_batch(del_id_str)
        logger.info(">>>> 删除sql文件数据（条）：{}".format(del_count))
        return del_count

    @staticmethod
    def __ins_sql_file_to_db_with_batch(db_add_sql_file_list, opt_user="howbuyscm", opt_time=None):
        """批量处理sql文件：新增。zt@2024-09-11"""

        ins_count = 0
        if db_add_sql_file_list:
            db_ins_tuple_list = []
            for obj in db_add_sql_file_list:
                if obj:
                    if not opt_time:
                        opt_time = datetime.now()
                    # 系统属性
                    create_user = opt_user
                    create_time = opt_time
                    update_user = opt_user
                    update_time = opt_time
                    stamp = 0
                    # 本阶段业务属性
                    sql_file_name = obj.get("sql_file_name")
                    sql_file_hash = obj.get("sql_file_hash")
                    module_name = obj.get("module_name")
                    iteration_id = obj.get("iteration_id")
                    git_group = obj.get("git_group")
                    br_name = obj.get("br_name")
                    sql_file_path = obj.get("sql_file_path")
                    sql_src_type = obj.get("sql_src_type")
                    sql_src_path = obj.get("sql_src_path")
                    sql_ver_upload_status = False
                    # 后续阶段业务属性
                    sql_ver_name = None
                    sql_ver_db = obj.get('sql_ver_db')
                    sql_ver_group = obj.get('sql_ver_group')
                    sql_ver_tgt = None
                    sql_ver_upload_time = None
                    sql_file_desc = None

                    db_ins_tuple = (
                        create_user,
                        create_time,
                        update_user,
                        update_time,
                        stamp,
                        module_name,
                        iteration_id,
                        git_group,
                        br_name,
                        sql_file_path,
                        sql_file_name,
                        sql_file_hash,
                        sql_src_type,
                        sql_src_path,
                        sql_ver_name,
                        sql_ver_db,
                        sql_ver_group,
                        sql_ver_tgt,
                        sql_ver_upload_status,
                        sql_ver_upload_time,
                        sql_file_desc,
                    )
                    db_ins_tuple_list.append(db_ins_tuple)
            if db_ins_tuple_list:
                ins_count = db_mgt_info.ins_db_mgt_sql(db_ins_tuple_list)

        logger.info(">>>> 新增sql文件数据（条）：{}".format(ins_count))

        return ins_count

    @staticmethod
    def __ck_gitlab_code(vcs_iter_url, br_name, sql_src_dir_name):
        # 与svn的克隆本地目录保持一致，例如：devops-sql/patest-0303/param/DDL/select1.sql
        cmd = 'pwd && git clone -b {} {} {}/{}'.format(br_name, vcs_iter_url, 'devops-std', br_name)
        logger.info(cmd)
        os.system(cmd)

    @staticmethod
    def __ck_gitlab(db_group_name, br_name):
        if not os.path.exists(db_group_name):
            sql_version_tgt = '************************:devops-sql/{}.git'.format(db_group_name)
            cmd = 'pwd && git clone {}'.format(sql_version_tgt)
            os.system(cmd)

        cmd = 'pwd && cd {} && git checkout -b {} && git branch -a'.format(db_group_name, br_name)
        os.system(cmd)

        cmd = 'pwd && cd {} && git pull origin {}'.format(db_group_name, br_name)
        os.system(cmd)

    @staticmethod
    def __cp_gitlab(sql_ver_group, sql_ver_db, sql_file_path, sql_ver_name):
        logger.info("========================****========================={}".format(sql_file_path.split('/')[-2]))
        if sql_file_path.split('/')[-2] in ['DDL', 'DML']:
            db_path = os.path.join(sql_ver_group, sql_ver_db, sql_file_path.split('/')[-2])
        else:
            db_path = os.path.join(sql_ver_group, sql_ver_db)
        if not os.path.exists(db_path):
            cmd = 'pwd && mkdir -p {}'.format(db_path)
            os.system(cmd)
        sql_ver_path = os.path.join(db_path, sql_ver_name)
        cmd = 'pwd && cp {} {}'.format(sql_file_path, sql_ver_path)
        logger.info("========================cmd========================={}".format(cmd))
        os.system(cmd)

    @staticmethod
    def __clean_gitlab(workspace, sql_ver_group, sql_ver_db):
        db_path = os.path.join(workspace, sql_ver_group, sql_ver_db)
        logger.info(db_path)
        if os.path.exists(db_path):
            cmd = 'rm -rf {}'.format(os.path.join(db_path, '*'))
            logger.info("删除命令：{}".format(cmd))
            os.system(cmd)

    @staticmethod
    def __add_commit_push_gitlab(sql_ver_group, sql_ver_db, commit_msg, br_name):
        db_path = os.path.join(sql_ver_group, sql_ver_db)
        cmd = 'pwd && cd {} && git pull origin {}'.format(db_path, br_name)
        os.system(cmd)
        cmd = 'pwd && cd {} && git add -A'.format(db_path)
        os.system(cmd)
        cmd = 'pwd && cd {} && git commit -m "{}"'.format(db_path, commit_msg)
        os.system(cmd)
        cmd = 'pwd && cd {} && git push origin {}'.format(db_path, br_name)
        os.system(cmd)

    @staticmethod
    def __get_db_group_dict(sql_migrate_bo):
        db_group_dict = None
        iteration_id = sql_migrate_bo.iteration_id
        # module_name = sql_migrate_bo.module_name
        # sql_update = sql_migrate_bo.sql_update
        # if sql_update:
        #     module_name = None
        db_group_list = db_mgt_info.get_db_group_by_iteration_id(iteration_id)
        if db_group_list and len(db_group_list) > 0:
            db_group_dict = {}
            for db_group_obj in db_group_list:
                k = db_group_obj.get('db_info_suffix_name')
                v = db_group_obj.get('db_group_name')
                db_group_dict[k] = v
        return db_group_dict

    @staticmethod
    def __get_db_name_list(sql_migrate_bo):
        iteration_id = sql_migrate_bo.iteration_id
        db_name_list = []
        db_name_group_dict = {}
        db_name_app_dict = {}
        db_group_list = db_mgt_info.get_db_group_by_iteration_id(iteration_id)
        if db_group_list and len(db_group_list) > 0:
            for db_group_obj in db_group_list:
                db_name_list.append(db_group_obj.get('db_info_suffix_name'))
                db_name_group_dict[db_group_obj.get('db_info_suffix_name')] = db_group_obj.get('db_group_name')
                app_module_name = db_group_obj.get('app_module_name')
                db_name_app_dict[db_group_obj.get('db_info_suffix_name')] = app_module_name
        return db_name_list, db_name_group_dict, db_name_app_dict

    strategy_pattern = {
        "STEP_PULL_SQL": __pull_sql.__func__,
        "STEP_MAKE_SQL": __make_sql.__func__,
        "STEP_PUSH_SQL": __push_sql.__func__,
        "STEP_CHECK_SQL": __check_sql.__func__,
    }

    @classmethod
    @PipelineRecorder()
    def _run_step(cls, step_name, sql_migrate_bo: SqlMigrateBo) -> tuple[str, str]:
        exec_status, exec_msg = cls.strategy_pattern[step_name](sql_migrate_bo)
        return exec_status, exec_msg

    @classmethod
    def call(cls, params: list):
        step_name = params[0]
        flag_file_dir = params[1]
        job_name = params[2]
        workspace = params[3]
        logger.info('-----------------------params:{}'.format(params))
        logger.info('-----------------------params:{}'.format(len(params)))
        biz_base_db = params[4]
        iteration_id = "_".join(job_name.split("_")[:-1])
        # 根据迭代名解析「组」和「分支」zt@2023-02-13
        iter_list = iteration_id.split('_')
        if iteration_id and len(iter_list) > 1:
            git_group = iter_list[0]
            br_name = iter_list[1]
        app_name = job_name.split("_")[-1]
        if "{" in app_name:
            app_name = app_name.split("{")[0]
        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
        logger.info(">>>> 缓存文件内容是 {}".format(json_dict))
        sid = json_dict["sid"]
        obj = DbMgtArcheryCheckTestEnv.select().where(DbMgtArcheryCheckTestEnv.module_name == app_name).get_or_none()
        if obj:
            check_suite_code = obj.check_suite_code
        else:
            check_suite_code = None
        sql_migrate_bo = SqlMigrateBo.Builder() \
            .set_iteration_id(iteration_id) \
            .set_app_name(app_name) \
            .set_sid(sid) \
            .set_flag_file_dir(flag_file_dir) \
            .set_workspace(workspace) \
            .set_git_group(git_group) \
            .set_br_name(br_name) \
            .set_biz_base_db(biz_base_db) \
            .set_check_suite_code(check_suite_code) \
            .build_bo()
        cls._run_step(step_name, sql_migrate_bo)


def split_sql_file_to_seq(sql_file):
    logger.info("src sqlFile:{},exist:{}".format(sql_file, os.path.exists(sql_file)))
    with open(sql_file, "r") as f:
        sqls = f.read()
    sql_seqs = sqlparse.split(sqls)
    sql_count = len(sql_seqs)
    # src_file_name = os.path.basename(sql_file)
    new_files_seq = [sql_file]
    # for sql in sql_seqs:
    #     sql_file_idx = '%03d' % count_sql
    #     new_file_name = str(sql_file_idx) + '_split_' + src_file_name
    #     new_file_path = sql_file.replace(src_file_name, new_file_name)
    #     with open(new_file_path, 'w') as f:
    #         f.write(sqlparse.format(sql.strip(), reindent=True))
    #     new_files_seq.append(new_file_path)
    #     count_sql = count_sql + 1
    # # 删除原文件只报留新文件
    # os.remove(sql_file)
    return new_files_seq, sql_count


def main(param_step_enum, param_str):
    """「SQL制品化」主方法 zt@2023-02-13"""
    logger.info("======== SQL制品化主方法：{} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    logger.info("==== 『入参』：{}".format(param_str))
    if param_step_enum == StepEnum.STEP_PRINT_INFO:
        logger.info("==== 『参数错误』：未传入执行步骤。")
    else:
        SqlMigrateBusiness.call(param_str[1:])


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_step_enum = StepEnum.STEP_PRINT_INFO
    if len(sys.argv) > 1:
        req_step_str = sys.argv[1]
        # 确定步骤
        try:
            req_step_enum = StepEnum[req_step_str]
        except KeyError:
            log.error("==== ！！！！参数异常！！！！ ====")
            err_msg = ">>>> 参数错误，只支持：「pull_sql」动作。"
            log.error(err_msg)
            exit(1)
    try:
        # 临时验证

        main(req_step_enum, sys.argv)
        logger.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        traceback.print_exc()
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        e_time = datetime.now()
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        logger.info("== 执行最终耗时（秒）：{}".format(cost_time))
        logger.info("====================")
        logger.info("====================\n\n")
