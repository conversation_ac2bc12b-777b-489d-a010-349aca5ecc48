from datetime import datetime

from dao.get.mysql import db_mgt_info


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]

if __name__ == '__main__':
    iteration_id = "FPS_patest20250526-checksqlclean"
    module_name = "howbuy-cms-server"
    sql_file_dict_list = db_mgt_info.get_min_sql_time_for_clear(iteration_id, module_name)
    print(sql_file_dict_list)
    archive_time_dict = db_mgt_info.get_archive_time_for_group(iteration_id)
    print(archive_time_dict)
    archive_time_str = archive_time_dict.get('archive_time')

    archive_time = datetime.strptime(archive_time_str, '%Y年%m月%d日 %H时%M分')
    print(archive_time)
