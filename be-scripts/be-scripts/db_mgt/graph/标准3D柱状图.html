<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts-gl.min.js"></script>

</head>
<body>
    <div id="e02850fa28d848049a2d341a1d861a73" class="chart-container" style="width:900px; height:600px;"></div>
    <script>
        var chart_e02850fa28d848049a2d341a1d861a73 = echarts.init(
            document.getElementById('e02850fa28d848049a2d341a1d861a73'), 'white', {renderer: 'canvas'});
        var option_e02850fa28d848049a2d341a1d861a73 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar3D",
            "data": [
                [
                    0,
                    0,
                    4
                ],
                [
                    1,
                    0,
                    0
                ],
                [
                    2,
                    0,
                    7
                ],
                [
                    3,
                    0,
                    1
                ],
                [
                    4,
                    0,
                    4
                ],
                [
                    5,
                    0,
                    10
                ],
                [
                    6,
                    0,
                    6
                ],
                [
                    7,
                    0,
                    6
                ],
                [
                    8,
                    0,
                    8
                ],
                [
                    9,
                    0,
                    1
                ],
                [
                    10,
                    0,
                    11
                ],
                [
                    11,
                    0,
                    9
                ],
                [
                    12,
                    0,
                    5
                ],
                [
                    13,
                    0,
                    6
                ],
                [
                    14,
                    0,
                    11
                ],
                [
                    15,
                    0,
                    12
                ],
                [
                    16,
                    0,
                    6
                ],
                [
                    17,
                    0,
                    5
                ],
                [
                    18,
                    0,
                    0
                ],
                [
                    19,
                    0,
                    3
                ],
                [
                    20,
                    0,
                    5
                ],
                [
                    21,
                    0,
                    3
                ],
                [
                    22,
                    0,
                    6
                ],
                [
                    23,
                    0,
                    12
                ],
                [
                    0,
                    1,
                    6
                ],
                [
                    1,
                    1,
                    8
                ],
                [
                    2,
                    1,
                    12
                ],
                [
                    3,
                    1,
                    1
                ],
                [
                    4,
                    1,
                    2
                ],
                [
                    5,
                    1,
                    9
                ],
                [
                    6,
                    1,
                    10
                ],
                [
                    7,
                    1,
                    1
                ],
                [
                    8,
                    1,
                    2
                ],
                [
                    9,
                    1,
                    11
                ],
                [
                    10,
                    1,
                    6
                ],
                [
                    11,
                    1,
                    9
                ],
                [
                    12,
                    1,
                    3
                ],
                [
                    13,
                    1,
                    12
                ],
                [
                    14,
                    1,
                    0
                ],
                [
                    15,
                    1,
                    10
                ],
                [
                    16,
                    1,
                    6
                ],
                [
                    17,
                    1,
                    8
                ],
                [
                    18,
                    1,
                    8
                ],
                [
                    19,
                    1,
                    10
                ],
                [
                    20,
                    1,
                    4
                ],
                [
                    21,
                    1,
                    7
                ],
                [
                    22,
                    1,
                    5
                ],
                [
                    23,
                    1,
                    4
                ],
                [
                    0,
                    2,
                    12
                ],
                [
                    1,
                    2,
                    10
                ],
                [
                    2,
                    2,
                    2
                ],
                [
                    3,
                    2,
                    7
                ],
                [
                    4,
                    2,
                    7
                ],
                [
                    5,
                    2,
                    11
                ],
                [
                    6,
                    2,
                    10
                ],
                [
                    7,
                    2,
                    8
                ],
                [
                    8,
                    2,
                    12
                ],
                [
                    9,
                    2,
                    2
                ],
                [
                    10,
                    2,
                    11
                ],
                [
                    11,
                    2,
                    5
                ],
                [
                    12,
                    2,
                    12
                ],
                [
                    13,
                    2,
                    0
                ],
                [
                    14,
                    2,
                    0
                ],
                [
                    15,
                    2,
                    1
                ],
                [
                    16,
                    2,
                    7
                ],
                [
                    17,
                    2,
                    8
                ],
                [
                    18,
                    2,
                    4
                ],
                [
                    19,
                    2,
                    2
                ],
                [
                    20,
                    2,
                    10
                ],
                [
                    21,
                    2,
                    4
                ],
                [
                    22,
                    2,
                    11
                ],
                [
                    23,
                    2,
                    5
                ],
                [
                    0,
                    3,
                    9
                ],
                [
                    1,
                    3,
                    7
                ],
                [
                    2,
                    3,
                    5
                ],
                [
                    3,
                    3,
                    11
                ],
                [
                    4,
                    3,
                    0
                ],
                [
                    5,
                    3,
                    12
                ],
                [
                    6,
                    3,
                    10
                ],
                [
                    7,
                    3,
                    11
                ],
                [
                    8,
                    3,
                    2
                ],
                [
                    9,
                    3,
                    6
                ],
                [
                    10,
                    3,
                    6
                ],
                [
                    11,
                    3,
                    9
                ],
                [
                    12,
                    3,
                    9
                ],
                [
                    13,
                    3,
                    12
                ],
                [
                    14,
                    3,
                    6
                ],
                [
                    15,
                    3,
                    0
                ],
                [
                    16,
                    3,
                    12
                ],
                [
                    17,
                    3,
                    1
                ],
                [
                    18,
                    3,
                    8
                ],
                [
                    19,
                    3,
                    4
                ],
                [
                    20,
                    3,
                    2
                ],
                [
                    21,
                    3,
                    6
                ],
                [
                    22,
                    3,
                    7
                ],
                [
                    23,
                    3,
                    9
                ],
                [
                    0,
                    4,
                    2
                ],
                [
                    1,
                    4,
                    6
                ],
                [
                    2,
                    4,
                    3
                ],
                [
                    3,
                    4,
                    0
                ],
                [
                    4,
                    4,
                    2
                ],
                [
                    5,
                    4,
                    10
                ],
                [
                    6,
                    4,
                    10
                ],
                [
                    7,
                    4,
                    1
                ],
                [
                    8,
                    4,
                    2
                ],
                [
                    9,
                    4,
                    0
                ],
                [
                    10,
                    4,
                    6
                ],
                [
                    11,
                    4,
                    10
                ],
                [
                    12,
                    4,
                    11
                ],
                [
                    13,
                    4,
                    1
                ],
                [
                    14,
                    4,
                    9
                ],
                [
                    15,
                    4,
                    1
                ],
                [
                    16,
                    4,
                    8
                ],
                [
                    17,
                    4,
                    12
                ],
                [
                    18,
                    4,
                    9
                ],
                [
                    19,
                    4,
                    1
                ],
                [
                    20,
                    4,
                    12
                ],
                [
                    21,
                    4,
                    9
                ],
                [
                    22,
                    4,
                    8
                ],
                [
                    23,
                    4,
                    4
                ],
                [
                    0,
                    5,
                    8
                ],
                [
                    1,
                    5,
                    0
                ],
                [
                    2,
                    5,
                    6
                ],
                [
                    3,
                    5,
                    0
                ],
                [
                    4,
                    5,
                    0
                ],
                [
                    5,
                    5,
                    4
                ],
                [
                    6,
                    5,
                    3
                ],
                [
                    7,
                    5,
                    12
                ],
                [
                    8,
                    5,
                    2
                ],
                [
                    9,
                    5,
                    7
                ],
                [
                    10,
                    5,
                    5
                ],
                [
                    11,
                    5,
                    3
                ],
                [
                    12,
                    5,
                    0
                ],
                [
                    13,
                    5,
                    0
                ],
                [
                    14,
                    5,
                    2
                ],
                [
                    15,
                    5,
                    4
                ],
                [
                    16,
                    5,
                    9
                ],
                [
                    17,
                    5,
                    8
                ],
                [
                    18,
                    5,
                    5
                ],
                [
                    19,
                    5,
                    9
                ],
                [
                    20,
                    5,
                    11
                ],
                [
                    21,
                    5,
                    6
                ],
                [
                    22,
                    5,
                    11
                ],
                [
                    23,
                    5,
                    10
                ]
            ],
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                ""
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 0,
        "max": 20,
        "inRange": {
            "color": [
                "#313695",
                "#4575b4",
                "#74add1",
                "#abd9e9",
                "#e0f3f8",
                "#ffffbf",
                "#fee090",
                "#fdae61",
                "#f46d43",
                "#d73027",
                "#a50026"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "orient": "vertical",
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    },
    "xAxis3D": {
        "data": [
            "12a",
            "1a",
            "2a",
            "3a",
            "4a",
            "5a",
            "6a",
            "7a",
            "8a",
            "9a",
            "10a",
            "11a",
            "12p",
            "1p",
            "2p",
            "3p",
            "4p",
            "5p",
            "6p",
            "7p",
            "8p",
            "9p",
            "10p",
            "11p"
        ],
        "nameGap": 20,
        "type": "category",
        "axisLabel": {
            "margin": 8
        }
    },
    "yAxis3D": {
        "data": [
            "Saturday",
            "Friday",
            "Thursday",
            "Wednesday",
            "Tuesday",
            "Monday",
            "Sunday"
        ],
        "nameGap": 20,
        "type": "category",
        "axisLabel": {
            "margin": 8
        }
    },
    "zAxis3D": {
        "nameGap": 20,
        "type": "value",
        "axisLabel": {
            "margin": 8
        }
    },
    "grid3D": {
        "boxWidth": 200,
        "boxHeight": 100,
        "boxDepth": 80,
        "viewControl": {
            "autoRotate": false,
            "autoRotateSpeed": 10,
            "rotateSensitivity": 1
        }
    },
    "title": [
        {
            "text": "\u6807\u51c63D\u67f1\u72b6\u56fe",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_e02850fa28d848049a2d341a1d861a73.setOption(option_e02850fa28d848049a2d341a1d861a73);
    </script>
</body>
</html>
