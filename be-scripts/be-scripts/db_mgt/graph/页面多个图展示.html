<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts-liquidfill.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/vintage.js"></script>

    
</head>
<body>
    <style>.box { justify-content:center; display:flex; flex-wrap:wrap;  }; </style>
    <div class="box">
                <div id="********************************" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_******************************** = echarts.init(
            document.getElementById('********************************'), 'vintage', {renderer: 'canvas'});
        var option_******************************** = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "Q2\u5404\u6708\u4efd\u53d1\u73b0\u7f3a\u9677",
            "legendHoverLink": true,
            "data": [
                55,
                52,
                74
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "Q2\u5404\u6708\u4efd\u53d1\u73b0\u7f3a\u9677"
            ],
            "selected": {
                "Q2\u5404\u6708\u4efd\u53d1\u73b0\u7f3a\u9677": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "4\u6708",
                "5\u6708",
                "6\u6708"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "\u6708\u4efd\u7ef4\u5ea6\u5206\u6790",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_********************************.setOption(option_********************************);
    </script>
<br/>                <div id="1f010abb830f493895747959c83c702a" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_1f010abb830f493895747959c83c702a = echarts.init(
            document.getElementById('1f010abb830f493895747959c83c702a'), 'vintage', {renderer: 'canvas'});
        var option_1f010abb830f493895747959c83c702a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "line",
            "name": "\u5546\u5bb6A",
            "connectNulls": false,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "\u5c0f\u7c73",
                    146
                ],
                [
                    "\u4e09\u661f",
                    125
                ],
                [
                    "\u534e\u4e3a",
                    47
                ],
                [
                    "\u82f9\u679c",
                    76
                ],
                [
                    "\u9b45\u65cf",
                    122
                ],
                [
                    "VIVO",
                    69
                ],
                [
                    "OPPO",
                    31
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "type": "min"
                    }
                ]
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u5546\u5bb6B",
            "connectNulls": false,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "\u5c0f\u7c73",
                    78
                ],
                [
                    "\u4e09\u661f",
                    148
                ],
                [
                    "\u534e\u4e3a",
                    76
                ],
                [
                    "\u82f9\u679c",
                    99
                ],
                [
                    "\u9b45\u65cf",
                    130
                ],
                [
                    "VIVO",
                    138
                ],
                [
                    "OPPO",
                    118
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "type": "max"
                    }
                ]
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u5c0f\u7c73",
                "\u4e09\u661f",
                "\u534e\u4e3a",
                "\u82f9\u679c",
                "\u9b45\u65cf",
                "VIVO",
                "OPPO"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Line-MarkPoint",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_1f010abb830f493895747959c83c702a.setOption(option_1f010abb830f493895747959c83c702a);
    </script>
<br/>                <div id="310aa0e12219458e8a738c755f67c2d6" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_310aa0e12219458e8a738c755f67c2d6 = echarts.init(
            document.getElementById('310aa0e12219458e8a738c755f67c2d6'), 'vintage', {renderer: 'canvas'});
        var option_310aa0e12219458e8a738c755f67c2d6 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "pie",
            "clockwise": true,
            "data": [
                {
                    "name": "\u53ef\u4e50",
                    "value": 109
                },
                {
                    "name": "\u96ea\u78a7",
                    "value": 132
                },
                {
                    "name": "\u6a59\u6c41",
                    "value": 119
                },
                {
                    "name": "\u7eff\u8336",
                    "value": 67
                },
                {
                    "name": "\u5976\u8336",
                    "value": 124
                },
                {
                    "name": "\u767e\u5a01",
                    "value": 101
                },
                {
                    "name": "\u9752\u5c9b",
                    "value": 49
                }
            ],
            "radius": [
                "30%",
                "75%"
            ],
            "center": [
                "25%",
                "50%"
            ],
            "roseType": "radius",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "pie",
            "clockwise": true,
            "data": [
                {
                    "name": "\u53ef\u4e50",
                    "value": 49
                },
                {
                    "name": "\u96ea\u78a7",
                    "value": 62
                },
                {
                    "name": "\u6a59\u6c41",
                    "value": 100
                },
                {
                    "name": "\u7eff\u8336",
                    "value": 57
                },
                {
                    "name": "\u5976\u8336",
                    "value": 86
                },
                {
                    "name": "\u767e\u5a01",
                    "value": 138
                },
                {
                    "name": "\u9752\u5c9b",
                    "value": 34
                }
            ],
            "radius": [
                "30%",
                "75%"
            ],
            "center": [
                "75%",
                "50%"
            ],
            "roseType": "area",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u53ef\u4e50",
                "\u96ea\u78a7",
                "\u6a59\u6c41",
                "\u7eff\u8336",
                "\u5976\u8336",
                "\u767e\u5a01",
                "\u9752\u5c9b"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "Pie-\u73ab\u7470\u56fe\u793a\u4f8b",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_310aa0e12219458e8a738c755f67c2d6.setOption(option_310aa0e12219458e8a738c755f67c2d6);
    </script>
<br/>                <div id="e2a0db023da94124ac241ea7edd013ee" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_e2a0db023da94124ac241ea7edd013ee = echarts.init(
            document.getElementById('e2a0db023da94124ac241ea7edd013ee'), 'white', {renderer: 'canvas'});
        var option_e2a0db023da94124ac241ea7edd013ee = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "\u84b8\u53d1\u91cf",
            "yAxisIndex": 0,
            "legendHoverLink": true,
            "data": [
                2.0,
                4.9,
                7.0,
                23.2,
                25.6,
                76.7,
                135.6,
                162.2,
                32.6,
                20.0,
                6.4,
                3.3
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u964d\u6c34\u91cf",
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                2.6,
                5.9,
                9.0,
                26.4,
                28.7,
                70.7,
                175.6,
                182.2,
                48.7,
                18.8,
                6.0,
                2.3
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "line",
            "name": "\u5e73\u5747\u6e29\u5ea6",
            "connectNulls": false,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "1\u6708",
                    2.0
                ],
                [
                    "2\u6708",
                    2.2
                ],
                [
                    "3\u6708",
                    3.3
                ],
                [
                    "4\u6708",
                    4.5
                ],
                [
                    "5\u6708",
                    6.3
                ],
                [
                    "6\u6708",
                    10.2
                ],
                [
                    "7\u6708",
                    20.3
                ],
                [
                    "8\u6708",
                    23.4
                ],
                [
                    "9\u6708",
                    23.0
                ],
                [
                    "10\u6708",
                    16.5
                ],
                [
                    "11\u6708",
                    12.0
                ],
                [
                    "12\u6708",
                    6.2
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u84b8\u53d1\u91cf",
                "\u964d\u6c34\u91cf",
                "\u5e73\u5747\u6e29\u5ea6"
            ],
            "selected": {
                "\u84b8\u53d1\u91cf": true,
                "\u964d\u6c34\u91cf": true,
                "\u5e73\u5747\u6e29\u5ea6": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "1\u6708",
                "2\u6708",
                "3\u6708",
                "4\u6708",
                "5\u6708",
                "6\u6708",
                "7\u6708",
                "8\u6708",
                "9\u6708",
                "10\u6708",
                "11\u6708",
                "12\u6708"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u964d\u6c34\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#5793f3"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 80,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u84b8\u53d1\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#d14a61"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u6e29\u5ea6",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#675bba"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} \u00b0C"
            },
            "inverse": false,
            "position": "left",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 25,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Grid-\u591a Y \u8f74\u793a\u4f8b",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "right": "20%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1
        }
    ]
};
        chart_e2a0db023da94124ac241ea7edd013ee.setOption(option_e2a0db023da94124ac241ea7edd013ee);
    </script>
<br/>                <div id="0bcf901abea34d5a855dd519dddeff80" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_0bcf901abea34d5a855dd519dddeff80 = echarts.init(
            document.getElementById('0bcf901abea34d5a855dd519dddeff80'), 'vintage', {renderer: 'canvas'});
        var option_0bcf901abea34d5a855dd519dddeff80 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "liquidFill",
            "name": "lq",
            "data": [
                0.3254
            ],
            "waveAnimation": true,
            "animationDuration": 2000,
            "animationDurationUpdate": 1000,
            "color": [
                "#294D99",
                "#156ACF",
                "#1598ED",
                "#45BDFF"
            ],
            "shape": "circle",
            "backgroundStyle": {},
            "outline": {
                "show": true,
                "borderDistance": 8
            },
            "label": {
                "show": true,
                "position": "inside",
                "margin": 8,
                "fontSize": 50,
                "formatter": function (param) {                        return (Math.floor(param.value * 10000) / 100) + '%';                    }
            }
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "Liquid-\u6570\u636e\u7cbe\u5ea6",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_0bcf901abea34d5a855dd519dddeff80.setOption(option_0bcf901abea34d5a855dd519dddeff80);
    </script>
<br/>                        <style>
            .fl-table {
                margin: 20px;
                border-radius: 5px;
                font-size: 12px;
                border: none;
                border-collapse: collapse;
                max-width: 100%;
                white-space: nowrap;
                word-break: keep-all;
            }

            .fl-table th {
                text-align: left;
                font-size: 20px;
            }

            .fl-table tr {
                display: table-row;
                vertical-align: inherit;
                border-color: inherit;
            }

            .fl-table tr:hover td {
                background: #00d1b2;
                color: #F8F8F8;
            }

            .fl-table td, .fl-table th {
                border-style: none;
                border-top: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-bottom: 3px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                padding: .5em .55em;
                font-size: 15px;
            }

            .fl-table td {
                border-style: none;
                font-size: 15px;
                vertical-align: center;
                border-bottom: 1px solid #dbdbdb;
                border-left: 1px solid #dbdbdb;
                border-right: 1px solid #dbdbdb;
                height: 30px;
            }

            .fl-table tr:nth-child(even) {
                background: #F8F8F8;
            }
        </style>
        <div id="87ecc1b463a748159cbf247efd62bcbd" class="chart-container" style="">
            <p class="title" style="font-size: 18px; font-weight:bold;" > Table</p>
            <p class="subtitle" style="font-size: 12px;" > </p>
            <table class="fl-table">
    <thead>
        <tr>
            <th>City name</th>
            <th>Area</th>
            <th>Population</th>
            <th>Annual Rainfall</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>Brisbane</td>
            <td>5905</td>
            <td>1857594</td>
            <td>1146.4</td>
        </tr>
        <tr>
            <td>Adelaide</td>
            <td>1295</td>
            <td>1158259</td>
            <td>600.5</td>
        </tr>
        <tr>
            <td>Darwin</td>
            <td>112</td>
            <td>120900</td>
            <td>1714.7</td>
        </tr>
        <tr>
            <td>Hobart</td>
            <td>1357</td>
            <td>205556</td>
            <td>619.5</td>
        </tr>
        <tr>
            <td>Sydney</td>
            <td>2058</td>
            <td>4336374</td>
            <td>1214.8</td>
        </tr>
        <tr>
            <td>Melbourne</td>
            <td>1566</td>
            <td>3806092</td>
            <td>646.9</td>
        </tr>
        <tr>
            <td>Perth</td>
            <td>5386</td>
            <td>1554769</td>
            <td>869.4</td>
        </tr>
    </tbody>
</table>
        </div>

<br/>    </div>
    <script>
    </script>
</body>
</html>
