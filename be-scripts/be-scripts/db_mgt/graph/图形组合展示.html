<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>

</head>
<body>
    <div id="96599d405cc34b4882003a6823d88232" class="chart-container" style="width:1200px; height:800px;"></div>
    <script>
        var chart_96599d405cc34b4882003a6823d88232 = echarts.init(
            document.getElementById('96599d405cc34b4882003a6823d88232'), 'white', {renderer: 'canvas'});
        var option_96599d405cc34b4882003a6823d88232 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#d14a61",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "Q2\u53d1\u73b0\u7f3a\u9677\u6570",
            "yAxisIndex": 0,
            "legendHoverLink": true,
            "data": [
                55,
                52,
                74
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "line",
            "name": "\u5e73\u5747\u6e29\u5ea6",
            "connectNulls": false,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "1\u6708",
                    2.0
                ],
                [
                    "2\u6708",
                    2.2
                ],
                [
                    "3\u6708",
                    3.3
                ],
                [
                    "4\u6708",
                    4.5
                ],
                [
                    "5\u6708",
                    6.3
                ],
                [
                    "6\u6708",
                    10.2
                ],
                [
                    "7\u6708",
                    20.3
                ],
                [
                    "8\u6708",
                    23.4
                ],
                [
                    "9\u6708",
                    23.0
                ],
                [
                    "10\u6708",
                    16.5
                ],
                [
                    "11\u6708",
                    12.0
                ],
                [
                    "12\u6708",
                    6.2
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "bar",
            "name": "\u84b8\u53d1\u91cf 1",
            "xAxisIndex": 1,
            "yAxisIndex": 3,
            "legendHoverLink": true,
            "data": [
                2.0,
                4.9,
                7.0,
                23.2,
                25.6,
                76.7,
                135.6,
                162.2,
                32.6,
                20.0,
                6.4,
                3.3
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u964d\u6c34\u91cf 2",
            "xAxisIndex": 1,
            "yAxisIndex": 3,
            "legendHoverLink": true,
            "data": [
                2.6,
                5.9,
                9.0,
                26.4,
                28.7,
                70.7,
                175.6,
                182.2,
                48.7,
                18.8,
                6.0,
                2.3
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "line",
            "name": "\u5e73\u5747\u6e29\u5ea6 1",
            "connectNulls": false,
            "xAxisIndex": 1,
            "yAxisIndex": 5,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "1\u6708",
                    2.0
                ],
                [
                    "2\u6708",
                    2.2
                ],
                [
                    "3\u6708",
                    3.3
                ],
                [
                    "4\u6708",
                    4.5
                ],
                [
                    "5\u6708",
                    6.3
                ],
                [
                    "6\u6708",
                    10.2
                ],
                [
                    "7\u6708",
                    20.3
                ],
                [
                    "8\u6708",
                    23.4
                ],
                [
                    "9\u6708",
                    23.0
                ],
                [
                    "10\u6708",
                    16.5
                ],
                [
                    "11\u6708",
                    12.0
                ],
                [
                    "12\u6708",
                    6.2
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "Q2\u53d1\u73b0\u7f3a\u9677\u6570",
                "\u5e73\u5747\u6e29\u5ea6"
            ],
            "selected": {
                "Q2\u53d1\u73b0\u7f3a\u9677\u6570": true,
                "\u5e73\u5747\u6e29\u5ea6": true
            }
        },
        {
            "data": [
                "\u84b8\u53d1\u91cf 1",
                "\u964d\u6c34\u91cf 2",
                "\u5e73\u5747\u6e29\u5ea6 1"
            ],
            "selected": {
                "\u84b8\u53d1\u91cf 1": true,
                "\u964d\u6c34\u91cf 2": true,
                "\u5e73\u5747\u6e29\u5ea6 1": true
            },
            "show": true,
            "left": "65%",
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "4\u6708",
                "5\u6708",
                "6\u6708"
            ]
        },
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "1\u6708",
                "2\u6708",
                "3\u6708",
                "4\u6708",
                "5\u6708",
                "6\u6708",
                "7\u6708",
                "8\u6708",
                "9\u6708",
                "10\u6708",
                "11\u6708",
                "12\u6708"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u84b8\u53d1\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#d14a61"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u7f3a\u9677",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#675bba"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} \u4e2a"
            },
            "inverse": false,
            "position": "left",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 25,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "name": "\u964d\u6c34\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#5793f3"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 80,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u84b8\u53d1\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#d14a61"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u6e29\u5ea6",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 1,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#675bba"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} \u00b0C"
            },
            "inverse": false,
            "position": "left",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 25,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "right": "58%",
            "bottom": "50%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1
        },
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "58%",
            "bottom": "50%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1
        }
    ],
    "title": [
        {
            "padding": 5,
            "itemGap": 10
        },
        {
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_96599d405cc34b4882003a6823d88232.setOption(option_96599d405cc34b4882003a6823d88232);
    </script>
</body>
</html>
