<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>

    
</head>
<body>
        <style>
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }

        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 12px 16px;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .chart-container {
            display: none;
            padding: 6px 12px;
            border-top: none;
        }
    </style>
    <div class="tab">
            <button class="tablinks" onclick="showChart(event, '********************************')">bar-example</button>
            <button class="tablinks" onclick="showChart(event, '09671b893f224bac9b711898e14b0878')">line-example</button>
            <button class="tablinks" onclick="showChart(event, 'fcf19b01e30a44398ee8bd76518562d4')">pie-example</button>
            <button class="tablinks" onclick="showChart(event, '9c4b2ada0cfd4518a95c8a1f4ef1de5d')">grid-example</button>
    </div>

    <div class="box">
                <div id="********************************" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_******************************** = echarts.init(
            document.getElementById('********************************'), 'white', {renderer: 'canvas'});
        var option_******************************** = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "legendHoverLink": true,
            "data": [
                20,
                18,
                24,
                25,
                21,
                18,
                11,
                24,
                9,
                24,
                25,
                9,
                14,
                1,
                14,
                20,
                12,
                25,
                30,
                18,
                20,
                26,
                4,
                4,
                13,
                21,
                9,
                1,
                25,
                1
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "0\u5929",
                "1\u5929",
                "2\u5929",
                "3\u5929",
                "4\u5929",
                "5\u5929",
                "6\u5929",
                "7\u5929",
                "8\u5929",
                "9\u5929",
                "10\u5929",
                "11\u5929",
                "12\u5929",
                "13\u5929",
                "14\u5929",
                "15\u5929",
                "16\u5929",
                "17\u5929",
                "18\u5929",
                "19\u5929",
                "20\u5929",
                "21\u5929",
                "22\u5929",
                "23\u5929",
                "24\u5929",
                "25\u5929",
                "26\u5929",
                "27\u5929",
                "28\u5929",
                "29\u5929"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-DataZoom\uff08slider-\u6c34\u5e73\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false,
            "filterMode": "filter"
        }
    ]
};
        chart_********************************.setOption(option_********************************);
    </script>
                <div id="09671b893f224bac9b711898e14b0878" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_09671b893f224bac9b711898e14b0878 = echarts.init(
            document.getElementById('09671b893f224bac9b711898e14b0878'), 'white', {renderer: 'canvas'});
        var option_09671b893f224bac9b711898e14b0878 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "line",
            "name": "\u5546\u5bb6A",
            "connectNulls": false,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "\u5468\u4e00",
                    131
                ],
                [
                    "\u5468\u4e8c",
                    32
                ],
                [
                    "\u5468\u4e09",
                    114
                ],
                [
                    "\u5468\u56db",
                    100
                ],
                [
                    "\u5468\u4e94",
                    149
                ],
                [
                    "\u5468\u516d",
                    47
                ],
                [
                    "\u5468\u65e5",
                    115
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "type": "min"
                    }
                ]
            },
            "zlevel": 0,
            "z": 0
        },
        {
            "type": "line",
            "name": "\u5546\u5bb6B",
            "connectNulls": false,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "\u5468\u4e00",
                    110
                ],
                [
                    "\u5468\u4e8c",
                    101
                ],
                [
                    "\u5468\u4e09",
                    66
                ],
                [
                    "\u5468\u56db",
                    95
                ],
                [
                    "\u5468\u4e94",
                    32
                ],
                [
                    "\u5468\u516d",
                    135
                ],
                [
                    "\u5468\u65e5",
                    90
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "type": "max"
                    }
                ]
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u5468\u4e00",
                "\u5468\u4e8c",
                "\u5468\u4e09",
                "\u5468\u56db",
                "\u5468\u4e94",
                "\u5468\u516d",
                "\u5468\u65e5"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Line-MarkPoint",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_09671b893f224bac9b711898e14b0878.setOption(option_09671b893f224bac9b711898e14b0878);
    </script>
                <div id="fcf19b01e30a44398ee8bd76518562d4" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_fcf19b01e30a44398ee8bd76518562d4 = echarts.init(
            document.getElementById('fcf19b01e30a44398ee8bd76518562d4'), 'white', {renderer: 'canvas'});
        var option_fcf19b01e30a44398ee8bd76518562d4 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "pie",
            "clockwise": true,
            "data": [
                {
                    "name": "\u6cb3\u9a6c",
                    "value": 103
                },
                {
                    "name": "\u87d2\u86c7",
                    "value": 29
                },
                {
                    "name": "\u8001\u864e",
                    "value": 61
                },
                {
                    "name": "\u5927\u8c61",
                    "value": 74
                },
                {
                    "name": "\u5154\u5b50",
                    "value": 71
                },
                {
                    "name": "\u718a\u732b",
                    "value": 129
                },
                {
                    "name": "\u72ee\u5b50",
                    "value": 126
                }
            ],
            "radius": [
                "30%",
                "75%"
            ],
            "center": [
                "25%",
                "50%"
            ],
            "roseType": "radius",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "pie",
            "clockwise": true,
            "data": [
                {
                    "name": "\u6cb3\u9a6c",
                    "value": 117
                },
                {
                    "name": "\u87d2\u86c7",
                    "value": 128
                },
                {
                    "name": "\u8001\u864e",
                    "value": 143
                },
                {
                    "name": "\u5927\u8c61",
                    "value": 45
                },
                {
                    "name": "\u5154\u5b50",
                    "value": 70
                },
                {
                    "name": "\u718a\u732b",
                    "value": 38
                },
                {
                    "name": "\u72ee\u5b50",
                    "value": 60
                }
            ],
            "radius": [
                "30%",
                "75%"
            ],
            "center": [
                "75%",
                "50%"
            ],
            "roseType": "area",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "title": [
        {
            "text": "Pie-\u73ab\u7470\u56fe\u793a\u4f8b",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_fcf19b01e30a44398ee8bd76518562d4.setOption(option_fcf19b01e30a44398ee8bd76518562d4);
    </script>
                <div id="9c4b2ada0cfd4518a95c8a1f4ef1de5d" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_9c4b2ada0cfd4518a95c8a1f4ef1de5d = echarts.init(
            document.getElementById('9c4b2ada0cfd4518a95c8a1f4ef1de5d'), 'white', {renderer: 'canvas'});
        var option_9c4b2ada0cfd4518a95c8a1f4ef1de5d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#5793f3",
        "#d14a61",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u84b8\u53d1\u91cf",
            "yAxisIndex": 0,
            "legendHoverLink": true,
            "data": [
                2.0,
                4.9,
                7.0,
                23.2,
                25.6,
                76.7,
                135.6,
                162.2,
                32.6,
                20.0,
                6.4,
                3.3
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u964d\u6c34\u91cf",
            "yAxisIndex": 1,
            "legendHoverLink": true,
            "data": [
                2.6,
                5.9,
                9.0,
                26.4,
                28.7,
                70.7,
                175.6,
                182.2,
                48.7,
                18.8,
                6.0,
                2.3
            ],
            "showBackground": false,
            "barMinHeight": 0,
            "barCategoryGap": "20%",
            "barGap": "30%",
            "large": false,
            "largeThreshold": 400,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "clip": true,
            "zlevel": 0,
            "z": 2,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "line",
            "name": "\u5e73\u5747\u6e29\u5ea6",
            "connectNulls": false,
            "yAxisIndex": 2,
            "symbolSize": 4,
            "showSymbol": true,
            "smooth": false,
            "clip": true,
            "step": false,
            "data": [
                [
                    "1\u6708",
                    2.0
                ],
                [
                    "2\u6708",
                    2.2
                ],
                [
                    "3\u6708",
                    3.3
                ],
                [
                    "4\u6708",
                    4.5
                ],
                [
                    "5\u6708",
                    6.3
                ],
                [
                    "6\u6708",
                    10.2
                ],
                [
                    "7\u6708",
                    20.3
                ],
                [
                    "8\u6708",
                    23.4
                ],
                [
                    "9\u6708",
                    23.0
                ],
                [
                    "10\u6708",
                    16.5
                ],
                [
                    "11\u6708",
                    12.0
                ],
                [
                    "12\u6708",
                    6.2
                ]
            ],
            "hoverAnimation": true,
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "areaStyle": {
                "opacity": 0
            },
            "zlevel": 0,
            "z": 0
        }
    ],
    "legend": [
        {
            "data": [
                "\u84b8\u53d1\u91cf",
                "\u964d\u6c34\u91cf",
                "\u5e73\u5747\u6e29\u5ea6"
            ],
            "selected": {
                "\u84b8\u53d1\u91cf": true,
                "\u964d\u6c34\u91cf": true,
                "\u5e73\u5747\u6e29\u5ea6": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "axis",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "cross"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "1\u6708",
                "2\u6708",
                "3\u6708",
                "4\u6708",
                "5\u6708",
                "6\u6708",
                "7\u6708",
                "8\u6708",
                "9\u6708",
                "10\u6708",
                "11\u6708",
                "12\u6708"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u964d\u6c34\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#5793f3"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 80,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u84b8\u53d1\u91cf",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#d14a61"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} ml"
            },
            "inverse": false,
            "position": "right",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 250,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        },
        {
            "type": "value",
            "name": "\u6e29\u5ea6",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLine": {
                "show": true,
                "onZero": true,
                "onZeroAxisIndex": 0,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid",
                    "color": "#675bba"
                }
            },
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} \u00b0C"
            },
            "inverse": false,
            "position": "left",
            "offset": 0,
            "splitNumber": 5,
            "min": 0,
            "max": 25,
            "minInterval": 0,
            "splitLine": {
                "show": true,
                "lineStyle": {
                    "show": true,
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Grid-\u591a Y \u8f74\u793a\u4f8b",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "grid": [
        {
            "show": false,
            "zlevel": 0,
            "z": 2,
            "left": "5%",
            "right": "20%",
            "containLabel": false,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderWidth": 1
        }
    ]
};
        chart_9c4b2ada0cfd4518a95c8a1f4ef1de5d.setOption(option_9c4b2ada0cfd4518a95c8a1f4ef1de5d);
    </script>
    </div>

    <script>
    </script>
    <script>
        (function() {
            containers = document.getElementsByClassName("chart-container");
            if(containers.length > 0) {
                containers[0].style.display = "block";
            }
        })()

        function showChart(evt, chartID) {
            let containers = document.getElementsByClassName("chart-container");
            for (let i = 0; i < containers.length; i++) {
                containers[i].style.display = "none";
            }

            let tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = "tablinks";
            }

            document.getElementById(chartID).style.display = "block";
            evt.currentTarget.className += " active";
        }
    </script>
</body>
</html>
