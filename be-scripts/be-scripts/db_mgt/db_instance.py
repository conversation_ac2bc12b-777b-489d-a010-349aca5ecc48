from dao.connect.mysql import DBConnectionManager
from settings import logger
import xlwt
import json


class DatabaseInstance:
    def __init__(self):
        self.exist_name_db_app_list = []
        self.out_name_db_app_list = []

        self.out_server_app_list = []

        self.db_instance_list = []

    def insert_db_instance(self):
        with DBConnectionManager() as db:
            try:
                sql = 'insert into db_mgt_db_instance(create_user,update_user,db_logic_name,' \
                      'suite_code,db_instance_name,db_server_id) value ("shuai.liu","shuai.liu",%s, %s,%s,%s)'
                db.cur.executemany(sql, self.db_instance_list)
                db.connection.commit()
            except Exception as e:
                logger.error(str(e))
                db.connection.rollback()

    def insert_app_bind_db(self):
        # 插入存在db 逻辑库的 应用和 db关系的数据
        with DBConnectionManager() as db:
            try:
                sql = 'insert into db_mgt_app_bind_db(create_user,update_user,db_logic_name,' \
                      'app_name) value ("shuai.liu","shuai.liu",%s, %s)'
                db.cur.executemany(sql, [[row["logic_name"], row["app_name"]] for row in self.exist_name_db_app_list])
                db.connection.commit()
            except Exception as e:
                logger.error(str(e))
                db.connection.rollback()

    def get_flyway_db_info(self):
        sql = "SELECT app_name,suite_code,db_name,db_url,db_type FROM test_flyway_db_info"
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        return db.cur.fetchall()

    def get_db_server(self):
        db_info_dict = {}
        sql = "SELECT db_url,db_type,id FROM db_mgt_db_server "
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            db_info_dict[row["db_url"]] = row
        return db_info_dict

    def get_db_name(self):
        db_logic_name_list = []
        sql = "SELECT db_logic_name FROM db_mgt_db_name "
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            db_logic_name_list.append(row["db_logic_name"])
        return db_logic_name_list

    def filter_no_rule_data(self, no_rule_data):
        for row1 in no_rule_data:
            mark = 1
            for row2 in self.exist_name_db_app_list:
                # logger.info(row1)
                #logger.info(row2)
                if row1["app_name"] == row2['app_name']:
                    mark = 0
                    continue
            for row3 in self.out_name_db_app_list:
                if row1["app_name"] == row3['app_name']:
                    mark = 0
                    continue
            if mark:
                logger.info("不符合docker规则的数据")
                logger.info(row1)

    def filter_no_server_data(self, no_server_data):
        #logger.info("没有维护server 的数据")
        for row1 in no_server_data:
            mark = 1
            for row2 in self.exist_name_db_app_list:
                if row1["app_name"] == row2['app_name']:
                    mark = 0
                    continue
            for row3 in self.out_name_db_app_list:
                if row1["app_name"] == row3['app_name']:
                    mark = 0
                    continue
            if mark:
                self.out_server_app_list.append(row1)
                #logger.info(row1)

    def data_analyze(self):
        db_info_dict = db_ins.get_db_server()
        db_logic_name_list = db_ins.get_db_name()
        # exist_name_db_app_list = []
        # out_name_db_app_list = []
        no_rule_data = []
        no_server_data = []
        for row in db_ins.get_flyway_db_info():
            # logger.info(row)
            row["server_id"] = 0
            for db_server in db_info_dict:
                #logger.info(db_server)
                if db_server in row["db_url"] or db_server.replace("/", ":") in row["db_url"]:
                    row["server_id"] = db_info_dict[db_server]["id"]
                    break
            # row["db_name"].split("_")[0].lower() == "docker"
            # 维护了 server信息，代表需要进行环境初始化的 server信息
            if row["server_id"] > 0:
                # 名称为docker开头的 符合环境 flyway的数据库命名规则
                if row["db_name"].split("_")[0].lower() == "docker":
                    row["logic_name"] = row["db_name"].split("_")[2].lower()
                    # 存在逻辑数据库名册数据库，会进行初始化
                    if row["logic_name"].upper() in db_logic_name_list:
                        # logger.info({row['app_name']: row['logic_name']})
                        if {"app_name": row['app_name'], "logic_name": row['logic_name']} not in self.exist_name_db_app_list:
                            self.exist_name_db_app_list.append({"app_name":row['app_name'], "logic_name": row['logic_name']})
                    else:
                        if {"app_name": row['app_name'], "logic_name": row['logic_name']} not in self.out_name_db_app_list:
                            self.out_name_db_app_list.append({"app_name": row['app_name'], "logic_name": row['logic_name']})
                    logger.info([row['logic_name'], row['suite_code'],row['db_name'], row["server_id"]])
                    self.db_instance_list.append([row['logic_name'], row['suite_code'],row['db_name'], row["server_id"]])

                else:
                    no_rule_data.append(row)
            else:
                if {"app_name": row['app_name'], "db_name": row['db_name']} not in no_server_data:
                    no_server_data.append({"app_name": row['app_name'], "db_name": row['db_name']})
                    #logger.info({"app_name": row['app_name'], "db_name": row['db_name']})
        # 过滤没有规则的数据
        self.filter_no_rule_data(no_rule_data)
        # 过滤没有规则的数据
        self.filter_no_server_data(no_server_data)


def excel(data_list, file_path, sheel_name):
    workbook = xlwt.Workbook(encoding='utf-8')
    worksheet1 = workbook.add_sheet(sheel_name)
    row_num = 0

    for row in data_list:
        col_num = 0
        for key, value in row.items():
            if row_num == 0:
                worksheet1.write(row_num, col_num, label=key)
                worksheet1.write(row_num+1, col_num, label=value)
            else:
                worksheet1.write(row_num, col_num, label=value)
            col_num=col_num+1

        if row_num == 0:
            row_num = row_num+2
        else:
            row_num = row_num+1
    workbook.save(file_path)


db_ins = DatabaseInstance()
db_ins.data_analyze()
# 插入数据库实例 数据
#db_ins.insert_db_instance()

# 插入应用和 数据库关系
db_ins.insert_app_bind_db()
logger.info("没有维护server的数据")
excel(db_ins.out_server_app_list,"D:\\文档\\数据库版本化\\data\\out_server_app_list.xls","没有维护server的数据")
# with open("D:\\文档\\数据库版本化\\data\\out_server_app_list.json",'w') as f:
#     f.write(json.dumps(db_ins.out_server_app_list))

logger.info("存在逻辑库名称的，应用和数据库的关系")
excel(db_ins.exist_name_db_app_list,"D:\\文档\\数据库版本化\\data\\exist_name_db_app_list.xls", "存在逻辑库名称的，应用和数据库的关系")
# with open("D:\\文档\\数据库版本化\\data\\exist_name_db_app_list.json",'w') as f:
#     f.write(json.dumps(db_ins.exist_name_db_app_list))

# for row in db_ins.exist_name_db_app_list:
#     logger.info(row)
logger.info("不辑库名称的，应用和数据库的关系")
excel(db_ins.out_name_db_app_list,"D:\\文档\\数据库版本化\\data\\out_name_db_app_list.xls", "不辑库名称的，应用和数据库的关系")

# with open("D:\\文档\\数据库版本化\\data\\out_name_db_app_list.json",'w') as f:
#     f.write(json.dumps(db_ins.out_name_db_app_list))

# for row in db_ins.out_name_db_app_list:
#     logger.info(row)
# db_info_dict = db_ins.get_db_server()
# db_logic_name_list = db_ins.get_db_name()
# db_app_list = []
# for row in db_ins.get_flyway_db_info():
#     #logger.info(row)
#     row["server_id"] = 0
#     for db_server in db_info_dict:
#         if db_server in row["db_url"]:
#             row["server_id"] = db_info_dict[db_server]["id"]
#             break
#     #row["db_name"].split("_")[0].lower() == "docker"
#     if row["server_id"] > 0 and row["db_name"].split("_")[0].lower() == "docker":
#         row["logic_name"] = row["db_name"].split("_")[2].lower()
#         if row["logic_name"].upper() in db_logic_name_list:
#             #logger.info({row['app_name']: row['logic_name']})
#             if {row['app_name']: row['logic_name']} not in db_app_list:
#                 db_app_list.append({row['app_name']: row['logic_name']})
#                 logger.info({row['app_name']: row['logic_name']})
            #pass
            #logger.info({'app_name': row['app_name'], 'suite_code': row['suite_code'], 'logic_name': row['logic_name'], 'instance_name': row['db_name']})
        # else:
        #
        #     logger.info({'app_name': row['app_name'], 'suite_code': row['suite_code'], 'logic_name': row['logic_name'],
        #                  'instance_name': row['db_name']})
