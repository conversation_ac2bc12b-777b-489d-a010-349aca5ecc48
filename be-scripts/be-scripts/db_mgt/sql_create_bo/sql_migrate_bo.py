class SqlMigrateBo:
    def __init__(self, builder):
        self.__flag_file_dir = builder.flag_file_dir
        self.__iteration_id = builder.iteration_id
        self.__app_name = builder.app_name
        self.__workspace = builder.workspace
        self.__sid = builder.sid
        self.__sql_update = builder.sql_update
        self.__git_group = builder.git_group
        self.__br_name = builder.br_name
        self.__vcs_iter_url = builder.vcs_iter_url
        self.__db_vcs_type = builder.db_vcs_type
        self.__check_suite_code = builder.check_suite_code
        self.__biz_base_db = builder.biz_base_db

    def set_app_name(self, app_name: str):
        self.__app_name = app_name
        return self

    @property
    def flag_file_dir(self):
        return self.__flag_file_dir

    @property
    def iteration_id(self):
        return self.__iteration_id

    @property
    def module_name(self):
        return self.__app_name

    @property
    def check_suite_code(self):
        return self.__check_suite_code

    @property
    def app_name(self):
        return self.__app_name

    @property
    def workspace(self):
        return self.__workspace

    @property
    def sid(self):
        return self.__sid
    @property
    def sql_update(self):
        return self.__sql_update

    @property
    def git_group(self):
        return self.__git_group

    @property
    def br_name(self):
        return self.__br_name

    @property
    def biz_base_db(self):
        return self.__biz_base_db

    @property
    def vcs_iter_url(self):
        return self.__vcs_iter_url

    def set_vcs_iter_url(self, vcs_iter_url: str):
        self.__vcs_iter_url = vcs_iter_url
        return self

    @property
    def db_vcs_type(self):
        return self.__db_vcs_type

    def set_db_vcs_type(self, db_vcs_type: str):
        self.__db_vcs_type = db_vcs_type
        return self

    class Builder:
        flag_file_dir: str = ""
        iteration_id: str = ""
        app_name: str = ""
        workspace: str = ""
        sid: int = None
        sql_update: bool = False
        git_group: str = ""
        br_name: str = ""
        vcs_iter_url: str = ""
        db_vcs_type: str = ""
        check_suite_code: str = ""
        biz_base_db: str = ""

        def set_flag_file_dir(self, flag_file_dir: str):
            self.flag_file_dir = flag_file_dir
            return self

        def set_iteration_id(self, iteration_id: str):
            self.iteration_id = iteration_id
            return self

        def set_app_name(self, app_name: str):
            self.app_name = app_name
            return self

        def set_workspace(self, workspace: str):
            self.workspace = workspace
            return self

        def set_sid(self, sid: int):
            self.sid = sid
            return self

        def set_git_group(self, git_group: int):
            self.git_group = git_group
            return self

        def set_br_name(self, br_name: int):
            self.br_name = br_name
            return self

        def set_biz_base_db(self, biz_base_db: str):
            self.biz_base_db = biz_base_db
            return self

        def set_check_suite_code(self, check_suite_code: str):
            self.check_suite_code = check_suite_code
            return self

        def verify_basic_property(self):
            if self.app_name == "":
                raise ValueError("app_name 不可为空")
            if self.iteration_id == "":
                raise ValueError("iteration_id 不可为空")
            if self.sid is None:
                raise ValueError("sid 不可为空")
            if self.flag_file_dir == "":
                raise ValueError("flag_file_dir 不可为空")
            if self.workspace == "":
                raise ValueError("workspace 不可为空")
            if self.git_group == "":
                raise ValueError("git_group 解析失败")
            if self.br_name == "":
                raise ValueError("br_name 解析失败")

        def build_bo(self):
            self.verify_basic_property()
            return SqlMigrateBo(self)
