from dao.connect.mysql import DBConnectionManager
from settings import logger


# def get_new_dump_archive_iter_id(biz_code, start_br_date, end_br_date, app_name_list):
#     '''
#     不同业务共用基础库集，
#     比如，TRADE-BATCH， TRADE-BATCH-ONLINE 共用基础库集it100，
#     TRADE-BATC_3.19.3 对Trade库开发了sql，
#     TRADE-BATCH-ONLINE_3.19.4 也开发了sql，
#     这两个分支的开发的sql需要共用
#     '''
#     app_name_str = '","'.join(app_name_list)
#     with DBConnectionManager() as db:
#         sql = '''
#                 select distinct dmg.db_group_name,a.biz_test_iter_br
#                 from biz_test_iter a
#                 inner join biz_test_iter_app b on a.biz_test_iter_id = b.biz_test_iter_id
#                 inner join db_mgt_app_bind c on c.app_module_name = b.app_module_name
#                 inner join db_mgt_domain dmd on c.db_domain_id = dmd.id
#                 inner join db_mgt_group dmg on dmg.id = dmd.db_group_id
#                 where a.br_status = 'close'  and a.biz_code IN (SELECT db1.biz_code
#                 FROM biz_base_db_bind db1
#                 JOIN biz_base_db_bind db2 ON db1.biz_base_db_code = db2.biz_base_db_code
#                 WHERE  db2.biz_code =  '{}') and b.app_module_name in ("{}")
#                     '''.format(biz_code, app_name_str)
#         if start_br_date:
#             sql += " and a.br_end_time > '{}' ".format(start_br_date)
#         if end_br_date:
#             sql += " and a.br_end_time <= '{}' ".format(end_br_date)
#         logger.info(sql)
#         db.cur.execute(sql)
#         res_dict = {}
#         for item in db.cur.fetchall():
#             if item["db_group_name"] in res_dict:
#                 res_dict[item["db_group_name"]].append(item["biz_test_iter_br"])
#             else:
#                 res_dict[item["db_group_name"]] = [item["biz_test_iter_br"]]
#         return res_dict


# def get_dump_archive_biz_iter_id(biz_code, base_db_code, opt_type):
#     res_dict = list()
#     if opt_type and opt_type == 3:
#         opt_type = 0
#     else:
#         opt_type = 1
#     if biz_code:
#         with DBConnectionManager() as db:
#             sql = ''' SELECT DISTINCT
#                             f.biz_iter_id
#                         FROM
#                             db_mgt_dump_file f
#                         INNER JOIN biz_base_db_bind b ON f.biz_base_db_bind_id = b.id
#                         WHERE b.biz_code = "{}" AND b.biz_base_db_code = "{}" AND f.dump_file_is_active = {}
#                         ORDER BY f.dump_file_batch DESC LIMIT 1
#                 '''.format(biz_code, base_db_code, opt_type)
#             logger.info(sql)
#             db.cur.execute(sql)
#             for item in db.cur.fetchall():
#                 res_dict.append(item["biz_iter_id"])
#         return res_dict


def get_dump_archive_pipeline_id(biz_iter_id):
    res_list = list()

    if biz_iter_id:
        with DBConnectionManager() as db:
            sql = ''' SELECT DISTINCT                  
                            distinct f.pipeline_id
                        FROM
                            db_mgt_dump_file f
                        WHERE f.biz_test_iter_id = "{}" AND f.dump_file_is_active = 1
                '''.format(biz_iter_id)
            logger.info(sql)
            db.cur.execute(sql)
            for item in db.cur.fetchall():
                res_list.append(item["pipeline_id"])
        return res_list


def get_dump_archive_branch_name_by_app_name(app_name_list: list, start_date=None, end_date=None):
    app_name_str = '","'.join(app_name_list)
    with DBConnectionManager() as db:
        sql = '''
            select distinct dmg.db_group_name, imii.br_name
            from iter_mgt_iter_info imii
            inner join iter_mgt_iter_app_info imiai on imii.pipeline_id = imiai.pipeline_id 
            inner join db_mgt_app_bind dmab on dmab.app_module_name = imiai.appName 
            inner join db_mgt_domain dmd on dmab.db_domain_id = dmd.id 
            inner join db_mgt_group dmg on dmg.id = dmd.db_group_id  
            where imii.br_status = 'close' and imiai.appName in ("{}") 
            '''.format(app_name_str)
        if start_date:
            sql += " and imii.br_end_date > '{}' ".format(start_date)
        if end_date:
            sql += " and imii.br_end_date <= '{}' ".format(end_date)
        logger.info(sql)
        db.cur.execute(sql)
        res_dict = {}

        for item in db.cur.fetchall():
            if item["db_group_name"] in res_dict:
                res_dict[item["db_group_name"]].append(item["br_name"])
            else:
                res_dict[item["db_group_name"]] = [item["br_name"]]
        return res_dict
