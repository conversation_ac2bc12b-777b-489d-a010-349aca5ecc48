import datetime
import json

from dao.connect.mysql import DBConnectionManager
from dao.connect.oracle import OracleConnectionManager
from settings import logger, DATABASES
from common.routers.router import Result
from test_mgt.models import DbMgtTableInfoTemp, DbMgtTableInfo
from test_mgt.test_mgt_ser import get_db_info_by_app


class DbInfoCollectForm:

    @classmethod
    def as_view(cls, request):
        collect_from = DbInfoCollectForm()
        return collect_from.db_info_collect_from(request)

    def __collect_mysql_table_info(self, db_info):
        db_hosts = db_info.get("db_hosts")
        db_port = db_info.get("db_port")
        db_user = db_info.get("db_user")
        db_passwd = db_info.get("db_passwd")
        module_name = db_info.get("app_name")
        suite_code = db_info.get("suite_code")
        db_alias = db_info.get("db_alias")
        db_srv_name = db_info.get("db_srv_name")
        cur_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        creator = 'be-scripts'
        db_table_list = []
        logger.info("mysql数据库连接信息：host={}, port={}, user={}, password={}, db={}, charset={}".
                    format(db_hosts, db_port, db_user, db_passwd, db_srv_name, DATABASES["CHARSET"]))
        with DBConnectionManager(host=db_hosts, port=int(db_port), user=db_user, password=db_passwd,
                                 db=db_srv_name, charset=DATABASES["CHARSET"]) as db:
            sql = '''
                    SELECT TABLE_SCHEMA, table_name, column_name, data_type, character_maximum_length, column_comment 
                    FROM information_schema.`COLUMNS`  
                    WHERE TABLE_SCHEMA = "{}"
                  '''.format(db_srv_name)
            logger.info(sql)
            db.cur.execute(sql)

            for row in db.cur.fetchall():
                result = {"module_name": module_name, "suite_code": suite_code, "db_alias": db_alias,
                          "table_name": row.get("table_name"), "column_name": row.get("column_name"),
                          "data_type": row.get("data_type"),
                          "data_max_length": row.get("character_maximum_length"),
                          "column_comment": row.get("column_comment"), "create_time": cur_time_str,
                          "creator": creator}
                db_table_list.append(result)

        self.__db_mgt_table_info_handler(db_table_list, module_name, suite_code, db_alias)

    def collect_oracle_table_info(self, db_info):
        db_hosts = db_info.get("db_hosts")
        db_user = db_info.get("db_user")
        db_passwd = db_info.get("db_passwd")
        module_name = db_info.get("app_name")
        suite_code = db_info.get("suite_code")
        db_alias = db_info.get("db_alias")
        db_name = db_info.get("db_name")
        cur_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        creator = 'be-scripts'
        db_table_list = []
        logger.info("oracle数据库连接信息：db_user={}, db_passwd={}, db_hosts={}, db_name={}".
                    format(db_user, db_passwd, db_hosts, db_name))
        with OracleConnectionManager(db_user, db_passwd, db_hosts, db_name,
                                     encoding="UTF-8") as db:
            sql_result = db.cur.execute('''select utc.table_name, utc.column_name, 
                                       utc.data_type, utc.data_length, ucc.COMMENTS from user_tab_columns utc
                                       inner join user_col_comments ucc on utc.TABLE_NAME = ucc.TABLE_NAME and utc.COLUMN_NAME = ucc.COLUMN_NAME
                                       order by table_name desc''')

            for row in sql_result:
                result = {"module_name": module_name, "suite_code": suite_code, "db_alias": db_alias,
                          "table_name": row[0], "column_name": row[1], "data_type": row[2], "data_max_length": row[3],
                          "column_comment": row[4] if row[4] else '', "create_time": cur_time_str,
                          "creator": creator}
                db_table_list.append(result)

        self.__db_mgt_table_info_handler(db_table_list, module_name, suite_code, db_alias)

    def __db_mgt_table_info_handler(self, db_table_list, module_name, suite_code, db_alias):
        try:
            d_obj = DbMgtTableInfo.select().where(DbMgtTableInfo.module_name == module_name,
                                                  DbMgtTableInfo.suite_code == suite_code,
                                                  DbMgtTableInfo.db_alias == db_alias)
            if d_obj:
                DbMgtTableInfo.delete().where(DbMgtTableInfo.module_name == module_name,
                                              DbMgtTableInfo.suite_code == suite_code,
                                              DbMgtTableInfo.db_alias == db_alias).execute()

            DbMgtTableInfo.insert_many(db_table_list).execute()
            logger.info("{}在{}下的{}数据库表接口信息收集成功！".format(module_name, suite_code, db_alias))
        except Exception as ex:
            logger.error("{}在{}下的{}数据库表接口信息收集失败: {}".format(module_name, suite_code, db_alias, str(ex)))
            raise ValueError("{}在{}下的{}数据库表接口信息收集失败: {}".format(module_name, suite_code, db_alias, str(ex)))

    def db_info_collect_from(self, request):
        logger.info("请求参数 {}".format(request))
        suite_code = request.get('suite_code')
        app_name = request.get('app_name')

        db_info_list = get_db_info_by_app(app_name, suite_code)

        for db_info in db_info_list:
            if db_info.get("db_type") == "mysql":
                self.__collect_mysql_table_info(db_info)
            elif db_info.get("db_type") == "oracle":
                self.collect_oracle_table_info(db_info)

        return Result.success_dict("容器相关信息采集成功！")


if __name__ == "__main__":
    ti = DbInfoCollectForm()
    ti.db_info_collect_from(json.loads('{"app_name": "fbs-online-service", "suite_code": "it51"}'))



