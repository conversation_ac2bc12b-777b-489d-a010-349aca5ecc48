from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import db_mgt_bind_view
from settings import logger
from settings import MIRROR_FACTORY
from test_mgt.models import DbMgtSql


def get_db_info_by_app(app_name=None, suite_code=None, db_group_name=None, db_srv_type=None, db_name=None):
    db_info_list = []
    query_result = db_mgt_bind_view.get(module_name=app_name, suite_code=suite_code, db_group_name=db_group_name,
                                        db_srv_type=db_srv_type, db_name=db_name)
    for row in query_result:
        result = {"app_name": row.get("app_module_name"), "suite_code": row.get("suite_code"),
                  "db_group_name": row.get("db_group_name"), "db_name_info": row.get("db_name"),
                  "db_url": row.get("vcs_url"), "db_user": row.get("username"),
                  "db_passwd": row.get("password"), "db_type": row.get("db_srv_type"), "db_alias": row.get("db_alias"),
                  "db_hosts": row.get("db_srv_hosts"), "db_port": row.get("db_srv_port"),
                  "db_name": row.get("db_srv_name"),
                  "db_srv_name": row.get("suite_db_name"), "conn_url": row.get("conn_url"),
                  "data_dump_dir": row.get("data_dump_dir"), "db_srv_socket_path": row.get("db_srv_socket_path"),
                  "db_srv_bash_profile": row.get("db_srv_bash_profile"), "db_srv_username": row.get("db_srv_username"),
                  "db_srv_password": row.get("db_srv_password"), "db_info_id": row.get("db_info_id")}
        db_info_list.append(result)
    return db_info_list


def get_db_deploy_info(db_group_name):
    sql = '''
            SELECT g.db_group_name, di.need_seq, di.need_synonym, di.synonym_opt_content 
            FROM db_mgt_deploy_info di
            JOIN db_mgt_logic_info i ON di.db_info_id = i.id
            JOIN db_mgt_domain dmd ON i.db_domain_id = dmd.id
            JOIN db_mgt_group g ON g.id = dmd.db_group_id         
            WHERE g.db_group_name = '{}'
        '''.format(db_group_name)
    logger.info(sql)

    db_deploy_info_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        result = {"db_gup_name": row.get("db_group_name"), "need_seq": row.get("need_seq"),
                  "need_synonym": row.get("need_synonym"), "synonym_opt_content": row.get("synonym_opt_content")}
        db_deploy_info_list.append(result)
    return db_deploy_info_list

def get_k8s_server(suite_code):
    sql = '''
            SELECT ks.k8s_host, ks.k8s_username, ks.k8s_password 
            FROM env_mgt_k8s_server ks
            LEFT JOIN env_mgt_suite_k8s_bind kb ON kb.k8s_id = ks.id and kb.is_active = 1     
            WHERE kb.suite_code = '{}'
            and ks.k8s_is_active = 1
        '''.format(suite_code)
    logger.info(sql)

    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result =  db.cur.fetchone()
        if result:
            data = {"k8s_host": result.get("k8s_host"), "k8s_username": result.get("k8s_username"),
                    "k8s_password": result.get("k8s_password")}
        else:
            data = {"k8s_host": MIRROR_FACTORY["IP"], "k8s_username": MIRROR_FACTORY["USER"],
                    "k8s_password": MIRROR_FACTORY["PASSWORD"]}
        return data


if __name__ == '__main__':
    # get_db_info_by_app( 'fin-console-web', 'it29')
    # get_create_dump_info('ftx_patest-0105','ftx-console-web')
    if DbMgtSql.select().where(DbMgtSql.iteration_id == "acc_pa-sql-access-0516",
                               DbMgtSql.sql_file_name == "zhzx_0_dml.sql"):
        print("判断为真")
    else:
        print("判断为假")
