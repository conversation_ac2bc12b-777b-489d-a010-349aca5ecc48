from peewee import *
from dao.base_model import BaseModel


class EnvMgtContainerModel(BaseModel):

    container_code = CharField(verbose_name='容器编码', max_length=100)
    container_name = CharField(verbose_name='容器名称', max_length=100)
    container_is_active = CharField(verbose_name='容器可用性', max_length=100)
    container_desc = CharField(verbose_name='容器说明', max_length=255)
    k8s_code = CharField(verbose_name='所属K8S的集群编码（暂时只有一套）', max_length=100)
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')
    stamp = IntegerField(verbose_name='版本')

    class Meta:
        table_name = 'env_mgt_container'
        verbose_name = '容器信息表'


class DbMgtTableInfoTemp(BaseModel):

    module_name = CharField(verbose_name='应用名', max_length=100)
    suite_code = CharField(verbose_name='环境编码', max_length=20)
    db_alias = CharField(verbose_name='数据库别名', max_length=50)
    table_name = CharField(verbose_name='表名', max_length=100)
    column_name = CharField(verbose_name='字段名', max_length=50)
    data_type = CharField(verbose_name='数据类型', max_length=10)
    data_max_length = IntegerField(verbose_name='数据最大长度')
    column_comment = CharField(verbose_name='字段描述', max_length=200)
    create_time = DateTimeField(verbose_name='创建时间')
    creator = CharField(verbose_name='创建者', max_length=50)
    update_time = DateTimeField(verbose_name='更新时间')
    updater = CharField(verbose_name='更新者', max_length=50)

    class Meta:
        table_name = 'db_mgt_table_info_temp'
        verbose_name = '数据库表信息临时表'


class DbMgtTableInfo(BaseModel):

    module_name = CharField(verbose_name='应用名', max_length=100)
    suite_code = CharField(verbose_name='环境编码', max_length=20)
    db_alias = CharField(verbose_name='数据库别名', max_length=50)
    table_name = CharField(verbose_name='表名', max_length=100)
    column_name = CharField(verbose_name='字段名', max_length=50)
    data_type = CharField(verbose_name='数据类型', max_length=10)
    data_max_length = IntegerField(verbose_name='数据最大长度')
    column_comment = CharField(verbose_name='字段描述', max_length=200)
    create_time = DateTimeField(verbose_name='创建时间')
    creator = CharField(verbose_name='创建者', max_length=50)
    update_time = DateTimeField(verbose_name='更新时间')
    updater = CharField(verbose_name='更新者', max_length=50)

    class Meta:
        table_name = 'db_mgt_table_info'
        verbose_name = '数据库表信息表'


class DbMgtSql(BaseModel):

    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')
    stamp = IntegerField(verbose_name='版本')
    module_name = CharField(verbose_name='应用名', max_length=100)
    iteration_id = CharField(verbose_name='迭代名', max_length=100)
    git_group = CharField(verbose_name='应用git分组', max_length=100)
    br_name = CharField(verbose_name='迭代分支名', max_length=100)
    sql_file_path = CharField(verbose_name='sql文件路径', max_length=999)
    sql_file_name = CharField(verbose_name='sql文件名', max_length=255)
    sql_file_hash = CharField(verbose_name='sql文件hash', max_length=255)
    sql_src_type = CharField(verbose_name='sql文件来源类型：类型不同对应不同的解析规则', max_length=100)
    sql_src_path = CharField(verbose_name='sql文件来源路径', max_length=999)
    sql_ver_name = CharField(verbose_name='sql制品名', max_length=255)
    sql_ver_db = CharField(verbose_name='sql制品对应库', max_length=100)
    sql_ver_group = CharField(verbose_name='sql制品对应组', max_length=100)
    sql_ver_tgt = CharField(verbose_name='sql制品库', max_length=999)
    sql_ver_upload_status = IntegerField(verbose_name='sql制品上传状态')
    sql_ver_upload_time = DateTimeField(verbose_name='sql制品上传时间')
    sql_file_desc = CharField(verbose_name='sql文件说明', max_length=255)
    arc_sql_ver_name = CharField(verbose_name='归档后的制品名', max_length=255)

    class Meta:
        table_name = 'db_mgt_sql'
        verbose_name = '数据库迭代sql表'
