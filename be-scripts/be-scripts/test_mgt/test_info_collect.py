import datetime
import telnetlib

from time import sleep

from ci_pipeline.ci_pipeline_models.iter_models import EnvMgtNodeBindDynamic
from settings import logger
from ci_pipeline.ci_pipeline_models.env_node_bind_table import EnvNodeBindTable
from common.routers.router import Result
from common.k8s.k8s_operation import K8sOperation
from test_mgt.models import EnvMgtContainerModel
from test_mgt.test_mgt_ser import get_k8s_server


class TestInfoCollectForm:

    @classmethod
    def as_view(cls, request):
        collect_from = TestInfoCollectForm()
        return collect_from.test_info_collect_from(request)

    def run_telnet(self, host, port):
        try:
            result = None

            tn = telnetlib.Telnet(host=host, port=port, timeout=10)

            tn.set_debuglevel(1)

            tn.write(b'\n')
            sleep(1)

            command_result = tn.read_very_eager().decode('utf-8')
            logger.info("command_result={}".format(command_result))

            if "dubbo>" in command_result:
                # tn.write("help\n".encode('utf-8'))
                # sleep(1)
                # command_result = tn.read_very_eager().decode('utf-8')
                # if "invoke" in command_result:
                #     result = port
                result = port

            tn.close()

            return result
        except Exception as e:
            logger.error(str(e))
            return None

    def collect_service_ip_and_dubbo_port(self, suite_code, app_name, k8s_server):
        k8s_operation = K8sOperation()
        node_svc_info_dict = k8s_operation.get_service_ip_and_port(suite_code, app_name, k8s_server)
        logger.info("node_svc_info_dict=={}".format(node_svc_info_dict))

        if node_svc_info_dict:
            for k, node_svc_info in node_svc_info_dict.items():
                service_ip = node_svc_info.get("CLUSTER-IP")
                dubbo_port = ''
                # 'PORT(S)': '8080/TCP,10090/TCP,8000/TCP'
                ports_info_list = node_svc_info.get("PORT(S)").split(",") if node_svc_info.get("PORT(S)") else []

                for ports_info in ports_info_list:
                    port = ports_info.split("/")[0]
                    if self.run_telnet(service_ip, port):
                        dubbo_port = port
                        break

                env_node_bind_table = EnvNodeBindTable()
                bind_id = env_node_bind_table.get_bind_id_by_app_env(k, suite_code)
                cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 检测到dubbo_port则更新，否则不更新 20230323 by fwm
                if dubbo_port:
                    p, created = EnvMgtNodeBindDynamic.get_or_create(bind_id=bind_id,
                                                                     defaults={'node_docker_service_ip': service_ip,
                                                                               'node_docker_dubbo_port': dubbo_port,
                                                                               'create_user': 'be-scripts',
                                                                               'create_time': cur_time,
                                                                               'bind_dynamic_desc': ''})
                    if not created:
                        logger.info("更新bind_id={}的信息：node_docker_service_ip={},node_docker_dubbo_port={}".format(p.bind_id, service_ip, dubbo_port))
                        EnvMgtNodeBindDynamic.update({EnvMgtNodeBindDynamic.node_docker_service_ip: service_ip,
                                                      EnvMgtNodeBindDynamic.node_docker_dubbo_port: dubbo_port,
                                                      EnvMgtNodeBindDynamic.update_user: 'be-scripts',
                                                      EnvMgtNodeBindDynamic.update_time: cur_time,
                                                      EnvMgtNodeBindDynamic.bind_dynamic_desc: ''}). \
                            where(EnvMgtNodeBindDynamic.bind_id == p.bind_id).execute()
                    else:
                        logger.info("新增bind_id={}的信息：node_docker_service_ip={},node_docker_dubbo_port={}".format(p.bind_id, service_ip, dubbo_port))
                else:
                    logger.info("未检测到dubbo端口，保持原值，时间{}".format(cur_time))
                    p, created = EnvMgtNodeBindDynamic.get_or_create(bind_id=bind_id,
                                                                     defaults={'node_docker_service_ip': service_ip,
                                                                               'create_user': 'be-scripts',
                                                                               'create_time': cur_time,
                                                                               'bind_dynamic_desc': "本次未检测到dubbo端口，保持原值，时间{}".format(
                                                                                   cur_time)})
                    if not created:
                        EnvMgtNodeBindDynamic.update({EnvMgtNodeBindDynamic.node_docker_service_ip: service_ip,
                                                      EnvMgtNodeBindDynamic.update_user: 'be-scripts',
                                                      EnvMgtNodeBindDynamic.update_time: cur_time,
                                                      EnvMgtNodeBindDynamic.bind_dynamic_desc: "本次未检测到dubbo端口，保持原值，时间{}".format(
                                                          cur_time)}). \
                            where(EnvMgtNodeBindDynamic.bind_id == p.bind_id).execute()

    def collect_hosts(self, suite_code, app_name, k8s_server):
        k8s_operation = K8sOperation()
        node_ingress_info_dict = k8s_operation.get_k8s_hosts(suite_code, app_name, k8s_server)
        ignore_app = ['cgi-ehowbuy-container','order-plan-center-remote', 'coupon-center-remote','coop-admin-remote', 'coop-cgi-container','activity-center-remote','coop-promotion-remote']
        if node_ingress_info_dict:
            for k, node_ingress_info in node_ingress_info_dict.items():
                hosts = node_ingress_info.get("HOSTS")

                env_node_bind_table = EnvNodeBindTable()

                # 跳过cgi-ehowbuy-container应用（任何环境）
                if k in ignore_app:
                    continue
                
                # 定义其他需要跳过的应用和环境组合
                skip_combinations = {
                    ('otc-batch-mock-web', 'it08'),
                    ('otc-direct-pre-server', 'it08'),
                    ('otc-mock-pre', 'it08')
                }
                
                if (k, suite_code) in skip_combinations:
                    continue

                bind_id = env_node_bind_table.get_bind_id_by_app_env(k, suite_code)
                cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                p, created = EnvMgtNodeBindDynamic.get_or_create(bind_id=bind_id,
                                                                 defaults={'node_docker_service_hosts': hosts,
                                                                           'create_user': 'be-scripts',
                                                                           'create_time': cur_time})
                if not created:
                    if hosts:
                        EnvMgtNodeBindDynamic.update({EnvMgtNodeBindDynamic.node_docker_service_hosts: hosts,
                                                      EnvMgtNodeBindDynamic.update_user: 'be-scripts',
                                                      EnvMgtNodeBindDynamic.update_time: cur_time}). \
                            where(EnvMgtNodeBindDynamic.bind_id == p.bind_id).execute()

    def test_info_collect_from(self, request):
        logger.info("发布分析请求参数 {}".format(request))
        suite_code = request.get('suite_code')
        app_name = request.get('app_name')

        if not suite_code:
            suite_code_list = EnvMgtContainerModel.select().where(EnvMgtContainerModel.container_is_active == "1")

            for item in suite_code_list:
                suite_code = item.container_code
                k8s_server = get_k8s_server(suite_code)
                self.collect_service_ip_and_dubbo_port(suite_code, app_name, k8s_server)
                self.collect_hosts(suite_code, app_name, k8s_server)
        else:
            k8s_server = get_k8s_server(suite_code)
            self.collect_service_ip_and_dubbo_port(suite_code, app_name, k8s_server)
            self.collect_hosts(suite_code, app_name, k8s_server)

        return Result.success_dict("容器相关信息采集成功！")


if __name__ == "__main__":
    ti = TestInfoCollectForm()
    print(ti.run_telnet('*************', '20888'))
