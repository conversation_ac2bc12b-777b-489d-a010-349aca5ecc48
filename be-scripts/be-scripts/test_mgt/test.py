import datetime
import re

import xmind
from xmind.core import workbook, saver
from xmind.core.topic import TopicElement
from xmindparser import xmind_to_dict

# if __name__ == '__main__':
#     workbook = xmind_to_dict('D:\\test.xmind')
#
#     print(workbook)
#
#     mind_str = [{'title': '画布 1', 'topic': {'title': '业务数据开发场景', 'topics': [{'title': '1、拉分支'}, {'title': '2、编译', 'topics': [
#                 {'title': '1、feature-sql提交'}, {'title': '2、diff-sql提交'}]}, {'title': '3、归档'}, {'title': '4、初始化'}]},
#               'structure': 'org.xmind.ui.map.clockwise'}]


# def design_sheet1(sheet1):
#     # ***** 第一个画布 *****
#     sheet1.setTitle("first sheet")  # 设置画布名称
#
#     # 获取画布的中心主题，默认创建画布时会新建一个空白中心主题
#     root_topic1 = sheet1.getRootTopic()
#     root_topic1.setTitle("根")  # 设置主题名称
#
#     # 创建一个子主题，并设置其名称
#     sub_topic1 = root_topic1.addSubTopic()
#     sub_topic1.setTitle("节点1")
#
#     sub_topic2 = root_topic1.addSubTopic()
#     sub_topic2.setTitle("节点2")
#
#     sub_topic3 = root_topic1.addSubTopic()
#     sub_topic3.setTitle("节点3")
#
#
# if __name__ == '__main__':
#
#     # 1、如果指定的XMind文件存在，则加载，否则创建一个新的
#     workbook = xmind.load("D:\\test.xmind")
#     # 2、获取第一个画布（Sheet），默认新建一个XMind文件时，自动创建一个空白的画布
#     sheet1 = workbook.getPrimarySheet()
#     # 对第一个画布进行设计完善
#     design_sheet1(sheet1)
#     # 3、保存（如果指定path参数，另存为该文件名）
#     xmind.save(workbook, path='D:\\test.xmind')


if __name__ == '__main__':
    binlog_loacl_name = 'D:\\pa.binlog.2023-04-21_13_56_06.log'
    pattern = r'# at (\d+)'
    pattern = r'created\s+(\d+) '

    with open(binlog_loacl_name, 'r', encoding='utf-8') as f:
        i = 0
        for line in f:
            match = re.search(pattern, line)
            if match:
                print(match.group(1))

    curr = datetime.datetime.now().strftime('%y%m%d')
    print(curr)
