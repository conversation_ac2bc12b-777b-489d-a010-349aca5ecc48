import datetime
import os

from common.mysql.models import DbMgtBisTestSql
from settings import logger, TEST_DATA_INIT, GITLAB_LIB_SSH
from common.ext_cmd import shell_cmd as common_shell_cmd


class TestDmlPushRepoForm:

    @classmethod
    def as_view(cls, request):
        push_repo_from = TestDmlPushRepoForm()
        return push_repo_from.test_dml_push_repo_form(request)

    def test_dml_push_repo_form(self, request):
        logger.info("请求参数 {}".format(request))

        bis_code = request.get("bis_code")
        test_dml_repo_group = TEST_DATA_INIT.get("test_dml_repo_group")
        bis_git_repo_url = '{}:{}/{}.git'.format(GITLAB_LIB_SSH, test_dml_repo_group, bis_code)
        bis_branch_name = request.get("bis_branch_name")
        bis_pipeline_id = request.get("bis_pipeline_id")
        bis_root_path = os.path.join('/data', test_dml_repo_group, bis_code)
        operate_user = request.get("operate_user")

        # 克隆或更新制品库
        self.__ck_gitlab(bis_git_repo_url, bis_branch_name, bis_root_path)

        # 转换sql为flywaysql
        self.__make_sql(bis_pipeline_id)

        # 更新sql并推送制品库
        self.__push_sql(bis_pipeline_id, bis_code, bis_branch_name, operate_user,bi)

    @staticmethod
    def __ck_gitlab(bis_git_repo_url, bis_branch_name, bis_root_path):
        if not os.path.isdir(bis_root_path):
            cmd = 'mkdir -p {}'.format(bis_root_path)
            logger.info(cmd)
            os.system(cmd)

        os.chdir(bis_root_path)
        if not os.path.isdir(bis_branch_name):
            cmd = 'git clone -b {} {} {}'.format(bis_branch_name, bis_git_repo_url, bis_branch_name)
            logger.info(cmd)
            os.system(cmd)
            if not os.path.isdir(bis_branch_name):
                cmd = 'git clone -b master {} {}'.format(bis_git_repo_url, bis_branch_name)
                logger.info(cmd)
                os.system(cmd)
                os.chdir(os.path.join(bis_root_path, bis_branch_name))
                cmd = 'git checkout -b {}'.format(bis_branch_name)
                os.system(cmd)
                cmd = 'git add -A'
                os.system(cmd)
                cmd = 'git commit -m "Create {}"'.format(bis_branch_name)
                os.system(cmd)
                cmd = 'git push --set-upstream origin {}'.format(bis_branch_name)
                logger.info(cmd)
                os.system(cmd)

        os.chdir(os.path.join(bis_root_path, bis_branch_name))
        os.system('git checkout .')
        logger.info('git checkout .')
        os.system('git checkout {}'.format(bis_branch_name))
        logger.info('git checkout {}'.format(bis_branch_name))
        os.system('git pull origin {}'.format(bis_branch_name))
        logger.info('git pull origin {}'.format(bis_branch_name))

    @staticmethod
    def __make_sql(bis_pipeline_id):

        query_set = DbMgtBisTestSql.select().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id)
        curr_time = datetime.datetime.now()
        db_ver_prefix = curr_time.strftime('%Y.%m.%d.%H.%M.%S')
        for test_sql in query_set:
            sql_ver_name = test_sql.sql_ver_name
            sql_ver_upload_status = test_sql.sql_ver_upload_status
            sql_file_name = test_sql.sql_file_name
            sql_file_path = test_sql.sql_file_path

            if not sql_ver_name or not sql_ver_upload_status:
                sql_ver_str = 'V{}.{}__{}'.format(db_ver_prefix, '001', sql_file_name)

                DbMgtBisTestSql.update({DbMgtBisTestSql.sql_ver_name: sql_ver_str,
                                        DbMgtBisTestSql.sql_ver_db: sql_file_path.split('/')[2],
                                        DbMgtBisTestSql.sql_ver_group: sql_file_path.split('/')[1]}).\
                    where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id,
                          DbMgtBisTestSql.sql_file_name == sql_file_name).execute()

    @staticmethod
    def __push_sql(bis_pipeline_id, bis_code, bis_branch_name, operate_user):
        query_set = DbMgtBisTestSql.select().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id)
        curr_time = datetime.datetime.now()

        for test_sql in query_set:
            sql_file_name = test_sql.sql_file_name
            sql_ver_name = test_sql.sql_ver_name
            sql_ver_upload_status = test_sql.sql_ver_upload_status
            test_dml_repo_group = TEST_DATA_INIT.get("test_dml_repo_group")
            test_dml_group = TEST_DATA_INIT.get("test_dml_group")
            sql_file_path = test_sql.sql_file_path
            sql_src_path = os.path.join('/data', test_dml_group, bis_code, bis_branch_name, sql_file_path, sql_file_name)
            sql_ver_path = os.path.join('/data', test_dml_repo_group, bis_code, bis_branch_name, sql_file_path)

            if not os.path.exists(sql_ver_path):
                cmd = 'mkdir -p {}'.format(sql_ver_path)
                os.system(cmd)

            if sql_ver_name and not sql_ver_upload_status:
                sql_ver_path = os.path.join(sql_ver_path, sql_ver_name)
                cmd = 'pwd && cp {} {}'.format(sql_src_path, sql_ver_path)
                logger.info(cmd)
                os.system(cmd)

        os.chdir(os.path.join('/data', test_dml_repo_group, bis_code, ))
        cmd = 'git pull origin {}'.format(bis_branch_name)
        os.system(cmd)
        cmd = 'git add -A'
        os.system(cmd)
        cmd = 'git commit -m "{}"'.format('提交diff_sql')
        os.system(cmd)
        cmd = 'git push origin {}'.format(bis_branch_name)
        os.system(cmd)

        get_commit_id_command = 'git rev-parse HEAD'
        stdcode, latest_commit_id = common_shell_cmd.shell_cmd(get_commit_id_command)
        logger.info("获取最后一次提交记录结果为{}，最后一次提交记录为{}".format(stdcode, latest_commit_id))
        if stdcode == 0:
            # 去除回车符
            latest_commit_id = latest_commit_id.replace('\n', '').replace('\r', '')
            for test_sql in query_set:
                sql_file_name = test_sql.sql_file_name
                sql_ver_upload_status = test_sql.sql_ver_upload_status
                if sql_ver_name and not sql_ver_upload_status:
                    DbMgtBisTestSql.update({DbMgtBisTestSql.update_user: operate_user,
                                            DbMgtBisTestSql.update_time: curr_time,
                                            DbMgtBisTestSql.gitlab_code_version: latest_commit_id,
                                            DbMgtBisTestSql.sql_ver_upload_status: 1,
                                            DbMgtBisTestSql.sql_ver_upload_time: curr_time}). \
                        where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id,
                              DbMgtBisTestSql.sql_file_name == sql_file_name).execute()
