# import os
#
# file_path = 'D:\\SVNProject\\devops-sql\\tms-gongmu\\pa-sql-0529\\orders1\\DML'
#
# # 获取文件夹中所有文件的路径
# files = [os.path.join(file_path, file) for file in os.listdir(file_path)]
#
# # 遍历文件列表，找到所有0kb大小的文件
# result = [file for file in files if os.path.isfile(file) and os.path.getsize(file) == 0]
#
# for item in result:
#     print(item.split('\\')[-1])
# import sqlparse
#
# sql_file = 'D:\\mysql.sql'
# with open(sql_file, "r") as f:
#     sqls = f.read()
#
# sql_seqs = sqlparse.split(sqls)
#
# for sql in sql_seqs:
#     new_file_path = 'D:\\mysql_new.sql'
#     with open(new_file_path, 'w') as f:
#         f.write(sqlparse.format(sqls.strip(), reindent=True))
import json
import pandas as pd

from dao.connect.mysql import DBConnectionManager

if __name__ == '__main__':
    sql = '''SELECT JSON_EXTRACT(job_http_dict, '$.APP_DICT_LIST') AS app_dict_list, job_http_suite_code AS suite_code FROM test_env_mgt_test_suite_init_log
    WHERE create_time > "2024-10-01"
    order by suite_code'''

    sql_third_app_sql = '''
    SELECT app_name FROM app_mgt_app_info t WHERE t.third_party_middleware = 1;
                        '''
    with DBConnectionManager() as db:
        third_app_list = []
        db.cur.execute(sql_third_app_sql)
        for item in db.cur.fetchall():
            third_app_list.append(item.get("app_name"))

        db.cur.execute(sql)
        data_list = db.cur.fetchall()
        # print(len(data_list))
        tutle1 = set()

        for data in data_list:
            app_dict_list = data.get("app_dict_list")
            if isinstance(app_dict_list, str):
                app_dict_list = eval(app_dict_list)
            if not isinstance(app_dict_list, list):
                app_dict_list = json.loads(app_dict_list)
            suite_code = data.get("suite_code")
            # print(app_dict_list)
            for app_dic in app_dict_list:
                for k, v in app_dic.items():
                    if k in third_app_list:
                        if (suite_code, k) not in tutle1:
                            tutle1.add((suite_code, k))

        print(tutle1)
        for item in tutle1:
            print(item[0], item[1])
