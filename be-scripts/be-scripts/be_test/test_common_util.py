# from spider_common_utils.external_command.spider_ssh.ssh_connect import SSHConnectionManager
# from settings import logger
#
# if __name__ == '__main__':
#     # logging.info(type("192.168.221.171"))
#     with SSHConnectionManager("192.168.212.28", "tomcat", "howbuy2015") as ssh:
#         tdin, stdout, stderr = ssh.SSH.exec_command("df -lh", bufsize=-1)
#         logger.info(stdout.read())
import re

file_content = '''
-- 新增es机构大宽表公募索引： fp_jg_gm_all_fwmtest
PUT fp_jg_gm_all_fwmtest
{
  "settings":{
    "number_of_shards" : 5,
    "number_of_replicas" : 1,
    "refresh_interval" : "30s",
    "index.mapping.total_fields.limit": 3000
  }
}

PUT fp_jg_gm_all_fwmtest/_mapping/_doc
{
  "properties": {
    "dx_id":{"type":"keyword"},
    "dx_lx":{"type":"keyword"},
    "dx_mc":{"type":"keyword"},
    "dx_pc":{"type":"keyword"},
    "dx_gxsj":{"type":"long"},
            "jgjc":{
                "type":"keyword"
           },
            "bazt":{
                "type":"keyword"
            },
           "jgmc":{
               "type":"keyword"
           },
           "xhdjbh":{
               "type":"keyword"
           },
           "jgclrq":{
               "type":"integer"
           },
           "jjgslx":{
               "type":"keyword"
           },
           "gsxz":{
               "type":"keyword"
           },
           "glgmqj":{
               "type":"keyword"
           },
           "zzfxgm":{
               "type":"keyword"
           },
           "gwglgm":{
               "type":"keyword"
           },
           "zczb":{
               "type":"double"
           },
           "dqdm":{
               "type":"keyword"
           },
           "zcdz":{
               "type":"keyword"
           },
           "frdb":{
               "type":"keyword"
           },
           "dsz":{
               "type":"keyword"
           },
           "zjl":{
               "type":"keyword"
           },
           "bgdz":{
               "type":"keyword"
           },
           "gsdh":{
               "type":"keyword"
           },
           "gswz":{
               "type":"keyword"
           },
           "sfsmgs":{
               "type":"keyword"
           },
           "sfgmgs":{
               "type":"keyword"
           },
    "zgjjsl":{"type":"integer"}
  }
}

'''

# mapping_pattern = re.compile(r'PUT\s+([^\s{]+)/_mapping/_doc\s*\n?\s*\{([^}]+)\}', re.DOTALL)
mapping_pattern = re.compile(r'PUT\s+([^\s{]+)/_mapping/_doc\s*(\{.*?\})', re.DOTALL)
mapping_matches = mapping_pattern.findall(file_content)

for mapping_path, properties in mapping_matches:
    # 从映射路径中提取索引名称
    index_name = mapping_path.split('/')[0].strip()
    print(index_name)
    print(properties)