from dao.connect.mysql import DBConnectionManager
from settings import logger


# def get_origin_dump_file_info():
#     sql = '''
#            SELECT DISTINCT amai.git_url, t.biz_iter_id AS old_biz_iter_id, CONCAT(b.biz_code, '_dev') AS biz_iter_id, sb.db_logic_id, t.db_info_id, t.dump_file_name, t.dump_file_batch,  b.biz_code, t.biz_base_db_bind_id
#             FROM db_mgt_dump_file t
#             INNER JOIN biz_base_db_bind b ON t.biz_base_db_bind_id = b.id
#             INNER JOIN (
#             SELECT MAX( dump_file_batch ) AS max_batch_num, biz_base_db_bind_id
#
#             FROM db_mgt_dump_file df
#
#             INNER JOIN db_mgt_suite_bind dmsb ON df.db_info_id = dmsb.db_info_id
#
#             WHERE dump_file_is_active = 1 AND biz_base_db_bind_id IS NOT NULL
#
#             GROUP BY biz_base_db_bind_id
#             ) vv ON t.dump_file_batch = vv.max_batch_num
#             INNER JOIN db_mgt_suite_bind sb ON t.db_info_id = sb.db_info_id
#             INNER JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id
#             INNER JOIN db_mgt_domain dmd ON li.db_domain_id = dmd.id
#             INNER JOIN db_mgt_app_bind ab ON dmd.id = ab.db_domain_id AND ab.read_or_write = 1
#             INNER JOIN app_mgt_app_module m ON ab.app_module_name = m.module_name
#             INNER JOIN app_mgt_app_info amai ON m.app_id = amai.id
# 	        WHERE git_url <> 'test' AND t.dump_file_is_active = 1
#             ORDER BY b.biz_code;
#           '''
#     git_url_list = []
#     with DBConnectionManager() as db:
#         db.cur.execute(sql)
#     for row in db.cur.fetchall():
#         git_url_list.append({'git_url': row.get('git_url'), 'old_biz_iter_id': row.get('old_biz_iter_id'),
#                              'biz_iter_id': row.get('biz_iter_id'), 'db_logic_id': row.get('db_logic_id'),
#                              'db_info_id': row.get('db_info_id'), 'dump_file_name': row.get('dump_file_name'),
#                              'dump_file_batch': row.get('dump_file_batch'), 'biz_code': row.get('biz_code')})
#     logger.info("数据总行数:{}".format(len(git_url_list)))
#     return git_url_list

def get_pipeline_id_list(git_url_list):
    new_git_url_list = []
    for item in git_url_list:
        sql = '''
                SELECT t.pipeline_id FROM iter_mgt_iter_info t 
                WHERE project_group = '{git_url}' AND br_status = 'close' AND br_end_date < (SELECT DATE_FORMAT(br_end_time, '%Y年%m月%d日 %H时%i分') FROM biz_test_iter t WHERE t.biz_test_iter_id = '{biz_iter_id}')
                ORDER BY br_end_date DESC
                LIMIT 1;
              '''.format(git_url=item.get('git_url'), biz_iter_id=item.get('old_biz_iter_id'))

        with DBConnectionManager() as db:
            db.cur.execute(sql)
            if db.cur.rowcount == 0:
                sql = '''
                        SELECT t.pipeline_id FROM iter_mgt_iter_info t 
                        WHERE project_group = '{git_url}' AND br_status = 'close'
                        ORDER BY br_end_date DESC
                        LIMIT 1;
                      '''.format(git_url=item.get('git_url'))
                db.cur.execute(sql)

            for row in db.cur.fetchall():
                item.update({'pipeline_id': row.get('pipeline_id')})

            new_git_url_list.append(item)
    logger.info("数据总行数:{}".format(len(new_git_url_list)))
    return new_git_url_list

def produce_insert_sql(git_url_list):
    sql = ''
    for item in git_url_list:
        sql += '''INSERT INTO db_mgt_dump_file (pipeline_id, biz_test_iter_id, db_logic_id, db_info_id, dump_file_name, dump_source, dump_file_is_active)
                  VALUES ('{pipeline_id}', '{biz_iter_id}', '{db_logic_id}', '{db_info_id}', '{dump_file_name}',1, 1);
              '''.format(pipeline_id=item.get('pipeline_id'), biz_iter_id=item.get('biz_iter_id'), db_logic_id=item.get('db_logic_id'),
                         db_info_id=item.get('db_info_id'), dump_file_name=item.get('dump_file_name'))
    # 将sql写入本地d盘的dump_file.sql文件
    with open('d:/dump_file.sql', 'w') as f:
        f.write(sql)
    logger.info("完成sql文件写入")

if __name__ == '__main__':
    # git_url_list = get_origin_dump_file_info()
    # new_git_url_list = get_pipeline_id_list(git_url_list)
    # produce_insert_sql(new_git_url_list)
    pass