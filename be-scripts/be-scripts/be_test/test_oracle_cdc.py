import os
import sys

PROJECT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(PROJECT_DIR)

from sqlalchemy import text

from dao.connect.oracle_sqlalchemy import DBConnectionManagerForSqlalchemyOracle

if __name__ == '__main__':
    sql = """SELECT MACHINE, PROGRAM FROM v$session WHERE username = '{}'""".format('DOCKER_IT54_CUST')
    print(sql)
    with DBConnectionManagerForSqlalchemyOracle("192.168.220.170", "1521", "ORACDC",
                                                "ORACDC", "hbcrm") as db:
        value = db.session.execute(text(sql)).fetchall()
        for i in value:
            print("user_connect_info:{},{}".format(i[0], i[1]))