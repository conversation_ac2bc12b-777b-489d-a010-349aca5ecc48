# from elasticsearch import Elasticsearch
#
# # # 创建Elasticsearch连接
# # es = Elasticsearch([{'host': 'es.it55.k8s.howbuy.com', 'port': 80}])
# # #es = Elasticsearch([{'host': 'es7.it01.k8s.howbuy.com', 'port': 80}])
# #
# # # 检查连接是否成功
# # if es.ping():
# #     print("连接成功！")
# # else:
# #     print("连接失败！")
# #
# # # 查询fp_gm_all_v1索引的内容
# # index_name = "fp_gm_all_v1"
# #
# # print(es.indices.exists(index=index_name))
#
# def create_index(es_client, index_name):
#     # 索引配置（包含分片、副本等参数）
#     body = {
#         "settings": {
#             "number_of_shards": 5,
#             "number_of_replicas": 1,
#             "refresh_interval": "30s",
#             "index.mapping.total_fields.limit": 3000
#         }
#     }
#
#     try:
#         # 检查索引是否存在
#         if not es_client.indices.exists(index=index_name):
#             # 创建索引
#             response = es_client.indices.create(
#                 index=index_name,
#                 body=body,
#                 ignore=400  # 忽略已存在的错误
#             )
#
#             if response.get("acknowledged"):
#                 print(f"索引 {index_name} 创建成功")
#             else:
#                 print(f"索引创建失败: {response}")
#         else:
#             print(f"索引 {index_name} 已存在")
#
#     except Exception as e:
#         print(f"操作异常: {str(e)}")
#
#
# if __name__ == "__main__":
#     # 创建ES连接（根据实际配置修改）
#     es = Elasticsearch(
#         hosts=[{"host": "es.it55.k8s.howbuy.com", "port": 80}],
#         # 强制使用JSON内容类型
#         headers={
#             "Accept": "application/json",
#             "Content-Type": "application/json"
#         },
#         # 禁用兼容性模式
#         compatibility_mode=False,
#     )
#     if es.ping():
#         print("连接成功！")
#     else:
#         print("连接失败！")
#     # 调用创建方法
#     create_index(es, "fp_jg_gm_all")
#
#

import re
import json
from typing import List, Dict


def parse_multiple_es_requests(text: str) -> List[Dict]:
    """解析包含多个ES请求的文本"""
    # 使用正则匹配多个请求块（支持跨行匹配）
    pattern = r'(PUT|POST|GET|DELETE|HEAD|PATCH|OPTIONS|BULK|SEARCH|MSEARCH|SCROLL)\s+([\w_/]+)\s*({.*?})(?=\s*(?:PUT|POST|GET|DELETE|HEAD|PATCH|OPTIONS|BULK|SEARCH|MSEARCH|SCROLL|$))'

    matches = re.finditer(pattern, text, re.DOTALL)

    results = []
    for match in matches:
        method = match.group(1)
        full_path = match.group(2).strip()
        raw_body = match.group(3).strip()

        # 提取索引名称（处理路径参数）
        index_name = process_index_name(full_path.split('/')[0])

        try:
            # 转换JSON并处理字段名
            body = json.loads(raw_body)
            transformed_body = transform_keys(body)
        except json.JSONDecodeError:
            raise ValueError(f"无效的JSON内容: {raw_body}")

        results.append({
            "method": method,
            "index_name": index_name,
            "api_path": full_path,
            "body": transformed_body
        })

    return results


def process_index_name(name: str) -> str:
    """处理索引名称（保留最后一个下划线）"""
    if '_' not in name:
        return name
    parts = name.rsplit('_', 1)
    return f"{parts[0].replace('_', '')}_{parts[1]}"


def transform_keys(data: dict) -> dict:
    """递归转换所有字段名"""
    if isinstance(data, dict):
        return {k.replace('_', ''): transform_keys(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [transform_keys(item) for item in data]
    return data


# 测试文本
input_text = """-- 新增es机构大宽表公募索引： fp_jg_gm_all 
PUT fp_jg_gm_all {  
  "settings":{    
    "number_of_shards" : 5,     
    "number_of_replicas" : 1,     
    "refresh_interval" : "30s",     
    "index.mapping.total_fields.limit": 3000   
  } 
}  

PUT fp_jg_gm_all/_mapping/_doc {  
  "properties": {     
    "dx_id":{"type":"keyword"},     
    "dx_lx":{"type":"keyword"},     
    "dx_mc":{"type":"keyword"},     
    "dx_pc":{"type":"keyword"},     
    "dx_gxsj":{"type":"long"},             
    "jgjc":{                 
      "type":"keyword"            
    },             
    "bazt":{                 
      "type":"keyword"             
    },            
    "jgmc":{                
      "type":"keyword"            
    },            
    "xhdjbh":{                
      "type":"keyword"            
    },            
    "jgclrq":{                
      "type":"integer"            
    },            
    "jjgslx":{                
      "type":"keyword"            
    },            
    "gsxz":{                
      "type":"keyword"            
    },            
    "glgmqj":{                
      "type":"keyword"            
    },            
    "zzfxgm":{                
      "type":"keyword"            
    },            
    "gwglgm":{                
      "type":"keyword"            
    },            
    "zczb":{                
      "type":"double"            
    },            
    "dqdm":{                
      "type":"keyword"            
    },            
    "zcdz":{                
      "type":"keyword"            
    },            
    "frdb":{                
      "type":"keyword"            
    },            
    "dsz":{                
      "type":"keyword"            
    },            
    "zjl":{                
      "type":"keyword"            
    },            
    "bgdz":{                
      "type":"keyword"            
    },            
    "gsdh":{                
      "type":"keyword"            
    },            
    "gswz":{                
      "type":"keyword"            
    },            
    "sfsmgs":{                
      "type":"keyword"            
    },            
    "sfgmgs":{                
      "type":"keyword"            
    },     
    "zgjjsl":{"type":"integer"}   
  } 
}"""

# 执行解析
parsed_requests = parse_multiple_es_requests(input_text)

# 打印结果
for i, req in enumerate(parsed_requests, 1):
    print(f"第 {i} 组请求:")
    print(f"方法: {req['method']}")
    print(f"索引名称: {req['index_name']}")
    print(f"API路径: {req['api_path']}")
    print("消息体:")
    print(json.dumps(req['body'], indent=2, ensure_ascii=False))
    print("\n" + "=" * 50 + "\n")