import os

# 设置参数
file_path = 'D:\\SVNProject\\devops-sql\\tms-gongmu\\pa-sql-0606\\orders1'
file_name = 'HIS_CUST_BOOKS.sql_14003.sql'
split_num = 100

# 定义生成器函数，用于动态读取txt文件的数据行
def read_lines(file_name):
    with open(file_name, 'r', encoding='utf-8') as f:
        while True:
            line = f.readline()
            if not line:
                break
            yield line

# 定义计数器
count = 0

# 创建目录
os.chdir(file_path)
output_dir = f'{file_name}_output'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 循环遍历生成器返回的数据行，同时计数
for line in read_lines(file_name):
    # 计算当前行所在的分割文件序号
    suffix = count // split_num + 1

    # 构造分割文件名
    split_file_name = f'{output_dir}/{file_name}_1{suffix:02d}.sql'

    # 将数据行写入分割文件
    with open(split_file_name, 'a', encoding='utf-8') as f:
        f.write(line)

    # 计数器自增
    count += 1

print('Done.')