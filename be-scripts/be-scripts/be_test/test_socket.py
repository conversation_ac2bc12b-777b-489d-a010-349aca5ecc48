import socket
from urllib.parse import urlparse

def get_url(url):
    # 通过socket请求html
    url = urlparse(url) # 解析url
    host = url.netloc   # 获取主机(域名)
    print(host)
    path = url.path
    if path == "":
        path = "/"
    # 建立socket连接
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client.connect((host, 80))
    client.send("GET {0} HTTP/1.1\r\nHost:{1}\r\nConnection:close\r\n\r\n".format(path, host).encode('utf-8'))

    data = b""  # 表示是byte类型
    while True:
        d = client.recv(1024)   # 一次性获取多少byte
        if d:
            data += d
        else:
            break
    data = data.decode('utf-8')
    html_data = data.split("\r\n\r\n")[1]   # 只取html内容
    print(html_data)
    client.close()


if __name__ == '__main__':
    get_url("https://api.tapd.cn/bugs?workspace_id=55014084&limit=200&page=1&iteration_id=1155014084001002093")
