# 测试环境发布相关的数据模型
# 第1版 zt@2020-08-24
import json
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from dao.get.mysql.env_info import get_pipeline_id_by_branch
from dao.get.mysql.env_info import get_max_br_number
from dao.get.mysql.env_info import get_node_info_for_test
from dao.get.mysql.env_info import get_app_info_by_name


class TestPublishBasePo:
    """
    测试环境发布基础数据模型 zt@2020-08-24
    """
    def __init__(self, app_name, suite_code, br_name=None):
        self.app_name = app_name
        self.suite_code = suite_code
        self.br_name = br_name


class PipelineTestPublishPo(TestPublishBasePo):
    """
    流水线应用测试环境发布数据模型 zt@2020-08-24
    """

    def __init__(self, app_name, suite_code, br_name=None):
        super().__init__(app_name, suite_code, br_name)
        self.container_name = None
        self.lib_repo = None
        self.zeus_type = None
        self.package_type = None
        self.package_full = None
        self.package_name = None
        self.pipeline_id = None
        self.node_list = None
        # 数据初始化
        # 1、应用信息
        self.__init_app_info()
        # 2、分支名
        self.__init_br_name()
        # 3、流水线ID
        self.__init_pipeline_id()
        # 4、节点列表
        self.__init_node_info()

    def __str__(self):
        return json.dumps(self, default=lambda o: o.__dict__, sort_keys=True, indent=4)

    def __init_app_info(self):
        data_list = get_app_info_by_name(self.app_name)
        if data_list and len(data_list) > 0:
            obj = data_list[0]
            if not obj['lib_repo']:
                raise ValueError("「{}」制品库信息未维护".format(self.app_name))

            self.container_name = obj['container_name']
            self.lib_repo = obj['lib_repo']
            self.zeus_type = obj['zeus_type']
            self.package_type = obj['package_type']
            self.package_name = obj['package_name']

            if len(data_list) > 1:
                log.warn(">>>> 警告：「{}」存在重复数据：{}".format(self.app_name, data_list))
        else:
            raise ValueError("「{}」应用信息初始化失败，应用信息没有录入。".format(self.app_name))

    def __init_br_name(self):
        if self.br_name == 'latest':
            data_list = get_max_br_number(self.app_name)
            if data_list and len(data_list) > 0:
                obj = data_list[0]
                self.br_name = obj['br_name']
            else:
                raise ValueError("「{}」分支初始化失败，没有找到已上线的分支".format(self.app_name))

    def __init_pipeline_id(self):
        data_list = get_pipeline_id_by_branch(self.app_name, self.br_name)
        if data_list and len(data_list) > 0:
            obj = data_list[0]
            self.pipeline_id = obj['pipeline_id']
        else:
            raise ValueError("「{}」迭代号初始化失败，找不到对应的迭代信息，分支号：{}".format(self.app_name, self.br_name))

    def __init_node_info(self):
        data_list = get_node_info_for_test(self.app_name, self.suite_code)
        if data_list and len(data_list) > 0:
            node_list = []
            for obj in data_list:
                node_obj = {
                    'node_type': obj['node_type'],
                    'node_code': obj['node_code'],
                    'node_name': obj['node_name'],
                    'deploy_path': obj['deploy_path'],
                    'tomcat_path': obj['tomcat_path'],
                    'script_path': obj['script_path'],
                    'script_name': obj['script_name'],
                    'config_path': obj['config_path'],
                    'log_path': obj['log_path'],
                }
                node_list.append(node_obj)

            self.node_list = node_list
        else:
            raise ValueError("「{}」节点信息初始化失败，「{}」环境下找不到绑定的节点信息".format(
                self.app_name,
                self.suite_code))
