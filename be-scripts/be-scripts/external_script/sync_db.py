# coding: utf-8
"""
同步指定版本的数据库脚本到指定数据库.

Author: <PERSON>
Date: 2023-10-26
"""
import os
import re
import subprocess
import sys
import logging
import traceback
from datetime import datetime

# 定义日志格式
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# 创建日志记录器
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(formatter)
# 将处理器添加到记录器
logger.addHandler(console_handler)


# 执行本地命令
def exec_local_cmd(cmd, timeout=None):
    try:
        if not timeout:
            timeout = 300
        subprocess.run(cmd, shell=True,
                       timeout=timeout,
                       check=True)
    except subprocess.CalledProcessError as err:
        msg = "本地执行命令出错：{}".format(err.stderr)
        logger.error(msg)
        raise Exception(msg)


if __name__ == '__main__':
    param_list = sys.argv
    err_msg = None
    db_script_root_dir = '/data/db_script/'
    now = datetime.now()
    today_db_script = os.path.join(db_script_root_dir, now.strftime('%Y-%m-%d'))

    try:
        logger.info('参数列表:{}'.format(param_list))
        if len(param_list) < 4:
            raise ValueError("缺少必要参数：{}".format(param_list))
        # 数据库名
        DB_NAME = param_list[1]
        # 归档分支
        BR_NAME = param_list[2]
        # 同步环境
        SUITE_CODE = param_list[3]
        logger.info("数据库名:{}".format(DB_NAME))
        logger.info("归档分支:{}".format(BR_NAME))
        logger.info("同步环境:{}".format(SUITE_CODE))
        logger.info("本次将同步的数据库脚本如下:".format(SUITE_CODE))
        exec_local_cmd("tree {}".format(today_db_script))
    except Exception as ex:
        err_msg = str(ex)
        traceback.print_exc()
        exit(1)
