import os
from settings import GITLAB_URL, logger


def push_code(workspace, branch_name, msg):
    """
    进入工作空间 push代码
    :param workspace:
    :param branch_name:分支名
    :param msg:
    :return:
    """
    logger.info("切换至{}目录，提交并push到远端".format(workspace))
    os.chdir(workspace)
    os.system("git add --all")
    os.system('git commit -m "{}"'.format(msg))
    os.system("git push origin {}".format(branch_name))


def git_clone(workspace, git_repos, branch_name, src_branch="master"):
    """
    clone 代码并切换分支
    :param workspace:
    :param git_repos:
    :param branch_name:
    :return:
    """
    logger.info("切换至{}目录，克隆仓库{}，换到分支{}".format(workspace, git_repos, branch_name))
    os.chdir(workspace)
    logger.info("git clone --depth 1 -b {} {}:{}".format(src_branch, GITLAB_URL, git_repos))
    os.system("git clone --depth 1 -b {} {}:{}".format(src_branch, GITLAB_URL, git_repos))
    os.chdir(os.path.join(workspace, git_repos.split("/")[1]))
    os.system("git branch {}".format(branch_name))
    os.system("git checkout {}".format(branch_name))

def git_clone_brach(workspace, git_repos, branch_name):
    """
    clone 代码并切换分支
    :param workspace:
    :param git_repos:
    :param branch_name:
    :return:
    """
    logger.info("切换至{}目录，克隆仓库{}，换到分支{}".format(workspace, git_repos, branch_name))
    os.chdir(workspace)
    os.system("git clone --depth 1 -b {} {}:{}".format(branch_name, GITLAB_URL, git_repos))


def git_checkout(workspace, git_repos, branch_name):
    """
    clone 代码并切换分支
    :param workspace:
    :param git_repos:
    :param branch_name:
    :return:
    """
    logger.info("切换至{}目录，克隆仓库{}，换到分支{}".format(workspace, git_repos, branch_name))
    os.chdir(workspace)
    os.system("git clone --depth 1 {}:{}".format(GITLAB_URL, git_repos))
    os.chdir(os.path.join(workspace, git_repos.split("/")[1]))
    os.system("git checkout {}".format(branch_name))
