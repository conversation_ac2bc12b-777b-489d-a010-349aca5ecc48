import paramiko
from settings import logger
from settings import BG_USER
from settings import BG_PASSWORD

class SSHConnectionManager:

    def __init__(self, ip, username, password):
        """
        :param ip:
        :param username:
        :param password:
        """
        self.ip = ip
        self.username = username
        self.password = password
        self.SSH = paramiko.SSHClient()
        self.SSH.load_system_host_keys()
        self.SSH.set_missing_host_key_policy(paramiko.AutoAddPolicy())

    def __enter__(self):
        logger.info("建立到{}的SSH连接".format(self.ip))
        self.SSH.connect(self.ip, port=22, username=self.username,
                                           password=self.password, compress=True)
        logger.info("{}连接成功".format(self.ip))
        self.SFTP = self.SSH.open_sftp()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        logger.info("断开到{}的SSH连接".format(self.ip))
        self.SSH.close()
        logger.info("{}断开连接成功".format(self.ip))


if __name__ == '__main__':
    logger.info(type("***************"))
    with SSHConnectionManager("***************", "tomcat", "howbuy2015") as ssh:
        tdin, stdout, stderr = ssh.SSH.exec_command("df -lh", bufsize=-1)
        print(stdout.read())