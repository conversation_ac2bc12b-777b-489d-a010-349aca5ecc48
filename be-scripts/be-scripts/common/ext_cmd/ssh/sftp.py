import os
import stat


class SCP:
    def __init__(self, sftp):
        """

        :param sftp: 传入建立好连接的 sftp对象
        """
        self.sftp = sftp

    @staticmethod
    def __remove_end_sep(path):
        if path[-1] == '/':
            path = path[0:-1]
        return path

    def push(self, local_path, remote_path):
        if os.path.isdir(local_path):
            self.push_dir(local_path, remote_path)
        else:
            self.push_file(local_path, remote_path)

    def get(self, remote_path, local_path):
        file = self.SFTP.stat(remote_path)
        if stat.S_ISDIR(file.st_mode):
            self.get_dir(local_path, remote_path)
        else:
            self.get_file(local_path, remote_path)

    def push_dir(self, local_path, remote_path):
        # 去掉尾部斜杠
        remote_path = self.__remove_end_sep(remote_path)
        local_path = self.__remove_end_sep(local_path)
        print (remote_path,local_path)

        def push_all_files(current_dir):

            for filename in os.listdir(current_dir):
                # 每个文件的完整路径
                file_path = os.path.join(current_dir, filename)
                if os.path.isdir(os.path.join(current_dir, filename)):
                    # 创建远程目录
                    self.sftp.mkdir(file_path.replace(local_path, remote_path))
                    push_all_files(file_path)
                else:
                    # 推送文件到远端
                    self.push_file(file_path, file_path.replace(local_path, remote_path))
        push_all_files(local_path)

    def get_dir(self, remote_path, local_path):
        # 去掉尾部斜杠
        remote_path = self.__remove_end_sep(remote_path)
        local_path = self.__remove_end_sep(local_path)

        def get_all_files(current_dir):
            for filename in self.sftp.listdir_attr(current_dir):
                # 每个文件的完整路径
                file_path=os.path.join(current_dir, filename)
                if stat.S_ISDIR(filename.st_mode):
                    # 创建本地目录
                    os.mkdir(file_path.replace(remote_path, local_path))
                    get_all_files(file_path)
                else:
                    # 获取远程文件到本地
                    self.get_file(file_path, file_path.replace(remote_path, local_path))

        get_all_files(remote_path)

    def is_file_found(self, path):
        """
        判断远程目录有没有
        :param path:
        :return:
        """
        try:
            self.sftp.stat(path)
            return True
        except FileNotFoundError:
            return False

    def mkdir_p(self, path):
        """
        mkdir -p
        :param path:
        :return:
        """
        try:
            file = self.sftp.stat(os.path.dirname(path))
            # if stat.S_ISDIR(file.st_mode):
            #     hm_info("目录已经存在{}".format(path))
            # else:
            self.sftp.mkdir(path)
        except (IOError, FileNotFoundError) as err:
            self.mkdir_p(os.path.dirname(path))
            self.sftp.mkdir(path)

    def push_file(self, local_path, remote_path):
        self.sftp.put(local_path, remote_path)

    def get_file(self, remote_path, local_path):
        self.sftp.get(remote_path, local_path)


if __name__ == "__main__":

    from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
    from settings import MIRROR_FACTORY

    with SSHConnectionManager(MIRROR_FACTORY["IP"], MIRROR_FACTORY["USER"], MIRROR_FACTORY["PASSWORD"]) as ssh:
        scp = SCP(ssh.SFTP)
        scp.push_dir("/data/env_init/docker/tomcat", "/home/<USER>/img-factory-online/test_tms/test")




