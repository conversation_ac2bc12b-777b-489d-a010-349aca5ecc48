import os
import hashlib
from settings import logger
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from common.ext_cmd.ssh.sftp import SCP

from settings import PIPELINE_TEST_PUBLISH, NGINX_LIB_REPO


class LibInfoBo:
    lib_rul: str
    lib_size: str
    lib_md5: str
    file_name: str

    def __init__(self, lib_repo_rul, lib_repo_size, lib_repo_md5, file_name):
        self.lib_rul = lib_repo_rul
        self.lib_size = lib_repo_size
        self.lib_md5 = lib_repo_md5
        self.file_name = file_name


class NginxRepos:
    __nginx_info = NGINX_LIB_REPO

    def upload_file(self, file_local_path, remote_path):
        abs_remote_path = "{}/{}".format(self.__nginx_info["root_path"], remote_path)
        logger.info("远端绝对路径 {}".format(abs_remote_path))
        logger.info("本地文件路径 {}".format(file_local_path))
        with SSHConnectionManager(self.__nginx_info["ip"], self.__nginx_info["username"], self.__nginx_info["password"]) as ssh:
            scp = SCP(ssh.SFTP)
            # 先创建目录目录 在ssh copy报告
            if not scp.is_file_found(os.path.dirname(abs_remote_path)):
                scp.mkdir_p(os.path.dirname(abs_remote_path))
            scp.push_file(file_local_path, abs_remote_path)
        test_report_url = "{}/{}".format(self.__nginx_info["base_url"], remote_path)
        file_size = os.path.getsize(file_local_path)
        with open(os.path.join(file_local_path), 'rb') as f:
            md5obj = hashlib.md5()
            md5obj.update(f.read())
            md5 = md5obj.hexdigest()
        return LibInfoBo(test_report_url, file_size, md5, os.path.basename(remote_path))
