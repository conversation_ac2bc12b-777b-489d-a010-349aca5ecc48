import os
import asyncio


async def sync_dir(src_dir, target_dir):
    os.system('rsync -avz --delete --exclude ".svn" --exclude ".git" %s/ %s/' % (src_dir, target_dir))


async def sync_all_dir(dir_list):
    """
    异步同步目录
    :param dir_list: 目录列表 [("/data/src","/data/target"),("/usr/src","/local/target"),...]
    :return:
    """
    tasks = [sync_dir(src_dir, src_dir) for src_dir, src_dir in dir_list]
    await asyncio.gather(*tasks)
