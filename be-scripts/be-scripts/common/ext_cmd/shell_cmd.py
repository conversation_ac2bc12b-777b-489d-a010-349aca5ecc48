# -*- coding: utf-8 -*-
import subprocess
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger
from settings import CODE_STYLE


def clean_workspace(workspace):
    logger.info('-----------开始清理工作空间-----------')
    logger.info('工作空间路径:{}'.format(workspace))
    if len(workspace.split("/")) > 2:
        logger.info("开始清理工作空间  rm -rf {}/*".format(workspace))
        os.system("rm -rf {}/*".format(workspace))
    else:
        logger.error("工作空间路径太短 {}".format(workspace))
        sys.exit(1)


def shell_cmd(command, exit_conditions="", read_line=False, all_log=False):
    """
        用于执行需要在控制台输出日志的shell操作，如编译操作。

    :param command: 需要执行的命令 如：mvn install
    :param exit_conditions: 程序退出条件 如：exit_conditions="Build failure" ，
    那么当输出日志中存在Build failure程序将异常退出，默认无退出条件。
    :param read_line: 是否逐行输出日志。
    :return: 1 执行命令错误，输出中包含退出条件
    0 执行成功
    std_out_lines 调用外部命令的输出内容
    """
    print_cmd = command.replace('--username scm --password scm', '--username xxx --password xxx')
    logger.info(">>>> command: {}".format(print_cmd))
    std_out = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    # error_log = std_out.stderr.read().decode(CODE_STYLE)
    # if error_log:
    #     logger.error(error_log)
    #     return 1, error_log
    # 逐行读取
    exit_conditions_flag = 0
    if read_line:
        std_out_list = []
        while True:
            std_out_line = std_out.stdout.readline().decode(CODE_STYLE)
            if std_out_line:
                logger.info(std_out_line)
                std_out_list.append(std_out_line)
                # 如果没有退出条件，继续读取下一行输出
                if exit_conditions == "":
                    continue
                for exit_con in exit_conditions.split(","):
                    if exit_con in std_out_line:
                        exit_conditions_flag = 1
                        if not all_log:
                            return 1, "\n".join(std_out_list)
            else:
                break
        if exit_conditions_flag:
            return 1, "\n".join(std_out_list)
        else:
            return 0, "\n".join(std_out_list)
    else:
        std_out_lines = std_out.stdout.read().decode(CODE_STYLE)
        logger.info(std_out_lines)
        # 如果没有退出条件,结束
        if exit_conditions == "":
            return 0, std_out_lines
        for exit_con in exit_conditions.split(","):
            if exit_con in std_out_lines:
                return 1, std_out_lines
        return 0, std_out_lines


def shell_cmd_exit_found2notfound(command, exit_conditions="", exit_conditions_not_found="", read_line=False, all_log=False):
    """
        用于执行需要在控制台输出日志的shell操作，如编译操作。

    :param command: 需要执行的命令 如：mvn install
    :param exit_conditions: 程序退出条件 如：exit_conditions="Build failure" ，
    :param exit_conditions_not_found: 程序退出条件 如：exit_conditions="Build failure" ，
    那么当输出日志中存在Build failure程序将异常退出，默认无退出条件。
    :param read_line: 是否逐行输出日志。
    :return: 1 执行命令错误，输出中包含退出条件
    0 执行成功
    std_out_lines 调用外部命令的输出内容
    """
    std_out = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    # error_log = std_out.stderr.read().decode(CODE_STYLE)
    # if error_log:
    #     logger.error('执行shell_cmd_exit_found2notfound发生异常')
    #     logger.error(error_log)
    #     return 1, error_log
    # 逐行读取
    exit_conditions_flag = False
    exit_conditions_not_found_flag = False    # 必须包含指定字符;默认false

    if read_line:
        std_out_list = []
        while True:
            std_out_line = std_out.stdout.readline().decode(CODE_STYLE)
            if std_out_line:
                logger.info(std_out_line)
                std_out_list.append(std_out_line)
                # 如果没有退出条件，继续读取下一行输出
                if exit_conditions == "":
                    continue
                for exit_con in exit_conditions.split(","):
                    if exit_con in std_out_line:
                        exit_conditions_flag = True
                        if not all_log:
                            return 1, "\n".join(std_out_list)

                # 如果没有退出条件，继续读取下一行输出
                if exit_conditions_not_found == "":
                    continue
                for exit_con in exit_conditions_not_found.split(","):
                    if exit_con in std_out_line:
                        exit_conditions_not_found_flag = True
            else:
                break

        if exit_conditions_flag or not exit_conditions_not_found_flag:
            return 1, "\n".join(std_out_list)
        else:
            return 0, "\n".join(std_out_list)
    else:
        std_out_lines = std_out.stdout.read().decode(CODE_STYLE)
        logger.info(std_out_lines)

        # 如果没有退出条件,结束
        if exit_conditions != "":
            for exit_con in exit_conditions.split(","):
                if exit_con in std_out_lines:
                    exit_conditions_flag = True

        if exit_conditions_not_found != "":
            for exit_con in exit_conditions_not_found.split(","):
                if exit_con in std_out_lines:
                    exit_conditions_not_found_flag = True

        if exit_conditions_flag or not exit_conditions_not_found_flag:
            return 1, std_out_lines
        else:
            return 0, std_out_lines


if __name__ == "__main__":
    output = shell_cmd_exit_found2notfound("export JAVA_HOME=/data/jdk/jdk1.8.0 && mvn clean deploy --settings "
                       "/data/maven_repo/maven_settings/tms_settings.xml -U -Dmaven.test.skip=true -f "
                       "/root/.jenkins/workspace/mojie_2.2.0_mring-performance-service/mring-performance/mring-performance-dao/pom.xml",
                       exit_conditions="Build failure", exit_conditions_not_found="Build failure", read_line=True)
    print(output)
