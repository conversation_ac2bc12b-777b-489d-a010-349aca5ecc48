import os
import re

from common.ext_cmd.shell_cmd import shell_cmd
from settings import logger
from settings import SVN_BASES, SYSTEM


class SvnCmd:
    RE_DEL_PATTERN = r'^!\s*(\S*)'

    def __init__(self):
        self.svn_bases_info = " --username {} --password {} --no-auth-cache --non-interactive".\
            format(SVN_BASES["USER"], SVN_BASES["PASSWORD"])

    def svn_add(self, path=None):
        """
        svn add
        :param path:传入单个文件路径，或者目录，不填写默认添加当前工作空间的所有文件
        :return:
        """
        if path and not os.path.isdir(path):
            return self.svn_add_file(path)
        else:
            return self.svn_add_files(workspace=path)

    def svn_add_file(self, path):
        """
         svn add 单个文件
        :param path: 文件路径
        :return:
        """
        return shell_cmd("svn add {}".format(path)+self.svn_bases_info)

    def svn_add_files(self, workspace=None):
        """
        svn add 目录
        :param workspace: 不填写默认为当前目录
        :return:
        """
        if workspace:
            status = shell_cmd('cd {} && svn status'.format(workspace) + self.svn_bases_info)
            # 切换到工作空间
            os.chdir(workspace)
        else:
            status = shell_cmd('svn status' + self.svn_bases_info)
        file_list = []
        # 获取开头为？ 的文件
        for line in re.findall("\? *([^ \r\n]*)", status):
            file_list.append(line)
            self.svn_add_file(line)
        return file_list

    def svn_commit(self, message, workspace=None):
        """
        svn commit 命令
        :param message:提交日志
        :param workspace:工作空间，默认当前所在目录
        :return:
        """
        logger.info("提交中")
        if workspace:
            logger.info('cd {} && svn commit -m "{}"'.format(workspace, message) + self.svn_bases_info)
            return shell_cmd('cd {} && svn commit -m "{}"'.format(workspace, message) + self.svn_bases_info)
        return shell_cmd('svn commit -m "{}"'.format(message)+self.svn_bases_info)

    def svn_checkout(self, svn_url, workspace=None):
        """
        svn checkout
        :param svn_url: svn url
        :param workspace: 检出到指定目录，默认为当前所在目录
        :return:
        """
        if workspace:
            if not os.path.isdir(workspace):
                logger.info("检出目录不存在，开始创建目录{}".format(workspace))
                os.system("mkdir -p {}".format(workspace))
            return shell_cmd('cd {} && svn checkout "{}"'.format(workspace, svn_url) + self.svn_bases_info)
        return shell_cmd('svn checkout "{}"'.format(svn_url)+self.svn_bases_info)

    def svn_log(self):
        pass

    def svn_list(self, svn_url):
        """

        :param svn_url:
        :return:
        """
        result = shell_cmd('svn list "{}"'.format(svn_url) + self.svn_bases_info)
        if result[0] == 0:
            if SYSTEM == "Windows":
                return result[1].split("/\r\n")
            else:
                return result[1].split("/\n")
        else:
            return result[1]


    def svn_import(self, local_path, svn_path):
        """

        :param local_path:
        :param svn_path:
        :return:
        """
        return shell_cmd("svn import {} {} -m '导入操作'".format(local_path, svn_path)+ self.svn_bases_info)
    def svn_mkdir(self, svn_path):
        """

        :param svn_path:
        :return:
        """
        return shell_cmd("svn mkdir {} -m '创建目录'".format(svn_path) + self.svn_bases_info)

    def svn_del(self, svn_path):
        """

        :param svn_path:
        :return:
        """
        return shell_cmd("svn delete {}  -m '删除目录'".format(svn_path)+ self.svn_bases_info)

    def svn_del_files(self, workspace=None):
        """svn批量删除 zt@2020-10-09"""
        if workspace:
            re_status, re_str = shell_cmd('cd {} && svn status'.format(workspace) + self.svn_bases_info)
            # 切换到工作空间
            os.chdir(workspace)
        else:
            re_status, re_str = shell_cmd('svn status' + self.svn_bases_info)

        if re_status != 0:
            err_msg = "执行svn status出错！"
            raise ValueError(err_msg)

        file_list = []
        if re_str:
            # 获取开头为! 的文件
            re_iter = re.finditer(SvnCmd.RE_DEL_PATTERN, re_str, re.M)
            for match in re_iter:
                file_name = match.group(1)
                file_list.append(file_name)
                svn_del_cmd = "svn delete {}".format(file_name)
                shell_cmd(svn_del_cmd)
                logger.info("执行svn删除命令：{}".format(svn_del_cmd))
        return file_list


if __name__ == "__main__":
    svn_cmd = SvnCmd()
    print(svn_cmd.svn_list("svn://192.168.220.100/usr/local/subversion-1.4.4/repos_doc/IT/部门级/质量管理部QA/配置管理/config"))
