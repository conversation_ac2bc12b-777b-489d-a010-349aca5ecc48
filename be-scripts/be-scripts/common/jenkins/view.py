import jenkins
from common.files.xml import xml_cmd
import common.files.xml.ElementTree as ET
import re


def add_elem(tag,text):
    elem1 = ET.Element(tag)
    elem1.text = text
    elem1.tail = "\n"
    return elem1


def add_job_to_view(server, job_names, view_name):
    view_xml = server.get_view_config(view_name)
    # with open("./view_temp.xml","w") as f:
    #     f.write(view_xml)
    # with open("./view_temp.xml","rt") as f:
    #     tree = ET.parse(f)
    tree = ET.XML(view_xml, parser=ET.XMLParser(encoding='utf-8'))

    elem_list = []
    for job_name in job_names:
        elem_list.append(add_elem("string", job_name))
    job_node = tree.find('./jobNames')
    job_node.extend(elem_list)
    new_view_xml = ET.tostring(tree, encoding="utf-8").decode()
    new_view_xml= '<?xml version="1.1" encoding="UTF-8"?>\n' + new_view_xml
    print (new_view_xml)
    server.reconfig_view(view_name,new_view_xml)


if __name__ == "__main__":
    server = jenkins.Jenkins('http://192.168.223.27:8080/jenkins', username='shuai.liu', password='Pandora!75')
    add_job_to_view(server,["mojie_release_9.9.9_mring"],"Mojie")

