#
import os
import sys
import time
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql import DBConnectionManager


def compile_timer(**kw):
    def des(func):
        compile_id = None
        stage = None
        business_id = None
        if 'stage' in kw:
            stage = kw['stage']

        def wrap(*args, **kwargs):
            start_time = time.time()
            origin_result = func(*args, **kwargs)
            elapse = int(time.time() - start_time)

            try:
                business_id = args[0].business_id
            except:
                pass

            if stage and business_id:
                with DBConnectionManager(
                    host='**************', port=3306, db='django_scm',
                    user='scm', password='howbuyscm', charset='utf8'
                ) as db:
                    db.cur.execute(
                        "SELECT * FROM record_compileoperatehistory \
                         WHERE businessID=%s \
                         ORDER BY buildID DESC \
                         LIMIT 1", (business_id,))
                    compile_list = db.cur.fetchone()
                    if compile_list:
                        compile_id = compile_list['id']
                        db.cur.execute(
                            "INSERT INTO record_compiletimecost(compile_id, stage, elapse) \
                             VALUES (%s, %s, %s);",  (compile_id, stage, elapse))
                        db.connection.commit()

            return origin_result

        return wrap

    return des


if __name__ == '__main__':
    class TestCompile(object):
        def __init__(self, business_id):
            self.business_id = business_id

        @compile_timer(stage='prework')
        def prework(self, sec):
            time.sleep(sec)
            print('prework: %ss.' % sec)

        @compile_timer(stage='compile')
        def compile(self, sec):
            time.sleep(sec)
            print('compile : %ss.' % sec)

        @compile_timer(stage='push')
        def push(self, sec):
            time.sleep(sec)
            print('push: %ss.' % sec)


    compile_obj = TestCompile('pay_test_1.0.29')
    compile_obj.prework(1)
    compile_obj.compile(2)
    compile_obj.push(1)
