#
import time
import pymysql


class DBConnectionManager:
    def __init__(self, host, port, user, password, db, charset="utf8"):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset

    def __enter__(self):
        self.connection = pymysql.connect(host=self.host, port=self.port, user=self.user,
                                          passwd=self.password, db=self.db,
                                          charset=self.charset)
        self.cur = self.connection.cursor(cursor=pymysql.cursors.DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()


def h5_package_timer(**kw):
    def des(func):
        h5_package_id = None
        stage = None
        if 'stage' in kw:
            stage = kw['stage']

        def wrap(*args, **kwargs):
            start_time = time.time()
            origin_result = func(*args, **kwargs)
            elapse = int(time.time() - start_time)

            try:
                h5_package_id = args[0].h5_package_id
            except:
                pass

            if stage and h5_package_id:
                with DBConnectionManager(
                        host='**************', port=3306, db='django_scm',
                        user='scm', password='howbuyscm', charset='utf8'
                ) as db:
                    db.cur.execute(
                        "SELECT * FROM record_h5packagehistory \
                         WHERE h5_package_id=%s", (h5_package_id,))
                    compile_list = db.cur.fetchone()
                    if compile_list:
                        h5_package_id = compile_list['h5_package_id']
                        db.cur.execute(
                            "INSERT INTO record_compiletimecost(h5_package_id, stage, elapse) \
                             VALUES (%s, %s, %s);",  (h5_package_id, stage, elapse))
                        db.connection.commit()

            return origin_result

        return wrap

    return des


def h5_package_stage_record(h5_package_id, stage, elapse):
    if stage and h5_package_id:
        with DBConnectionManager(
                host='**************', port=3306, db='django_scm',
                user='scm', password='howbuyscm', charset='utf8'
        ) as db:
            db.cur.execute(
                "SELECT * FROM record_h5packagehistory \
                 WHERE h5_package_id=%s", (h5_package_id,))
            compile_list = db.cur.fetchone()
            if compile_list:
                db.cur.execute(
                    "INSERT INTO record_compiletimecost(h5_package_id, stage, elapse) \
                     VALUES (%s, %s, %s);", (h5_package_id, stage, elapse))
                db.connection.commit()


if __name__ == '__main__':
    class TestH5Package(object):
        def __init__(self):
            self.h5_package_id = 1

        @h5_package_timer(stage='pull')
        def pull(self, sec):
            time.sleep(sec)
            print('pull: %ss.' % sec)

        @h5_package_timer(stage='compile')
        def compile(self, sec):
            time.sleep(sec)
            print('compile : %ss.' % sec)

        @h5_package_timer(stage='commit')
        def commit(self, sec):
            time.sleep(sec)
            print('commit: %ss.' % sec)


    h5_package_obj = TestH5Package()
    h5_package_obj.pull(1)
    h5_package_stage_record(1, 'compile', 2)
    # h5_package_obj.compile(2)
    h5_package_obj.commit(1)
