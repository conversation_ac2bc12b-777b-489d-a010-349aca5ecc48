import datetime

from common.mysql.db_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from db_mgt.creat_sql_migrate.models import DbMgtExecSqlFileHistory
from settings import logger
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_remote_cmd_by_username_password
from utils.form.models import DbMgtDumpDeployHis


class MysqlHandler(DbHandler):

    def __init__(self, module_name=None, suite_code=None, db_info_list=None, biz_iter_id=None):
        super().__init__(module_name, suite_code, db_info_list, biz_iter_id)

    def set_param(self, dump_file_list):
        super().set_param(dump_file_list)

    def _db_drop(self):
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        password = db_info.get("password")
                        suite_db_name = db_info.get("suite_db_name")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        db_srv_socket_path = db_info.get("db_srv_socket_path")
                        logger.info("======开始执行数据库删除，数据库名：{}======".format(db_info.get("db_name")))

                        cmd = '''
                                source {db_srv_bash_profile};
                                mysql --socket={db_srv_socket_path} -P3306 -u{username} -p'{password}' -N -se "DROP DATABASE IF EXISTS {suite_db_name};"
                               '''.format(db_srv_bash_profile=db_srv_bash_profile, username=username,
                                          password=password, suite_db_name=suite_db_name,
                                          db_srv_socket_path=db_srv_socket_path)
                        logger.info("删除数据库命令为：{}".format(cmd))
                        res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
                        logger.info(res)
                        DbMgtExecSqlFileHistory.delete().where(DbMgtExecSqlFileHistory.suite_code == self.suite_code,
                                                               DbMgtExecSqlFileHistory.db_name == db_info.get(
                                                                   "db_name")).execute()
                        logger.info("======结束执行数据库删除，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("没找到相关dump文件！！！无需处理！！！")

    def _db_create(self):
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        password = db_info.get("password")
                        suite_db_name = db_info.get("suite_db_name")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        db_srv_socket_path = db_info.get("db_srv_socket_path")

                        logger.info("======开始执行创建数据库，数据库名：{}======".format(db_info.get("db_name")))

                        cmd = '''
                                source {db_srv_bash_profile};
                                mysql --socket={db_srv_socket_path}  -P3306 -u{username} -p'{password}' -N -se "CREATE DATABASE {suite_db_name} DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci;"
                               '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                          db_srv_socket_path=db_srv_socket_path,
                                          username=username, password=password, suite_db_name=suite_db_name)
                        logger.info("创建数据库命令为：{}".format(cmd))

                        res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
                        logger.info(res)
                        logger.info("======结束执行创建数据库，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("没找到相关dump文件！！！无需处理！！！")

    def _db_restore(self):
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        password = db_info.get("password")
                        suite_db_name = db_info.get("suite_db_name")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        db_srv_socket_path = db_info.get("db_srv_socket_path")
                        logger.info("======开始执行恢复数据库，数据库名：{}======".format(db_info.get("db_name")))
                        start_time = datetime.datetime.now()

                        cmd = '''source {db_srv_bash_profile};
                                 mysql --socket={db_srv_socket_path} -P3306 -u{username} -p'{password}' -N {suite_db_name} < {dump_file_path}/{dump_file_name}
                           '''.format(db_srv_bash_profile=db_srv_bash_profile, db_srv_socket_path=db_srv_socket_path,
                                      username=username, password=password,
                                      suite_db_name=suite_db_name, dump_file_path=db_info.get("data_dump_dir"),
                                      dump_file_name=dump_file.get("dump_file_name"))
                        logger.info("恢复数据库命令为：{}".format(cmd))

                        res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
                        logger.info(res)
                        cur_time = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
                        his_data = {"create_user": 'be-scripts', "create_time": cur_time, "update_user": 'be-scripts',
                                    "update_time": cur_time, "dump_file_id": dump_file.get("id"), "stamp": 0,
                                    "module_name": self.module_name, "biz_iter_id": self.biz_iter_id,
                                    "suite_code": self.suite_code}
                        DbMgtDumpDeployHis.insert(his_data).execute()
                        end_time = datetime.datetime.now()
                        logger.info(
                            "======结束执行恢复数据库，数据库名：{}======，  恢复操作耗时：{}s".format(db_info.get("db_name"),
                            round((end_time - start_time).total_seconds(), 2)))
        else:
            logger.info("没找到相关dump文件！！！无需处理！！！")
            raise Exception('应用：{},业务分支：{}没找到相关dump文件！！！'.format(self.module_name, self.biz_iter_id))
