import json
import os

from dao.get.mysql.db_mgt_dump_file import get_db_dump_info_by_biz_iter_id
from settings import logger, NGINX_LIB_REPO, DUMP
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_remote_cmd_by_username_password


class DbHandler:

    def __init__(self, module_name=None, suite_code=None, db_info_list=None, biz_iter_id=None):
        self.module_name = module_name
        self.suite_code = suite_code
        self.biz_iter_id = biz_iter_id
        self.db_info_list = db_info_list
        if self.module_name and self.biz_iter_id:
            self.dump_file_list = get_db_dump_info_by_biz_iter_id(self.module_name, self.biz_iter_id)

    def set_param(self, dump_file_list):
        self.dump_file_list = dump_file_list

    def _check_dump_exist(self, db_type):
        if self.dump_file_list:
            dump_file_not_exist = []
            for dump_file in self.dump_file_list:
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        dump_file_name = dump_file.get("dump_file_name")
                        dump_file_path = db_info.get("data_dump_dir")
                        db_srv_source_profile = db_info.get("db_srv_source_profile")
                        # dump_file_path = self.__update_dump_file_path(self.opt_type, db_type, dump_file_path)
                        logger.info("开始确认dump文件")
                        cmd = '''cd {dump_file_path};find . -name {dump_file_name}'''.format(
                            dump_file_path=dump_file_path,
                            dump_file_name=dump_file_name)
                        logger.info(cmd)
                        res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
                        logger.info(res)

                        if dump_file_name not in res:
                            logger.info("dump文件不存在，去nginx上拉取")
                            mkdir_cmd = '''mkdir -p {dump_file_path};'''.format(dump_file_path=dump_file_path)
                            logger.info('执行创建文件夹命令：{}'.format(mkdir_cmd))
                            exec_remote_cmd_by_username_password(db_srv_hosts, mkdir_cmd, db_srv_username,
                                                                 db_srv_password)
                            dump_ng_url = os.path.join(NGINX_LIB_REPO["dump_base_url"], db_type, dump_file_name)
                            cmd = '''source {db_srv_source_profile};cd {dump_file_path};wget {dump_ng_url}'''.format(
                                db_srv_source_profile=db_srv_source_profile, dump_ng_url=dump_ng_url,
                                dump_file_path=dump_file_path)
                            logger.info('执行nginx上拉取文件夹命令：{}'.format(cmd))
                            res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username,
                                                                       db_srv_password)
                            logger.info(res)

                            logger.info("再次确认dump文件")
                            cmd = '''cd {dump_file_path};find . -name {dump_file_name}'''.format(
                                dump_file_path=dump_file_path,
                                dump_file_name=dump_file_name)
                            logger.info(cmd)
                            res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username,
                                                                       db_srv_password)
                            logger.info(res)
                        if dump_file_name not in res:
                            dump_file_not_exist.append(dump_file_name)
                            logger.error("{}文件不存在，请联系pa处理！".format(dump_file_name))
            if dump_file_not_exist:
                raise Exception('dump初始化文件不存在：{}，请联系pa处理'.format(dump_file_not_exist))
        else:
            logger.info("应用：{}, 业务迭代：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def __update_dump_file_path(self, opt_type, db_type, dump_file_path):
        if opt_type == 1:
            logger.info('opt_type = 1')
        elif opt_type == 2:
            parameter = None
            if db_type == 'mysql':
                dump_create_config = DUMP.get('mysql_dump_create_config')
                parameter = json.loads(dump_create_config)
            elif db_type == 'oracle':
                dump_create_config = DUMP.get('oracle_dump_create_config')
                parameter = json.loads(dump_create_config)
            if parameter:
                dump_file_path = parameter.get("data_dump_dir")
        elif opt_type == 3:
            parameter = None
            if db_type == 'mysql':
                dump_create_config = DUMP.get('mysql_dump_check_config')
                parameter = json.loads(dump_create_config)
            elif db_type == 'oracle':
                dump_create_config = DUMP.get('oracle_dump_check_config')
                parameter = json.loads(dump_create_config)
            if parameter:
                dump_file_path = parameter.get("data_dump_dir")
        else:
            raise Exception('错误的操作类型:{}'.format(opt_type))
        return dump_file_path

    def _kill_session(self):
        pass

    def _db_drop(self):
        pass

    def _drop_ddl(self):
        pass

    def _db_restore(self):
        pass

    def _user_create(self):
        pass

    def _db_unlock(self):
        pass

    def _db_set_user_and_password(self):
        pass

    def _db_dump(self):
        pass
