import datetime
import json
import logging
import os
import sys
from decimal import Decimal

from sqlalchemy import text

from common.mysql.models import DbMgtBisTestSql, DbMgtCdcRecordInfo
from pymysqlreplication import BinLogStreamReader
from pymysqlreplication.row_event import DeleteRowsEvent, UpdateRowsEvent, WriteRowsEvent
from dao.connect.mysql import DBConnectionManager
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from job.jenkins.test_data_dev.bo.mysql_settings_bo import MysqlSettingsBo
from job.jenkins.test_data_dev.cdc_common.cdc_constants import DbCdcEnum
from job.jenkins.test_data_dev.param.bis_iter_info_param import BisIterInfoParam
from job.jenkins.test_data_dev.test_diff_sql_ser import update_mysql_cdc_position
from job.jenkins.test_data_dev.util.cdc_file_util import get_diff_dml_sql_path, get_diff_log_file_path
from settings import DATABASES, TEST_DATA_INIT, logger


class MySQLDiffSqlCollect:
    operate_user = None
    bis_iter_info_param: BisIterInfoParam = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def mysql_diff_sql_collect(self):
        mu = MysqlUtils(self.bis_iter_info_param)
        log_bin_status, log_bin_dir = mu._get_log_bin_info()
        if log_bin_status:
            current_binlog_filename, current_log_pos = mu._get_current_log_bin_file()
            collect_binlog_list = self.get_collect_binlog_list(mu, current_binlog_filename, current_log_pos)
            logger.info("要采集的binlog信息collect_binlog_list:{}".format(collect_binlog_list))
            if not collect_binlog_list:
                logger.info("没有复合条件的binlog文件，不需要采集diff_sql")
                return False, "没有复合条件的binlog文件，不需要采集diff_sql"
            # 组装参数mysql_settings
            mysql_settings = MysqlSettingsBo(host=self.bis_iter_info_param.db_srv_hosts,
                                             port=self.bis_iter_info_param.db_srv_port,
                                             user=self.bis_iter_info_param.db_info_username,
                                             passwd=self.bis_iter_info_param.db_info_password)
            diff_sql_loggers = {}
            for item in collect_binlog_list:
                binlog_file = os.path.join(log_bin_dir, item[0])
                mb = MysqlTestDml(binlog_file, self.bis_iter_info_param.suite_db_name, mysql_settings, item[1],
                                  bis_iter_info_param=self.bis_iter_info_param)
                mb._generate_diff_sql(diff_sql_loggers)
            self.update_mysql_log_record_info(current_binlog_filename, current_log_pos, collect_binlog_list)
            return True, "数据库：{} diff_sql 采集完成！".format(self.bis_iter_info_param.suite_db_name)
        else:
            return False, "数据库的binlog开关未打开，联系DBA解决！"

    def update_mysql_log_record_info(self, current_binlog_filename, current_log_pos, collect_binlog_list):
        update_mysql_cdc_position(current_binlog_filename, current_log_pos, self.bis_iter_info_param)
        for item in collect_binlog_list:
            DbMgtCdcRecordInfo.create(
                cdc_batch_no=self.bis_iter_info_param.cdc_batch_no,
                restore_his_id=self.bis_iter_info_param.restore_his_id,
                suite_code=self.bis_iter_info_param.suite_code,
                db_info_id=self.bis_iter_info_param.db_info_id,
                cdc_pipeline_url=TEST_DATA_INIT['test_dml_diff_cdc_pipeline_url'].format(
                    self.bis_iter_info_param.build_id),
                binlog_name=item[0],
                start_cdc_position=item[1],
                create_time=datetime.datetime.now(),
                stamp=0)

    def get_collect_binlog_list(self, mu_obj, current_binlog_filename, current_log_pos):
        total_binlog_file_list = mu_obj._get_binlog_file_list()
        collect_binlog_list = []
        binlog_tuple = (self.bis_iter_info_param.binlog_name, self.bis_iter_info_param.log_pos, None)
        collect_binlog_list.append(binlog_tuple)
        collect_binlog_list = self.get_all_collect_binlog_list(self.bis_iter_info_param.binlog_name,
                                                               current_binlog_filename,
                                                               total_binlog_file_list,
                                                               collect_binlog_list,
                                                               current_log_pos)
        return collect_binlog_list

    def get_all_binlog_list(self, start_bin, end_bin, binlog_list):
        index_start = binlog_list.index(start_bin)
        index_end = binlog_list.index(end_bin)
        return binlog_list[index_start:index_end + 1]

    def get_all_collect_binlog_list(self, start_binlog, end_binlog, total_binlog_list, collect_binlog_list,
                                    current_log_pos):
        ready_to_collect_binlog_list = self.get_all_binlog_list(start_binlog, end_binlog, total_binlog_list)
        if len(ready_to_collect_binlog_list) > 1:
            for item in ready_to_collect_binlog_list[1:]:
                # 如果是最后一个item，则binlog_tuple的第三个值更新为当前的current_log_pos
                if item == ready_to_collect_binlog_list[-1]:
                    binlog_tuple = (item, '4', current_log_pos)
                else:
                    binlog_tuple = (item, '4', None)
                collect_binlog_list.append(binlog_tuple)
        elif len(ready_to_collect_binlog_list) == 1:
            pass
        else:
            collect_binlog_list = []

        return collect_binlog_list


class MysqlTestDml:

    def __init__(self, log_file, only_schemas, mysql_settings, log_pos, only_tables=None,
                 bis_iter_info_param: BisIterInfoParam = None):
        self.log_file = log_file
        self.only_schemas = only_schemas
        self.log_pos = int(log_pos)
        self.mysql_settings = dict(vars(mysql_settings))
        self.only_tables = only_tables if only_tables else []
        self.only_events = [DeleteRowsEvent, UpdateRowsEvent, WriteRowsEvent]
        self.diff_sql = ''
        self.bis_iter_info_param = bis_iter_info_param

    def _get_diff_sql(self):
        return self.diff_sql

    def setup_logger(self, table_name, log_file, level=logging.DEBUG):
        """Function setup as many loggers as you want"""
        handler = logging.FileHandler(log_file)  # 创建一个FileHandler实例，用于写入日志文件
        diff_logger = logging.getLogger(table_name)
        # 遍历logger的所有handlers，如果发现是StreamHandler并且指向的是标准错误流，则移除
        for handler in diff_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stderr:
                diff_logger.removeHandler(handler)
        diff_logger.setLevel(level)  # 设置日志级别
        diff_logger.addHandler(handler)  # 添加处理器
        return diff_logger

    def _generate_diff_sql(self, diff_sql_loggers):
        logger.info("=====> log_file:{}".format(self.log_file))
        logger.info("=====> only_schemas:{}".format(self.only_schemas))
        logger.info("=====> log_pos:{}".format(self.log_pos))
        logger.info("=====> mysql_settings:{}".format(self.mysql_settings))
        logger.info("=====> only_tables:{}".format(self.only_tables))
        logger.info("=====> only_events:{}".format(self.only_events))
        if self.only_tables:
            stream = BinLogStreamReader(
                connection_settings=self.mysql_settings,
                server_id=100,
                blocking=False,
                log_file=self.log_file,
                freeze_schema=True,
                only_schemas=self.only_schemas,
                only_tables=self.only_tables,
                ignored_tables=['flyway_schema_history'],
                resume_stream=True,
                log_pos=self.log_pos,
                only_events=self.only_events
            )
        else:
            stream = BinLogStreamReader(
                connection_settings=self.mysql_settings,
                server_id=100,
                blocking=False,
                log_file=self.log_file,
                freeze_schema=True,
                only_schemas=self.only_schemas,
                ignored_tables=['flyway_schema_history'],
                resume_stream=True,
                log_pos=self.log_pos,
                only_events=self.only_events
            )
        insert_count, update_count, delete_count = 0, 0, 0
        for binlog_event in stream:
            try:
                # 保存原始的 stdout
                original_stdout = sys.stdout
                # 将 stdout 重定向到 null
                sys.stdout = open(os.devnull, 'w')
                # 调用方法
                binlog_event.dump()
                # 恢复 stdout
                dt_object = datetime.datetime.fromtimestamp(binlog_event.timestamp)
                change_time = dt_object.strftime("%Y-%m-%d %H:%M:%S")
                sys.stdout = original_stdout
                for row in binlog_event.rows:
                    event = {"schema": binlog_event.schema, "table": binlog_event.table}
                    if isinstance(binlog_event, DeleteRowsEvent):
                        event["action"] = "delete"
                        event["data"] = row["values"]
                        delete_count += 1
                    elif isinstance(binlog_event, UpdateRowsEvent):
                        event["action"] = "update"
                        event["old_data"] = row["before_values"]
                        event["data"] = row["after_values"]
                        update_count += 1
                    elif isinstance(binlog_event, WriteRowsEvent):
                        event["action"] = "insert"
                        event["data"] = row["values"]
                        insert_count += 1
                    redo_sql = self.__transfer_event_to_dml(event)
                    # 获取数据变更时间
                    if not diff_sql_loggers.get(binlog_event.table.strip()):
                        if not os.path.exists(get_diff_dml_sql_path(self.bis_iter_info_param)):
                            os.makedirs(get_diff_dml_sql_path(self.bis_iter_info_param))
                        diff_sql_loggers[binlog_event.table.strip()] = self.setup_logger(table_name=binlog_event.table,
                                                                                         log_file=os.path.sep.join(
                                                                                             [get_diff_dml_sql_path(
                                                                                                 self.bis_iter_info_param),
                                                                                                 binlog_event.table.strip() + ".sql"]))
                    diff_sql_loggers[binlog_event.table.strip()].debug(redo_sql)

                    self.bis_iter_info_param.diff_sql_log_logger(get_diff_log_file_path()[0],
                                                                 get_diff_log_file_path()[1]).debug(
                        self.build_diff_sql_log(event["action"], binlog_event, redo_sql, change_time, self.log_file))

            except Exception as e:
                logging.error(str(e))
        logger.info("insert变更：{}条".format(insert_count))
        logger.info("update变更：{}条".format(update_count))
        logger.info("delete变更：{}条".format(delete_count))
        stream.close()

    def build_diff_sql_log(self, operation, binlog_event, redo_sql, change_time, log_file):
        return json.dumps({
            "biz_iter_id": self.bis_iter_info_param.bis_pipeline_id,
            "db_srv_id": self.bis_iter_info_param.db_srv_id,
            "db_srv_type": DbCdcEnum.MYSQL.value[0],
            "scn": binlog_event.packet.log_pos,
            "table_name": binlog_event.table.strip(),
            "change_time": change_time,
            "operation": operation,
            "log_file": log_file,
            "suite_db_name": self.bis_iter_info_param.suite_db_name,
            "db_name": self.bis_iter_info_param.db_info_suffix_name,
            "redo_sql": redo_sql,
            'time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%S")
        })

    def _save_diff_sql(self, test_dml_info, start_datetime, operate_user, diff_sql=None):
        # test_dml_info = {'bis_code': 'it29', 'bis_branch_name': '1.0.0', 'db_group_name': 'ZTST',
        #                      'db_name': 'wechatmessage'}
        if not diff_sql:
            diff_sql = self.diff_sql
        test_dml_group = TEST_DATA_INIT.get("test_dml_group")
        bis_code = test_dml_info.bis_code
        bis_branch_name = test_dml_info.bis_branch_name
        bis_pipeline_id = bis_code + '_' + bis_branch_name
        db_group_name = test_dml_info.db_group_name
        db_name = test_dml_info.db_name
        bis_root_path = os.path.join('/data', test_dml_group, test_dml_info.bis_base_db_code)
        bis_branch_path = os.path.join(bis_root_path, bis_branch_name)

        diff_sql_path = os.path.join(bis_branch_path, db_group_name, db_name, 'DML')
        sql_file_path = os.path.join(db_group_name, db_name, 'DML')

        if not os.path.isdir(diff_sql_path):
            cmd = 'mkdir -p {}'.format(diff_sql_path)
            os.system(cmd)

        diff_sql_name = '{}_diff_{}.sql'.format(db_name, start_datetime.split(" ")[0])

        os.chdir(diff_sql_path)
        sql_file_abs_path = os.path.join(diff_sql_path, diff_sql_name)

        with open(sql_file_abs_path, 'w') as f:
            logging.info("========>将采集的diff_sql写入文件: {}".format(sql_file_abs_path))
            f.write(diff_sql)
        curr_time = datetime.datetime.now()
        if not os.path.exists(os.path.dirname(sql_file_abs_path)):
            logging.info("{} 无diff_sql 生成".format(db_name))
            return False
        p, created = DbMgtBisTestSql.get_or_create(bis_pipeline_id=bis_pipeline_id,
                                                   sql_file_name=diff_sql_name,
                                                   defaults={'create_user': operate_user,
                                                             'create_time': curr_time,
                                                             'update_user': operate_user,
                                                             'update_time': curr_time,
                                                             'stamp': 0,
                                                             'bis_branch_name': bis_branch_name,
                                                             'sql_file_path': sql_file_path,
                                                             'sql_ver_upload_status': 0})
        if not created:
            DbMgtBisTestSql.update({DbMgtBisTestSql.update_user: operate_user,
                                    DbMgtBisTestSql.update_time: curr_time,
                                    DbMgtBisTestSql.sql_ver_upload_status: 0}). \
                where(DbMgtBisTestSql.id == p.id).execute()

    def __transfer_insert_dml(self, event):
        dml = 'insert into ' + event.get("table") + ' ( '
        column = ''
        values = ''
        for key in event.get('data').keys():
            column += key + ', '
        for value in event.get('data').values():
            if isinstance(value, datetime.datetime):
                values += "'" + value.strftime("%Y-%m-%d %H:%M:%S") + "'" + ', '
            elif isinstance(value, int) or isinstance(value, Decimal):
                values += str(value) + ', '
            elif value is None:
                values += 'NULL' + ', '
            else:
                values += "'" + value + "'" + ', '
        dml += column.rstrip(', ') + ' ) values (' + values.rstrip(', ') + ');'
        # logging.info(dml)
        return dml

    def __transfer_update_dml(self, event):
        dml = 'update ' + event.get("table") + ' set '
        for k, v in event.get("data").items():
            if isinstance(v, datetime.datetime):
                dml += k + "='" + v.strftime("%Y-%m-%d %H:%M:%S") + "'" + ', '
            elif isinstance(v, int) or isinstance(v, Decimal):
                dml += k + "=" + str(v) + ', '
            elif v is None:
                dml += k + "=NULL" + ', '
            else:
                dml += k + "='" + v + "'" + ', '
        dml = dml.rstrip(', ') + " where "
        for k, v in event.get("old_data").items():
            if isinstance(v, datetime.datetime):
                # 在where条件中先拿掉时间格式的条件 20240619 by fwm
                # dml += k + "='" + v.strftime("%Y-%m-%d %H:%M:%S") + "'" + ' and '
                pass
            elif isinstance(v, int) or isinstance(v, Decimal):
                dml += k + "=" + str(v) + ' and '
            elif v is None:
                dml += k + " is NULL" + ' and '
            else:
                dml += k + "='" + v + "'" + ' and '
        dml = dml.rstrip(' and ') + ';'
        return dml

    def __transfer_delete_dml(self, event):
        dml = "delete from " + event.get("table") + ' where '
        for k, v in event.get("data").items():
            if isinstance(v, datetime.datetime):
                dml += k + "='" + v.strftime("%Y-%m-%d %H:%M:%S") + "'" + ' and '
            elif isinstance(v, int) or isinstance(v, Decimal):
                dml += k + "=" + str(v) + ' and '
            elif v is None:
                dml += k + " is NULL" + ' and '
            else:
                dml += k + "='" + v + "'" + ' and '
        dml = dml.rstrip(' and ') + ';'
        return dml

    def __transfer_event_to_dml(self, event):
        dml = ''
        if event.get("action") == 'insert':
            dml = self.__transfer_insert_dml(event)
        elif event.get("action") == "update":
            dml = self.__transfer_update_dml(event)
        elif event.get("action") == "delete":
            dml = self.__transfer_delete_dml(event)
        return dml


class MysqlUtils:

    def __init__(self, mysql_db_info):
        self.db_hosts = mysql_db_info.db_srv_hosts
        self.db_port = mysql_db_info.db_srv_port
        self.db_user = mysql_db_info.db_info_username
        self.db_passwd = mysql_db_info.db_info_password
        self.db_srv_name = mysql_db_info.db_srv_name
        self.db_srv_username = mysql_db_info.db_srv_username
        self.db_srv_password = mysql_db_info.db_srv_password

    def _get_log_bin_info(self):
        log_bin_status = False
        log_bin_dir = ''
        with DBConnectionManager(host=self.db_hosts, port=int(self.db_port), user=self.db_user, password=self.db_passwd,
                                 db=self.db_srv_name, charset=DATABASES["CHARSET"]) as db:
            sql = '''SHOW VARIABLES LIKE '%log_bin%';'''
            logging.info(sql)
            db.cur.execute(sql)

            for row in db.cur.fetchall():
                if row.get("Variable_name") == 'log_bin' and row.get("Value") == 'ON':
                    log_bin_status = True
                if row.get("Variable_name") == 'log_bin_basename':
                    log_bin_dir = '/'.join(row.get("Value").split('/')[:-1])
        return log_bin_status, log_bin_dir

    def _get_current_log_bin_file(self):
        with DBConnectionManagerForSqlalchemy(host=self.db_hosts, port=int(self.db_port), user=self.db_user,
                                              password=self.db_passwd,
                                              db=self.db_srv_name, charset=DATABASES["CHARSET"]) as db:
            sql = '''SHOW MASTER STATUS;'''
            logging.info(sql)
            for row in db.session.execute(text(sql)).fetchall():
                logging.info(row)
                return row[0], row[1]

    def _get_binlog_file_list(self):
        with DBConnectionManagerForSqlalchemy(host=self.db_hosts, port=int(self.db_port), user=self.db_user,
                                              password=self.db_passwd,
                                              db=self.db_srv_name, charset=DATABASES["CHARSET"]) as db:
            sql = '''SHOW BINARY LOGS;'''
            logging.info(sql)
            bin_log_file_list = []
            for row in db.session.execute(text(sql)).fetchall():
                bin_log_file_list.append(row[0])
            return sorted(bin_log_file_list)


if __name__ == '__main__':

    d1 = datetime.datetime.now()
    date_string = "2024-1-23"
    d2 = datetime.datetime.strptime(date_string, "%Y-%m-%d").date()
    # if d1 < d2:
    #     print("d1<d2")
    # else:
    #     print("d1>d2")
    if isinstance(d2, datetime.date):
        print("datetime.date")
    else:
        print("not datetime.datetime")
