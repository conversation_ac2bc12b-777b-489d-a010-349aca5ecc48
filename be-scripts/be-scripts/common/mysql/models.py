from peewee import *
from dao.base_model import BaseModel, SpiderBaseModels


class DbMgtBisTestSql(BaseModel):
    cdc_batch_no = CharField(verbose_name='数据采集批次', max_length='100')
    suite_code = CharField(verbose_name='实际采集的库', max_length='100')
    db_info_id = BigIntegerField(verbose_name='数据库 id')
    bis_pipeline_id = CharField(verbose_name='业务数据生产迭代id', max_length='100')
    bis_branch_name = CharField(verbose_name='迭代分支名', max_length='100')
    sql_file_path = CharField(verbose_name='sql文件路径', max_length='999')
    sql_file_name = CharField(verbose_name='sql文件名', max_length='255')
    sql_file_hash = CharField(verbose_name='sql文件hash', max_length='255')
    sql_ver_name = CharField(verbose_name='sql制品名', max_length='255')
    gitlab_lib_version = CharField(verbose_name='gitlab-lib仓库commit-id', max_length='255')
    sql_ver_db = CharField(verbose_name='sql制品对应库', max_length='100')
    sql_ver_group = CharField(verbose_name='sql制品对应组', max_length='100')
    sql_ver_tgt = CharField(verbose_name='sql制品库', max_length='999')
    sql_ver_upload_status = IntegerField(verbose_name='sql制品上传状态')
    sql_ver_upload_time = DateTimeField(verbose_name='sql制品上传时间')
    sql_file_desc = CharField(verbose_name='sql制品说明', max_length='255')
    arc_sql_ver_name = CharField(verbose_name='归档后的制品名', max_length='255')
    create_user = CharField(verbose_name='创建人', max_length='20')
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length='20')
    update_time = DateTimeField(verbose_name='更新时间')
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        table_name = 'db_mgt_bis_test_sql'


class DbMgtBisTestSqlCommitHis(BaseModel):
    bis_pipeline_id = CharField(verbose_name='业务数据生产迭代id', max_length='100')
    db_info_id = BigIntegerField(verbose_name='数据库ID')
    sql_ver_name = CharField(verbose_name='sql版本化文件名', max_length='255')
    gitlab_repo_version = CharField(verbose_name='gitlab-repo仓库提交版本号', max_length='255')
    git_commit_time = DateTimeField(verbose_name='git提交时间')
    operate_user = CharField(verbose_name='操作人', max_length='50')

    class Meta:
        table_name = 'db_mgt_bis_test_sql_commit_his'


class DbMgtBisTestSqlArchiveHis(BaseModel):
    bis_pipeline_id = CharField(verbose_name='业务数据生产迭代id', max_length='100')
    db_info_id = BigIntegerField(verbose_name='数据库ID')
    arc_sql_ver_name = CharField(verbose_name='归档sql版本化文件名', max_length='255')
    gitlab_repo_version = CharField(verbose_name='gitlab-repo仓库提交版本号', max_length='255')
    git_archive_time = DateTimeField(verbose_name='git归档时间')
    operate_user = CharField(verbose_name='操作人', max_length='50')

    class Meta:
        table_name = 'db_mgt_bis_test_sql_archive_his'


class DbMgtCdcRecordInfo(BaseModel):
    cdc_batch_no = CharField(verbose_name='采集批次号', max_length='50')
    db_info_id = BigIntegerField(verbose_name='数据库ID')
    suite_code = CharField(verbose_name='环境', max_length='100')
    restore_his_id = CharField(verbose_name='业务数据生产迭代恢复记录ID', max_length='100')
    cdc_pipeline_url = CharField(verbose_name='采集jenkins构建pipeline路径', max_length='1000')
    start_cdc_position = CharField(verbose_name='binlog当天的起始位置', max_length='20')
    end_cdc_position = CharField(verbose_name='binlog当天的起始位置', max_length='20')
    binlog_name = CharField(verbose_name='mysql_binlog文件', max_length='100')
    create_user = CharField(verbose_name='创建人', max_length='50')
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length='50')
    update_time = DateTimeField(verbose_name='更新时间')
    stamp = IntegerField(verbose_name='版本')

    class Meta:
        table_name = 'db_mgt_cdc_record_info'


class DbMgtLogRecordDetail(BaseModel):
    record_id = BigIntegerField(verbose_name='log_record_info序号')
    binlog_name = CharField(verbose_name='binlog文件名', max_length='100')
    start_log_pos = CharField(verbose_name='binlog采集开始点', max_length='20')
    end_log_pos = CharField(verbose_name='binlog采集结束点', max_length='20')

    class Meta:
        table_name = 'db_mgt_log_record_detail'


# class DbMgtDbRestoreHis(BaseModel):
#     db_name = CharField(verbose_name='数据库名称', max_length='50')
#     suite_code = CharField(verbose_name='环境', max_length='50')
#     db_info_id = IntegerField(verbose_name='数据库ID')
#     restore_datetime = DateTimeField(verbose_name='数据库恢复时间')
#     opt_pipeline_id = CharField(verbose_name='操作的迭代', max_length='100')
#     create_user = CharField(verbose_name='创建人', max_length='50')
#     create_time = DateTimeField(verbose_name='创建时间')
#     update_user = CharField(verbose_name='更新人', max_length='50')
#     update_time = DateTimeField(verbose_name='更新时间')
#
#     class Meta:
#         table_name = 'db_mgt_db_restore_his'


class DbMgtCdcInfo(SpiderBaseModels):
    id = BigIntegerField(verbose_name='ID')
    suite_code = CharField(verbose_name='环境', max_length='100')
    db_info_id = BigIntegerField(verbose_name='数据库ID')
    restore_his_id = CharField(verbose_name='业务数据生产迭代恢复记录ID', max_length='100')
    cdc_position = CharField(verbose_name='oracle 采集点位', max_length='100')
    binlog_name = CharField(verbose_name='mysql binlog 文件名', max_length='100')
    log_pos = CharField(verbose_name='mysql binlog 采集点位', max_length='100')
    cdc_flag = IntegerField(verbose_name='数据采集标识 1 正在采集/0 采集结束')

    class Meta:
        table_name = 'db_mgt_cdc_info'
