#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from common.email.template.apply_template import apply_email_template


def generate_apply_email_content(team, branch_name, applicant, pipeline_description,
                                 sql_address, description, app_name_list, warning_info, schedule="", configure=""):
    return apply_email_template.format(team=team, branch_name=branch_name, applicant=applicant,
                                       pipeline_description=pipeline_description, sql_address=sql_address,
                                       description=description, app_name_list=app_name_list,
                                       warning_info=warning_info, schedule=schedule, configure=configure)


if __name__ == '__main__':
    print(generate_apply_email_content("asset", "0.0.1", "xing.zhang", "新资产中心分支0.0.1",
                                       "None", "", "asset-batch-center-remote,asset-center-remote", ""))
