import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
# import os
# import sys
# PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# sys.path.append(PROJECT_DIR)
from settings import logger
from settings import EMAIL_BASES


class SendMail:
    def __init__(self, mail_host=EMAIL_BASES["MAIL_HOST"], password=EMAIL_BASES["PASSWORD"], sender=EMAIL_BASES["SENDER"]):
        self.mail_host = mail_host
        self.__mail_password = password
        self.sender = sender
        self.main_msg = MIMEMultipart()

    def add_attachment(self, attachment_path):
        att = MIMEText(open(attachment_path, 'rb').read(), 'base64', 'utf-8')
        att["Content-Type"] = 'application/octet-stream'
        att.add_header('Content-Disposition', u'attachment; filename="%s"' % attachment_path.split('/')[-1])
        self.main_msg.attach(att)

    def set_content(self, msg):
        content = MIMEText(msg, "html", "utf-8")
        self.main_msg.attach(content)

    def set_subject(self, subject):
        self.main_msg['Subject'] = subject

    def set_to(self, to_list):
        self.main_msg['To'] = to_list

    def check_email_content(self):
        if self.main_msg['Subject'] is None:
            raise ValueError('主题不能为空')
        elif self.main_msg['To'] is None:
            raise ValueError('收件人不能为空')

    def send(self):
        self.check_email_content()
        self.main_msg['From'] = self.sender
        #try:
        server = smtplib.SMTP(self.mail_host)
        server.connect(self.mail_host, "25")
        server.ehlo()
        server.starttls()
        server.login(self.main_msg['From'], self.__mail_password)
        server.sendmail(self.main_msg['From'], self.main_msg['To'].split(","), self.main_msg.as_string())
        server.quit()
        logger.info("邮件发送成功！")
        # except Exception as e:
        #     logger.error(e)

    def send_mail(self, params, template):
        self.set_subject(params["subject"])
        self.set_content(template.format(**params))
        self.set_to(params["mail_to"])
        self.send()
