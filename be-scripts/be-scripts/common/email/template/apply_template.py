#

apply_email_template = '''
        <!DOCTYPE html>
        <html>
          <head>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title>申请</title>
            <style type="text/css">
                h2{{
                    margin:0 auto;
                    text-align: center;
                }}
                table{{
                    word-break:keep-all;
                    border-collapse:collapse;
                    text-align: left;
                    margin: 0 auto;
                }}
                table span{{
                    display: inline-block;
                    width: 100px;
                    text-align: right;
                }}
            </style>
          </head>
          <body>
            <table border="1" style=" word-break:keep-all;border-collapse:collapse;text-align: left;margin: 0 auto;">
              <tr>
                <td height="40" colspan="2">所属团队</td>
                <td colspan="2" width="200">{team}</td>
                <td colspan="2" width="200">分支名称</td>
                <td colspan="2" width="200">{branch_name}</td></tr>
              <tr>
                <td height="40" colspan="2">申请人</td>
                <td colspan="6">{applicant}</td>
              <tr>
                <td height="30" colspan="2">需求/测试功能</td>
                <td colspan="6">{pipeline_description}</td></tr>
              <tr>
                <td height="30" colspan="2">SQL执行</td>
                <td colspan="6">{sql_address}</td></tr>
              <tr>
                <td height="30" colspan="2">调度</td>
                <td colspan="6">{schedule}</td></tr>
              <tr>
                <td height="30" colspan="2">配置</td>
                <td colspan="6">{configure}</td></tr>
              <tr>
                <td height="30" colspan="2">上线注意事项</td>
                <td colspan="6">{description}</td></tr>
              <tr>
                <td height="40" colspan="2">应用名称</td>
                <td colspan="6">{app_name_list}</td></tr>
            </table>
            <br/>
            {warning_info}
          </body>
        </html>
'''