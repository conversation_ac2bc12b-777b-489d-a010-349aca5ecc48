# coding:utf-8
import os
import glob
import re
from settings import logger as log
from settings import TEST_PUBLISH_AIO as TPA


def file_search(path, file_name=None, ignore=None):
    """
    根据path搜索路径下所有文件，返回一个生成器，
    根据file_name搜索path下匹配的文件名，返回生成器
    根据ignore这个list忽略所有匹配的路径
    :param path:
    :param file_name:
    :param ignore:
    :return:
    """
    for file in os.listdir(path):
        file_path = os.path.join(path, file)
        if ignore is not None:
            if __match(file_path, ignore):
                continue
        if file_name is not None:
            if os.path.isfile(file_path):
                tmp_list = []
                tmp_list.append(file_name)
                if __match(file, tmp_list):
                    yield file_path
            elif os.path.isdir(file_path):
                for file in file_search(file_path, file_name, ignore):
                    yield file
        else:
            if os.path.isfile(file_path):
                yield file_path
            elif os.path.isdir(file_path):
                for file in file_search(file_path, file_name, ignore):
                    yield file


def __match(test_str, pattern_list):
    is_match = False
    try:
        for pattern in pattern_list:
            # 匹配成功时
            if re.search(pattern, test_str):
                #log.info(">>>> 跳过「宙斯配置项」替换，规则(%s)，文件：%s", pattern, test_str)
                is_match = True
                break

    except re.error as e:
        log.error(">>>> 正则匹配错误，正则(%s)，字符(%s)，错误：%s", test_str, e)

    return is_match


# tmp_str = file.replace("\\","\\\\")
# def search(path, file_name=None):
#     """
#     根据文件路径搜索给定路径下的全部文件或文件夹，如果添加正则表达式于第二个参数则
#     返回给定路径下的匹配文件的路径
#     :param path:
#     :param file_name:
#     :return:
#     """
#     if file_name is not None:
#         for root, dirs, files in os.walk(path):
#             directory = os.path.join(root, file_name)
#             for file in glob.glob(directory):
#                 print(file)
#     else:
#         __file_path_format(path, "--")
#
#
# def __file_path_format(path, label):
#     for file in os.listdir(path):
#         file_path = os.path.join(path, file)
#         if os.path.isdir(file_path):
#             print("{}{}".format(label, file_path))
#             new_label = label*2
#             __file_path_format(file_path, new_label)
#         elif os.path.isfile(file_path):
#             print("{}{}".format(label, file_path))


if __name__ == "__main__":
    print (__match("ehowbuy-pom.xml", [".*-pom.xml"]))
    # test = file_search('E:\SogouInput', 'ssf', [r'E:\\SogouInput\\SogouExe'])
    # print(list(test))

