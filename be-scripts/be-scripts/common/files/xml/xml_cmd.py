import xml.etree.ElementTree as ET
import re

# CommentedTreeBuilder类来重载处理注释
# class CommentedTreeBuilder (ET.XMLParser):
#     def __init__(self, html=0, target=None):
#         ET.XMLParser.__init__(self, html, target)
#         self._parser.CommentHandler = self.handle_comment
#
#     def handle_comment(self, data):
#         self._target.start(ET.Comment, {})
#         self._target.data(data)
        #self._target.end(ET.Comment)

class CommentedTreeBuilder(ET.TreeBuilder):
    def comment(self, data):
        self._target.start(ET.Comment, {})
        self._target.data(data)
        self._target.end(ET.Comment)
# CommentedTreeBuilder类来重载处理注释
# class CommentedTreeBuilder (ET.XMLParser):
#     def __init__(self, html= ['sentinel'], target=None, encoding=None):
#
#         #ET.XMLParser.__init__(self, )
#
#         #parser.CommentHandler = self.handle_comment
#         super(CommentedTreeBuilder, self).__init__(html=['sentinel'], target=None, encoding='utf-8')
#         super(ET.XMLParser).parser
#         self.test()
#         print (self.parser)
#         super(CommentedTreeBuilder, self).parser.CommentHandler = self.handle_comment




def get_xmlns(tree):
    """
    获取命名空间
    :param tree:
    :return:
    """
    root = tree.getroot()  # 获取root节点，返回element,root.tag是获取节点的值
    mach_string = '.*}'  # 增加正则匹配条件
    ns = re.match(mach_string, root.tag)
    if ns is not None:
        ns = ns.group()
    else:
        ns = ''
    return ns  # 通过匹配所有.*}, 找到对应namespace, 列表返回;


def read_xml(xml_path):
    """
    读取并解析xml文件
    :param xml_path: xml路径
    :return:
    """
    tree = ET.ElementTree()
    tree.parse(xml_path)
    return tree


def write_xml(tree, out_path):
    """
    :param tree: xml树
    :param out_path: 输出路径
    :return:
    """
    tree.write(out_path, encoding="utf-8", xml_declaration=True)


def if_match(node, kv_map):
    """
    判断某个节点是否包含所有传入参数属性
    :param node: 节点
    :param kv_map: 属性及属性值组成的map
    :return:
    """
    for key in kv_map:
        if node.get(key) is None:
            raise KeyError(format(key))
        if node.get(key) != kv_map.get(key):
            return False
    return True


def get_node_by_keyvalue(nodelist, kv_map):
    """
    根据属性及属性值定位符合的节点，返回节点
    :param nodelist: 节点列表
    :param kv_map: 匹配属性及属性值map
    :return:
    """
    result_nodes = []
    for node in nodelist:
        if if_match(node, kv_map):
            result_nodes.append(node)
    if result_nodes:
        return result_nodes
    else:
        return False


def change_node_properties(nodelist, kv_map, is_delete=False):
    """
    修改/增加 /删除 节点的属性及属性值
    :param nodelist: 节点列表
    :param kv_map: 属性及属性值map
    :param is_delete:
    :return:
    """
    for node in nodelist:
        for key in kv_map:
            if is_delete:
                if key in node.attrib:
                    del node.attrib[key]
            else:
                node.set(key, kv_map.get(key))


def change_node_text(nodelist, text, is_add=False, is_delete=False):
    """
    改变/增加/删除一个节点的文本
    :param nodelist: 节点列表
    :param text: 更新后的文本
    :param is_add: 追加
    :param is_delete: 清空
    :return:
    """

    for node in nodelist:
        if is_add:
            node.text += text
        elif is_delete:
            node.text = ""
        else:
            node.text = text


def create_node(tag, property_map, content):
    """
    新造一个节点
    :param tag: 节点标签
    :param property_map: 属性及属性值map
    :param content: 节点闭合标签里的文本内容
    :return:
    """

    element = ET.Element(tag, property_map)
    element.text = content
    return element


def add_child_node(nodelist, element):
    """
    给一个节点添加子节点
    :param nodelist: 节点列表
    :param element: 子节点
    :return:
    """

    for node in nodelist:
        node.append(element)


def del_node_by_tagkeyvalue(nodelist, tag, kv_map):
    """
    同过属性及属性值定位一个节点，并删除之
    :param nodelist: 父节点列表
    :param tag: 子节点标签
    :param kv_map: 属性及属性值列表
    :return:
    """
    for parent_node in nodelist:
        children = parent_node.getchildren()
        for child in children:
            if child.tag == tag and if_match(child, kv_map):
                parent_node.remove(child)


def modify_spring_xml(build_file_path, bean_id, property_name, new_value):
    tree = read_xml(build_file_path)
    xmlns = None
    try:
        xmlns = get_xmlns(tree)

    except AttributeError as e:
        print(e)

    finally:

        if xmlns:
            primary_node = './/' + xmlns + 'bean'
            secondary_node = './/' + xmlns + 'property'
            value_text = './/' + xmlns + 'value'
        else:
            primary_node = "bean"
            secondary_node = "property"
            value_text = 'value'

        bean_id_node = get_node_by_keyvalue(tree.findall(primary_node), {'id': bean_id})
        if not bean_id_node:
            return False
        property_node = get_node_by_keyvalue(bean_id_node[0].findall(secondary_node), {'name': property_name})
        if not property_node:
            return False
        property_node[0].find(value_text).text = new_value
        write_xml(tree, build_file_path)
        return True


def modify_log4j_xml(build_file_path, property_name, new_value):
    tree = read_xml(build_file_path)
    xmlns = None
    try:
        xmlns = get_xmlns(tree)

    except AttributeError as e:
        print(e)

    finally:

        if xmlns:
            primary_node = './/' + xmlns + 'properties'
            secondary_node = './/' + xmlns + 'property'

        else:

            primary_node = "properties"
            secondary_node = "property"

        property_node = get_node_by_keyvalue(tree.find(primary_node).findall(secondary_node), {'name': property_name})
        if not property_node:
            return False
        property_node[0].text = new_value
        write_xml(tree, build_file_path)
        return True

