import datetime
import json
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)

from sqlalchemy import text
import traceback
from common.mysql.db_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from common.oracle.models import DbMgtOptScript
from dao.connect.oracle_sqlalchemy import DBConnectionManagerForSqlalchemyOracle
from db_mgt.creat_sql_migrate.models import DbMgtExecSqlFileHistory
from job.jenkins.test_data_dev.cdc_common.cdc_constants import DbCdcEnum
from job.jenkins.test_data_dev.param.oracle_cdc_param import DbCdcParam
from job.jenkins.test_data_dev.test_diff_sql_ser import get_user_connect_info, get_unlock_user
from settings import logger, DUMP
from test_mgt.test_mgt_ser import get_db_deploy_info
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_remote_cmd_by_username_password
from utils.form.models import DbMgtDumpDeployHis
from dao.get.mysql.db_mgt_dump_file import get_empty_dump_with_module_name_for_oracle_clear


class OracleHandler(DbHandler):

    def __init__(self, module_name=None, suite_code=None, db_info_list=None, biz_iter_id=None):
        super().__init__(module_name, suite_code, db_info_list, biz_iter_id)
        # 增加Oracle库清理的逻辑实现。zt@2024-09-20
        if self.module_name and not biz_iter_id:
            self.dump_file_list = get_empty_dump_with_module_name_for_oracle_clear(self.module_name, "PA-TEST_master")
            if not self.dump_file_list:
                err_msg = ">>>> 应用：{}，没有维护用来清空库的dump数据。平台处理DDL冲突失败，请自行处理冲突后再重新初始化，或联系冯伟敏维护空dump数据。".format(
                    self.module_name)
                raise Exception(err_msg)

    def set_param(self, dump_file_list):
        super().set_param(dump_file_list)

    def _execute_sql_cmd(self, dump_file_path, sql_script, username, db_srv_hosts, db_srv_username,
                         db_srv_password, db_srv_bash_profile, opt_type, is_not_sys=None, db_srv_name=None, password="howbuy2015"):
        start_time = datetime.datetime.now()

        logger.info('开始生成sql文件')
        cmd = '''cd {dump_file_path};echo "{sql_script}">{username}.sql'''.format(dump_file_path=dump_file_path,
                                                                                  sql_script=sql_script,
                                                                                  username=username)
        logger.info("生成sql文件命令：{}".format(cmd))
        res1 = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
        logger.info(res1)

        if not is_not_sys:
            logger.info('开始执行用sys用户执行sql文件')
            cmd = '''source {db_srv_bash_profile};
                     sqlplus 'sys/sys as sysdba' @{dump_file_path}/{username}.sql {username}
                  '''.format(db_srv_bash_profile=db_srv_bash_profile,
                             dump_file_path=dump_file_path,
                             username=username)
            logger.info(cmd)
            res2 = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
            logger.info(res2)
            if 'ORA-01918' in res2:
                raise Exception('oracle数据库 [{}] 可能不存在,请检查'.format(username))
        else:
            logger.info('开始执行用{}用户执行sql文件'.format(username))
            cmd = '''source {db_srv_bash_profile};
                     sqlplus {username}/{password} @{dump_file_path}/{username}.sql {username}
                  '''.format(db_srv_bash_profile=db_srv_bash_profile, dump_file_path=dump_file_path, username=username,
                             password=password)
            logger.info(cmd)
            res2 = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
            logger.info(res2)

        self.catch_oracle_sys_error(res2, username, db_srv_hosts, db_srv_name)

        logger.info('移除生成的sql文件')
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        cmd = '''mv {dump_file_path}/{username}.sql /home/<USER>/bak/{username}.sql.{opt_type}.bak{cur_time}
              '''.format(dump_file_path=dump_file_path, username=username, opt_type=opt_type, cur_time=cur_time)
        logger.info(cmd)
        res3 = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
        logger.info(res3)

        end_time = datetime.datetime.now()
        logger.info("======数据库名：{}, 操作： {}======， 总耗时：{}s".format(username, opt_type, round(
            (end_time - start_time).total_seconds(), 2)))

    def _kill_session(self):
        # 数据库断连
        opt_type = 8
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)

                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        password = db_info.get("password")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        db_srv_name = db_info.get("db_srv_name")
                        # 系统用户执行
                        logger.info("======开始执行数据库断连，数据库名：{}======".format(db_info.get("db_name")))
                        self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                             sql_script=dos.script.format(username=username),
                                             username=username, db_srv_hosts=db_srv_hosts,
                                             db_srv_username=db_srv_username,
                                             db_srv_password=db_srv_password,
                                             db_srv_bash_profile=db_srv_bash_profile,
                                             opt_type=opt_type, db_srv_name=db_srv_name,
                                             password=password)
                        logger.info("======结束执行数据库断连，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def _drop_ddl(self):
        opt_type = 16
        logger.info("======drop_ddl======{}".format(self.dump_file_list))
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        password = db_info.get("password")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        db_srv_name = db_info.get("db_srv_name")
                        # 系统用户执行
                        logger.info("======开始执行数据库，数据库名：{}======".format(db_info.get("db_name")))
                        self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                             sql_script=dos.script.format(username=username),
                                             username=username, db_srv_hosts=db_srv_hosts,
                                             db_srv_username=db_srv_username,
                                             db_srv_password=db_srv_password,
                                             db_srv_bash_profile=db_srv_bash_profile,
                                             opt_type=opt_type, is_not_sys=True, db_srv_name=db_srv_name,
                                             password=password)
                        logger.info("======结束执行数据库，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("drop_ddl->>应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def _db_drop(self):
        # 数据库删除
        opt_type = 7
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)

                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        # 系统用户执行
                        logger.info("======开始执行数据库删除，数据库名：{}======".format(db_info.get("db_name")))
                        try:
                            self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                                  sql_script=dos.script.format(username),
                                                  username=username, db_srv_hosts=db_srv_hosts,
                                                  db_srv_username=db_srv_username,
                                                  db_srv_password=db_srv_password,
                                                  db_srv_bash_profile=db_srv_bash_profile,
                                                  opt_type=opt_type)
                        except Exception as e:
                            logger.error('数据库解锁异常，请检查数据库是否被锁，或数据库是否存在')
                            traceback.print_exc()
                            raise Exception('数据库解锁失败，检查数据库是否存在')
                            pass
                        DbMgtExecSqlFileHistory.delete().where(DbMgtExecSqlFileHistory.suite_code == self.suite_code,
                                                               DbMgtExecSqlFileHistory.db_name == db_info.get(
                                                                   "db_name")).execute()
                        logger.info("======结束执行数据库删除，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def _user_create(self):
        # 用户创建
        opt_type = 12
        logger.info("======user_create======{}".format(self.dump_file_list))
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        # 系统用户执行
                        logger.info("======开始执行用户创建，数据库名：{}======".format(db_info.get("db_name")))
                        table_space = db_info.get('db_table_space')
                        if not table_space:
                            raise Exception('数据库无表空间：{}'.format(db_info.get("db_name")))
                        self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                              sql_script=dos.script.format(username, table_space),
                                              username=username, db_srv_hosts=db_srv_hosts,
                                              db_srv_username=db_srv_username,
                                              db_srv_password=db_srv_password,
                                              db_srv_bash_profile=db_srv_bash_profile,
                                              opt_type=opt_type)
                        logger.info("======结束执行用户创建，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))
    def _db_restore(self):
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        logger.info("======开始执行数据库恢复，数据库名：{}======".format(db_info.get("db_name")))
                        start_time = datetime.datetime.now()

                        dump_source = dump_file.get('dump_source')
                        data_base_name = dump_file.get("source_db_name")
                        # if db_info.get("db_srv_type") == 'oracle':
                            # dump_create_config = DUMP.get('oracle_dump_create_config')
                            # parameter = json.loads(dump_create_config)
                            # table_space = parameter.get('table_space', 'BASIC_TBS')
                            # if self.opt_type == 1:
                            #     if dump_source == 1:
                            #         if not table_space:
                            #             raise Exception('数据库无表空间：{}'.format(db_info.get("db_name")))
                            #         table_space = 'remap_tablespace={}:{}'.format(table_space, db_info.get('db_table_space'))
                            #     else:
                            #         table_space = ''
                            # elif self.opt_type == 2:
                            #     if not table_space:
                            #         raise Exception('数据库无表空间：{}'.format(db_info.get("db_name")))
                            #     table_space = 'remap_tablespace={}:{}'.format(db_info.get('db_table_space'), table_space)
                            # elif self.opt_type == 3:
                            #     table_space = ''
                            # else:
                            #     raise Exception('DB恢复时，opt_type类型：{}'.format(self.opt_type))

                        if dump_source == 0:
                            data_base_name = 'DOCKER_IT29_{}'.format(dump_file.get("db_name").upper())
                        cmd = '''source {db_srv_bash_profile};impdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile={dump_file_name} remap_schema={schema_source}:{username} table_exists_action=replace transform=oid:n
                                 '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                            dump_file_path=db_info.get("data_dump_dir"),
                                            dump_file_name=dump_file.get("dump_file_name"),
                                            schema_source=data_base_name,
                                            username=username)
                        logger.info("远程恢复dump命令：{}".format(cmd))
                        res = exec_remote_cmd_by_username_password(db_srv_hosts, cmd, db_srv_username, db_srv_password)
                        logger.info("data_base_name:{},DB恢复执行结果：{}".format(data_base_name, res))
                        cur_time = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
                        his_data = {"create_user": 'be-scripts', "create_time": cur_time, "update_user": 'be-scripts',
                                    "update_time": cur_time, "dump_file_id": dump_file.get("id"), "stamp": 0,
                                    "module_name": self.module_name, "biz_iter_id": self.biz_iter_id,
                                    "suite_code": self.suite_code}
                        DbMgtDumpDeployHis.insert(his_data).execute()

                        end_time = datetime.datetime.now()
                        logger.info("======结束执行数据库恢复，数据库名：{}======， 恢复操作耗时：{}s".format(db_info.get("db_name"), round((end_time - start_time).total_seconds(), 2)))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))
            raise Exception('应用：{},业务分支：{}没找到相关dump文件！！！'.format(self.module_name, self.biz_iter_id))

    def _db_unlock(self):
        # 数据库解锁
        opt_type = 3
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)

                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        # 系统用户执行
                        logger.info("======开始执行数据库解锁，数据库名：{}======".format(db_info.get("db_name")))
                        self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                              sql_script=dos.script.format(username),
                                              username=username, db_srv_hosts=db_srv_hosts,
                                              db_srv_username=db_srv_username,
                                              db_srv_password=db_srv_password,
                                              db_srv_bash_profile=db_srv_bash_profile,
                                              opt_type=opt_type)
                        logger.info("======结束执行数据库解锁，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def _db_lock(self):
        # 数据库锁定
        opt_type = 4
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)

                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        # 系统用户执行
                        logger.info("======开始执行数据库锁定，数据库名：{}======".format(db_info.get("db_name")))
                        self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                              sql_script=dos.script.format(username),
                                              username=username, db_srv_hosts=db_srv_hosts,
                                              db_srv_username=db_srv_username,
                                              db_srv_password=db_srv_password,
                                              db_srv_bash_profile=db_srv_bash_profile,
                                              opt_type=opt_type)
                        logger.info("======结束执行数据库锁定，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def _db_set_user_and_password(self):
        # 设置密码
        opt_type = 6
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)

                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        # 系统用户执行
                        logger.info("======开始执行数据库用户密码，数据库名：{}======".format(db_info.get("db_name")))
                        self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                              sql_script=dos.script.format(username, db_info.get("password")),
                                              username=username, db_srv_hosts=db_srv_hosts,
                                              db_srv_username=db_srv_username,
                                              db_srv_password=db_srv_password,
                                              db_srv_bash_profile=db_srv_bash_profile,
                                              opt_type=opt_type)
                        logger.info("======结束执行数据库用户密码，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def _db_update_synonym(self):
        # 同义词更新
        opt_type = 1
        if self.dump_file_list:
            for dump_file in self.dump_file_list:
                dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)

                for db_info in self.db_info_list:
                    if db_info.get("db_name") == dump_file.get("db_name"):
                        username = db_info.get("username")
                        db_srv_hosts = db_info.get("db_srv_hosts")
                        db_srv_username = db_info.get("db_srv_username")
                        db_srv_password = db_info.get("db_srv_password")
                        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
                        db_group_name = db_info.get("db_group_name")
                        db_deploy_info = get_db_deploy_info(db_group_name)
                        if db_deploy_info and db_deploy_info[0].get("need_synonym"):
                            synonym_opt_content = db_deploy_info[0].get("synonym_opt_content")
                            sql_script = dos.script.format(synonym_opt_content)

                            logger.info(
                                "======开始执行数据库同义词更新，数据库名：{}======".format(db_info.get("db_name")))
                            # 非系统用户执行
                            self._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                                                  sql_script=sql_script,
                                                  username=username, db_srv_hosts=db_srv_hosts,
                                                  db_srv_username=db_srv_username,
                                                  db_srv_password=db_srv_password,
                                                  db_srv_bash_profile=db_srv_bash_profile,
                                                  opt_type=opt_type, is_not_sys=True)
                            logger.info(
                                "======结束执行数据库同义词更新，数据库名：{}======".format(db_info.get("db_name")))
        else:
            logger.info("应用：{},业务分支：{}没找到相关dump文件！！！".format(self.module_name, self.biz_iter_id))

    def catch_oracle_sys_error(self, res2, username, db_srv_hosts, db_srv_name):
        if "ORA-01940" in res2:
            get_user_connect_info(DbCdcParam(host=db_srv_hosts,
                                             port=1521,
                                             user=DbCdcEnum.ORACLE_CDC_USER.value,
                                             password=DbCdcEnum.ORACLE_CDC_PWD.value,
                                             service_name=db_srv_name
                                             ), username)
            get_unlock_user(DbCdcParam(host=db_srv_hosts,
                                       port=1521,
                                       user=DbCdcEnum.ORACLE_CDC_USER.value,
                                       password=DbCdcEnum.ORACLE_CDC_PWD.value,
                                       service_name=db_srv_name
                                       ), username)
            raise Exception("ORA-01940======数据库用户{}正在连接中，初始化失败======".format(username))


#


if __name__ == "__main__":
    sql = """SELECT MACHINE, PROGRAM FROM v$session WHERE username = '{}'""".format('DOCKER_IT54_CUST')
    logger.info(sql)
    with DBConnectionManagerForSqlalchemyOracle("***************", "1521", "ORACDC",
                                                "ORACDC", "hbcrm") as db:
        value = db.session.execute(text(sql)).fetchall()
        for i in value:
            logger.info("user_connect_info:{},{}".format(i[0], i[1]))
