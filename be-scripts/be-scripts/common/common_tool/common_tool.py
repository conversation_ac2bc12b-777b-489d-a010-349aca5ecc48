import re

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from db_mgt.creat_sql_migrate.models import DbMgtArcheryAuthInfo
from settings import logger as log
from settings import TEST_PUBLISH_AIO as TPA
from settings import PIPELINE_TEST_PUBLISH as PTP, PRODUCT_STORE_SQL_URL
import subprocess
import os


def exec_local_cmd(cmd, timeout=None):
    """本地执行命令 zt@2020-09-08"""
    log.info("本地执行命令：{}".format(re.sub(r'^sshpass -p \S*', "sshpass -p ******", cmd)))
    try:
        if not timeout:
            timeout = int(PTP['exec_cmd_timeout'])
        completed_process_obj = subprocess.run(cmd, shell=True,
                                               timeout=timeout,
                                               capture_output=True, check=True)
        log.info("本地执行结果：{}".format(bytes.decode(completed_process_obj.stdout)))
    except subprocess.CalledProcessError as err:
        msg = "本地执行命令出错：{}".format(bytes.decode(err.stderr))
        log.error(msg)
        raise msg
    return completed_process_obj


def clean_dir(target_dir):
    '''
    清理目录
    '''
    if len(target_dir) > 3 and os.path.exists(target_dir):
        exec_local_cmd('rm -rf {}/*'.format(target_dir))
    else:
        log.error('删除的目录长度小于3,或目录不存在，请人工check{}'.format(target_dir))


def get_user_archery_access_token(archery_id, username):
    with DBConnectionManagerForSqlalchemy() as db:
        obj = db.session.query(DbMgtArcheryAuthInfo).filter(DbMgtArcheryAuthInfo.archery_id == archery_id,
                                                            DbMgtArcheryAuthInfo.username == username).first()
        if obj:
            access_token = obj.token
            return access_token
        else:
            raise ValueError("无archery登录信息，请尝试重新登录Devops平台再次构建解决！地址：http://appdeploy.intelnal.howbuy.com/")
