__author__ = 'howbuy'

import ssl
import json
import urllib
from urllib import parse
from urllib import request
from settings import logger


class SaltAPI(object):
    def __init__(self, url, username, password, **kwargs):
        self.timeout = kwargs.get("timeout", 300)
        self.__url = url.rstrip('/')
        self.header = kwargs.get("header", ["Content-Type:application/json"])
        self.__token_id = self.salt_login(username, password)
        self.response = None

    def post(self, prefix="/", token=None, **data):
        # url拼接
        url = self.__url + prefix
        # 实例化
        data = parse.urlencode(data).encode()
        if token:
            headers = {'X-Auth-Token': token}
            req = request.Request(url, headers=headers, data=data)
        else:
            req = request.Request(url, data=data)
        context = ssl._create_unverified_context()
        res_obj = urllib.request.urlopen(req, context=context)
        if res_obj.code == 200:
            return json.loads(res_obj.read())
        else:
            self.response = "response code %s".format(res_obj.code)
            return self.response

    def salt_login(self, username, password):
        obj = {'eauth': "pam", 'username': "{}".format(username), 'password': "{}".format(password)}
        result = self.post(prefix="/login", **obj)
        if result:
            try:
                self.__token_id = result['return'][0]['token']
            except KeyError:
                raise KeyError
        return self.__token_id

    def postRequest(self, obj, prefix='/'):
        url = self.__url + prefix
        headers = {'X-Auth-Token': self.__token_id}
        req = request.Request(url, obj, headers)
        # This restores the same behavior as before.
        context = ssl._create_unverified_context()
        opener = urllib.request.urlopen(req, context=context)
        content = json.loads(opener.read())
        return content

    def asyncMasterToMinion(self, tgt, fun, arg):

        """
        异步执行，当target为部分minion时，Master操作Minion；
        :param tgt: 目标服务器ID组成的字符串；
        :param fun: 使用的salt模块，如state.sls, cmd.run
        :param arg: 传入的命令或sls文件
        :return: jid字符串
        """

        if tgt == '*':
            params = [('client', 'local_async'), ('tgt', tgt), ('fun', fun)]
            if isinstance(arg, list):
                for x in arg:
                    params.append(('arg', x))
            else:
                params.append(('arg', arg))
        else:
            params = [('client', 'local_async'), ('tgt', tgt), ('fun', fun), ('expr_form', 'list')]
            if isinstance(arg, list):
                for x in arg:
                    params.append(('arg', x))
            else:
                params.append(('arg', arg))
        logger.info(params)
        obj = parse.urlencode(params).encode(encoding='UTF8')
        content = self.postRequest(obj)
        logger.info(content)
        jid = content['return'][0]['jid']
        return jid

    def masterToMinionContent(self, tgt, fun, arg):
        """
            Master控制Minion，返回的结果是内容，不是jid；
            目标参数tgt是一个如下格式的字符串：'*' 或 'zhaogb-201, zhaogb-202, zhaogb-203, ...'
        """
        if tgt == '*':
            params = {'client': 'local', 'tgt': tgt, 'fun': fun, 'arg': arg}
        else:
            params = {'client': 'local', 'tgt': tgt, 'fun': fun, 'arg': arg, 'expr_form': 'list'}
        obj = parse.urlencode(params).encode(encoding='UTF8')
        content = self.postRequest(obj)
        result = content['return'][0]
        return result

    def allMinionKeys(self):
        """
        获取所有的minion_key
        """
        token = 'X-Auth-Token:%s' % self.__token_id
        obj = {'client': 'wheel', 'fun': 'key.list_all'}
        content = self.post(token=token, **obj)
        # 取出认证已经通过的
        minions = content['return'][0]['data']['return']['minions']
        # print('已认证',minions)
        # 取出未通过认证的
        minions_pre = content['return'][0]['data']['return']['minions_pre']
        # print('未认证',minions_pre)
        minions_rej = content['return'][0]['data']['return']['minions_rejected']
        self.minion_list = []
        self.minions_pre = []
        self.minions_rej = []
        for minion in minions:
            self.minion_list.append((minion, self.get_ip(minion)))
        for minion in minions_pre:
            self.minions_pre.append((minion, None))
        for minion in minions_rej:
            self.minions_rej.append((minion, None))
        # print (minion_list)
        return minions, minions_pre, minions_rej

    def get_ip(self, hostname):
        token = 'X-Auth-Token:%s' % self.__token_id
        obj = {'client': 'local', 'tgt': hostname, 'fun': 'network.ip_addrs'}
        content = self.post(token=token, **obj)
        print(content)
        try:
            return content['return'][0][hostname][0]
        except Exception as e:
            print(e)

    def actionKyes(self, keystrings, action):
        """
        对Minion keys 进行指定处理；
        :param keystrings: 将要处理的minion id字符串；
        :param action: 将要进行的处理，如接受、拒绝、删除；
        :return:
        {"return": [{"tag": "salt/wheel/20160322171740805129", "data": {"jid": "20160322171740805129", "return": {}, "success": true, "_stamp": "2016-03-22T09:17:40.899757", "tag": "salt/wheel/20160322171740805129", "user": "zhaogb", "fun": "wheel.key.delete"}}]}
        """
        token = 'X-Auth-Token:%s' % self.__token_id
        func = 'key.' + action
        obj = {'client': 'wheel', 'fun': func, 'match': keystrings}
        content = self.post(token=token, **obj)
        ret = content['return'][0]['data']['success']
        return ret

    def acceptKeys(self, node_name):

        # 如果你想认证某个主机 那么调用此方法

        token = 'X-Auth-Token:%s' % self.__token_id
        obj = {'client': 'wheel', 'fun': 'key.accept', 'match': node_name}
        content = self.post(token=token, **obj)
        ret = content['return'][0]['data']['success']
        return ret

    def deleteKeys(self, node_name):
        """
        删除Minion keys；
        :param node_name:
        :return:
        """
        obj = {'client': 'wheel', 'fun': 'key.delete', 'match': node_name}
        token = 'X-Auth-Token:%s' % self.__token_id
        content = self.post(token=token, **obj)

        ret = content['return'][0]['data']['success']
        return ret
