import jenkins

from settings import logger, JENKINS_INFO


class JenkinsApi:
    def __init__(self):
        self.server = jen<PERSON><PERSON>(JENKINS_INFO["URL"], username=JENKINS_INFO["USER"], password=JENKINS_INFO["PASSWORD"])

    @classmethod
    def instance(cls,):
        if not hasattr(Jenkins<PERSON><PERSON>, "_instance"):
            JenkinsApi._instance = JenkinsApi()
        return JenkinsApi._instance

    def create_job(self):
        pass

    def delete_job(self, job_name):
        self.server.delete_job(job_name)

