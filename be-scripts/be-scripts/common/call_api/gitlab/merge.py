import datetime
import random

import gitlab
import os
import sys
import time


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from common.routers.router import Result
from job.iter_mgt.archive_step_enum import ArchiveStepEnum
from job.iter_mgt.models import IterMgtArchiveLog
from concurrent.futures import ThreadPoolExecutor, as_completed
from settings import logger, GITLAB_HTTP, GITLAB_TOKEN, GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN
from enum import Enum, unique


@unique
class MergeStatus(Enum):
    CAN_BE_MERGED = "can_be_merged"
    CANNOT_BE_MERGED = "cannot_be_merged"


class Merger:
    def __init__(self, br_name, git_repos_list, gitlab_http=GITLAB_HTTP, gitlab_token=GITLAB_TOKEN):
        self.gl = gitlab.Gitlab(gitlab_http, gitlab_token)
        self.git_repos_list = git_repos_list
        self.br_name = br_name

    def merge_trunk(self):
        """根据仓库列表回合分支到主干"""
        id_list = []
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in self.git_repos_list:
                id_list.append(p.id)
        merge_status_dic = {}
        for repos_id in id_list:
            repos = self.gl.projects.get(repos_id)
            mr = repos.mergerequests.create(
                {'source_branch': self.br_name, 'target_branch': "master", 'title': '快速合并主干', })
            mr.description = '将{}合并到主干'.format(self.br_name)
            mr.save()
            if mr.changes_count is None:
                merge_status_dic[repos.name] = "没有合并内容"
            else:
                if mr.merge_status == "can_be_merged":
                    mr.merge()
                    logger.info("{}库{}".format(repos.name, "合并完成"))
                    merge_status_dic[repos.name] = "合并完成"
                    # 此处不需要删除分支 后续阶段删除
                    # repos.branches.delete(self.br_name)
                    # self.create_tag(repos)
                else:
                    logger.info("{}库{}".format(repos.name, "不可合并"))
                    merge_status_dic[repos.name] = "不可合并"
            mr.delete()
        return merge_status_dic

    def merge_master_to_branch(self, is_archive=True):
        """合并主干到分支"""
        logger.info("开始合并主干到分支")
        id_list = []
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in self.git_repos_list:
                id_list.append(p.id)
        merge_status_dic = {}
        for repos_id in id_list:
            repos = self.gl.projects.get(repos_id)
            mr = repos.mergerequests.create(
                {'source_branch': "master", 'target_branch': self.br_name, 'title': '快速合并主干', })
            mr.description = '将主干合并到{}'.format(self.br_name)
            mr.save()
            if mr.changes_count is None:
                merge_status_dic[repos.name] = "没有合并内容"
            else:
                merge_timeout = 120
                start_time = 0
                new_merge_status = None
                while start_time < merge_timeout:
                    time.sleep(1)
                    for new_mr in repos.mergerequests.list():
                        if new_mr.id == mr.id:
                            logger.info(new_mr.__dict__)
                            new_merge_status = new_mr.merge_status
                            # 如果状态是checking或未确定状态，尝试刷新MR对象
                            if new_merge_status not in [MergeStatus.CAN_BE_MERGED.value, MergeStatus.CANNOT_BE_MERGED.value]:
                                try:
                                    # 尝试刷新MR对象 - 使用get方法重新获取MR对象而不是直接refresh
                                    refreshed_mr = repos.mergerequests.get(mr.iid)
                                    logger.info("刷新MR对象，当前状态: {}".format(refreshed_mr.merge_status))
                                    # 更新状态
                                    new_merge_status = refreshed_mr.merge_status
                                except Exception as e:
                                    logger.warning("刷新MR对象失败: {}".format(str(e)))
                                    # 如果重新获取失败，保持当前状态，继续等待

                    if new_merge_status in [MergeStatus.CAN_BE_MERGED.value, MergeStatus.CANNOT_BE_MERGED.value]:
                        break
                    logger.info(new_merge_status)
                    start_time = start_time + 1
                # 使用最新获取的状态，而不是原始MR对象的状态
                if new_merge_status == MergeStatus.CAN_BE_MERGED.value:
                    mr.merge()
                    logger.info("{}库{}".format(repos.name, "合并完成"))
                    merge_status_dic[repos.name] = "合并完成"
                else:
                    logger.info("{}库{}".format(repos.name, "不可合并"))
                    merge_status_dic[repos.name] = "不可合并"

            mr.delete()
        if is_archive:
            imal = IterMgtArchiveLog(iteration_id=self.br_name,
                                     step_order=ArchiveStepEnum.CODE_MERGE.value[0] + "-" + str(
                                         random.randint(1, 9999)),
                                     step_name=ArchiveStepEnum.CODE_MERGE.value[1],
                                     step_desc=ArchiveStepEnum.CODE_MERGE.value[2],
                                     step_status="success", request_params={"git_repos_list": self.git_repos_list},
                                     response_result=merge_status_dic, opt_user='howbuyscm',
                                     opt_time=datetime.datetime.now())
            imal.save()
        return merge_status_dic

    def merge_trunk_full_process(self, is_create_tag_and_del_branch=True, iteration_id=None):
        """根据仓库列表回合分支到主干并删除当前分支和创建主干对应tag，
        可传参False关闭删除分支和创建主干tag的动作"""
        logger.info("开始合并分支到主干")

        merge_status_dic = {}
        # todo 可以根据git_repo查询，而不需要查询整个项目列表，前提组名不允许重复
        # for repos in self.gl.projects.list(all=True, all_list=False):
        for repos_path in self.git_repos_list:
            repos = self.gl.projects.get(repos_path)
            mr = None
            try:
                mr = repos.mergerequests.create(
                    {'source_branch': self.br_name, 'target_branch': "master", 'title': '快速合并主干', })
                mr.description = '将{}合并到主干'.format(self.br_name)
                mr.save()
                logger.info("{}库{},文件改变数量{}".format(repos.name, mr.merge_status, mr.changes_count))
                logger.info("{}".format(mr.__dict__))
                """分支不存在时，changes_count = None，返回到消息仍然提示为 代码与主干一致，没有合并内容"""
                if mr.changes_count is None:
                    merge_status_dic[repos.name] = "代码与主干一致，没有合并内容"
                    if is_create_tag_and_del_branch:
                        self.__create_tag_and_del_branch(repos)
                else:
                    # 设置两分钟的超时时间，因为新的gitlab 先返回 checking状态，需要 轮询去拿结果 by帅 20210713
                    merge_timeout = 120
                    start_time = 0
                    new_merge_status = None
                    while start_time < merge_timeout:
                        time.sleep(1)
                        for new_mr in repos.mergerequests.list():
                            if new_mr.id == mr.id:
                                new_merge_status = new_mr.merge_status
                                # 如果状态是checking或未确定状态，尝试刷新MR对象
                                if new_merge_status not in [MergeStatus.CAN_BE_MERGED.value, MergeStatus.CANNOT_BE_MERGED.value]:
                                    try:
                                        # 尝试刷新MR对象 - 使用get方法重新获取MR对象而不是直接refresh
                                        refreshed_mr = repos.mergerequests.get(mr.iid)
                                        logger.info("刷新MR对象，当前状态: {}".format(refreshed_mr.merge_status))
                                        # 更新状态
                                        new_merge_status = refreshed_mr.merge_status
                                    except Exception as e:
                                        logger.warning("刷新MR对象失败: {}".format(str(e)))
                                        # 如果重新获取失败，保持当前状态，继续等待
                        if new_merge_status in [MergeStatus.CAN_BE_MERGED.value, MergeStatus.CANNOT_BE_MERGED.value]:
                            break
                        logger.info(new_merge_status)
                        start_time = start_time + 1
                    logger.info(new_merge_status)
                    # 使用最新获取的状态，而不是原始MR对象的状态
                    if new_merge_status == MergeStatus.CAN_BE_MERGED.value:
                        logger.info("开始回合主干")
                        # todo 是否可以校验回合后的值，根据之前其它git api的判断，此处会抛异常，进不去下面的逻辑，若出现问题到时候排查
                        mr.merge()
                        logger.info("{}库{}".format(repos.name, "合并完成"))
                        merge_status_dic[repos.name] = "合并完成"
                        if is_create_tag_and_del_branch:
                            self.__create_tag_and_del_branch(repos)
                    else:
                        logger.info("{}库{}".format(repos.name, "不可合并到主干,可能存在合并冲突，需人工解决"))
                        merge_status_dic[repos.name] = "{}库分支{}{}".format(repos.name, self.br_name,
                                                                             "不可合并到主干,可能存在合并冲突，需人工解决")
            except Exception as e:
                merge_status_dic[repos.name] = "代码归档步骤异常" + str(e)
            finally:
                mr.delete()
        if len(merge_status_dic) == 0:
            merge_status_dic["message"] = "未能查询代码到对应组与仓库信息"
        imal = IterMgtArchiveLog(iteration_id=iteration_id,
                                 step_order=ArchiveStepEnum.CODE_MERGE.value[0] + "-" + str(
                                     random.randint(1, 9999)),
                                 step_name=ArchiveStepEnum.CODE_MERGE.value[1],
                                 step_desc=ArchiveStepEnum.CODE_MERGE.value[2],
                                 step_status="success", request_params={"git_repos_list": self.git_repos_list},
                                 response_result=merge_status_dic, opt_user='howbuyscm',
                                 opt_time=datetime.datetime.now())
        imal.save()
        return merge_status_dic

    def pre_merge_trunk(self):
        """预归档的检测"""
        merge_status_dic = {}

        for repos_path in self.git_repos_list:
            repos = self.gl.projects.get(repos_path)
            try:
                mrs = repos.mergerequests.list()
                for h in mrs:
                    if h.target_branch == 'master':
                        h.delete()
                mr = repos.mergerequests.create(
                    {'source_branch': self.br_name, 'target_branch': "master", 'title': '快速合并主干', })
                mr.description = '将{}合并到主干'.format(self.br_name)
                mr.save()
                logger.info("{}库{},文件改变数量{}".format(repos.name, mr.merge_status, mr.changes_count))
                if mr.changes_count is None:
                    merge_status_dic[repos.name] = "代码与主干一致，没有合并内容"
                else:
                    merge_timeout = 120
                    start_time = 0
                    new_merge_status = None
                    while start_time < merge_timeout:
                        time.sleep(1)
                        for new_mr in repos.mergerequests.list():
                            if new_mr.id == mr.id:
                                new_merge_status = new_mr.merge_status
                                # 如果状态是checking或未确定状态，尝试刷新MR对象
                                if new_merge_status not in [MergeStatus.CAN_BE_MERGED.value, MergeStatus.CANNOT_BE_MERGED.value]:
                                    try:
                                        # 尝试刷新MR对象 - 使用get方法重新获取MR对象而不是直接refresh
                                        refreshed_mr = repos.mergerequests.get(mr.iid)
                                        logger.info("刷新MR对象，当前状态: {}".format(refreshed_mr.merge_status))
                                        # 更新状态
                                        new_merge_status = refreshed_mr.merge_status
                                    except Exception as e:
                                        logger.warning("刷新MR对象失败: {}".format(str(e)))
                                        # 如果重新获取失败，保持当前状态，继续等待
                        if new_merge_status in [MergeStatus.CAN_BE_MERGED.value, MergeStatus.CANNOT_BE_MERGED.value]:
                            break
                        logger.info(new_merge_status)
                        start_time = start_time + 1
                    # 使用最新获取的状态，而不是原始MR对象的状态
                    if new_merge_status == MergeStatus.CAN_BE_MERGED.value:
                        merge_status_dic[repos.name] = "{}库分支{}{}".format(repos.name, self.br_name,
                                                                             "可正常合并")
                    else:
                        merge_status_dic[repos.name] = "{}库分支{}{}".format(repos.name, self.br_name,
                                                                             "不可合并到主干,可能存在合并冲突，请先解决冲突后再尝试")
                        return Result.failed_dict(merge_status_dic)
            except Exception as e:
                merge_status_dic[repos.name] = "代码归档步骤异常" + str(e)
            finally:
                mr.delete()
        return Result.success_dict(merge_status_dic)

    def __create_tag_and_del_branch(self, repos):
        # 删除之前的tags中包含当前tag号的
        logger.info("开始创建当前分支{}主干tag".format(self.br_name))
        try:
            # 先检查tag是否存在，再删除
            tag_name = "tag_" + self.br_name
            tags = repos.tags.list()
            tag_exists = any(tag.name == tag_name for tag in tags)
            
            if tag_exists:
                repos.tags.delete(tag_name)
                logger.info("已删除旧的tag: {}".format(tag_name))
            else:
                logger.info("没有找到需要删除的tag: {}".format(tag_name))
        except Exception as e:
            # 删除tag异常告警，不阻塞
            logger.warning("删除tag异常: {} - {}".format(tag_name, str(e)))

        try:
            # 创建主干当前分支命名的tag
            tag_name = "tag_" + self.br_name
            tag = repos.tags.create({'tag_name': tag_name, 'ref': 'master'})
            logger.info("{}库成功创建tag: {}".format(repos.name, tag_name))
            
            # 设置tags 说明，优先使用set_release_description，失败则尝试使用releases API
            release_description = '分支{}上线完成，打tag'.format(self.br_name)
            release_created = False
            
            # 方法1：使用tag.set_release_description
            try:
                tag.set_release_description(release_description)
                logger.info("使用set_release_description成功设置release描述")
                release_created = True
            except gitlab.exceptions.GitlabHttpError as e:
                error_msg = str(e)
                if "404" in error_msg:
                    logger.warning("设置tag描述失败(404)，尝试使用releases API替代: {}".format(error_msg))
                    # 继续尝试方法2
                else:
                    logger.warning("设置tag描述失败: {}".format(error_msg))
                    # 继续尝试方法2
            except Exception as e:
                logger.warning("设置tag描述时发生未知异常: {}".format(str(e)))
                # 继续尝试方法2
            
            # 方法2：如果方法1失败，使用releases API
            if not release_created:
                try:
                    # 尝试使用releases API作为替代方案
                    release_data = {
                        'name': 'Release ' + tag_name,
                        'tag_name': tag_name,
                        'description': release_description
                    }
                    repos.releases.create(release_data)
                    logger.info("使用releases API成功创建release描述")
                    release_created = True
                except gitlab.exceptions.GitlabHttpError as e:
                    error_msg = str(e)
                    if "404" in error_msg:
                        logger.error("使用releases API创建release描述失败(404): {}".format(error_msg))
                        logger.error("可能原因：1. GitLab版本不兼容 2. 权限不足 3. 项目配置问题")
                    elif "403" in error_msg:
                        logger.error("使用releases API创建release描述失败(403): 权限不足")
                        logger.error("详细错误信息: {}".format(error_msg))
                    elif "401" in error_msg:
                        logger.error("使用releases API创建release描述失败(401): 认证失败")
                        logger.error("详细错误信息: {}".format(error_msg))
                    else:
                        logger.error("使用releases API创建release描述失败: {}".format(error_msg))
                except Exception as e:
                    logger.error("使用releases API创建release描述时发生未知异常: {}".format(str(e)))
            
            logger.info("{}库{}{}".format(repos.name, self.br_name, "主干tag创建完成"))
            
            # 回合成功后删除分支前先检查分支是否存在
            logger.info("开始检查并删除当前分支{}".format(self.br_name))
            try:
                # 直接尝试获取指定分支，如果分支不存在会抛出异常
                branch = repos.branches.get(self.br_name)
                # 如果能获取到分支，说明分支存在，执行删除操作
                repos.branches.delete(self.br_name)
                logger.info("{}库{}分支{}".format(repos.name, self.br_name, "删除完成"))
            except gitlab.exceptions.GitlabGetError as e:
                if "404" in str(e):
                    logger.warning("{}库{}分支{}".format(repos.name, self.br_name, "不存在，无法删除"))
                else:
                    logger.error("检查分支时发生错误: {}".format(str(e)))
                    raise
        except Exception as e:
            error_msg = str(e)
            logger.error("创建tag或删除分支失败: {}".format(error_msg))
            # 记录更详细的错误信息，帮助排查问题
            if "404" in error_msg:
                if "Tag" in error_msg or "tag" in error_msg:
                    logger.error("创建tag失败(404)，可能是权限不足或引用的master分支不存在")
                    logger.error("详细错误信息: {}".format(error_msg))
                elif "Branch" in error_msg or "branch" in error_msg:
                    logger.error("删除分支失败(404)，分支可能已被删除或不存在")
                    logger.error("详细错误信息: {}".format(error_msg))
                elif "Release" in error_msg or "release" in error_msg:
                    logger.error("创建release失败(404)，可能是API版本不兼容或权限不足")
                    logger.error("详细错误信息: {}".format(error_msg))
                else:
                    logger.error("未知的404错误，详细信息: {}".format(error_msg))
                    logger.error("请检查GitLab API版本兼容性和项目权限设置")
            elif "403" in error_msg:
                logger.error("权限不足(403)，无法执行操作，请检查GitLab Token权限")
                logger.error("详细错误信息: {}".format(error_msg))
            elif "401" in error_msg:
                logger.error("认证失败(401)，请检查GitLab Token是否有效")
                logger.error("详细错误信息: {}".format(error_msg))
            else:
                logger.error("未分类的错误，详细信息: {}".format(error_msg))
                logger.error("请检查GitLab服务器状态和网络连接")

    def delete_branch(self):
        id_list = []
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in self.git_repos_list:
                id_list.append(p.id)
        for repos_id in id_list:
            repos = self.gl.projects.get(repos_id)
            # 删除分支
            repos.branches.delete(self.br_name)

    def create_branch(self, src_branch="master"):
        id_list = []
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in self.git_repos_list:
                id_list.append(p.id)
        for repos_id in id_list:
            repos = self.gl.projects.get(repos_id)
            # 创建分支
            repos.branches.create({'branch': self.br_name, 'ref': src_branch})

    def create_tag(self):
        id_list = []
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in self.git_repos_list:
                id_list.append(p.id)
        for repos_id in id_list:
            repos = self.gl.projects.get(repos_id)
            try:
                repos.tags.delete("tag_" + self.br_name)
            except Exception as e:
                logger.warning(e)
            # 创建一个tag
            tag = repos.tags.create({'tag_name': "tag_" + self.br_name, 'ref': 'master'})
            # 设置tags 说明:
            tag.set_release_description('分支{}上线完成，打tag'.format(self.br_name))

    # def merge_status(self, source_branch="master"):
    #     id_list = []
    #     # 开始回合===获取gitlab所有的项目对象列表
    #     for p in self.gl.projects.list(all=True, all_list=False):
    #         # if p.name in self.git_repos_list:
    #         # 找到当前需要回合的项目的objectId
    #         if p.path_with_namespace in self.git_repos_list:
    #             id_list.append(p.id)
    #     merge_status_dic = {}
    #     logger.info(id_list)
    #     for repos_id in id_list:
    #         repos = self.gl.projects.get(repos_id)
    #         mrs = repos.mergerequests.list()
    #         for h in mrs:
    #             if h.source_branch == 'master':
    #                 h.delete()
    #         mr = repos.mergerequests.create({'source_branch': source_branch,
    #                                          'target_branch': self.br_name, 'title': '主干回合分支', })
    #         mr.description = 'New description'
    #         mr.save()
    #         logger.info("{}库{},文件改变数量{}".format(repos.name, mr.merge_status, mr.changes_count))
    #         merge_status_dic[repos.name] = mr.changes_count
    #         mr.delete()
    #     return merge_status_dic
    def process_repository(self, repos):
        try:
            # 删除所有源分支为 'master' 的合并请求
            mrs = repos.mergerequests.list()
            for h in mrs:
                if h.source_branch == 'master':
                    h.delete()

            # 创建新的合并请求
            mr = repos.mergerequests.create({
                'source_branch': 'master',  # 根据需要修改 source_branch
                'target_branch': self.br_name,
                'title': '主干回合分支',
            })
            mr.description = 'New description'
            mr.save()

            logger.info(f"{repos.name} 库, 文件改变数量 {mr.changes_count}")

            # 返回结果
            return repos.name, mr.changes_count

        except Exception as e:
            logger.error(f"处理仓库 {repos.name} 时出错: {e}")
            return repos.name, None
        finally:
            # 确保合并请求被删除，即使出现异常
            try:
                mr.delete()
            except:
                pass

    def merge_status(self, max_workers=10):
        id_list = []
        # 获取所有需要处理的项目 ID
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in self.git_repos_list:
                id_list.append(p.id)

        logger.info(f"需要处理的项目 ID 列表: {id_list}")

        merge_status_dic = {}
        repos_list = [self.gl.projects.get(repo_id) for repo_id in id_list]

        # 使用 ThreadPoolExecutor 进行多线程处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_repo = {executor.submit(self.process_repository, repos): repos for repos in repos_list}

            for future in as_completed(future_to_repo):
                repo_name, changes_count = future.result()
                if changes_count is not None:
                    merge_status_dic[repo_name] = changes_count

        return merge_status_dic

    # 暂时未用
    def merge_status2(self, source_branch="master"):
        merge_status_dic = {}
        # 开始回合===获取gitlab所有的项目对象列表  todo 是否可以根据repo直接查询到对应的gitlab项目块
        for repos in self.gl.projects.list(all=True, all_list=False):
            # 找到当前需要回合的项目的objectId
            if repos.path_with_namespace in self.git_repos_list:
                # id_list.append(p.id)
                mrs = repos.mergerequests.list()
                # 清空目前还有merger请求的列表
                for h in mrs:
                    if h.source_branch == 'master':
                        h.delete()
                mr = repos.mergerequests.create({'source_branch': source_branch,
                                                 'target_branch': self.br_name, 'title': '主干回合分支', })
                mr.description = 'New description'
                mr.save()
                logger.info("{}库{},文件改变数量{}".format(repos.name, mr.merge_status, mr.changes_count))
                merge_status_dic[repos.name] = mr.changes_count
                mr.delete()

        return merge_status_dic

    def __lib_create_tag_and_del_branch(self, repos):
        # 删除之前的tags中包含当前tag号的
        logger.info("开始创建当前分支{}主干tag".format(self.br_name))
        try:
            repos.tags.delete("tag_" + self.br_name)
        except Exception as e:
            # 删除tag异常告警，不阻塞
            logger.warning(str(e))
        # 创建主干当前分支命名的tag
        tag = repos.tags.create({'tag_name': "tag_" + self.br_name, 'ref': self.br_name})
        # 设置tags 说明:
        # tag.set_release_description('分支{}上线完成，打tag'.format(self.br_name))制品创建tag打log404暂时解决不了
        logger.info("{}库{}{}".format(repos.name, self.br_name, "主干tag创建完成"))
        # 回合成功后删除分支
        logger.info("开始删除当前分支{}".format(self.br_name))
        repos.branches.delete(self.br_name)
        logger.info("{}库{}{}".format(repos.name, self.br_name, "删除完成"))

    def merge_lib_repo_to_trunk(self):
        for repos_path in self.git_repos_list:
            repos = self.gl.projects.get(repos_path)
            self.__lib_create_tag_and_del_branch(repos)


if __name__ == "__main__":
    # me = Merger("patest-092501", ["ztst/howbuy-wechat-message"])
    # repos = me.gl.projects.get("ztst/howbuy-wechat-message")
    #
    # mrs = repos.mergerequests.list()
    # for h in mrs:
    #     if h.source_branch == "master":
    #         h.delete()
    #     print(h)

    # mr = repos.mergerequests.create(
    #     {'source_branch': "master", 'target_branch': "patest-060803", 'title': '快速合并主干'})
    # mr.description = '将{}合并到主干'.format("patest051204")
    # mr.save()
    # logger.info("{}库{},文件改变数量{}".format(repos.name, mr.merge_status, mr.changes_count))
    # mr.delete()
    # me = Merger("2.71.6", ['scm/be-scripts'])
    # me.pre_merge_trunk()
    gl = gitlab.Gitlab(GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN)
    repos = gl.projects.get(946)

    merge = Merger("ztst_patest2025052202", ["ES-SCRIPT/es-script"],
                   gitlab_http=GITLAB_LIB_HTTP,
                   gitlab_token=GITLAB_LIB_TOKEN)
    merge._create_tag_and_del_branch(repos)