import gitlab
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from common.script_exception.gitlab_exception import ReposNonExistError
from settings import logger, GITLAB_HTTP, GITLAB_TOKEN


class GitLabApi:
    def __init__(self, gitlab_http=GITLAB_HTTP, gitlab_token=GITLAB_TOKEN):
        self.gl = gitlab.Gitlab(gitlab_http, gitlab_token)

    def get_git_repos_last_version(self, br_name, git_repos_list):
        repos_info = {}
        for p in self.gl.projects.list(all=True, all_list=False):
            if p.path_with_namespace in git_repos_list:
                repos_info[p.path_with_namespace] = p.id
        for repos_path in git_repos_list:
            if repos_path not in repos_info:
                logger.error('%s : Can not find the app in gitlab.' % repos_path)
                raise ReposNonExistError(repos_path)
        commit_info = {}
        #logger.info(repos_info)
        #logger.info(br_name)
        for reps_path in repos_info:
            app_repo = self.gl.projects.get(repos_info[reps_path])
            for b in app_repo.branches.list(all=True, all_list=False):
                #logger.info(b.name)
                if b.name == br_name:
                    commit_info[reps_path] = b.commit['id']
        return commit_info

    def get_one_git_repos_last_version(self, br_name, git_repos):
        commit_info = {}
        app_repo = self.gl.projects.get(git_repos)
        branch_info = app_repo.branches.get(br_name)
        if branch_info:
            commit_info[git_repos] = branch_info.commit['id']
        return commit_info

if __name__ == "__main__":
    gitlab_api = GitLabApi('http://gitlab-lib.howbuy.pa', '********************')

    print(gitlab_api.get_one_git_repos_last_version('flyway-test', 'lib_repo/zeus-service'))