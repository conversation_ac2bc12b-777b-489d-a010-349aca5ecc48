from settings import logger
from settings import MIRROR_FACTORY
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from common.k8s.k8s_info_handler import K8sInfoHandler, K8sSvcInfoHandler
from test_mgt.test_mgt_ser import get_k8s_server


class K8sOperation:
    __node_status_cmd = "kubectl get po -n {suite_code} -o wide"
    __K8sInfoHandler = K8sInfoHandler
    __k8s_svc_cmd = "kubectl get svc -n {suite_code}"
    __K8sSvcInfoHandler = K8sSvcInfoHandler
    __k8s_ingress_cmd = "kubectl get ingress -o custom-columns=NAME:.metadata.name,HOSTS:.spec.rules[0].host -n {suite_code}"
    __K8sIngressInfoHandler = K8sSvcInfoHandler

    def __init__(self):
        pass

    def get_node_status(self, suite_code, app_name=None):
        k8s_info_handler = self.__K8sInfoHandler()

        k8s_server = get_k8s_server(suite_code)
        if k8s_server:
            with SSHConnectionManager(k8s_server.get('k8s_host'), k8s_server.get('k8s_username'), k8s_server.get('k8s_password')) as ssh:
                logger.info(self.__node_status_cmd.format(suite_code=suite_code))
                cmd = self.__node_status_cmd.format(suite_code=suite_code)
                stdin, stdout, stderr = ssh.SSH.exec_command(cmd, bufsize=-1)
                node_status_info = stdout.read().decode("utf-8")
            logger.info(f"\r\n{node_status_info}")
            k8s_info_handler.set_data(node_status_info)
            node_status_dict = k8s_info_handler.get_node_status_info()
            if app_name is None:
                return node_status_dict
            else:
                if app_name.lower() in node_status_dict:
                    return node_status_dict.get(app_name.lower())
                else:
                    raise IOError("未找到应用{}的状态".format(app_name))
        else:
            raise IOError("环境{}对应的k8s_server信息为空".format(suite_code))

    def get_service_ip_and_port(self, suite_code, app_name=None, k8s_server=None):
        k8s_svc_info_handler = self.__K8sSvcInfoHandler()

        with SSHConnectionManager(k8s_server.get('k8s_host'), k8s_server.get('k8s_username'), k8s_server.get('k8s_password')) as ssh:
            logger.info(self.__k8s_svc_cmd.format(suite_code=suite_code))
            cmd = self.__k8s_svc_cmd.format(suite_code=suite_code)
            stdin, stdout, stderr = ssh.SSH.exec_command(cmd, bufsize=-1)
            node_svc_info = stdout.read().decode("utf-8")
        k8s_svc_info_handler.set_data(node_svc_info)
        node_svc_info_dict = k8s_svc_info_handler.get_node_status_info()
        if not app_name:
            return node_svc_info_dict
        else:
            if app_name.lower() in node_svc_info_dict:
                return {app_name.lower(): node_svc_info_dict[app_name.lower()]}
            else:
                return ""

    def get_k8s_hosts(self, suite_code, app_name=None, k8s_server=None):
        k8s_ingress_info_handler = self.__K8sIngressInfoHandler()

        with SSHConnectionManager(k8s_server.get('k8s_host'), k8s_server.get('k8s_username'), k8s_server.get('k8s_password')) as ssh:
            logger.info(self.__k8s_ingress_cmd.format(suite_code=suite_code))
            cmd = self.__k8s_ingress_cmd.format(suite_code=suite_code)
            stdin, stdout, stderr = ssh.SSH.exec_command(cmd, bufsize=-1)
            node_ingress_info = stdout.read().decode("utf-8")
        k8s_ingress_info_handler.set_data(node_ingress_info)
        node_ingress__info_dict = k8s_ingress_info_handler.get_node_status_info()
        if not app_name:
            return node_ingress__info_dict
        else:
            if app_name.lower() in node_ingress__info_dict:
                return {app_name.lower(): node_ingress__info_dict[app_name.lower() ]}
            else:
                return ""


if __name__ == "__main__":
    ko = K8sOperation()
    ko.get_node_status("it52", "Redis")


