from settings import logger


class K8sInfoHandler:
    """
            NAME                                                READY   STATUS             RESTARTS   AGE
        acc-center-web-7dc6d4f65b-4q8cb                     1/1     Running            2          6d2h
        acc-console-web-679b5696c6-dctpf                    1/1     Running            1          6d2h
        activemq-5fd9c459df-2nmls                           1/1     Running            2          6d2h
        adviser-batch-center-remote-7c8d7fb67d-24m5f        1/1     Running            0          20m
        batch-center-remote-97bbc8696-jw2zw                 1/1     Running            1          6d2h
        canal-server-5c578d87b5-xr87p                       1/1     Running            0          29h
        cgi-ehowbuy-container-69c5c4bcbb-njzbb              1/1     Running            9          23h
        cgi-gateway-7f5c7c8fd4-jmt5x                        1/1     Running            0          6d2h
        cgi-simu-container-65674469d7-4df7k                 1/1     Running            0          23h
        coupon-center-remote-84fd76db47-rc8ms               1/1     Running            1          6d2h
    """
    def __init__(self):
        self.k8s_info = ""

    def set_data(self, k8s_info):
        self.k8s_info = k8s_info

    def get_node_status_key(self):
        for row in self.k8s_info.split("\n"):
            row_list = row.strip().split(" ")
            if row_list[0] != "":
                return [i for i in row_list if i != ""]

    def get_node_status_info(self):
        node_status_info_dict = {}
        key_list = self.get_node_status_key()
        for row in self.k8s_info.split("\n"):
            row_list = row.strip().split(" ")
            name_list = row_list[0].split("-")
            if len(name_list) > 2:
                values_list = [i for i in row_list if i != ""]
                row_node_status_info = dict(zip(key_list, values_list))
                # logger.info(row_node_status_info)
                node_status_info_dict["-".join(name_list[0:-2])] = row_node_status_info
        # logger.info(node_status_info_dict)
        return node_status_info_dict


class K8sSvcInfoHandler(K8sInfoHandler):
    """
        NAME            TYPE        CLUSTER-IP      EXTERNAL-IP       PORT(S)                       AGE
        acc-center-web  ClusterIP   ************    <none>            8080/TCP,10090/TCP,8000/TCP   211d
    """

    def get_node_status_info(self):
        node_status_info_dict = {}
        key_list = self.get_node_status_key()
        for row in self.k8s_info.split("\n"):
            row_list = row.strip().split(" ")
            name_list = row_list[0].split("-")
            if len(name_list) > 0:
                values_list = [i for i in row_list if i != ""]
                row_node_status_info = dict(zip(key_list, values_list))
                # logger.info(row_node_status_info)
                node_status_info_dict["-".join(name_list)] = row_node_status_info
        # logger.info(node_status_info_dict)
        return node_status_info_dict


class K8sIngressInfoHandler(K8sInfoHandler):
    """
        NAME            TYPE        CLUSTER-IP      EXTERNAL-IP       PORT(S)                       AGE
        acc-center-web  ClusterIP   ************    <none>            8080/TCP,10090/TCP,8000/TCP   211d
    """

    def get_node_status_info(self):
        node_status_info_dict = {}
        key_list = self.get_node_status_key()
        for row in self.k8s_info.split("\n"):
            row_list = row.strip().split(" ")
            name_list = row_list[0].split("-")
            if len(name_list) > 0:
                values_list = [i for i in row_list if i != ""]
                row_node_status_info = dict(zip(key_list, values_list))
                # logger.info(row_node_status_info)
                node_status_info_dict["-".join(name_list)] = row_node_status_info
        # logger.info(node_status_info_dict)
        return node_status_info_dict

if __name__ == "__main__":
    kh = K8sSvcInfoHandler()
    data =  """
            NAME                                                READY   STATUS             RESTARTS   AGE
        acc-center-web-7dc6d4f65b-4q8cb                     1/1     Running            2          6d2h
        acc-console-web-679b5696c6-dctpf                    1/1     Running            1          6d2h
        activemq-5fd9c459df-2nmls                           1/1     Running            2          6d2h
        adviser-batch-center-remote-7c8d7fb67d-24m5f        1/1     Running            0          20m
        batch-center-remote-97bbc8696-jw2zw                 1/1     Running            1          6d2h
        canal-server-5c578d87b5-xr87p                       1/1     Running            0          29h
        cgi-ehowbuy-container-69c5c4bcbb-njzbb              1/1     Running            9          23h
        cgi-gateway-7f5c7c8fd4-jmt5x                        1/1     Running            0          6d2h
        cgi-simu-container-65674469d7-4df7k                 1/1     Running            0          23h
        coupon-center-remote-84fd76db47-rc8ms               1/1     Running            1          6d2h
    """
    data1 = """NAME            TYPE        CLUSTER-IP      EXTERNAL-IP       PORT(S)                       AGE
            acc-center-web  ClusterIP   ************    <none>            8080/TCP,10090/TCP,8000/TCP   211d
            redis  ClusterIP   ************    <none>            8080/TCP,10090/TCP,8000/TCP   211d"""
    kh.set_data(data1)
    kh.get_node_status_info()