import sys
import datetime
import time

from settings import logger
from settings import JENKINS_INFO
from dao.insert.mysql.pipeline_log import insert_pipeline_log_main, insert_pipeline_log_minor
from dao.update.mysql.pipeline_log import update_pipeline_minor_log, update_pipeline_main_log


def coroutine(func):
    def inner(*args, **kwargs):

        f = func(*args, **kwargs)
        next(f)
        return f
    return inner


def record_minor_log(step=None):
    def update_minor_log(func):
        def inner(*args, **kwargs):
            varnames = func.__code__.co_varnames
            module_name = None
            for i in range(func.__code__.co_argcount):
                if varnames[i] == "sid":
                    sid = args[i]
                if varnames[i] == "app_name":
                    app_name = args[i]
                if varnames[i] == "module_name":
                    module_name = args[i]
            if module_name is None:
                module_name = app_name
            insert_pipeline_log_minor(sid, "{}_{}".format(step, module_name), datetime.datetime.now(), module_name)
            status, content = func(*args, **kwargs)
            update_pipeline_minor_log(sid, "{}_{}".format(step, module_name), content, datetime.datetime.now(), status)
            if status == "failure":
                update_pipeline_main_log(sid, status, datetime.datetime.now())
                sys.exit(1)
            if app_name == module_name:
                update_pipeline_main_log(sid, 'success', end_at=datetime.datetime.now())
            return status, content
        return inner
    return update_minor_log


@coroutine
def record_log(*args, **kwargs):
    """
    记录日志功能
    :param caller:填写调用者 palatform or jenkins
    :param sid:  sid
    :return:
    """

    sid = insert_pipeline_log_main(exec_jenkins=JENKINS_INFO["URL"], exec_parameter=kwargs["exec_parameter"],
                                   start_at=datetime.datetime.now(), iteration_id=kwargs["iteration_id"],
                                   app_name=kwargs["app_name"], status="running", suite_name=kwargs["suite_name"])
    try:
        while True:
            try:
                message = yield
            except Exception as e:
                message = e
            if message is None:
                continue
            if len(message) == 0:
                update_pipeline_main_log(sid, "failure", end_at=datetime.datetime.now())
                sys.exit(1)
            logger.info(message)
            if "module_name" in message:
                insert_pipeline_log_minor(sid, message["step"], datetime.datetime.now(), message["module_name"])
            else:
                update_pipeline_minor_log(sid, message["step"], message["content"], datetime.datetime.now(), message["status"])
                if message["status"] == "failure":
                    logger.error(message)
                    update_pipeline_main_log(sid, message["status"], datetime.datetime.now())
                    sys.exit(1)

    finally:
        update_pipeline_main_log(sid, message["status"], end_at=datetime.datetime.now())



def __test_record_log(record):
    """

    :param record:
    :return:
    """
    record.send(None)
    record.send({"step": "拉包acc-common", "module_name":"acc-common"})
    time.sleep(1)
    record.send({"status": "success", "step": "拉包acc-common", "content": "拉取啊拉取"})

    time.sleep(1)

    record.send({"step": "拉包acc-facade", "module_name":"acc-facade"})
    time.sleep(1)
    record.send({"status": "success", "step": "拉包acc-facade", "content": "拉取啊拉取"})

    time.sleep(1)


if __name__ == "__main__":
    records = record_log(exec_parameter="lalalaa",
                                    iteration_id="acc_0.0.1",
                                   app_name="acc_center", status="running", suite_name="env01")
    __test_record_log(records)
    # records.close()
