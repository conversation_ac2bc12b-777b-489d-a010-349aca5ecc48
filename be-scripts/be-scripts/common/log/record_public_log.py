import threading
import traceback
import datetime
from publish_tool.publish.models import ServiceResults
from enum import Enum
from publish_tool.publish.publish_ser import H5DeployStatusCollector, LibRepoInfoRecorder, ConfigRepoInfoRecorder,\
    get_legitimacy_status, PublishMgtBackupInfoRecorder
from app_mgt.models import MobileBuild
from settings import logger

def script_record(func):
    __script_exec_record = ServiceResults
    exec_status_enum = Enum('_exec_status', ('running', 'success', 'failure'))
    __queue_status_record = H5DeployStatusCollector
    __lib_repo_record = LibRepoInfoRecorder
    __config_repo_recorder = ConfigRepoInfoRecorder
    __mobile_build = MobileBuild

    def _update_node_lib_repo_info(script_params):
        lib_repo_info_recorder = __lib_repo_record(script_params["app_name"],
                                                     script_params["iteration_id"],
                                                     node_ip=script_params["ip"],
                                                     suite_code=script_params["suite_code"])
        lib_repo_info_recorder.record_info()

    def _update_config_lib_repo_info(script_params):
        config_recorder = __config_repo_recorder(script_params["app_name"],
                                                     script_params["iteration_id"],
                                                     node_ip=script_params["ip"],
                                                     suite_code=script_params["suite_code"])
        config_recorder.record_info()

    def _insert_publish_mgt_backup_info(script_params, operator, backup_path, backup_desc, salt_upd_log,opt_type):
        backup_recorder = PublishMgtBackupInfoRecorder(script_params["app_name"],
                 script_params["ip"], backup_path, salt_upd_log, operator,
                 script_params["iteration_id"], backup_desc, node_docker=None,
                                                       suite_code=script_params["suite_code"],
                                                       opt_type=opt_type)
        if opt_type in ('deploy'):
            backup_recorder.record_info()

    def inner(*args, **kwargs):
        logger.info(args[1])
        start_at = datetime.datetime.now()
        # opt_type = None
        try:
            cmd_obj = args[1]
            # opt_type = cmd_obj.get('opt_type')
            # obj = __script_exec_record(
            #     business_name='publish_jenkins_tool', script_params=cmd_obj,
            #     operator=cmd_obj['action_id'], log_path='', start_at=start_at,
            #     exec_cmd=cmd_obj['exec_cmd'])
            status = get_legitimacy_status(cmd_obj['action_id'], cmd_obj['job_name'],
                                           cmd_obj['exec_id'], cmd_obj['build_id'])
            if status == False:
                logger.info("非法请求")
                raise Exception("非法请求，执行编号和构建编号不匹配")
            stat, rst = func(*args, **kwargs)
            detail = rst
            if stat == exec_status_enum.success.name:
                exec_status = exec_status_enum.success.name
                script_params = cmd_obj
                backup_path = ""
                backup_desc = ""
                _insert_publish_mgt_backup_info(cmd_obj, cmd_obj['action_id'], backup_path,
                                                backup_desc, detail,
                                                cmd_obj.get('opt_type'))
                if "iteration_id" in script_params:
                    logger.info("参数中存在迭代信息 {}".format(script_params["iteration_id"]))
                    if script_params["res_type"] == "pkg" and script_params["opt_type"] in ("deploy", "update"):
                        _update_node_lib_repo_info(script_params)
                    if script_params["res_type"] == "config" and script_params["opt_type"] == "update":
                        logger.info("需要更新 节点制品信息")
                        _update_config_lib_repo_info(script_params)
            else:
                exec_status = exec_status_enum.failure.name
        except Exception as err:
            logger.info(err)
            err_msg = traceback.format_exc()
            logger.error(err_msg)
            exec_status = exec_status_enum.failure.name
            detail = str(err_msg)
            raise Exception("执行异常：{}".format(detail))
        finally:
            end_at = datetime.datetime.now()
            # obj.status = exec_status
            # obj.detail = detail
            # obj.end_at = end_at
            __queue_status_record().update_deploy_status(cmd_obj['action_id'], exec_status, detail, None,
                                   cmd_obj.get('ip'), cmd_obj.get('opt_type'))
            # __queue_status_record().insert_deploy_detail(exec_status, detail, start_at, end_at, opt_type, cmd_obj['action_id'], cmd_obj['app_name'], cmd_obj.get('ip'))

            # _insert_publish_mgt_backup_info(cmd_obj, cmd_obj['action_id'], backup_path,
            #                                                                backup_desc, detail,
            #                                                                cmd_obj.get('opt_type'))
            logger.info('{} 耗时: {}s'.format('publish_jenkins_tool', str((end_at - start_at).seconds)))

    return inner