import datetime
import sys
import time
from dao.insert.mysql.script_log import insert_minor_start_log
from dao.update.mysql.script_log import update_main_log, update_minor_log
from settings import logger


def send_email_to_applicant(sid, result):
    from dao.connect.mysql import DBConnectionManager
    from common.email.send_email import SendMail
    with DBConnectionManager() as db:
        db.cur.execute(
            "select order_id from env_order_record_script_main where script_main_id={}".format(sid))
        order_id = db.cur.fetchone()
        if order_id:
            db.cur.execute(
                "select applicant from env_order where id={}".format(order_id['order_id']))
            applicant = db.cur.fetchone()

            if applicant:
                db.cur.execute("select deploy_ip, exec_parameter from record_script_main where sid={}".format(sid))
                row_data = db.cur.fetchone()
                deploy_ip = row_data['deploy_ip']
                exec_parameter = row_data['exec_parameter']
                if 'tms' in eval(exec_parameter):
                    addition = '中台'
                else:
                    addition = '后台'

                title = '测试环境初始化: ' + addition + deploy_ip + ' 执行失败'
                if result == 'success':
                    title = title.replace('失败', '成功')
                recv = applicant['applicant'] + '@howbuy.com'
                send_mail = SendMail()
                send_mail.set_to(recv)
                send_mail.set_subject(title)
                send_mail.send()


def coroutine(func):
    def inner(*args, **kwargs):
        f = func(*args, **kwargs)
        next(f)
        return f
    return inner


@coroutine
def record_log(caller, sid):
    """
    记录日志功能
    :param caller:填写调用者 palatform or jenkins
    :param sid:  sid
    :return:
    """
    if caller == "platform":
        update_main_log(sid, "running", start_at=datetime.datetime.now())
    try:
        while True:
            try:
                message = yield
            except Exception as e:
                message = e
            if message is None:
                continue
            if caller in ("platform", "script"):
                if len(message) == 1:
                    insert_minor_start_log(sid, message["step"], datetime.datetime.now())
                elif len(message) == 0:
                    update_main_log(sid, "failure", end_at=datetime.datetime.now())
                    sys.exit(1)
                else:
                    update_minor_log(sid, message["step"], message["content"], datetime.datetime.now(), message["status"])
                    if message["status"] == "failure":
                        logger.error(message)
                        update_main_log(sid, message["status"], datetime.datetime.now())
                        sys.exit(1)
                    logger.info(message)
    finally:
        if caller == "platform":
            update_main_log(sid, message["status"], end_at=datetime.datetime.now())
            # send_email_to_applicant(sid, message["status"])


def __test_record_log(record):
    """

    :param record:
    :return:
    """
    record.send(None)
    record.send({"step": "拉包"})
    time.sleep(1)
    record.send({"status": "success", "step": "拉包", "content": "拉取啊拉取"})
    record.send({"step": "修改配置"})
    time.sleep(1)
    record.send({"status": "success", "step": "修改配置", "content": "修改配置丰富"})
    record.send({"step": "远程推送"})
    time.sleep(1)
    record.send({"status": "success", "step": "远程推送", "content": "远程推送丰富发"})
    record.send({"step": "重做镜像"})
    time.sleep(1)
    record.send({"status": "success", "step": "重做镜像", "content": "重做镜像丰富通过"})


if __name__ == "__main__":
    records = record_log("platform", 29)
    __test_record_log(records)
    records.close()
