from dao.base_model import BaseModel, SpiderBaseModels
from peewee import *


class IterInfo(BaseModel):
    pipeline_id = CharField(verbose_name='迭代版本', max_length=50)
    br_status = CharField(verbose_name='迭代状态', max_length=20)
    br_name = CharField(verbose_name='分支版本', max_length=50)
    br_style = CharField(verbose_name='分支类型', max_length=20)
    duedate = CharField(verbose_name='预期上线时间', max_length=50)
    br_status = CharField(verbose_name='分支状态', max_length=20)
    description = CharField(verbose_name='功能描述·', max_length=200)
    releaseNotic = CharField(verbose_name='注意事项', max_length=200)
    schedule = CharField(verbose_name='调度', max_length=200)
    file_ccms_config = CharField(verbose_name='文件或ccms配置', max_length=200)
    test_end_date = DateTimeField(verbose_name='测试结束时间')
    update_test_end_date_user = CharField(verbose_name='更新测试结束时间操作人', max_length=50)

    class Meta:
        db_table = 'iter_mgt_iter_info'
        verbose_name = '分支信息表'


class IterAppInfo(BaseModel):
    pipeline_id = CharField(verbose_name='流水线ID', max_length=50)
    appName = CharField(verbose_name='应用名', max_length=100)
    pom_path = CharField(verbose_name='pom文件路径', max_length=100)

    sys_status = CharField(verbose_name='应用状态', max_length=20)
    sys_duedate = CharField(verbose_name='发布日期', max_length=50)
    proposer = CharField(verbose_name='申请人', max_length=50)
    simulate_identifier = CharField(verbose_name='仿真验证人', max_length=20)
    config_content = CharField(verbose_name='配置文件', max_length=100)
    git_last_version = CharField(verbose_name='最后一次编译版本', max_length=50)
    jdkVersion = CharField(verbose_name='指定jdk版本', max_length=20)
    git_repo_version = CharField(verbose_name='分支制品版本', max_length=50)
    git_repos_time = DateTimeField(verbose_name='制品时间')
    package_type = CharField(max_length=10, verbose_name='应用类型')
    git_path = TextField(verbose_name='git路径')
    user_name = CharField(verbose_name='用户名', max_length=100)
    need_online = BooleanField(verbose_name='jar是否需要上线')
    build_cmd = CharField(verbose_name='编译命令', max_length=128)

    class Meta:
        db_table = 'iter_mgt_iter_app_info'
        verbose_name = '分支应用关联表'


class UserActionRecord(BaseModel):
    username = CharField(verbose_name='用户名', max_length=50)
    operate_time = DateTimeField(verbose_name='操作时间')
    action_item = CharField(verbose_name='操作项', max_length=500)
    action_value = TextField(verbose_name='操作参数')

    class Meta:
        db_table = 'user_action_record'
        verbose_name = '用户行为记录表'


class BizTestAppNeed(BaseModel):
    module_name = CharField(verbose_name='应用名', max_length=100)
    need_auto_test = IntegerField(verbose_name='是否必须自动化测试，1，是；0，否')
    biz_code = CharField(verbose_name='业务代码')
    biz_flow_name = CharField(verbose_name='业务流名称')
    pass_rate_threshold = IntegerField(verbose_name='阈值通过率')
    create_time = DateTimeField(verbose_name='创建时间')
    update_time = DateTimeField(verbose_name='更新时间')
    create_user = CharField(verbose_name='创建人', max_length=20)
    update_user = CharField(verbose_name='更新人', max_length=20)
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'biz_test_app_need'
        verbose_name = '业务测试需要与应用关联表'


class AppDependencyInfo(SpiderBaseModels):
    module_name = CharField(verbose_name='应用名', max_length=100)
    branch = CharField(verbose_name='应用版本', max_length=100)
    dependency_name = CharField(verbose_name='依赖包名称', max_length=100)
    dependency_version = CharField(verbose_name='依赖包版本', max_length=50)
    type = IntegerField(verbose_name='依赖模块类型:1、三方包，2、二方包')
    source = CharField(verbose_name='来源类型', max_length=100)
    create_time = DateTimeField(verbose_name='创建时间')
    update_time = DateTimeField(verbose_name='更新时间')
    create_user = CharField(verbose_name='创建人', max_length=20)
    update_user = CharField(verbose_name='更新人', max_length=20)
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'iter_mgt_app_dependency_info'
        verbose_name = '迭代应用依赖包'


class SDKDependencyRule(BaseModel):
    sdk_name = CharField(verbose_name='sdk名', max_length=100)
    sdk_branch = CharField(verbose_name='sdk版本', max_length=100)
    dependency_name = CharField(verbose_name='依赖包名称', max_length=100)
    min_version = CharField(verbose_name='依赖包最小版本', max_length=50)
    max_version = CharField(verbose_name='依赖包最大版本', max_length=50)
    whitelist = CharField(verbose_name='依赖包白名单版本', max_length=250)
    type = IntegerField(verbose_name='类型：1、三方包')
    is_active = IntegerField(verbose_name='是否可用')
    desc = CharField(verbose_name='说明')
    create_time = DateTimeField(verbose_name='创建时间')
    update_time = DateTimeField(verbose_name='更新时间')
    create_user = CharField(verbose_name='创建人', max_length=20)
    update_user = CharField(verbose_name='更新人', max_length=20)
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'iter_mgt_sdk_dependency_rule'
        verbose_name = 'sdk依赖配置'