from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_open_iter_app_repo_info(package_type=("jar", "war", "tar")):
    """获取迭代存在的仓库

    :param iteration_id: string.迭代版本号
    :return: list. repos list
    """
    with DBConnectionManager() as db:
        sql = """SELECT jb.job_name,j.pipeline_id as iteration_id,i.appName as app_name,j.br_name,
 CONCAT(a.git_url,a.git_path) AS code_path,jb.jenkins_info_id
FROM iter_mgt_iter_app_info i LEFT JOIN iter_mgt_iter_info j ON i.pipeline_id = j.pipeline_id
LEFT JOIN app_mgt_app_module m ON i.appName = m.module_name
LEFT JOIN app_mgt_app_build b ON b.module_name = m.module_name
LEFT JOIN app_mgt_app_info a ON a.id =m.app_id 
LEFT JOIN jenkins_mgt_jenkins_job_info jb ON jb.iteration_id = i.pipeline_id AND jb.app_name = i.appName
LEFT JOIN jenkins_mgt_jenkins_info ji ON ji.id = jb.jenkins_info_id
WHERE m.need_ops = 1 AND m.need_online = 1 AND j.br_status = "open" AND b.package_type IN
("{}") AND ji.jenkins_state =1 AND jenkins_identity = "slave" """.format('","'.join(package_type))
        logger.info(sql)
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_suite_code_by_iteration_id(iteration_id):
    sql = '''
            SELECT DISTINCT d.suite_code, e.type_short_name
            FROM iter_mgt_iter_app_info b
            LEFT JOIN env_mgt_node_bind c on b.appName = c.module_name
            LEFT JOIN env_mgt_suite d on c.suite_id = d.id
            LEFT JOIN env_mgt_region e on d.region_id = e.id
            WHERE b.pipeline_id = '{}'
            AND e.type_short_name IN ('prod','zb')
            AND e.region_is_active = 1
            AND c.enable_bind = 1
            AND d.suite_is_active = 1;
            '''.format(iteration_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        suite_list = db.cur.fetchall()
    return suite_list


def get_publish_app_by_iteration_id(iteration_id, suite_code):
    sql = '''
            SELECT b.appName,d.step_num, IFNULL(d.publish_mode, 0) as publish_mode,GROUP_CONCAT(f.node_ip) AS node_ips 
            FROM iter_mgt_iter_info a
            INNER JOIN iter_mgt_iter_app_info b on a.pipeline_id = b.pipeline_id
            INNER JOIN env_mgt_node_bind e on e.module_name = b.appName
            INNER JOIN env_mgt_node f on f.id = e.node_id
            INNER JOIN env_mgt_suite g on g.id = e.suite_id
            INNER JOIN iter_mgt_publish_application pa on pa.pipeline_id = a.pipeline_id
            INNER JOIN app_mgt_app_module am ON am.module_name = e.module_name
            LEFT JOIN iter_mgt_group_publish c ON c.iteration_id = a.pipeline_id and g.suite_code = c.suite_code
            LEFT JOIN iter_mgt_group_publish_step d on c.id = d.publish_id and d.app_name = e.module_name
            WHERE a.pipeline_id = '{}'
            AND g.suite_code = '{}'
            AND f.node_status = '0'
            AND e.enable_bind = 1
            AND am.jenkins_batch_publish = 1
            AND pa.`status` = "已完成"
            AND (e.is_cold_standby_node is null or e.is_cold_standby_node  = 0)
            GROUP BY b.appName
        '''.format(iteration_id, suite_code)
    result = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for item in db.cur.fetchall():
            node_ips = item['node_ips']
            if node_ips:
                str_list = node_ips.split(",")
                ip_set = list(set(str_list))
            result.append({"app_name": item['appName'], "step_num": item['step_num'], "publish_mode": item['publish_mode'], "node_list": ip_set})
    return result


def get_apidoc_guard_info(iteration_id, module_name):
    apidoc_guard_info = {}
    sql = '''
        SELECT s.guard_name,g.wl_group_name, g.wl_group_pass, g.wl_group_value,
        a.wl_name, a.wl_pass, a.wl_value,
        IFNULL(a.wl_pass, g.wl_group_pass) AS pass
        FROM qc_mgt_guard_switch_info s
        INNER JOIN iter_whitelist_group g ON g.wl_switch_id = s.id 
        LEFT JOIN iter_whitelist_app a ON a.wl_group_id = g.id AND a.wl_name = '{}'
        LEFT JOIN `iter_mgt_iter_app_info` ai ON ai.appName = a.wl_name
        LEFT JOIN `iter_mgt_iter_info` i ON i.pipeline_id = ai.pipeline_id AND i.project_group = g.wl_group_name
        WHERE 1=1
        AND g.wl_switch_id = 9 AND i.pipeline_id = '{}'
    '''.format(module_name, iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result = db.cur.fetchone()
        if not result:
            return apidoc_guard_info
        apidoc_guard_info['guard_name'] = result['guard_name']
        apidoc_guard_info['pass'] = result['pass']
    return apidoc_guard_info


def get_guard_info(iteration_id, module_name, wl_switch_id):
    apidoc_guard_info = {}
    sql = '''
        SELECT s.guard_name,g.wl_group_name, g.wl_group_pass, g.wl_group_value,
        a.wl_name, a.wl_pass, a.wl_value,
        IFNULL(a.wl_pass, g.wl_group_pass) AS pass
        FROM qc_mgt_guard_switch_info s
        INNER JOIN iter_whitelist_group g ON g.wl_switch_id = s.id 
        LEFT JOIN iter_whitelist_app a ON a.wl_group_id = g.id AND a.wl_name = '{module_name}'
        LEFT JOIN `iter_mgt_iter_app_info` ai ON ai.appName = a.wl_name
        LEFT JOIN `iter_mgt_iter_info` i ON i.pipeline_id = ai.pipeline_id AND i.project_group = g.wl_group_name
        WHERE 1=1
        AND g.wl_switch_id = {wl_switch_id} AND i.pipeline_id = '{iteration_id}'
    '''.format(module_name=module_name, iteration_id=iteration_id, wl_switch_id=wl_switch_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result = db.cur.fetchone()
        if not result:
            return apidoc_guard_info
        apidoc_guard_info['guard_name'] = result['guard_name']
        apidoc_guard_info['pass'] = result['pass']
    return apidoc_guard_info


def get_archive_iter_by_gt_iter_id(iteration_id):
    sql = '''
          SELECT o.pipeline_id,o.br_name,.o.br_end_date,i.br_end_date FROM iter_mgt_iter_info o JOIN 
                      (SELECT project_group,br_end_date FROM iter_mgt_iter_info WHERE pipeline_id = "{}") 
                      i ON o.project_group = i.project_group
                      WHERE o.br_status= "close" AND o.br_end_date > i.br_end_date'''.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return [item['br_name'] for item in db.cur.fetchall()]


if __name__ == '__main__':
    info = get_apidoc_guard_info("CRM_1.5.9.6", "crm-sys-webapp")
    print(info)
