import os
import sys
import traceback
from datetime import timedelta, datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)
import urllib
from test_mgt.test_mgt_ser import get_db_info_by_app
from test_publish_aio.test_suite_init_impl_db import DbPullBranchSqlFromMasterHandler, DbSortBranchSqlFromMasterHandler, \
    DbExecBranchSqlFromMasterHandler, DbSyncBranchSqlToTestingHandler

from dao.get.mysql.app_info import get_db_info_by_app_name, query_archived_applications_and_versions

import requests
from dao.connect.mysql import DBConnectionManager
from settings import INTERFACE_URL, logger


# 查询所有业务应用
def get_apps_from_spider():
    url = INTERFACE_URL['spider'] + INTERFACE_URL['spider_context'] + INTERFACE_URL[
        'get_apps']
    try:
        res = requests.get(url + '?' + urllib.parse.urlencode({"app_type": 1}),
                           headers={'Content-Type': 'application/json',
                                    "Authorization": INTERFACE_URL[
                                        'spider_api_Authorization']})
        result = res.json()
        if "status" in result and result["status"] == "success":
            apps = []
            for item in result["data"]:
                apps.append(item[0])
            return apps
        else:
            return "启动发生错误"
    except Exception as ex:
        logger.error(ex)
        return "启动发生错误"


# 查询测试环境所有数据库
def get_dbs():
    with DBConnectionManager() as db:
        sql = """  SELECT DISTINCT logic_info.logic_db_name, db_group.db_group_name,db_srv.db_srv_type 
                   FROM db_mgt_logic_info logic_info 
                   JOIN db_mgt_domain dmd ON dmd.id = logic_info.db_domain_id
                   JOIN db_mgt_group db_group ON dmd.db_group_id=db_group.id  
                   JOIN db_mgt_suite_bind sb ON logic_info.id = sb.db_logic_id
                   JOIN db_mgt_info dmi ON sb.db_info_id = dmi.id
                   JOIN db_mgt_srv db_srv ON dmi.db_srv_id=db_srv.id"""
        db.cur.execute(sql)
        dbs = []
        for item in db.cur.fetchall():
            dbs.append({"db_srv_type": item['db_srv_type'], "db_name": item['logic_db_name'],
                        "db_group_name": item['db_group_name']})
    return dbs


def merge_app_db(app_info_list, db_info_list):
    dbs = get_dbs()
    for db_info in db_info_list:
        for app_info in app_info_list:
            if app_info[0] == db_info['module_name']:
                db_info["br_name"] = app_info[1]
                # 定义日期时间字符串和格式
                dt_str = app_info[2]
                dt_format = '%Y年%m月%d日 %H时%M分'
                # 将日期时间字符串解析为datetime对象
                dt = datetime.strptime(dt_str, dt_format)
                # 将datetime对象格式化为字符串
                dt_str = dt.strftime('%Y%m%d%H%M')
                db_info["br_end_date"] = dt_str
        for db_item in dbs:
            if db_item['db_name'] == db_info['db_name']:
                s_db_info_list = get_db_info_by_app(suite_code=SUITE_CODE, db_name=db_info['db_name'])
                if s_db_info_list:
                    db_info = s_db_info_list[0]


if __name__ == '__main__':
    param_list = sys.argv
    err_msg = None
    try:
        logger.info(param_list)
        if len(param_list) < 5:
            raise ValueError("缺少必要参数：{}".format(param_list))
        # 应用名
        APP_NAME = param_list[2]
        # 数据库名
        DB_NAME = param_list[3]
        # 归档分支
        BR_NAME = param_list[4]
        # 初始化环境
        SUITE_CODE = param_list[5]
        class_name = param_list[1]
        method_name = "exec"
        db_info_list = []
        if DB_NAME != 'None' and DB_NAME and BR_NAME and SUITE_CODE:
            for db_info in get_dbs():
                if DB_NAME.lower() == db_info['db_name']:
                    s_db_info_list = get_db_info_by_app(suite_code=SUITE_CODE, db_name=db_info['db_name'])
                    for s_db_info in s_db_info_list:
                        s_db_info['br_name'] = BR_NAME
                        db_info_list.append(s_db_info)

        elif APP_NAME != 'None' and APP_NAME and BR_NAME and SUITE_CODE:
            for db_info in get_db_info_by_app(suite_code=SUITE_CODE, app_name=APP_NAME):
                db_info['br_name'] = BR_NAME
                db_info_list.append(db_info)
        else:
            # 查一天前所有归档的迭代 迭代相关的应用 应用相关的库
            # 获取当前日期
            current_date = datetime.now()
            formatted_current_date = current_date.strftime("%Y年%m月%d日")
            print("当前日期:", formatted_current_date)
            # 获取昨天的日期
            yesterday = current_date - timedelta(days=1)
            formatted_yesterday = yesterday.strftime("%Y年%m月%d日")
            print("昨天的日期:", formatted_yesterday)
            app_info_list = query_archived_applications_and_versions(formatted_yesterday, formatted_current_date)
            logger.info("app_info_list:{}".format(app_info_list))
            if not app_info_list:
                logger.info("无归档应用！跳过恢复数据库")
                # 正常返回
                sys.exit()
            apps_str = ', '.join("'" + item[0] + "'" for item in app_info_list)
            logger.info("apps_str:{}".format(apps_str))
            db_info_list = get_db_info_by_app_name(apps_str)
            merge_app_db(app_info_list, db_info_list)

        logger.info("db_info_list:{}".format(db_info_list))
        # db_info_list=[{'db_srv_type': 'oracle', 'db_name': 'trade', 'db_group_name': 'TRADE', 'br_name': '3.15.0'},{'db_srv_type': 'oracle', 'db_name': 'trade', 'db_group_name': 'TRADE', 'br_name': '3.14.0'}]
        if not db_info_list:
            logger.info("数据库信息为空！跳过同步数据库")
            # 正常返回
            sys.exit()
        # 实例化类
        handler = globals()[class_name]()
        # 调用方法
        getattr(handler, method_name)(db_info_list)
        # 正常返回
        sys.exit()
    except Exception as e:
        traceback.print_exc()
        # 异常返回
        sys.exit(-1)
