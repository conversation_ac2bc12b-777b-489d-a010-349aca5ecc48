import traceback
from time import sleep

from dao.connect.mysql import DBConnectionManager
from iter_mgt.publish_plan.dao.model.models import TaskMgtDeployResult
from publish_tool.publish.models import ServiceResults


def query_service_results(sid):
    if not sid:
        return False, "sid为空，请检查！"
    sleep_max = 300
    while sleep_max > 0:
        sleep(5)
        sleep_max -= 5
        sr = ServiceResults.get_or_none(ServiceResults.id == sid)

        if sr:
            if sr.status == ServiceResults.SUCCESS:
                return True, sr.detail
            elif sr.status == ServiceResults.FAILURE:
                return False, sr.detail

    return False, "查询sid为{}的结果5分钟超时".format(sid)


def query_task_result(action_id):
    if not action_id:
        return False, "action_id为空，请检查！"
    sleep_max = 600
    while sleep_max > 0:
        sleep(5)
        sleep_max -= 5
        tmdr = TaskMgtDeployResult.get_or_none(TaskMgtDeployResult.action_id == action_id)
        if tmdr:
            if 'failure' in tmdr.status:
                return False, tmdr.message
            elif tmdr.status == TaskMgtDeployResult.P_SUCCESS:
                return True, tmdr.message

    return False, "查询action_id：{} 产线申请结果超时(10分钟)".format(action_id)


def get_app_count_in_iteration(iteration_id):
    sql = '''
            SELECT COUNT(appName) AS num FROM iter_mgt_iter_app_info i
            LEFT JOIN app_mgt_app_module m ON i.appName = m.module_name
            LEFT JOIN iter_mgt_iter_info t ON i.pipeline_id = t.pipeline_id
            WHERE i.pipeline_id = '{}' AND m.need_online = 1 AND t.br_status = 'open';
          '''.format(iteration_id)

    with DBConnectionManager() as db:
        db.cur.execute(sql)

    num = db.cur.fetchone()
    return num.get("num")
