# coding: utf-8
from sqlalchemy import Column, DateTime, Index, String
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class IterMgtPublishPlan(Base):
    __tablename__ = 'iter_mgt_publish_plan'
    __table_args__ = (
        Index('udx_plan', 'module_name', 'iter_id', 'batch_no', unique=True),
        {'comment': '迭代计划表'}
    )

    id = Column(BIGINT(11), primary_key=True)
    batch_no = Column(String(20), comment='批次')
    module_name = Column(String(100), comment='应用名')
    branch_name = Column(String(100), comment='分支名')
    iter_id = Column(String(100), comment='迭代号')
    plan_type = Column(String(20), comment='发布计划类型:Express/Rapid/Normal')
    plan_status = Column(String(20), comment='计划状态')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    create_user = Column(String(20), comment='创建人')
    create_time = Column(DateTime, comment='发起时间')
    update_user = Column(String(20), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(20), comment='版本')
