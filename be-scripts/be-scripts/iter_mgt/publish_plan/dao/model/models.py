from peewee import *
from dao.base_model import BaseModel


class TaskMgtDeployResult(BaseModel):
    """发布状态"""
    """注意 RUNNING, 
        SUCCESS, FAILURE 给发布使用
        C_RUNNING, C_SUCCESS, C_FAILURE 给编译使用
        P_RUNNING, P_SUCCESS, P_FAILURE 给发布申请使用
        S_FAILURE 给脚本校验使用
        """
    (RUNNING, SUCCESS, FAILURE, C_RUNNING, C_SUCCESS, C_FAILURE, P_RUNNING, P_SUCCESS, P_FAILURE, S_FAILURE, ABORTED) = \
        ('running', 'success', 'failure', 'compile_running', 'compile_success', 'compile_failure',
         'publish_running', 'publish_success', 'publish_failure', 'script_failure', 'aborted')
    STATUS_CHOICE = ((RUNNING, '发布中'), (SUCCESS, '发布完成'), (FAILURE, '发布失败'),
                     (C_RUNNING, '编译中'), (C_SUCCESS, '编译成功'), (C_FAILURE, '编译失败'),
                     (P_RUNNING, '申请中'), (P_SUCCESS, '申请成功'), (P_FAILURE, '申请失败'),
                     (S_FAILURE, '校验失败'), (ABORTED, '已终止')
                     )

    action_id = IntegerField(verbose_name='行为id')
    app_name = CharField(max_length=100, verbose_name='操作应用名称')
    ip = CharField(max_length=100, verbose_name='ip')
    suite_name = CharField(max_length=100, verbose_name='环境套')
    status = CharField(choices=STATUS_CHOICE, max_length=100, verbose_name='执行状态')
    message = TextField(verbose_name='信息描述')
    op_time = DateTimeField(verbose_name='操作时间')
    op_user = CharField(max_length=100, verbose_name='user')
    op_type = CharField(max_length=100,  verbose_name='操作类型')
    job_name = CharField(max_length=200, verbose_name='job名')
    begin_ver = CharField(max_length=100, verbose_name='打包开始版本')
    end_ver = CharField(max_length=100, verbose_name='打包结束版本')
    iteration_id = CharField(max_length=200, verbose_name='迭代号')

    class Meta:
        db_table = 'task_mgt_deploy_result'
        verbose_name = '发布结果表'


# class TaskMgtDeployDetail(BaseModel):
#     """发布状态"""
#     """注意 RUNNING, SUCCESS, FAILURE 给发布使用
#         C_RUNNING, C_SUCCESS, C_FAILURE 给编译使用
#         P_RUNNING, P_SUCCESS, P_FAILURE 给发布申请使用
#         S_FAILURE 给脚本校验使用
#         """
#     (RUNNING, SUCCESS, FAILURE, C_RUNNING, C_SUCCESS, C_FAILURE, P_RUNNING, P_SUCCESS, P_FAILURE, S_FAILURE, ABORTED) = \
#         ('running', 'success', 'failure', 'compile_running', 'compile_success', 'compile_failure',
#          'publish_running', 'publish_success', 'publish_failure', 'script_failure', 'aborted')
#     STATUS_CHOICE = ((RUNNING, '发布中'), (SUCCESS, '发布完成'), (FAILURE, '发布失败'),
#                      (C_RUNNING, '编译中'), (C_SUCCESS, '编译成功'), (C_FAILURE, '编译失败'),
#                      (P_RUNNING, '申请中'), (P_SUCCESS, '申请成功'), (P_FAILURE, '申请失败'),
#                      (S_FAILURE, '校验失败'), (ABORTED, '已终止')
#                      )
#
#     result_id = IntegerField(verbose_name='发布结果id')
#     status = CharField(choices=STATUS_CHOICE, max_length=100, verbose_name='执行状态')
#     message = TextField(verbose_name='信息描述')
#     op_time = DateTimeField(verbose_name='操作时间')
#     op_type = CharField(max_length=100,  verbose_name='操作类型')
#     begin_time = DateTimeField(verbose_name='开始时间')
#     end_time = DateTimeField(verbose_name='结束时间')
#
#     class Meta:
#         db_table = 'task_mgt_deploy_result_detail'
#         verbose_name = '发布详情表'


class PublishApplicationConfirmation(BaseModel):
    STATUS_CHOICE = (
        ('success', '执行成功'),
        ('failure', '执行失败')
    )
    md5_id = CharField(verbose_name='md5_id', max_length=100)
    iteration_id = TextField(verbose_name='迭代列表')
    create_time = DateTimeField(verbose_name='创建时间')
    create_user = TextField(verbose_name='发送者')
    receiver = TextField(verbose_name='接收者')
    assertor = CharField(verbose_name='确认者', max_length=50)
    affirm_time = DateTimeField(verbose_name='点击确认时间')
    status = CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态' )
    suite_name = CharField(verbose_name='发布环境', max_length=10)

    class Meta:
        # db_table = 'iter_mgt_md5_email'
        db_table = 'iter_mgt_publish_application_confirmation'
        verbose_name = '确认邮件记录'

