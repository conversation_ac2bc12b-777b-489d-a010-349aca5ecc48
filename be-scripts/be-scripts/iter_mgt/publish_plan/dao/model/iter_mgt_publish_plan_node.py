# coding: utf-8
from sqlalchemy import Column, DateTime, Index, JSON, String
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class IterMgtPublishPlanNode(Base):
    __tablename__ = 'iter_mgt_publish_plan_node'
    __table_args__ = (
        Index('udx_plan_pipeline', 'module_name', 'iter_id', 'phase', 'node_name', unique=True),
        {'comment': '发布计划流水线节点表'}
    )

    id = Column(BIGINT(11), primary_key=True)
    batch_no = Column(INTEGER(10), comment='批次号')
    module_name = Column(String(100), comment='应用名')
    iter_id = Column(String(100), comment='迭代号')
    phase = Column(String(50), comment='阶段')
    phase_order = Column(INTEGER(2), comment='阶段顺序')
    phase_parallel = Column(String(20), comment='并行层级')
    next_phase = Column(String(50), comment='下一个阶段')
    next_phase_order = Column(INTEGER(2), comment='下一个阶段顺序')
    node_name = Column(String(100), comment='节点名称')
    node_param = Column(JSON, comment='节点参数')
    schedule_time = Column(DateTime, comment='创建时间')
    order_no = Column(INTEGER(2), comment='节点顺序')
    next_node_order_no = Column(INTEGER(2), comment='下一个节点顺序')
    query_node_run_result_param = Column(JSON, comment='节点运行中间状态参数（供异步执行任务、查询运行结果）')
    node_run_result = Column(JSON, comment='运行结果:status、data、result_desc;data:【show_type(new_page、module_table、custom),detail跟随展示类型与前端约定好定义】')
    node_status = Column(String(20), comment='节点运行状态')
    start_time = Column(DateTime, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    create_user = Column(String(20), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(20), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(20), comment='版本')

