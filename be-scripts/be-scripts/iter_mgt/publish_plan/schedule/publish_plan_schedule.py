# coding: utf-8
import json
import os
import sys
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from itertools import groupby


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from iter_mgt.publish_plan.executor.executor_enum import NodeStatusEnum

from sqlalchemy import text
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.executor.executor_factory import ExecutorFactory
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan import IterMgtPublishPlan
from iter_mgt.publish_plan.executor.base_executor import BaseObject

from settings import logger


class PublishPlanSchedule:

    @classmethod
    def get_all_nodes(cls) -> list:
        # 获取计划正在等待执行的节点
        all_nodes = []
        with DBConnectionManagerForSqlalchemy() as db:
            # 查询 IterMgtPublishPlanNode 中按照batch_no分组 node_status 为 READY 或 RUNNING 按照node_order字段排序, 并取出前1条的节点
            # 执行查询
            query = """
               SELECT node.*
               FROM iter_mgt_publish_plan_node AS node
               LEFT JOIN iter_mgt_publish_plan AS plan
                   ON node.batch_no = plan.batch_no
               WHERE node.node_status IN (:node_status_running,:node_status_ready)
                   AND node.order_no = (
                       SELECT MIN(order_no)
                       FROM iter_mgt_publish_plan_node AS max_node
                       WHERE max_node.batch_no = node.batch_no and max_node.node_status IN (:node_status_running,:node_status_ready)
                   )
               AND plan.plan_status = :node_status_running
            """
            """
            # 从SQL中注释掉快速失败的逻辑 20240201 by fwm 
            AND NOT EXISTS (
                       SELECT 1
                       FROM iter_mgt_publish_plan_node AS exist_node
                       WHERE exist_node.batch_no = node.batch_no
                           AND exist_node.node_status IN (:node_status_fail, :node_status_stop, :node_status_warn)
                   )
            """
            binds = {
                "node_status_success": NodeStatusEnum.SUCCESS.name,
                "node_status_fail": NodeStatusEnum.FAIL.name,
                "node_status_stop": NodeStatusEnum.STOP.name,
                "node_status_ready": NodeStatusEnum.READY.name,
                "node_status_running": NodeStatusEnum.RUNNING.name,
                "node_status_warn": NodeStatusEnum.WARN.name,
                "plan_status": NodeStatusEnum.RUNNING.name,
            }
            results = db.session.execute(text(query), binds)
            all_nodes_id = [
                result[0]
                for result in results.all()
            ]
            all_nodes = db.session.query(IterMgtPublishPlanNode).filter(
                IterMgtPublishPlanNode.id.in_(all_nodes_id)).all()
            # 找到需要运行的阶段 并找到该阶段需要运行的点
            need_run_nodes = set()
            for node in all_nodes:

                curr_batch_nodes = db.session.query(IterMgtPublishPlanNode).filter(
                    IterMgtPublishPlanNode.batch_no == node.batch_no,
                    IterMgtPublishPlanNode.phase_order == node.phase_order).all()
                success_phase = set()
                max_next_phase_order = 0
                # 利用python group_by 将curr_batch_nodes根据phase分组
                for phase, phase_nodes in groupby(curr_batch_nodes, lambda x: x.phase):
                    phase_nodes = list(phase_nodes)
                    if phase_nodes[0].next_phase_order > max_next_phase_order:
                        max_next_phase_order = phase_nodes[0].next_phase_order
                    phase_nodes.sort(key=lambda x: x.order_no)
                    phase_node_exist = False
                    phase_success_node_num = 0
                    for phase_node in phase_nodes:
                        if phase_node.node_status in [NodeStatusEnum.WARN.name, NodeStatusEnum.STOP.name,
                                                      NodeStatusEnum.FAIL.name]:
                            break
                        if phase_node.node_status in [NodeStatusEnum.READY.name,
                                                      NodeStatusEnum.RUNNING.name] and not phase_node_exist:
                            need_run_nodes.add(phase_node)
                            phase_node_exist = True
                            logger.info("批次:{} 找到阶段: {} 需要运行的节点:{}".format(phase_node.batch_no, phase,
                                                                                        phase_node.node_name))
                        if phase_node.node_status == NodeStatusEnum.SUCCESS.name:
                            phase_success_node_num += 1
                    if len(phase_nodes) == phase_success_node_num:
                        success_phase.add((phase,phase_nodes[0]))

                for phase in success_phase:
                    if phase[1].next_phase_order < max_next_phase_order:
                        next_phase_nodes = db.session.query(IterMgtPublishPlanNode).filter(
                            IterMgtPublishPlanNode.batch_no == phase[1].batch_no,
                            IterMgtPublishPlanNode.phase == phase[1].next_phase).all()
                        phase_nodes.sort(key=lambda x: x.order_no)
                        phase_node_exist = False
                        phase_success_node_num = 0
                        for phase_node in next_phase_nodes:
                            if phase_node.node_status in [NodeStatusEnum.WARN.name, NodeStatusEnum.STOP.name,
                                                          NodeStatusEnum.FAIL.name]:
                                break
                            if phase_node.node_status in [NodeStatusEnum.READY.name,
                                                          NodeStatusEnum.RUNNING.name] and not phase_node_exist:
                                need_run_nodes.add(phase_node)
                                phase_node_exist = True
                                logger.info("批次:{} 找到阶段: {} 需要运行的节点:{}".format(phase_node.batch_no, phase[0],
                                                                                            phase_node.node_name))
                            if phase_node.node_status == NodeStatusEnum.SUCCESS.name:
                                phase_success_node_num += 1
                        if len(phase_nodes) == phase_success_node_num:
                            success_phase.add(phase)
            logger.info("去重后正在等待执行的节点数量:{}".format(len(need_run_nodes)))
        return all_nodes

    @classmethod
    def trigger_plan(cls):
        plan_nodes = cls.get_all_nodes()
        with ThreadPoolExecutor(max_workers=20) as t:
            for exec_node in plan_nodes:
                try:
                    logger.info(f"开始执行批次:{exec_node.batch_no} 阶段: {exec_node.phase} 节点:{exec_node.node_name}")
                    node_name = exec_node.node_name
                    with DBConnectionManagerForSqlalchemy() as db:
                        impp = db.session.query(IterMgtPublishPlan).filter(IterMgtPublishPlan.batch_no==exec_node.batch_no).one_or_none()
                    bo = BaseObject(exec_node, impp)
                    executor = ExecutorFactory.create_executor(exec_node_name=node_name, exec_bo=bo)
                    if executor:
                        # 异步执行节点
                        t.submit(executor.execute)
                except Exception as ex:
                    traceback.print_exc()
                    logger.error("执行节点:{}失败,原因:{}".format(exec_node.node_name, ex))
            t.shutdown()


if __name__ == '__main__':
    try:
        start_time = time.time()
        batch_no = int(start_time * 1000)
        logger.info("新调度批次开始:{}".format(batch_no))
        PublishPlanSchedule().trigger_plan()
        end_time = time.time()
        total_time = end_time - start_time
        logger.info("新调度批次结束:{};总花费时间:{}s ".format(batch_no, total_time))
    except Exception as ex:
        traceback.print_exc()
        exit(1)
