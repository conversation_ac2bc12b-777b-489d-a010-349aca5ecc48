from enum import Enum


class ExecutorEnum(Enum):
    CICD = '构建/部署'
    PUBLISH_APPLY = '产线申请'
    PUBLISH_DECISION = '发布决策'
    PUBLISH = '节点发布'
    AUTO_TEST = '自动化测试'
    ARCHIVE = '归档'

    def __init__(self, executor_name):
        self.executor_name = executor_name


class NodeStatusEnum(Enum):
    READY = ('ready', '等待调用')
    RUNNING = ('running', '正在执行')
    SUCCESS = ('success', '执行成功')
    FAIL = ('fail', '执行失败')
    STOP = ('stop', '终止')
    WARN = ('warn', '警告')

    def __init__(self, status_name, status_name_desc):
        self.status_name = status_name
        self.status_name_desc = status_name_desc


class ArchiveStrategyEnum(Enum):
    FIRST_STRATEGY = ('六小时后', '6')

    def __init__(self, strategy_name, strategy_time):
        self.strategy_name = strategy_name
        self.strategy_time = strategy_time


class ArchiveStrategyResultEnum(Enum):
    CAN_ARCHIVE = '可以归档'
    WAIT_ARCHIVE = '等待归档'
    PUBLISH_TIME_EXCEPTION = '发布时间为空'
    PUBLISH_STATUS_EXCEPTION = '发布状态异常'
    PUBLISH_DATA_EXCEPTION = '发布数据异常'

    def __init__(self, result):
        self.result = result
