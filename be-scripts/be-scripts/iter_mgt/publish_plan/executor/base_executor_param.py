class BaseExecutorParam:
    def __init__(self, **kwargs):
        self._param_key = None
        self._param_value = None
        self._param_cn_name = None
        self._param_show_flag = True
        self._param_show_type = None
        self._param_options_type = None
        self._param_update_disable = False
        self._param_options = None

        for key, value in kwargs.items():
            setattr(self, key, value)

    @property
    def param_key(self):
        return self._param_key

    @param_key.setter
    def param_key(self, value):
        self._param_key = value

    @property
    def param_value(self):
        return self._param_value

    @param_value.setter
    def param_value(self, value):
        self._param_value = value

    @property
    def param_cn_name(self):
        return self._param_cn_name

    @param_cn_name.setter
    def param_cn_name(self, value):
        self._param_cn_name = value

    @property
    def param_show_flag(self):
        return self._param_show_flag

    @param_show_flag.setter
    def param_show_flag(self, value):
        self._param_show_flag = value

    @property
    def param_show_type(self):
        return self._param_show_type

    @param_show_type.setter
    def param_show_type(self, value):
        self._param_show_type = value

    @property
    def param_options_type(self):
        return self._param_options_type

    @param_options_type.setter
    def param_options_type(self, value):
        self._param_options_type = value

    @property
    def param_update_disable(self):
        return self._param_update_disable

    @param_update_disable.setter
    def param_update_disable(self, value):
        self._param_update_disable = value

    @property
    def param_options(self):
        return self._param_options

    @param_options.setter
    def param_options(self, value):
        self._param_options = value

    def __str__(self):
        attribute_list = [attr for attr in dir(self) if not attr.startswith('_')]
        attribute_str_list = [f"{attr}: {getattr(self, attr)!r}" for attr in attribute_list]
        return ', '.join(attribute_str_list)
