import traceback

from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, QueryNodeRunResult, InvokeNodeRunResult, \
    BaseObject
from iter_mgt.publish_plan.executor.executor_enum import ExecutorEnum, NodeStatusEnum
from test_mgt.models import DbMgtSql
from utils.call_external_interface.call_http_api import SpiderHttpCaller


class CicdExecutor(BaseExecutor):
    exec_node_name = ExecutorEnum.CICD.executor_name

    def __init__(self, exec_bo: BaseObject) -> None:
        super().__init__(exec_bo)

    def invoke_node(self) -> InvokeNodeRunResult:
        try:
            for item in self.get_exec_node().node_param:
                if item["param_key"] == "build_suite_code":
                    suite_code = item["param_value"]
                if item["param_key"] == "module_name":
                    module_name = item["param_value"]
                if item["param_key"] == "iter_id":
                    iteration_id = item["param_value"]
            # 1、流水线绑定环境
            bind_result = self.__pipeline_env_bind(iteration_id, module_name, suite_code)

            if bind_result == "success":
                # 2、 触发流水线编译
                build_result, query_param, job_url = self.__jenkins_build(iteration_id, module_name)
                if build_result:
                    node_run_result = {"detail": "运行中", "show_type": "external_link",
                                       "external_link_url": job_url}
                    return InvokeNodeRunResult(NodeStatusEnum.RUNNING.name, query_param, node_run_result)
                else:
                    node_run_result = {"detail": job_url, "show_type": "text"}
                    return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)
            else:
                node_run_result = {"detail": "流水线绑定环境失败！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

    def query_execute_result(self) -> QueryNodeRunResult:
        try:
            query_param = self.get_exec_node().query_node_run_result_param
            if not query_param:
                node_run_result = {"detail": "查询参数为空，稍后重试！", "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
            bussiness_name = "jenkins_pipeline"
            shc = SpiderHttpCaller()
            result = shc.spider_request_get(bussiness_name, query_param)

            if result.get("status") == "success":
                result_data = result.get("data")
                for item in result_data:
                    if item.get("job_name") == query_param.get("job_name"):
                        if int(item.get("job_id")) == int(query_param.get("job_id")):
                            if item.get("status") in ("RUNNING", "SUCCESS"):
                                node_run_result = {"detail": "运行中", "show_type": "external_link",
                                                   "external_link_url": item.get("jenkins_job_url")}
                                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name,
                                                          node_run_result)
                            else:
                                node_run_result = {"detail": "流水线执行失败", "show_type": "text"}
                                return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
                        if int(item.get("job_id")) == int(query_param.get("job_id")) + 1:
                            if item.get("status") == "FAILURE":
                                node_run_result = {"detail": "流水线执行失败", "show_type": "text"}
                                return QueryNodeRunResult(NodeStatusEnum.FAIL.name,
                                                          node_run_result)
                            elif item.get("status") == "RUNNING":
                                node_run_result = {"detail": "运行中", "show_type": "external_link",
                                                   "external_link_url": item.get("jenkins_job_url")}
                                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name,
                                                          node_run_result)
                            elif item.get("status") == "SUCCESS":
                                if self.__get_rapid_is_exist_sql(query_param.get("pipeline_id"),
                                                                 self.get_exec_plan().plan_type):
                                    node_run_result = {"detail": "rapid线不适用有SQL提交的迭代！",
                                                       "show_type": "text"}
                                    return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
                                node_run_result = {"detail": "运行成功", "show_type": "external_link",
                                                   "external_link_url": item.get("jenkins_job_url")}
                                return QueryNodeRunResult(NodeStatusEnum.SUCCESS.name, node_run_result)
            node_run_result = {"detail": result, "show_type": "text"}
            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)

    def __pipeline_env_bind(self, iteration_id, module_name, suite_code):
        params = {
            "job_name": iteration_id + "_" + module_name,
            "app_name": module_name,
            "env": suite_code,
        }
        business_name = "pipeline_env_bind"
        shc = SpiderHttpCaller()
        result = shc.spider_request_post(business_name, params)

        if result:
            return result.get("status")
        else:
            return "failed"

    def __jenkins_build(self, iteration_id, module_name):
        job_name = iteration_id + "_" + module_name
        params = {
            "job_name": job_name,
            "skip": 0,
        }
        business_name = "jenkins_pipeline"
        shc = SpiderHttpCaller()
        result = shc.spider_request_post(business_name, params)

        if result.get("status") == "success":
            result_data = result.get("data")
            for item in result_data:
                if (item.get("job_name") == job_name and item.get("result") == "success"
                        and item.get("msg") == "开始执行"):
                    run_result_param = {
                        "pipeline_id": iteration_id,
                        "get_pipeline_info_type": 'update',
                        "job_name": iteration_id + "_" + module_name,
                        "job_id": item.get("build_number"),
                    }
                    job_url = item.get("job_url")
                    return True, run_result_param, job_url
        return False, "pipeline流水线触发失败！", "pipeline流水线触发失败！"

    def __get_rapid_is_exist_sql(self, pipeline_id, plan_type):
        if plan_type == "rapid":
            if DbMgtSql.select().where(DbMgtSql.iteration_id == pipeline_id).get_or_none():
                return True
        return False


if __name__ == '__main__':
    # params = {
    #     "job_name": "prod-info_1.0.0_prod-mq-pqc-info",
    #     "app_name": "prod-mq-pqc-info",
    #     "env": "test",
    # }
    # business_name = "pipeline_env_bind"
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_post(business_name, params)

    # params = {
    #     "job_name": "prod-info_1.0.0_prod-mq-pqc-info",
    #     "skip": 0,
    # }
    # business_name = "jenkins_pipeline"
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_post(business_name, params)

    params = {
        "job_name": "prod-info_1.0.0_prod-mq-pqc-info",
        "skip": 0,
    }
    shc = SpiderHttpCaller()
    result = shc.spider_request_post("jenkins_pipeline", params)
    # run_result_param = {
    #     "pipeline_id": "prod-info_1.0.0",
    #     "get_pipeline_info_type": 'update',
    #     "job_name": "prod-info_1.0.0_prod-mq-pqc-info",
    #     "job_id": "36",
    # }
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_get("jenkins_pipeline", run_result_param)

    print(result)
