import traceback
from iter_mgt.publish_plan.dao.service.publish_plan_service import query_service_results, query_task_result
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, QueryNodeRunResult, InvokeNodeRunResult, \
    BaseObject
from iter_mgt.publish_plan.executor.executor_enum import ExecutorEnum, NodeStatusEnum
from utils.call_external_interface.call_http_api import SpiderHttpCaller


class PublishApplyExecutor(BaseExecutor):
    exec_node_name = ExecutorEnum.PUBLISH_APPLY.executor_name

    def __init__(self, exec_bo: BaseObject) -> None:
        super().__init__(exec_bo)

    def invoke_node(self) -> InvokeNodeRunResult:
        try:
            exec_node = self.get_exec_node()
            iteration_id = exec_node.iter_id
            app_name_str = exec_node.module_name
            app_name_list = app_name_str.split(",")
            for item in exec_node.node_param:
                if item["param_key"] == "suite_code":
                    suite_code = item["param_value"]
                if item["param_key"] == "commit_sql":
                    sql_list = item["param_value"]
            # 1、调用产线申请责任链
            action_id = self.__publish_apply_check(suite_code, iteration_id, app_name_list, sql_list)

            if not action_id:
                run_result_param = {"detail": "产线申请检查接口调用异常！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, run_result_param)

            # 2、根据action_id查询产线申请责任链结果，超时时间5分钟
            query_result, detail = query_task_result(action_id)
            if not query_result:
                run_result_param = {"detail": detail, "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, run_result_param)

            # 3、调用产线申请接口
            publish_result, detail = self.__publish_apply(iteration_id, suite_code, app_name_str)
            if publish_result:
                run_result_param = {"sid": detail}
                return InvokeNodeRunResult(NodeStatusEnum.RUNNING.name, run_result_param)
            else:
                run_result_param = {"detail": detail, "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, run_result_param)

        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

    def query_execute_result(self) -> QueryNodeRunResult:
        try:
            query_param = self.get_exec_node().query_node_run_result_param
            if not query_param:
                node_run_result = {"detail": "查询参数为空，稍后重试！", "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
            sid = query_param.get("sid")
            query_result, detail = query_service_results(sid)
            node_run_result = {"detail": detail, "show_type": "text"}
            if query_result:
                return QueryNodeRunResult(NodeStatusEnum.SUCCESS.name, node_run_result)
            else:
                return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)

        except Exception as e:
            traceback.print_exc()
            query_node_param = {"detail": str(e), "show_type": "text"}
            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, query_node_param)

    def __publish_apply_check(self, suite_code, iteration_id, app_name_list, sql_list):
        #sql_list所有应用都给
        apply_list = []
        for item in app_name_list:
            apply_list.append({
                'suite_code': suite_code,
                'iteration_id': iteration_id,
                'action_item': "servers_publish_apply",
                'app_name': item,
                "sql_check_list": sql_list
            })

        business_name = "servers_publish_apply_api"
        shc = SpiderHttpCaller()
        result = shc.spider_request_post(business_name, apply_list)

        if result.get("status") == "success":
            result_data = result.get("data")
            if result_data:
                return result_data.get("action_id")
        return False

    def __publish_apply(self, iteration_id, suite_code, app_name_str):
        business_name = "publish_apply"
        apply_param = {
            "pipeline_id": iteration_id,
            "env": suite_code,
            "app_name_list": app_name_str,
            "send_email": True,
        }
        shc = SpiderHttpCaller()
        result = shc.spider_request_post(business_name, apply_param)

        if result.get("status") == "success":
            result_data = result.get("data")
            if result_data:
                return True, result_data.get("sid")
        return False, result


if __name__ == '__main__':
    # query_param = {"pipeline_id": "prod-info_1.0.0",
    #             "get_pipeline_info_type": 'update',
    #             "job_name": "prod-info_1.0.0_prod-mq-pqc-info",}
    #
    # result = SpiderHttpCaller.spider_request_get("jenkins_pipeline", query_param)

    # apply_list = [{
    #     'suite_code': 'prod',
    #     'iteration_id': "prod-info_1.0.0",
    #     'action_item': "servers_publish_apply",
    #     'app_name': "prod-mq-pqc-info",
    #     "sql_check_list": []
    # }]
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_post("servers_publish_apply_api", apply_list)

    business_name = "publish_apply"
    apply_param = {
        "pipeline_id": "prod-info_1.0.0",
        "env": "prod",
        "app_name_list": "prod-mq-pqc-info",
        "send_email": True,
    }
    shc = SpiderHttpCaller()
    result = shc.spider_request_post(business_name, apply_param)

    print(result)
