class BaseExecutorParam:
    def __init__(self, **kwargs):
        self._detail = None
        self._show_type = None
        self._external_link_url = None
        for key, value in kwargs.items():
            setattr(self, key, value)

    @property
    def detail(self):
        return self._detail

    @detail.setter
    def detail(self, value):
        self._detail = value

    @property
    def show_type(self):
        return self._show_type

    @show_type.setter
    def show_type(self, value):
        self._show_type = value

    @property
    def external_link_url(self):
        return self._external_link_url

    @external_link_url.setter
    def external_link_url(self, value):
        self._external_link_url = value

    def __str__(self):
        attribute_list = [attr for attr in dir(self) if not attr.startswith('_')]
        attribute_str_list = [f"{attr}: {getattr(self, attr)!r}" for attr in attribute_list]
        return ', '.join(attribute_str_list)
