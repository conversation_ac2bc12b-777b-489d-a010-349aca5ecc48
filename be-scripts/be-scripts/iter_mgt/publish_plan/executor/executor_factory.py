import gc
import inspect

from iter_mgt.publish_plan.executor.executor_imports import *
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, BaseObject
from settings import logger


class ExecutorFactory:
    @staticmethod
    def create_executor(exec_node_name: str, exec_bo: BaseObject):
        # 获取当前所有的对象
        all_objects = gc.get_objects()
        for obj in all_objects:
            # 检查对象是否是一个类
            isclass = False
            try:
                isclass = inspect.isclass(obj)
            except Exception as e:
                pass
            if isclass:
                # 检查类是否是BaseExecutor的子类
                if issubclass(obj, BaseExecutor) and obj is not BaseExecutor and obj.exec_node_name == exec_node_name:
                    return obj(exec_bo=exec_bo)
        logger.error("执行器未找到实现:{}".format(exec_node_name))
