import traceback
from datetime import datetime, timedelta

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from iter_mgt.models import IterInfo
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.dao.service.publish_plan_service import query_service_results, get_app_count_in_iteration
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, QueryNodeRunResult, InvokeNodeRunResult, \
    BaseObject
from iter_mgt.publish_plan.executor.executor_enum import ExecutorEnum, NodeStatusEnum, ArchiveStrategyEnum, \
    ArchiveStrategyResultEnum
from test_publish_aio.test_suite_init_constants import PROCESS_CODE_PROD
from utils.call_external_interface.call_http_api import SpiderHttpCaller


class ArchiveExecutor(BaseExecutor):
    exec_node_name = ExecutorEnum.ARCHIVE.executor_name

    def __init__(self, exec_bo: BaseObject) -> None:
        super().__init__(exec_bo)

    def invoke_node(self) -> InvokeNodeRunResult:
        try:
            exec_node = self.get_exec_node()
            iteration_id = exec_node.iter_id
            app_name_list = exec_node.module_name.split(",")
            for item in exec_node.node_param:
                if item["param_key"] == "archive_strategy":
                    archive_strategy = item["param_value"]
                app_threshold = item["app_threshold"]
            # 0-1、 检查测试报告的通过率是否达到阈值
            for app_name in app_name_list:
                threshold = app_threshold.get(app_name)
                result, detail = self.__check_iter_test_report_result(iteration_id, app_name, threshold)
                if not result:
                    node_run_result = {"detail": detail, "show_type": "text"}
                    return InvokeNodeRunResult(NodeStatusEnum.WARN.name, {}, node_run_result)

            # 0-2、检查迭代是否已经归档
            if self.__check_iteration_is_archived(iteration_id):
                node_run_result = {"detail": "该迭代已经被手动归档！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.WARN.name, {}, node_run_result)

            # 0-3、检查迭代下是否含多应用
            # if self.__check_app_number_in_iteration(iteration_id) > 1:
            #     node_run_result = {"detail": "该迭代下存在多个应用，不允许自动归档！", "show_type": "text"}
            #     return InvokeNodeRunResult(NodeStatusEnum.WARN.name, {}, node_run_result)

            # 0-4、检查归档时间是否到了
            check_result = self.__check_archive_time(archive_strategy)
            if check_result == ArchiveStrategyResultEnum.WAIT_ARCHIVE:
                node_run_result = {"detail": "归档时间未到！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.READY.name, {}, node_run_result)
            elif check_result == ArchiveStrategyResultEnum.PUBLISH_TIME_EXCEPTION:
                node_run_result = {"detail": "发布时间异常！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)
            elif check_result == ArchiveStrategyResultEnum.PUBLISH_DATA_EXCEPTION:
                node_run_result = {"detail": "发布数据异常！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

            # 1、执行归档check

            sid = self.__archive_check(iteration_id)
            if not sid:
                node_run_result = {"detail": "归档检查接口调用异常！", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

            # 2、查询归档检查接口
            query_result, detail = self.__query_archive_check_result(sid)
            if not query_result:
                node_run_result = {"detail": detail, "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

            # 3、执行配置一致性校验
            for app_name in app_name_list:
                check_result, detail = self.__check_config_consistent(iteration_id, app_name)
                if not check_result:
                    node_run_result = {"detail": detail, "show_type": "text"}
                    return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

            # 4、执行配置归档
            archive_result, detail = self.__archive_zeus_config(iteration_id)
            if not archive_result:
                node_run_result = {"detail": detail, "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

            # 5、执行迭代归档
            sid = self.__iteration_archive(iteration_id)
            if sid:
                run_result_param = {"sid": sid}
                return InvokeNodeRunResult(NodeStatusEnum.RUNNING.name, run_result_param)
            else:
                node_run_result = {"detail": "归档失败，spider调用be-scripts异常", "show_type": "text"}
                return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)
        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

    def query_execute_result(self) -> QueryNodeRunResult:
        try:
            query_param = self.get_exec_node().query_node_run_result_param
            if not query_param:
                node_run_result = {"detail": "查询参数为空，稍后重试！", "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.READY.name, node_run_result)
            sid = query_param.get("sid")
            query_result, detail = query_service_results(sid)
            node_run_result = {"detail": detail, "show_type": "text"}
            if query_result:
                return QueryNodeRunResult(NodeStatusEnum.SUCCESS.name, node_run_result)
            else:
                return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
        except Exception as e:
            traceback.print_exc()
            query_node_param = {"detail": str(e), "show_type": "text"}
            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, query_node_param)

    def __check_app_number_in_iteration(self, iteration_id):
        return get_app_count_in_iteration(iteration_id)

    def __check_iteration_is_archived(self, iteration_id):
        obj = IterInfo.get_or_none(IterInfo.pipeline_id == iteration_id,
                                   IterInfo.br_status == 'close')
        if obj:
            return True
        else:
            return False

    def __check_archive_time(self, archive_strategy):
        batch_no = self.get_exec_node().batch_no
        with (DBConnectionManagerForSqlalchemy() as db):

            obj = db.session.query(IterMgtPublishPlanNode).filter(IterMgtPublishPlanNode.batch_no == batch_no,
                                                                  IterMgtPublishPlanNode.node_name == ExecutorEnum.PUBLISH.executor_name).first()
            if obj:
                publish_end_time = obj.end_time
                if publish_end_time:
                    archive_time = publish_end_time + timedelta(hours=int(ArchiveStrategyEnum[
                                                                              archive_strategy].strategy_time))
                    if archive_time < datetime.now() - timedelta(minutes=1):
                        return ArchiveStrategyResultEnum.CAN_ARCHIVE
                    else:
                        return ArchiveStrategyResultEnum.WAIT_ARCHIVE
                return ArchiveStrategyResultEnum.PUBLISH_TIME_EXCEPTION
        return ArchiveStrategyResultEnum.PUBLISH_DATA_EXCEPTION

    def __archive_check(self, iteration_id):
        params = {
            "iteration_id": iteration_id,
        }
        business_name = "archive_check_api"
        shc = SpiderHttpCaller()
        result = shc.spider_request_get(business_name, params)

        if result.get("status") == "success":
            result_data = result.get("data")
            if result_data:
                sid = result_data.get("sid")
                return sid
        return False

    def __query_archive_check_result(self, sid):
        return query_service_results(sid)

    def __check_config_consistent(self, iteration_id, app_name):
        params = {
            "iteration_id": iteration_id,
            "app_name[]": [app_name],
            "env": PROCESS_CODE_PROD
        }
        business_name = "check_config_consistent_api"
        shc = SpiderHttpCaller()
        result = shc.spider_request_get(business_name, params)
        if result.get("status") == "success":
            result_msg = result.get("msg")
            if result_msg:
                if result_msg.get("code") == 'success' and result_msg.get("block") == 'false':
                    return True, result_msg.get("message")
                else:
                    return False, result_msg.get("message")

        return False, "zuse接口请求异常！"

    def __archive_zeus_config(self, iteration_id):
        params = {
            "iteration_id": iteration_id
        }
        business_name = "archive_zeus_config"
        shc = SpiderHttpCaller()
        result = shc.spider_request_put(business_name, params)
        if result.get("status") == "success":
            return True, "宙斯配置归档成功"
        else:
            return False, "宙斯归档失败，原因:{}".format(result.get("msg"))

    def __iteration_archive(self, iteration_id):
        params = {
            "iteration_id": iteration_id
        }
        business_name = "iteration_archive"
        shc = SpiderHttpCaller()
        result = shc.spider_request_put(business_name, params)
        if result.get("status") == "success":
            result_data = result.get("data")
            if result_data:
                sid = result_data.get("sid")
                return sid
        return False

    def __check_iter_test_report_result(self, iteration_id, app_name, threshold):
        """
        检查测试报告的通过率是否达到阈值
        :param iteration_id:
        :param threshold:
        :return:
        """
        params = {"iteration_id": iteration_id}
        business_name = "check_iter_test_report_result"
        shc = SpiderHttpCaller()
        result = shc.spider_request_post(business_name, params)
        if result.get("status") == "success":
            result_data = result.get("data")
            if result_data:
                app_report = result_data.get("app_report")
                for app_report_detail in app_report:
                    if app_report_detail.get("module_name") == app_name:
                        pass_rate = app_report_detail.get("pass_rate").split("%")[0] if app_report_detail.get(
                            "pass_rate").endswith("%") else app_report_detail.get("pass_rate")
                        if float(pass_rate) < float(threshold):
                            return False, "{}的通过率为{}, 未达到阈值，不允许自动归档！".format(
                                app_report_detail.get("report_title"),
                                app_report_detail.get("pass_rate"))
                return True, "测试报告通过率达到阈值！"
            return False, "测试报告数据为空！"
        return False, "测试报告接口请求异常！"



if __name__ == '__main__':
    # query_param = {"pipeline_id": "prod-info_1.0.0",
    #             "get_pipeline_info_type": 'update',
    #             "job_name": "prod-info_1.0.0_prod-mq-pqc-info",}
    #
    # result = SpiderHttpCaller.spider_request_get("jenkins_pipeline", query_param)

    # business_name = "archive_check_api"
    # params = {
    #     "iteration_id": "prod-info_1.0.0",
    #     "app_name": "prod-mq-pqc-info",
    #     "env": 'prod'
    # }
    # business_name = "check_config_consistent_api"
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_get(business_name, params)
    #
    # print(result)

    str1 = '99.9'
    print(float(str1))
