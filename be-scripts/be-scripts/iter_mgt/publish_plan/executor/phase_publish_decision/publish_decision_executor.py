import datetime
import traceback

from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.dao.model.models import PublishApplicationConfirmation
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, QueryNodeRunResult, InvokeNodeRunResult, \
    BaseObject
from iter_mgt.publish_plan.executor.executor_enum import ExecutorEnum, NodeStatusEnum

from settings import INTERFACE_URL
from utils.call_external_interface.call_http_api import SpiderHttpCaller


class PublishDecisionExecutor(BaseExecutor):
    exec_node_name = ExecutorEnum.PUBLISH_DECISION.executor_name

    def __init__(self, exec_bo: BaseObject) -> None:
        super().__init__(exec_bo)

    def invoke_node(self) -> InvokeNodeRunResult:
        try:
            exec_node = self.get_exec_node()
            iteration_id = exec_node.iter_id
            for item in self.get_exec_node().node_param:
                if item["param_key"] == "suite_code":
                    suite_code = item["param_value"]
                if item["param_key"] == "receiver":
                    receiver = item["param_value"]

            params = {
                'pipeline_id': iteration_id,
                'receiver': receiver,
                'domain': INTERFACE_URL.get("spider"),
                'suite_name': suite_code,
                "warn_info": ''
            }

            business_name = "publish_apply_email"
            shc = SpiderHttpCaller()
            result = shc.spider_request_post(business_name, params)

            if result.get("status") == "success":
                run_result_param = {"result": result.get("msg"), "pipeline_id": iteration_id}
                return InvokeNodeRunResult(NodeStatusEnum.RUNNING.name, run_result_param)
            node_run_result = {"detail": result, "show_type": "text"}
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)
        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

    def query_execute_result(self) -> QueryNodeRunResult:
        try:
            query_param = self.get_exec_node().query_node_run_result_param
            if not query_param:
                node_run_result = {"detail": "查询参数为空，稍后重试！", "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
            pipeline_id = query_param.get("pipeline_id")
            today_date = datetime.datetime.today().date()
            objs = PublishApplicationConfirmation.get_or_none(iteration_id=pipeline_id, affirm_time__gt=today_date, status="success")
            if objs:
                node_run_result = {"detail": "确认人：{}".format(objs.assertor), "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.SUCCESS.name, node_run_result)
            else:
                node_run_result = {"detail": "确认人今日未确认！", "show_type": "text"}

                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}

            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)


if __name__ == '__main__':
    params = {
        'pipeline_id': "prod-info_1.0.0",
        'receiver': '<EMAIL>',
        'domain': 'http://appdeploy-test.howbuy.pa',
        'suite_name': 'prod',
        "warn_info": ''
    }

    business_name = "publish_apply_email"
    shc = SpiderHttpCaller()
    result = shc.spider_request_post(business_name, params)

    print(result)
