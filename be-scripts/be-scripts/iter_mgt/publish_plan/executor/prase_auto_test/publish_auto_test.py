import datetime
import json
import traceback
import jenkins

from time import sleep
from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
from dao.connect.mysql import DBConnectionManager
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from iter_mgt.models import BizTestAppNeed
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, QueryNodeRunResult, InvokeNodeRunResult, \
    BaseObject
from iter_mgt.publish_plan.executor.executor_enum import ExecutorEnum, NodeStatusEnum
from jenkins_mgt.models import BizTestFlowScheduleConfig, BizTestFlowGlobalParam, JenkinsInfo, JenkinsMgtJobComposition, \
    JenkinsMgtJobCompositionDetail
from job.jenkins.test_data_dev.models import BizTestFlowExecHistory
from settings import logger, BIZ_TEST
from utils.check.validator import <PERSON>TestChecker
from utils.public_utils import AppAutoTestCheckParam


class AutoTestExecutor(BaseExecutor):
    exec_node_name = ExecutorEnum.AUTO_TEST.executor_name

    def __init__(self, exec_bo: BaseObject) -> None:
        super().__init__(exec_bo)

    def invoke_node(self) -> InvokeNodeRunResult:
        try:
            node_params = self.get_exec_node().node_param
            iteration_id = self.get_exec_node().iter_id
            params_list = list()
            execute_pipeline = list()
            for node_param in node_params:
                execute_auto_test = node_param['execute_auto_test']
                if not execute_auto_test:
                    continue
                module_name = node_param['module_name']
                module_params = node_param['module_params']
                for item in module_params:
                    if item["param_key"] == "suite_code":
                        suite_code = item["param_value"]
                    if item["param_key"] == "biz_iter_branch":
                        biz_iter_branch = item["param_value"]
                    if item["param_key"] == "biz_flow_name":
                        biz_flow_name = item["param_value"]
                    if item["param_key"] == "threshold":
                        pass_rate_threshold = item["param_value"]
                # 1、找schedule_config_id,此处切割是因为前端保存数据发生变更导致
                if biz_flow_name in execute_pipeline:
                    continue
                else:
                    execute_pipeline.append(biz_flow_name)
                biz_code = biz_iter_branch.split('_')[0]
                br_name = biz_iter_branch.split('_')[1]
                flow_name = biz_flow_name.split('_')[1]
                schedule_config_id = self.__get_schedule_config_id(br_name, flow_name, suite_code)

                if not schedule_config_id:
                    node_run_result = {
                        "detail": "没找到biz_iter_branch={}, biz_flow_name={}, suite_code={}对应的schedule_config_id".format(
                            biz_iter_branch, biz_flow_name, suite_code), "show_type": "text"}
                    return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)
                # 2、将本次的应用分支存入迭代自动化的全局参数中
                if not self.__set_flow_global_param(schedule_config_id, iteration_id, module_name):
                    node_run_result = {"detail": "设置全局参数失败", "show_type": "text"}
                    return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)
                # 3、触发jenkins_job
                build_result, jenkins_schedule_url = self.__build_auto_test_job(schedule_config_id)
                if not build_result:
                    node_run_result = {"detail": "构建自动化测试失败", "show_type": "external_link",
                                       "external_link_url": jenkins_schedule_url}
                    return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

                biz_type = self.__get_biz_type(br_name, flow_name)

                if not biz_type:
                    node_run_result = {
                        "detail": "没找到biz_iter_branch={}, biz_flow_name={}对应的biz_type".format(
                            biz_iter_branch, biz_flow_name), "show_type": "text"}
                    return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

                with DBConnectionManagerForSqlalchemy() as db:
                    exec_history = db.session.query(BizTestFlowExecHistory).filter(
                        BizTestFlowExecHistory.biz_type == biz_type,
                        BizTestFlowExecHistory.exec_suite_code == suite_code,
                        BizTestFlowExecHistory.exec_action_type == 'flow_schedule').order_by(
                        BizTestFlowExecHistory.id.desc()).first()
                    if not exec_history:
                        node_run_result = {
                            "detail": "biz_test_flow_exec_history中没找到biz_type={}, suite_code={}, exec_action_type='flow_schedule'的最新执行记录".format(
                                biz_type, suite_code), "show_type": "text"}
                        return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

                    batch_no = exec_history.exec_detail_param.get("batch_no")

                    if not batch_no:
                        node_run_result = {"detail": "没找batch_no", "show_type": "text"}
                        return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

                    composition = db.session.query(JenkinsMgtJobComposition).filter(
                        JenkinsMgtJobComposition.batch_no == batch_no).one_or_none()

                    if not composition:
                        node_run_result = {
                            "detail": "找不到batch_no={}对应的组合线记录".format(batch_no),
                            "show_type": "text"}
                        return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

                    job_url = composition.job_url.format(job_build_id=composition.job_build_id)
                    node_run_result = {"detail": "触发组合线成功", "show_type": "external_link",
                                       "external_link_url": job_url}
                    self.insert_or_update_app_need(biz_code, biz_flow_name, module_name, pass_rate_threshold)

                    query_params = {"batch_no": batch_no, "job_url": job_url,
                                    "iteration_id": iteration_id,
                                    "biz_code": biz_code,
                                    "biz_flow_name": flow_name,
                                    "suite_code": suite_code,
                                    "threshold": pass_rate_threshold,
                                    "app_name_list": [module_name]}
                    params_list.append(query_params)
            return InvokeNodeRunResult(NodeStatusEnum.RUNNING.name, params_list,
                                       node_run_result)
        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, {}, node_run_result)

    def insert_or_update_app_need(self, biz_code, biz_flow_name, module_name, pass_rate_threshold):
        cur_time = datetime.datetime.now()
        p, created = BizTestAppNeed.get_or_create(module_name=module_name,
                                                  defaults={
                                                      'biz_code': biz_code,
                                                      'biz_flow_name': biz_flow_name,
                                                      "need_auto_test": 1,
                                                      "pass_rate_threshold": pass_rate_threshold,
                                                      'create_user': 'howbuyscm',
                                                      'create_time': cur_time})
        if not created:
            BizTestAppNeed.update(pass_rate_threshold=pass_rate_threshold,
                                  update_user="howbuyscm",
                                  update_time=cur_time) \
                .where(BizTestAppNeed.module_name == module_name).execute()

    def query_execute_result(self) -> QueryNodeRunResult:
        try:
            exec_node = self.get_exec_node()
            query_params = exec_node.query_node_run_result_param
            if not query_params:
                node_run_result = {"detail": "查询参数为空，稍后重试！", "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
            plan_node_id = exec_node.id
            iteration_id = exec_node.iter_id
            schedule_time = exec_node.schedule_time
            for query_param in query_params:
                batch_no = query_param.get("batch_no")
                job_url = query_param.get("job_url")
                app_name_list = query_param.get("app_name_list")
                biz_code = query_param.get("biz_code")
                biz_flow_name = query_param.get("biz_flow_name")
                suite_code = query_param.get("suite_code")
                pass_rate_threshold = query_param.get("threshold")
                with DBConnectionManagerForSqlalchemy() as db:
                    composition_detail = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                        JenkinsMgtJobCompositionDetail.batch_no == batch_no).all()
                    status_list = [item.status for item in composition_detail]
                    if ("failure" in status_list) or ("aborted" in status_list):
                        node_run_result = {"detail": "触发成功，执行失败或暂停", "show_type": "external_link",
                                           "external_link_url": job_url}
                        return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
                    elif "waiting" in status_list:
                        node_run_result = {"detail": "触发成功,请等待", "show_type": "external_link",
                                           "external_link_url": job_url}
                        return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
                    else:
                        # 查询测试集结果
                        result_msg = ""
                        br_name = iteration_id.split("_")[1]
                        execute_result = NodeStatusEnum.FAIL.name
                        for app_name in app_name_list:
                            execute_status = AutoTestChecker._check_app_auto_test_status(app_name, br_name, biz_code, biz_flow_name, suite_code)
                            if NodeStatusEnum.RUNNING.status_name == execute_status:
                                execute_result = NodeStatusEnum.RUNNING.name
                                result_msg = "测试集结果暂未找到:{}".format(execute_status)
                            elif 'failure' == execute_status:
                                execute_result = NodeStatusEnum.FAIL.name
                                result_msg = "测试集执行结果：失败"
                            else:
                                app_auto_test_check_param = AppAutoTestCheckParam(business_name="publish_apply_check",
                                                                                  biz_flow_name=biz_flow_name,
                                                                                  suite_code=None,
                                                                                  pass_rate_threshold=pass_rate_threshold,
                                                                                  app_name=app_name,
                                                                                  br_name=br_name,
                                                                                  iteration_id=iteration_id,
                                                                                  biz_code=biz_code)
                                status, msg = AutoTestChecker.check_app_auto_test_result(app_auto_test_check_param)
                                logger.info('应用{}的自动化测试结果为{},msg:{}'.format(app_name, status, msg))
                                result_msg = msg
                                if status == NodeStatusEnum.SUCCESS.status_name:
                                    execute_result = NodeStatusEnum.SUCCESS.name

                                elif 'failed' == status:
                                    execute_result = NodeStatusEnum.FAIL.name
                                else:
                                    execute_result = NodeStatusEnum.RUNNING.name
                                    break

                        if execute_result == NodeStatusEnum.RUNNING.name:
                            current_time = datetime.datetime.now()
                            if not schedule_time:
                                execute_result = NodeStatusEnum.RUNNING.name
                                db.session.query(IterMgtPublishPlanNode).filter(IterMgtPublishPlanNode.id == plan_node_id) \
                                    .update({"schedule_time": current_time})
                                db.session.commit()
                            else:
                                delta = current_time - schedule_time
                                minutes_diff = int(delta.total_seconds() / 60)
                                logger.info('测试集执行结果max等待时间:{}'.format(minutes_diff))
                                if minutes_diff > int(BIZ_TEST['max_wait_minute']):
                                    execute_result = NodeStatusEnum.FAIL.name
                                    result_msg = "测试集执行结果等待时间大于阈值"
                        node_run_result = {"detail": result_msg, "show_type": "text"}
                        if execute_result != NodeStatusEnum.SUCCESS.name:
                            return QueryNodeRunResult(execute_result, node_run_result)
            return QueryNodeRunResult(execute_result, node_run_result)

        except Exception as e:
            traceback.print_exc()
            node_run_result = {"detail": str(e), "show_type": "text"}
            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)

    def __get_schedule_config_id(self, biz_iter_branch, biz_flow_name, suite_code):
        with DBConnectionManagerForSqlalchemy() as db:
            schedule_config_info = db.session.query(BizTestFlowScheduleConfig).filter(
                BizTestFlowScheduleConfig.biz_iter_branch == biz_iter_branch,
                BizTestFlowScheduleConfig.biz_flow_name == biz_flow_name,
                BizTestFlowScheduleConfig.suite_code == suite_code).one_or_none()
            schedule_config_id = schedule_config_info.id if schedule_config_info else None
        return schedule_config_id

    def __set_flow_global_param(self, schedule_config_id, iteration_id, module_name):
        iter_info = BranchesModel.get(BranchesModel.pipeline_id == iteration_id)
        app_branch_name = iter_info.br_name
        with DBConnectionManagerForSqlalchemy() as db:
            global_param_info = db.session.query(BizTestFlowGlobalParam).filter(
                BizTestFlowGlobalParam.schedule_config_id == schedule_config_id,
                BizTestFlowGlobalParam.param_type == 'test_apps').one_or_none()
            if global_param_info:
                param = global_param_info.param
                found = False
                for item in param:
                    if item["app_module_name"] == module_name:
                        item["br_name"] = app_branch_name
                        found = True
                        break

                if not found:
                    new_dict = {"br_name": app_branch_name, "app_module_name": module_name}
                    param.append(new_dict)
                db.session.query(BizTestFlowGlobalParam).filter(
                    BizTestFlowGlobalParam.schedule_config_id == schedule_config_id,
                    BizTestFlowGlobalParam.param_type == 'test_apps').update({"param": param})
                db.session.commit()
                return True
        return False

    def __build_auto_test_job(self, schedule_config_id):
        with DBConnectionManagerForSqlalchemy() as db:
            schedule_config_info = db.session.query(BizTestFlowScheduleConfig).filter(
                BizTestFlowScheduleConfig.id == schedule_config_id).one()
            jenkins_info_id = schedule_config_info.jenkins_info_id
            jenkins_schedule_url = schedule_config_info.jenkins_url
            job_name = "__".join([schedule_config_info.biz_iter_branch, schedule_config_info.biz_flow_name,
                                  schedule_config_info.suite_code])
            jenkins_info = JenkinsInfo.get_or_none(JenkinsInfo.id == jenkins_info_id)
            server = jenkins.Jenkins(jenkins_info.jenkins_url, username=jenkins_info.username,
                                     password=jenkins_info.password, timeout=50000)
            next_build_number = server.get_job_info(job_name)['nextBuildNumber']
            job_num = server.build_job(job_name, parameters={"schedule_config_id":schedule_config_id})
            logger.info('job_name:{}, job_num:{}'.format(job_name, job_num))
            if not next_build_number:
                next_build_number = 1

            sleep(10)
            limit = 300
            while limit > 0:
                sleep(5)
                limit -= 5
                build_info = server.get_build_info(job_name, next_build_number)
                logger.info('get_build_info:{}'.format(json.dumps(build_info)))
                if build_info.get("result") is None:
                    continue
                elif build_info.get("result") == "SUCCESS":
                    break
                elif build_info.get("result") == "RUNNING":
                    continue
                else:
                    return False, jenkins_schedule_url
            return True, jenkins_schedule_url

    def __get_biz_type(self, biz_iter_branch, biz_flow_name):
        sql = """
                SELECT CONCAT(t.biz_test_iter_id, '_', i.biz_pipeline_name) AS biz_type 
                FROM biz_test_flow_info i
                LEFT JOIN biz_test_iter t ON i.biz_code = t.biz_code
                WHERE t.biz_test_iter_br = '{}' AND i.biz_flow_name = '{}';
              """.format(biz_iter_branch, biz_flow_name)
        biz_type = None
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            for row in db.cur.fetchall():
                biz_type = row.get("biz_type")
        return biz_type


if __name__ == '__main__':
    # query_param = {"pipeline_id": "prod-info_1.0.0",
    #             "get_pipeline_info_type": 'update',
    #             "job_name": "prod-info_1.0.0_prod-mq-pqc-info",}
    #
    # result = SpiderHttpCaller.spider_request_get("jenkins_pipeline", query_param)

    # apply_list = [{
    #     'suite_code': 'prod',
    #     'iteration_id': "prod-info_1.0.0",
    #     'action_item': "servers_publish_apply",
    #     'app_name': "prod-mq-pqc-info",
    #     "sql_check_list": []
    # }]
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_post("servers_publish_apply_api", apply_list)

    # business_name = "publish_apply"
    # apply_param = {
    #     "pipeline_id": "prod-info_1.0.0",
    #     "env": "prod",
    #     "app_name_list": "prod-mq-pqc-info",
    #     "send_email": True,
    # }
    # shc = SpiderHttpCaller()
    # result = shc.spider_request_post(business_name, apply_param)
    #
    # print(result)

    jenkins_url = 'http://jenkinstest-s1.howbuy.pa/jenkins/job/品控master__空编排__it29/'
    job_name = jenkins_url.split("/")[-2]
    print(job_name)
