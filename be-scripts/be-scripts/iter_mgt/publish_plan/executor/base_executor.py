import traceback
from datetime import datetime, timedelta
from time import sleep

from sqlalchemy import func, text

from dao.connect.mysql import DBConnectionManager
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan import IterMgtPublishPlan
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.executor.executor_enum import NodeStatusEnum
from settings import logger


class NodeRunResult:
    def __init__(self, node_status) -> None:
        self.__node_status = node_status

    def set_node_status(self, value):
        self.__node_status = value

    def get_node_status(self):
        return self.__node_status


class QueryNodeRunResult(NodeRunResult):
    def __init__(self, node_status, node_run_result: dict) -> None:
        self.__node_run_result = node_run_result
        super().__init__(node_status)

    def set_node_run_result(self, value):
        self.__node_run_result = value

    def get_node_run_result(self):
        return self.__node_run_result


class InvokeNodeRunResult(NodeRunResult):
    def __init__(self, node_status, query_node_run_result_param: dict, node_run_result: dict = None) -> None:
        self.__query_node_run_result_param = query_node_run_result_param
        self.__node_run_result = node_run_result
        super().__init__(node_status)

    def set_query_node_run_result_param(self, value):
        self.__query_node_run_result_param = value

    def get_query_node_run_result_param(self):
        return self.__query_node_run_result_param

    def set_node_run_result(self, value):
        self.__node_run_result = value

    def get_node_run_result(self):
        return self.__node_run_result


class BaseObject:
    _exec_node: IterMgtPublishPlanNode = None
    _exec_plan: IterMgtPublishPlan = None

    def __init__(self, exec_node: IterMgtPublishPlanNode, exec_plan: IterMgtPublishPlan) -> None:
        self._exec_node = exec_node
        self._exec_plan = exec_plan


class BaseExecutor:
    __exec_node_name = ""
    __exec_base_object: BaseObject = None

    def __init__(self, exec_bo: BaseObject) -> None:
        self.__exec_bo = exec_bo
        logger.info("init executor:{}".format(self.__exec_node_name))
        logger.info("executor param:{}".format(self.__exec_bo.__dict__))

    def get_exec_node_name(self):
        return self.__exec_node_name

    def set_exec_node_name(self, value):
        self.__exec_node_name = value

    def get_exec_bo(self):
        return self.__exec_bo

    def set_exec_bo(self, value):
        self.__exec_bo = value

    def set_exec_node(self, value):
        self.__exec_bo._exec_node = value

    def get_exec_node(self):
        return self.__exec_bo._exec_node

    def set_exec_plan(self, value):
        self.__exec_bo._exec_plan = value

    def get_exec_plan(self):
        return self.__exec_bo._exec_plan

    def execute(self) -> None:
        try:
            # 判斷當前時間有沒有到達 self.__exec_node的schedule_time時刻的前後1分鐘
            if self.__exec_bo._exec_node.schedule_time and self.__exec_bo._exec_node.schedule_time > datetime.now() - timedelta(
                    minutes=1):
                logger.info("当前时间小于schedule_time，不执行")
                return
            if self.__exec_bo._exec_node.node_status not in [NodeStatusEnum.READY.name, NodeStatusEnum.RUNNING.name]:
                logger.info("当前状态为{}，不执行".format(self.__exec_bo._exec_node.node_status))
                return
            with DBConnectionManager() as db:
                # 创建一个游标对象
                cursor = db.connection.cursor()
                connection = db.connection
                # 开启事务
                connection.begin()
                # 查询需要加锁的数据
                select_query = "SELECT * FROM iter_mgt_publish_plan_node WHERE node_status = '{}' and id={} FOR UPDATE".format(
                    NodeStatusEnum.READY.name, self.__exec_bo._exec_node.id)
                cursor.execute(select_query)
                # 获取查询结果
                results = cursor.fetchall()
                # 如果有查询结果，即获取到了锁
                if results:
                    # 更新status字段
                    update_query = "UPDATE iter_mgt_publish_plan_node SET node_status = '{}', start_time = now() WHERE node_status = '{}' and id={}".format(
                        NodeStatusEnum.RUNNING.name,
                        NodeStatusEnum.READY.name, self.__exec_bo._exec_node.id)
                    cursor.execute(update_query)
                    # 获取更新的行数
                    rows_updated = cursor.rowcount
                    # 提交事务
                    connection.commit()
                    if rows_updated == 1:
                        # 处理事务（執行）
                        invoke_node_run_result: InvokeNodeRunResult = self.invoke_node()
                        self.__update_invoke_result(invoke_node_run_result)
                        self.__update_query_param(invoke_node_run_result)
                        sleep(10)

                # 没有获取到锁，回滚事务
                connection.rollback()
                # 查詢結果
                query_node_run_result: QueryNodeRunResult = self.query_execute_result()
                self.__update_node_result(query_node_run_result)
        except Exception as e:
            traceback.print_exc()
            logger.error("execute error:{}".format(e))

    def invoke_node(self) -> InvokeNodeRunResult:
        # ...
        raise NotImplementedError

    def query_execute_result(self) -> QueryNodeRunResult:
        raise NotImplementedError

    def __set_publish_plan_status(self, node_status) -> None:
        with DBConnectionManagerForSqlalchemy() as db:
            batch_no = self.__exec_bo._exec_node.batch_no
            node_name = self.__exec_bo._exec_node.node_name

            sql = '''
                           SELECT COUNT(1) AS count_num FROM iter_mgt_publish_plan_node n 
                           WHERE SUBSTRING_INDEX(n.phase_parallel, '-', 1) = 
                           (SELECT SUBSTRING_INDEX(t.phase_parallel, '-', 1) FROM iter_mgt_publish_plan_node t 
                               WHERE t.batch_no = '{batch_no}' AND t.node_name = '{node_name}')
                               AND n.batch_no = '{batch_no}';
                       '''.format(batch_no=batch_no, node_name=node_name)

            count_num = db.session.execute(text(sql)).fetchone()[0]

            if node_status in [NodeStatusEnum.FAIL.name,
                                                           NodeStatusEnum.STOP.name,
                                                           NodeStatusEnum.WARN.name]:
                # 判断当前点是否为独立并行线节点，通过计数是否大于1来判断
                if count_num == 1:
                    logger.info(
                        "当前节点为独立并行线，更新发布计划状态为{}".format(node_status))
                    db.session.query(IterMgtPublishPlan).filter(
                        IterMgtPublishPlan.batch_no == self.__exec_bo._exec_node.batch_no).update(
                        {
                            IterMgtPublishPlan.plan_status: node_status,
                            IterMgtPublishPlan.end_time: datetime.now(),
                            IterMgtPublishPlan.update_time: datetime.now()
                        }
                    )
                elif count_num > 1:
                    logger.info("当前节点{}为并行线中的节点".format(self.__exec_bo._exec_node.node_name))
                    result = self.__check_other_parallel_node_stauts(batch_no, node_name)
                    if result.get("exist_running_node") or result.get("exist_ready_node"):
                        logger.info("并行线中的其他节点还有节点在运行中或者有待执行的节点，不更新发布计划状态")
                    elif result.get("parallel_phase_all_success") or result.get(
                            "parallel_phase_all_fail") or result.get("parallel_phase_all_finish"):
                        logger.info("并行线中的其他节点都成功或都失败或都执行完成，更新发布计划状态为{}".format(node_status))
                        db.session.query(IterMgtPublishPlan).filter(
                            IterMgtPublishPlan.batch_no == self.__exec_bo._exec_node.batch_no).update(
                            {
                                IterMgtPublishPlan.plan_status: node_status,
                                IterMgtPublishPlan.end_time: datetime.now(),
                                IterMgtPublishPlan.update_time: datetime.now()
                            }
                        )
            elif node_status in [NodeStatusEnum.SUCCESS.name]:
                if count_num == 1:
                    # 默认最后一个点为独立阶段的点
                    logger.info("独立阶段的节点是最后一个点，则更新发布计划状态为成功，如果不是，则不更新发布计划状态")
                    self.__set_plan_status_by_last_node()
                elif count_num > 1:
                    result = self.__check_other_parallel_node_stauts(batch_no, node_name)
                    if self.__exec_bo._exec_node.order_no == self.__get_same_parallel_phase_max_order_no() and result.get(
                            "parallel_phase_all_fail"):
                        logger.info("并行线中的其他节点都执行失败，更新发布计划状态为失败")
                        db.session.query(IterMgtPublishPlan).filter(
                            IterMgtPublishPlan.batch_no == self.__exec_bo._exec_node.batch_no).update(
                            {
                                IterMgtPublishPlan.plan_status: NodeStatusEnum.FAIL.name,
                                IterMgtPublishPlan.end_time: datetime.now(),
                                IterMgtPublishPlan.update_time: datetime.now()
                            }
                        )

            db.session.commit()

    def __set_plan_status_by_last_node(self):
        with DBConnectionManagerForSqlalchemy() as db:
            highest_order_no = db.session.query(func.max(IterMgtPublishPlanNode.order_no)). \
                filter(IterMgtPublishPlanNode.batch_no == self.__exec_bo._exec_node.batch_no). \
                scalar()
            exist_fail = db.session.query(IterMgtPublishPlanNode). \
                filter(IterMgtPublishPlanNode.batch_no == self.__exec_bo._exec_node.batch_no). \
                filter(~IterMgtPublishPlanNode.node_status.in_(['SUCCESS'])). \
                all()
            if highest_order_no == self.__exec_bo._exec_node.order_no and not exist_fail:
                db.session.query(IterMgtPublishPlan).filter(
                    IterMgtPublishPlan.batch_no == self.__exec_bo._exec_node.batch_no).update(
                    {
                        IterMgtPublishPlan.plan_status: NodeStatusEnum.SUCCESS.name,
                        IterMgtPublishPlan.end_time: datetime.now(),
                        IterMgtPublishPlan.update_time: datetime.now()
                    }
                )
            db.session.commit()

    def __get_same_parallel_phase_max_order_no(self):
        sql = '''
                SELECT MAX(order_no) as max_orader_no FROM iter_mgt_publish_plan_node n WHERE n.phase_parallel = 
                (SELECT t.phase_parallel FROM iter_mgt_publish_plan_node t 
                WHERE t.batch_no = '{batch_no}' AND t.node_name = '{node_name}')
                AND n.batch_no = '{batch_no}'; 
            '''.format(batch_no=self.__exec_bo._exec_node.batch_no, node_name=self.__exec_bo._exec_node.node_name)
        with DBConnectionManagerForSqlalchemy() as db:
            max_order_no = db.session.execute(text(sql)).fetchone()[0]
            return max_order_no

    def __check_other_parallel_node_stauts(self, batch_no, node_name):
        # 查其他并行线的各节点状态
        sql = '''
                SELECT n.phase_parallel, GROUP_CONCAT(n.node_status) as node_status_str 
                FROM iter_mgt_publish_plan_node n 
                WHERE SUBSTRING_INDEX(n.phase_parallel, '-', 1) = (
                    SELECT SUBSTRING_INDEX(t.phase_parallel, '-', 1) FROM iter_mgt_publish_plan_node t 
                    WHERE t.batch_no = '{batch_no}' AND t.node_name = '{node_name}') 
                    AND n.batch_no = '{batch_no}' 
                    AND n.phase_parallel NOT IN ( SELECT t1.phase_parallel 
                                                  FROM iter_mgt_publish_plan_node t1 
                                                  WHERE t1.batch_no = '{batch_no}' 
                                                  AND t1.node_name = '{node_name}')
                GROUP BY n.phase_parallel
            '''.format(batch_no=batch_no, node_name=node_name)
        with DBConnectionManagerForSqlalchemy() as db:
            node_status_info = db.session.execute(text(sql)).fetchall()
            phase_parallel_count_for_fail = len(node_status_info)
            phase_parallel_count_for_success = len(node_status_info)
            exist_ready_node = 0
            result_dict = {}
            for item in node_status_info:
                node_status_str = item.node_status_str
                if NodeStatusEnum.RUNNING.name in node_status_str:
                    logger.info(
                        "并行线{}中的节点{}还有节点在运行中，不更新发布计划状态".format(item.phase_parallel, node_name))
                    result_dict.update({"exist_running_node": True})
                if node_status_str in [NodeStatusEnum.FAIL.name, NodeStatusEnum.STOP.name, NodeStatusEnum.WARN.name]:
                    phase_parallel_count_for_fail = phase_parallel_count_for_fail - 1
                if node_status_str not in [NodeStatusEnum.FAIL.name, NodeStatusEnum.STOP.name, NodeStatusEnum.WARN.name, NodeStatusEnum.READY.name]:
                    phase_parallel_count_for_success = phase_parallel_count_for_success - 1

                status_list = node_status_str.split(',')
                status_set = set(status_list)
                if len(status_set) == 2 and {NodeStatusEnum.SUCCESS.name, NodeStatusEnum.READY.name} == status_set:
                    exist_ready_node = exist_ready_node + 1
            if phase_parallel_count_for_fail == 0:
                result_dict.update({"parallel_phase_all_fail": True})
            if phase_parallel_count_for_success == 0:
                result_dict.update({"parallel_phase_all_success": True})
            if exist_ready_node > 0:
                result_dict.update({"exist_ready_node": True})
            else:
                result_dict.update({"parallel_phase_all_finish": True})
            logger.info("并行线中的其他节点状态信息:{}".format(result_dict))
            return result_dict

    def __update_node_result(self, query_node_run_result: QueryNodeRunResult) -> None:
        end_time = None
        if query_node_run_result.get_node_status() in [NodeStatusEnum.SUCCESS.name, NodeStatusEnum.FAIL.name,
                                                       NodeStatusEnum.STOP.name, NodeStatusEnum.WARN.name]:
            end_time = datetime.now()
        with DBConnectionManagerForSqlalchemy() as db:
            if query_node_run_result.get_node_status() in [NodeStatusEnum.FAIL.name,
                                                           NodeStatusEnum.STOP.name,
                                                           NodeStatusEnum.WARN.name]:
                # 当前点失败，需要把并行线中的后续节点也置为失败
                db.session.query(IterMgtPublishPlanNode).filter(
                    # IterMgtPublishPlanNode.phase_parallel == self.__exec_bo._exec_node.phase_parallel,
                    IterMgtPublishPlanNode.order_no >= self.__exec_bo._exec_node.order_no,
                    IterMgtPublishPlanNode.batch_no == self.__exec_bo._exec_node.batch_no).update(
                    {
                        IterMgtPublishPlanNode.node_status: query_node_run_result.get_node_status(),
                        IterMgtPublishPlanNode.node_run_result: query_node_run_result.get_node_run_result(),
                        IterMgtPublishPlanNode.end_time: end_time,
                        IterMgtPublishPlanNode.update_time: datetime.now()
                    }
                )
            else:
                plan = db.session.query(IterMgtPublishPlan).filter(
                    IterMgtPublishPlan.batch_no == self.__exec_bo._exec_node.batch_no,
                    IterMgtPublishPlan.plan_status == NodeStatusEnum.RUNNING.name).all()
                if plan:
                    db.session.query(IterMgtPublishPlanNode).filter(
                        IterMgtPublishPlanNode.id == self.__exec_bo._exec_node.id,
                        IterMgtPublishPlanNode.node_status == NodeStatusEnum.RUNNING.name).update(
                        {
                            IterMgtPublishPlanNode.node_status: query_node_run_result.get_node_status(),
                            IterMgtPublishPlanNode.node_run_result: query_node_run_result.get_node_run_result(),
                            IterMgtPublishPlanNode.end_time: end_time,
                            IterMgtPublishPlanNode.update_time: datetime.now()
                        }
                    )
            db.session.commit()

            self.__set_publish_plan_status(query_node_run_result.get_node_status())

    def __update_invoke_result(self, invoke_node_run_result: InvokeNodeRunResult) -> None:
        if invoke_node_run_result.get_node_status() in [NodeStatusEnum.FAIL.name,
                                                        NodeStatusEnum.STOP.name, NodeStatusEnum.WARN.name]:
            with DBConnectionManagerForSqlalchemy() as db:
                db.session.query(IterMgtPublishPlanNode).filter(
                    IterMgtPublishPlanNode.id == self.__exec_bo._exec_node.id).update(
                    {
                        IterMgtPublishPlanNode.node_status: invoke_node_run_result.get_node_status(),
                        IterMgtPublishPlanNode.node_run_result: invoke_node_run_result.get_node_run_result(),
                        IterMgtPublishPlanNode.end_time: datetime.now(),
                        IterMgtPublishPlanNode.update_time: datetime.now()
                    }
                )
                db.session.commit()
            self.__set_publish_plan_status(invoke_node_run_result.get_node_status())
        else:
            with DBConnectionManagerForSqlalchemy() as db:
                db.session.query(IterMgtPublishPlanNode).filter(
                    IterMgtPublishPlanNode.id == self.__exec_bo._exec_node.id).update(
                    {
                        IterMgtPublishPlanNode.node_status: invoke_node_run_result.get_node_status(),
                        IterMgtPublishPlanNode.query_node_run_result_param: invoke_node_run_result.get_query_node_run_result_param(),
                        IterMgtPublishPlanNode.update_time: datetime.now()
                    }
                )
                db.session.commit()

    def __update_query_param(self, invoke_node_run_result):
        self.__exec_bo._exec_node.query_node_run_result_param = invoke_node_run_result.get_query_node_run_result_param()
