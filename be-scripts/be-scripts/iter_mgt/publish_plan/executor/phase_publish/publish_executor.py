import traceback
from datetime import datetime, timedelta
from time import sleep

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.get.mysql.task_mgt_external_interface import get_app_code_path
from iter_mgt.iter_mgt_ser import get_publish_app_by_iteration_id, get_suite_code_by_iteration_id
from iter_mgt.publish_plan.dao.model.iter_mgt_publish_plan_node import IterMgtPublishPlanNode
from iter_mgt.publish_plan.executor.base_executor import BaseExecutor, InvokeNodeRunResult, QueryNodeRunResult, \
    BaseObject
from iter_mgt.publish_plan.executor.executor_enum import ExecutorEnum, NodeStatusEnum
from settings import logger, JENKINS_INFO
from test_publish_aio.test_suite_init_constants import PROCESS_CODE_PROD, PROCESS_CODE_ZB
from utils.call_external_interface.call_http_api import SpiderHttpCaller


class PublishExecutor(BaseExecutor):
    exec_node_name = ExecutorEnum.PUBLISH.executor_name
    exec_type = "rapid_publish"

    def __init__(self, exec_bo: BaseObject) -> None:
        super().__init__(exec_bo)
        self.op_type = "update_and_deploy_and_verify"

    def _get_group_suite_and_node_dict(self, iteration_id):
        main_node = {}
        reserve_node = {}
        suite_list = get_suite_code_by_iteration_id(iteration_id)
        reserve_suite = list()
        main_suite = list()
        for item in suite_list:
            if PROCESS_CODE_ZB == item.get("type_short_name"):
                reserve_suite.append(item.get("suite_code"))
            elif PROCESS_CODE_PROD == item.get("type_short_name"):
                main_suite.append(item.get("suite_code"))
        if len(reserve_suite) > 0:
            self._get_app_suite_list(iteration_id, reserve_node, reserve_suite)
        if len(main_suite) > 0:
            self._get_app_suite_list(iteration_id, main_node, main_suite)
        return main_node, reserve_node

    def _get_app_suite_list(self, iteration_id, node_dict, suite_list):
        app_suite_list = list()
        for suite_code in suite_list:
            publish_app_list = get_publish_app_by_iteration_id(iteration_id, suite_code)
            app_list = list()
            for app in publish_app_list:
                app_list.append({"app_name": app.get('app_name'), "node_list": app.get('node_list')})
            app_suite_list.append({"suite_code": suite_code, "sequence": 0, "app_list": app_list})
        node_dict["strategy"] = 1
        node_dict["app_suite_list"] = app_suite_list

    def invoke_node(self) -> InvokeNodeRunResult:
        action_info = []
        try:
            iteration_id = self.get_exec_node().iter_id
            schedule_time = self.get_exec_node().schedule_time
            if not schedule_time:
                logger.info("schedule_time为空，不执行")
            else:
                if schedule_time > datetime.now() - timedelta(minutes=1):
                    logger.info("当前时间小于schedule_time，不执行")
                    return InvokeNodeRunResult(self.get_exec_node().node_status, None, None)

            main_node, reserve_node = self._get_group_suite_and_node_dict(iteration_id)
            group_params = {
                "iteration_id": iteration_id,
                "main_node": main_node,
                "reserve_node": reserve_node
            }
            shc = SpiderHttpCaller()
            result = shc.spider_request_post("group_publish_pipeline_api", group_params)

            status = result.get("status")
            logger.info("==========status:{}".format(status))
            logger.info("==========result:{}".format(result.get("msg")))
            logger.info("==========data:{}".format(result.get("data")))
            if status == "success":
                result = result.get("data")
            else:
                result = ''
            action_info.append(result)
            return InvokeNodeRunResult(NodeStatusEnum.RUNNING.name, action_info, None)
        except Exception as e:
            traceback.print_exc()
            return InvokeNodeRunResult(NodeStatusEnum.FAIL.name, None,
                                       node_run_result={"detail": str(e), "show_type": "text"})

    def query_execute_result(self) -> QueryNodeRunResult:
        status_list = []
        params = {}
        action_list = []
        try:
            action_info = self.get_exec_node().query_node_run_result_param
            module_name = self.get_exec_node().module_name
            if not action_info:
                node_run_result = {"detail": "查询参数为空，稍后重试！", "show_type": "text"}
                return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, node_run_result)
            logger.info("==========action_info:{}".format(action_info))
            for action in action_info:
                if action:
                    action_list.append(action['action_id'])
            http_caller = SpiderHttpCaller()
            http_caller.spider_login()
            params['action_info'] = action_list
            result_response = http_caller.spider_request_get("multi_node_publish_pipeline_api", params)
            result = result_response.get("data")
            build_id = None
            job_name = None
            message = ''
            if not result:
                sleep(10)
                limit = 300
                while limit > 0:
                    sleep(5)
                    limit -= 5
                    result_response = http_caller.spider_request_get("multi_node_publish_pipeline_api", params)
                    result = result_response.get("data")
                    if not result:
                        continue
            for status in result:
                status_list.append(status['status'])
                build_id = status['build_id']
                message = status['message']
                job_name = status['job_name']
            if build_id:
                if not job_name:
                    app_info = get_app_code_path(module_name)
                    job_name = "{}_group_publish".format(app_info['git_url'])
                jenkins_url = "{}/blue/organizations/jenkins/{}/detail/{}/{}" \
                    .format(JENKINS_INFO['URL'], job_name, job_name, build_id)
                node_run_result = {"detail": "查看结果", "show_type": "external_link", "external_link_url": jenkins_url}
            else:
                node_run_result = {"detail": message, "show_type": "text"}
                logger.info("发布详情日志{}".format(message))
            if status_list:
                status_str = str(status_list)
                if "running" in status_str:
                    return QueryNodeRunResult(NodeStatusEnum.RUNNING.name, action_info)
                else:
                    if 'failure' in status_str:
                        return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
                    else:
                        return QueryNodeRunResult(NodeStatusEnum.SUCCESS.name, node_run_result)
            else:
                return QueryNodeRunResult(NodeStatusEnum.FAIL.name, node_run_result)
        except Exception as e:
            traceback.print_exc()
            return QueryNodeRunResult(NodeStatusEnum.FAIL.name, {"detail": "运行失败", "show_type": "text"})


if __name__ == '__main__':
    with DBConnectionManagerForSqlalchemy() as db:
        obj = db.session.query(IterMgtPublishPlanNode).filter_by(id=8).first()
    # obj = IterMgtPublishPlanNode.select().where(IterMgtPublishPlanNode.id==8)
    PublishExecutor(exec_node=obj).execute()
