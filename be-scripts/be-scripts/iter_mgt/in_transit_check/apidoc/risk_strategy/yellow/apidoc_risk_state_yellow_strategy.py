from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import ApiDocCheckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class ApidocRiskStateYellowStrategy(IRiskStateStrategy):
    check_type = SensorEnum.APIDOC.type_name
    risk_check_phase = RiskCheckPhaseEnum.YELLOW.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        if ApiDocCheckUtil.check_deploy_success_in_last_hour(sensor_result_item.iteration_id, sensor_result_item.app_name):
            return RiskStateResult(False, None, None)
        return RiskStateResult(True, '超过1小时没有部署。通过部署才能判断是否有新接口漏写apidoc!!!', RiskResultShowTypeEnum.TEXT.value)


