from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import ApiDoc<PERSON>heckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowType<PERSON>num, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class ApidocNoApidocRiskStrategy(IRiskStateStrategy):
    check_type = SensorEnum.APIDOC.type_name
    risk_check_phase = RiskCheckPhaseEnum.RED.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        no_apidoc_api = ApiDocCheckUtil.no_apodic_api_check(sensor_result_item.branch, sensor_result_item.app_name)
        if no_apidoc_api[0]:
            return RiskStateResult(True, no_apidoc_api[1], RiskResultShowTypeEnum.TABLE.value)
        return RiskStateResult(False, None, None)
