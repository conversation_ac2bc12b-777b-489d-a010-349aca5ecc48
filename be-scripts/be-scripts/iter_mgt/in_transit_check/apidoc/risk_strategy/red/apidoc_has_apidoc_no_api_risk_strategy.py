from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import Api<PERSON><PERSON><PERSON>heckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowType<PERSON>num, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class ApidocHasApiDocNoApiRiskStrategy(IRiskStateStrategy):
    check_type = SensorEnum.APIDOC.type_name
    risk_check_phase = RiskCheckPhaseEnum.YELLOW.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        no_api = ApiDocCheckUtil.has_apidoc_no_api_check(sensor_result_item.branch, sensor_result_item.app_name)
        if no_api[0]:
            return RiskStateResult(True, no_api[1], RiskResultShowTypeEnum.TABLE.value)
        return RiskStateResult(False, None, None)
