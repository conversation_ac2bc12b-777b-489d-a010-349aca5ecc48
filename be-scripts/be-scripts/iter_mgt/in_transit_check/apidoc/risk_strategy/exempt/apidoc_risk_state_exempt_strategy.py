from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import Api<PERSON><PERSON><PERSON>heckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowType<PERSON>num, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class ApidocRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.APIDOC.type_name
    risk_check_phase = RiskCheckPhaseEnum.EXEMPT.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        if ApiDocCheckUtil.not_check(sensor_result_item.iteration_id, sensor_result_item.app_name):
            return RiskStateResult(True, 'APIDOC免检', RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(<PERSON>als<PERSON>, None, None)
