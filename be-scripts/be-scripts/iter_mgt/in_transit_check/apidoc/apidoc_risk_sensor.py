from iter_mgt.in_transit_check.apidoc.apidoc_risk_state_aggregator import ApidocRiskStateAggregator
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, SensorEnum


# apidoc风险探测器
class ApidocRiskSensor(IRiskSensible):
    order = 1
    risk_state_agg_strategy: IRiskStateAggregator = ApidocRiskStateAggregator()
    result_type: str = RiskResultShowTypeEnum.TABLE.value
    check_type: str = SensorEnum.APIDOC.type_name
    check_type_desc: str = SensorEnum.APIDOC.type_name

    def __init__(self, script_param):
        super().__init__(script_param)
