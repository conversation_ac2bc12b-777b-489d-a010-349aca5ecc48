# 每新增一个策略 必须在此导入一次
from iter_mgt.in_transit_check.apidoc.risk_strategy.exempt.apidoc_risk_state_exempt_strategy import \
    ApidocRiskStateExemptStrategy
from iter_mgt.in_transit_check.apidoc.risk_strategy.green.apidoc_risk_state_green_strategy import \
    ApidocRiskStateGreenStrategy
# from iter_mgt.in_transit_check.apidoc.risk_strategy.red.apidoc_no_parse_risk_strategy import ApidocNoParseRiskStrategy
from iter_mgt.in_transit_check.apidoc.risk_strategy.red.apidoc_has_apidoc_no_api_risk_strategy import \
 ApidocHasApiDocNoApiRiskStrategy
from iter_mgt.in_transit_check.apidoc.risk_strategy.red.apidoc_no_apidoc_risk_strategy import ApidocNoApidocRiskStrategy
from iter_mgt.in_transit_check.apidoc.risk_strategy.yellow.apidoc_risk_state_yellow_strategy import \
    ApidocRiskStateYellowStrategy
