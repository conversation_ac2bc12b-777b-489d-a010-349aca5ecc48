from enum import Enum


class SensorEnum(Enum):
    APIDOC = ('apidoc', '接口文档')
    INTERFACE = ('interface', '接口')
    CODE_ROUND = ('code_round', '代码回合')
    ONLINE_CHANNEL = ('online_channel', '上线通道')
    SONAR = ('sonar', 'sonar结果')
    AUALITY_REPORT = ('quality_report', '质量报告')

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


class RiskLevelEnum(Enum):
    RED = ('red', '阻塞')
    YELLOW = ('yellow', '警告')
    GREEN = ('green', '通过')

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


class RiskCheckPhaseEnum(Enum):
    EXEMPT = ('exempt', '白名单')
    RED = ('red', '阻塞')
    YELLOW = ('yellow', '警告')
    GREEN = ('green', '通过')

    def __init__(self, phase_name, phase_desc):
        self.phase_name = phase_name
        self.phase_desc = phase_desc


class RiskResultShowTypeEnum(Enum):
    TABLE = 'table'
    TEXT = 'text'
