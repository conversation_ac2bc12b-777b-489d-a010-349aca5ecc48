from dataclasses import dataclass


class SensorResult:
    """调用结果类"""
    success = "success"
    failed = "failure"
    status = ""
    sensor_results = None

    def __init__(self):
        self.sensor_results = {}
        self.status = self.success

    def set_status(self, status):
        self.status = status

    def __str__(self):
        return f"SensorResult: status={self.status}, sensor_results={self.sensor_results}"

    def set_sensor_results(self, sensor_results):
        self.sensor_results = sensor_results

    def __repr__(self):
        return str(self)


class SensorResultItem:
    iteration_id: str
    env: str
    app_name: str
    branch: str
    opt_user: str
    result_show_type: str
    check_type: str
    check_type_desc: str
    result: any
    risk_level: str
    risk_detail: any

    def __init__(self, iteration_id, app_name, check_type, env, opt_user, check_type_desc):
        self.iteration_id = iteration_id
        self.branch = iteration_id.split("_")[1]
        self.app_name = app_name
        self.opt_user = env
        self.opt_user = opt_user
        self.check_type = check_type
        self.check_type_desc = check_type_desc

    def set_result_show_type(self, result_show_type):
        self.result_show_type = result_show_type

    def set_risk_level(self, risk_level):
        self.risk_level = risk_level

    def set_risk_detail(self, risk_detail):
        self.risk_detail = risk_detail


class RiskStateResult:
    def __init__(self, is_export_risk, risk_detail, result_show_type):
        self.is_export_risk = is_export_risk
        self.risk_detail = risk_detail
        self.result_show_type = result_show_type
