# 风险探测器
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_result import SensorResult, SensorResultItem


class IRiskSensible:
    order = 0
    risk_state_agg_strategy: IRiskStateAggregator = None
    check_type: str = None
    check_type_desc: str = None
    iteration_id: str = None
    env: str = None
    opt_user: str = None
    app_name_list: list = None

    def __init__(self, script_param):
        self.script_param = script_param
        self.iteration_id = script_param.get("iteration_id")
        self.env = script_param.get("env")
        self.app_name_list = script_param.get("app_name_list")
        self.opt_user = script_param.get("opt_user")

    def inspect(self, sensor_result: SensorResult):
        for app_name in self.app_name_list:
            sensor_result_item = SensorResultItem(check_type=self.check_type, iteration_id=self.iteration_id,
                                                  app_name=app_name, env=self.env, opt_user=self.opt_user,check_type_desc=self.check_type_desc)
            sensor_result_item = self.risk_state_agg_strategy.aggregate(sensor_result_item)
            if sensor_result.sensor_results.get(app_name):
                sensor_result.sensor_results.get(app_name).append(sensor_result_item.__dict__)
            else:
                sensor_result.sensor_results[app_name] = [sensor_result_item.__dict__]
