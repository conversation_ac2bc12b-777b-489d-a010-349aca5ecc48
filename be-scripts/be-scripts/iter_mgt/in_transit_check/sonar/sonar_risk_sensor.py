
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, SensorEnum
from iter_mgt.in_transit_check.sonar.sonar_risk_state_aggregator import SonarRiskStateAggregator


# Sonar风险探测器
class SonarRiskSensor(IRiskSensible):
    order = 4
    risk_state_agg_strategy: IRiskStateAggregator = SonarRiskStateAggregator()
    result_type: str = RiskResultShowTypeEnum.TEXT.value
    check_type: str = SensorEnum.SONAR.type_name
    check_type_desc: str = SensorEnum.SONAR.type_desc

    def __init__(self, script_param):
        super().__init__(script_param)
