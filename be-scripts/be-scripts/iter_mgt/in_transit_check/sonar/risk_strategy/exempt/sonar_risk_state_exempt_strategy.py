from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import SonarCheckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class SonarRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.SONAR.type_name
    risk_check_phase = RiskCheckPhaseEnum.EXEMPT.phase_name

    def inspect(self, sensor_result_item: SensorResultItem)-> RiskStateResult:
        if SonarCheckUtil.not_check(sensor_result_item.iteration_id, sensor_result_item.app_name):
            return RiskStateResult(True, 'Sonar免检', RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, None, RiskResultShowTypeEnum.TEXT.value)
