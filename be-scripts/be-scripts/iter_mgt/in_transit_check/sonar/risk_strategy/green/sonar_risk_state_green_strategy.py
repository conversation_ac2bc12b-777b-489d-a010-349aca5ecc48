from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class SonarRiskStateGreenStrategy(IRiskStateStrategy):
    check_type = SensorEnum.SONAR.type_name
    risk_check_phase = RiskCheckPhaseEnum.GREEN.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        return RiskStateResult(False, "通过", RiskResultShowTypeEnum.TEXT.value)
