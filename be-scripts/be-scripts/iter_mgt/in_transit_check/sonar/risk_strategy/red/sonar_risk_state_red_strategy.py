import traceback

from ci_pipeline.ci_pipeline_models.iter_models import IterAppInfoModel
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult
from product_mgt.models import LibInfoDetail, LibInfo
from settings import logger
from utils.call_external_interface.call_http_api import MantisHttpCaller


class SonarRiskStateRedStrategy(IRiskStateStrategy):
    check_type = SensorEnum.SONAR.type_name
    risk_check_phase = RiskCheckPhaseEnum.RED.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        sonar_check_result, check_detail = check_sonar_result(sensor_result_item.iteration_id,
                                                              sensor_result_item.app_name)
        if sonar_check_result:
            return RiskStateResult(True, check_detail, RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, "通过", RiskResultShowTypeEnum.TEXT.value)


def check_sonar_result(iteration_id, app_name):
    query = (LibInfoDetail
             .select(LibInfoDetail.lib_md5, LibInfo.code_last_version, IterAppInfoModel.git_last_version)
             .join(LibInfo, on=(LibInfoDetail.lib_info_id == LibInfo.id))
             .switch(LibInfoDetail)
             .join(IterAppInfoModel, on=(IterAppInfoModel.appName == LibInfo.app_name) &
                                        (IterAppInfoModel.pipeline_id == LibInfo.iteration_id))
             .where((LibInfo.lib_type == 'sonar') &
                    (LibInfo.iteration_id == iteration_id) &
                    (LibInfo.app_name == app_name))
             )
    results = list(query)
    for result in results:
        logger.info("result:{}".format(result.lib_md5))
        logger.info("result:{}".format(result.libinfo.code_last_version))
        logger.info("result:{}".format(result.iterappinfomodel.git_last_version))

        if result.libinfo.code_last_version != result.iterappinfomodel.git_last_version:
            return True, "最新代码暂没有sonar扫描记录，请稍后查询（异步扫描）或者重新编译！"

        task_id = result.lib_md5

        params = {"app_name": app_name, "iteration_id": iteration_id,
                  "task_id": task_id}
        logger.info("check_sonar_result params: {}".format(params))
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("check_sonar_result", params=params)
            logger.info("check_sonar_result res: {}".format(res))
            if res.get("status") == "success":
                return False, ''
            else:
                return True, res.get("data")
        except Exception as e:
            traceback.print_exc()
            return True, str(e)
    logger.info("没查询到{} {}的sonar扫描结果数据".format(iteration_id, app_name))
    return True, "sonar至少要完成一次扫描！"


if __name__ == '__main__':
    check_sonar_result(iteration_id='prod-info_1.0.0', app_name="prod-mq-pqc-info1")
