from iter_mgt.in_transit_check.code_round.code_round_risk_state_aggregator import \
    CodeRoundRiskStateAggregator
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, SensorEnum


# 代码回合风险探测器
class CodeRoundRiskSensor(IRiskSensible):
    order = 2
    risk_state_agg_strategy: IRiskStateAggregator = CodeRoundRiskStateAggregator()
    result_type: str = RiskResultShowTypeEnum.TEXT.value
    check_type: str = SensorEnum.CODE_ROUND.type_name
    check_type_desc: str = SensorEnum.CODE_ROUND.type_desc

    def __init__(self, script_param):
        super().__init__(script_param)
