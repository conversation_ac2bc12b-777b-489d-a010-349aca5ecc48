from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import check_merged_master
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class CodeRoundRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.CODE_ROUND.type_name
    risk_check_phase = RiskCheckPhaseEnum.YELLOW.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        merged_master, is_one_day_ago = check_merged_master(sensor_result_item.iteration_id,
                                                            sensor_result_item.app_name)
        if not merged_master:
            if is_one_day_ago == 'Yes':
                return RiskStateResult(True, """
                                    - 近一天没有构建，只有活跃项目才会检测
                                  """, RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, None, None)
