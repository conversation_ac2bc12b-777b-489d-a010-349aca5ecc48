from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class CodeRoundRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.CODE_ROUND.type_name
    risk_check_phase = RiskCheckPhaseEnum.EXEMPT.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        return RiskStateResult(False, None, RiskResultShowTypeEnum.TEXT.value)
