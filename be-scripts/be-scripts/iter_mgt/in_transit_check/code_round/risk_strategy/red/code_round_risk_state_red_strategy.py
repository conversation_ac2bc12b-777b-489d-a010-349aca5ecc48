from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import check_merged_master, \
    check_last_archive_info
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class CodeRoundRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.CODE_ROUND.type_name
    risk_check_phase = RiskCheckPhaseEnum.RED.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        merged_master, is_one_day_ago = check_merged_master(sensor_result_item.iteration_id,
                                                            sensor_result_item.app_name)
        if merged_master == 0:
            last_archive_info = check_last_archive_info(sensor_result_item.app_name)
            return RiskStateResult(True, """
                     应用：{}在{}时间归档了分支:{}，请回合master代码到当前分支。若已回合，请忽略
                   """.format(sensor_result_item.app_name, last_archive_info['br_end_date'],
                              last_archive_info['br_name']), RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, None, None)
