import gc
import inspect

from iter_mgt.in_transit_check.risk_sensor_enum import RiskLevelEnum, RiskCheckPhaseEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy


# 风险状态汇总策略
class IRiskStateAggregator:
    strategies: [IRiskStateStrategy] = []
    check_type = None

    def __init__(self):
        super().__init__()
        self.__init_all_risk_state_strategies()

    def inspect(self, sensor_result_item: SensorResultItem, risk_check_phase):
        exempt_strategies = [strategy for strategy in self.strategies if
                             strategy.risk_check_phase == risk_check_phase]
        risk_state_result = RiskStateResult(False, None, None)
        for strategy in exempt_strategies:
            risk_state_result = strategy.inspect(sensor_result_item)
            if risk_state_result.is_export_risk:
                return risk_state_result
        return risk_state_result

    # 是否免检
    def inspect_no(self, sensor_result_item: SensorResultItem):
        return self.inspect(sensor_result_item, RiskCheckPhaseEnum.EXEMPT.phase_name)

    # 是否阻塞
    def inspect_red(self, sensor_result_item: SensorResultItem):
        return self.inspect(sensor_result_item, RiskCheckPhaseEnum.RED.phase_name)

    # 是否警告
    def inspect_yellow(self, sensor_result_item: SensorResultItem):
        return self.inspect(sensor_result_item, RiskCheckPhaseEnum.YELLOW.phase_name)

    # 是否通过
    def inspect_green(self, sensor_result_item: SensorResultItem):
        return self.inspect(sensor_result_item, RiskCheckPhaseEnum.GREEN.phase_name)

    def __init_all_risk_state_strategies(self):
        # 获取当前所有的对象
        all_objects = gc.get_objects()
        # 存储所有实现了IRiskSensible接口的类
        classes = []
        for obj in all_objects:
            # 检查对象是否是一个类
            if inspect.isclass(obj):
                # 检查类是否是IRiskSensible的子类
                if issubclass(obj,
                              IRiskStateStrategy) and obj is not IRiskStateStrategy and obj.check_type == self.check_type:
                    classes.append(obj)
        # 返回所有实例
        self.strategies = [cls() for cls in classes]

    def aggregate(self, sensor_result_item: SensorResultItem):
        def inspect_and_set(inspect_method, risk_level):
            risk_state_result = inspect_method(sensor_result_item)
            sensor_result_item.set_risk_level(risk_level)
            sensor_result_item.set_risk_detail(risk_state_result.risk_detail)
            sensor_result_item.set_result_show_type(risk_state_result.result_show_type)
            return risk_state_result.is_export_risk

        if inspect_and_set(self.inspect_no, RiskLevelEnum.GREEN.type_name):
            return sensor_result_item
        if inspect_and_set(self.inspect_red, RiskLevelEnum.RED.type_name):
            return sensor_result_item
        if inspect_and_set(self.inspect_yellow, RiskLevelEnum.YELLOW.type_name):
            return sensor_result_item

        inspect_and_set(self.inspect_green, RiskLevelEnum.GREEN.type_name)
        return sensor_result_item
