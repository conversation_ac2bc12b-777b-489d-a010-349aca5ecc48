from sqlalchemy import text

from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.get.mysql.app_info import get_need_online_server
from iter_mgt.iter_mgt_ser import get_apidoc_guard_info
from qc_mgt.modles import GuardSwitchInfo
from settings import logger


class ApiDeleteCheckUtil:

    @staticmethod
    def not_check(iteration_id, app_name):
        if not GuardSwitchInfo.get(GuardSwitchInfo.guard_name == 'api_threshold_check').guard_switch:
            return True
        app_name_list = get_need_online_server([app_name])
        if not app_name_list:
            return True
        data_list = BranchesModel.select(BranchesModel.br_style).where(BranchesModel.pipeline_id == iteration_id,
                                                                       BranchesModel.br_style == 'release')
        if not data_list:
            return True
        guard_info = get_apidoc_guard_info(iteration_id, app_name)
        if guard_info.get("pass"):
            return True
        return False

    @staticmethod
    def api_delete_gt_threshold_check(app_name, branch_name, interface_check_threshold):
        print('api_delete_gt_threshold_check')
        delete_api_list = []
        api_delete_gt_threshold_list = ApiDeleteCheckUtil.api_delete_gt_threshold_list(app_name, branch_name, interface_check_threshold)
        if api_delete_gt_threshold_list:
            for api_delete in api_delete_gt_threshold_list:
                delete_api_list.append({"risk_item": '{}:{}'.format(api_delete.interface_path, api_delete.interface_method),
                                      "warn_content": "[{}]-[当前版本:{}]接口减少超过10个".format(app_name, branch_name)})
            if delete_api_list:
                return True, delete_api_list
        return False, 'APIThreshold校验通过'

    @staticmethod
    def api_delete_gt_threshold_list(app_name, branch_name, interface_check_threshold):
        with DBConnectionManagerForSqlalchemy() as db:
            execute_sql = '''
                            select api.module_name, api.branch_name, api.interface_path, api.interface_method
                            FROM (
                            select distinct apit.module_name, apit.branch_name, count(0) as total, group_concat(CONCAT(apit.interface_path,':',apit.interface_method)) as paths
                            FROM
                                app_mgt_interface_info_temp apit
                                LEFT JOIN app_mgt_interface_and_api_exclude ex on apit.interface_path = ex.interface_path
                            WHERE apit.module_name = '{module_name}' AND apit.branch_name = '{branch}' and apit.status = 0 
                            AND ex.interface_path IS NULL
                            ) as x 
                            inner join app_mgt_interface_info_temp api
                            on x.total > {interface_check_threshold}
                            and api.module_name = x.module_name and api.branch_name = x.branch_name
                            and concat(',', x.paths, ',') like concat('%,', CONCAT(api.interface_path,':',api.interface_method), ',%')
                       '''.format(module_name=app_name, branch=branch_name, interface_check_threshold=interface_check_threshold)
            logger.info("api_delete_gt_threshold_list----->execute_sql：{}".format(execute_sql))
            api_list_db = db.session.execute(
                text(execute_sql)).fetchall()
            return api_list_db

