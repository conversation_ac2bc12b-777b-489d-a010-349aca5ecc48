import logging

from sqlalchemy import text
from app_mgt.app_apidoc.model.agent_api.app_mgt_interface_info import AppMgtInterfaceInfo
from app_mgt.app_apidoc.model.app_mgt_apidoc_info_model import AppMgtApidocInfo, AppMgtInterfaceAndApiExclude
from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
from dao.connect.mysql import DBConnectionManager
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.get.mysql.app_info import get_need_online_server, get_latest_and_has_interface_archive_version
from iter_mgt.models import BizTestAppNeed
from qc_mgt.modles import GuardSwitchInfo
from settings import logger
from iter_mgt.iter_mgt_ser import get_apidoc_guard_info, get_guard_info
from app_mgt.app_apidoc.model.app_mgt_apidoc_scan_log import AppMgtApidocScanLog
from utils.check.validator import It<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.public_utils import AppAutoTestCheckParam


class ApiDocCheckUtil:
    @staticmethod
    def not_check(iteration_id, app_name):
        if not GuardSwitchInfo.get(GuardSwitchInfo.guard_name == 'apidoc_check').guard_switch:
            return True
        app_name_list = get_need_online_server([app_name])
        if not app_name_list:
            return True
        data_list = BranchesModel.select(BranchesModel.br_style).where(BranchesModel.pipeline_id == iteration_id,
                                                                       BranchesModel.br_style == 'release')
        if not data_list:
            return True
        guard_info = get_apidoc_guard_info(iteration_id, app_name)
        if guard_info.get("pass"):
            return True
        return False

    @staticmethod
    def no_api_check(branch, app_name):
        with DBConnectionManagerForSqlalchemy() as db:
            interface_list = db.session.query(AppMgtInterfaceInfo).filter(
                AppMgtInterfaceInfo.module_name == app_name,
                AppMgtInterfaceInfo.branch_name == branch,
                AppMgtInterfaceInfo.status == 1
            ).all()
            if not interface_list:
                return True, '本迭代接口未扫描成功过'

        return False, '本迭代接口扫描成功'

    @staticmethod
    def get_current_or_achieve_branch_interface_list(branch, app_name):
        interface_list = []
        with DBConnectionManagerForSqlalchemy() as db:
            interface_list = db.session.query(AppMgtInterfaceInfo).filter(
                AppMgtInterfaceInfo.module_name == app_name,
                AppMgtInterfaceInfo.branch_name == branch,
                AppMgtInterfaceInfo.status == 1
            ).all()
            logger.info('应用：{},对应版本：{},Interface接口数量{}'.format(app_name, branch, len(interface_list)))
            if not interface_list:
                http_sql = '''
                            SELECT br_name 
                            FROM (
                                SELECT b.br_name, b.br_end_date, COUNT(a.id) AS interface_num 
                                FROM app_mgt_interface_info a 
                                INNER JOIN (
                                    SELECT a.appName, b.br_name, b.br_end_date 
                                    FROM iter_mgt_iter_app_info a 
                                    INNER JOIN iter_mgt_iter_info b ON a.pipeline_id = b.pipeline_id 
                                    WHERE a.appName = '{app_module_name}' AND a.sys_status = '已归档' AND b.br_status = 'close'
                                    ORDER BY b.br_end_date DESC
                                    LIMIT 5
                                ) AS b ON a.module_name = b.appName AND a.branch_name = b.br_name 
                                GROUP BY b.br_name, b.br_end_date 
                                HAVING COUNT(a.id) > 0 
                                ORDER BY b.br_end_date DESC
                            ) AS m LIMIT 1;
                           '''.format(app_module_name=app_name)

                achieve_br_result = db.session.execute(text(http_sql)).fetchall()
                if achieve_br_result:
                    achieve_br = achieve_br_result[0].br_name
                    interface_list = db.session.query(AppMgtInterfaceInfo).filter(
                        AppMgtInterfaceInfo.module_name == app_name,
                        AppMgtInterfaceInfo.branch_name == achieve_br,
                        AppMgtInterfaceInfo.status == 1
                    ).all()
                    logger.info(
                        '应用：{},对应版本{},Interface接口数量{}'.format(app_name, achieve_br, len(interface_list)))
            return interface_list

    @staticmethod
    def has_apidoc_no_api_check(branch, app_name):
        apidoc_list = ApiDocCheckUtil.get_apidoc_list(app_name, branch)
        logger.info("has_apidoc_no_api_check----->current_branch_apidoc_list：{}".format(len(apidoc_list)))
        if apidoc_list:
            interface_list = ApiDocCheckUtil.get_all_apis(app_name, branch)
            logger.info("api接口：{}".format(len(interface_list)))
            no_api_apis = ApiDocCheckUtil.check_has_apidoc_no_api(app_name, branch, apidoc_list,
                                                                  interface_list)
            if no_api_apis:
                return True, no_api_apis

        return False, 'APIDOC校验通过'

    @staticmethod
    def check_has_apidoc_no_api(app_info, branch, apidoc_list, agent_api_list):
        has_api_doc_no_api = []
        has_apidoc_no_api_list = set(apidoc_list) - set(agent_api_list)
        if has_apidoc_no_api_list:
            old_api_list = ApiDocCheckUtil.old_api_list(app_info)
            has_api_doc_no_api = set(has_apidoc_no_api_list) - set(old_api_list)

        no_api_apis = []
        logger.info("有apidoc，无接口的：{}".format(has_api_doc_no_api))
        if has_api_doc_no_api:
            for has_api_doc_no__api_item in has_api_doc_no_api:
                no_api_apis.append({"risk_item": has_api_doc_no__api_item,
                                    "warn_content": "[{}]-[当前版本:{}]有apidoc 无接口".format(app_info, branch)})
        return no_api_apis

    @staticmethod
    def old_api_list(module_name):
        with DBConnectionManagerForSqlalchemy() as db:
            http_sql = '''
                           SELECT distinct CONCAT_WS(':', api_path, api_method, api_type) AS apidoc
                           FROM app_mgt_apidoc_info
                           WHERE module_name = '{module_name}'
                           AND create_time < '2024-04-19 12:00:00';
                       '''.format(module_name=module_name)

            old_api_list_db = db.session.execute(text(http_sql)).fetchall()
            db_old_apidoc_pai_list = []
            for api_doc in old_api_list_db:
                db_old_apidoc_pai_list.append(api_doc[0])
        return db_old_apidoc_pai_list

    # 新接口，没写api_doc
    @staticmethod
    def no_apodic_api_check(branch, app_name):
        warnings_result = {}
        new_agent_api_list = []
        # 查询新的接口列表
        logger.info("开始查询基准版本for：{}".format(app_name))
        archive_branch_name = get_latest_and_has_interface_archive_version(
            app_name)
        logger.info("开始校验应用：{}".format(app_name))
        logger.info("基准版本为：{}".format(archive_branch_name))
        new_agent_api_list_db = ApiDocCheckUtil.get_http_apis(app_name, archive_branch_name, branch
                                                              )
        ApiDocCheckUtil.get_dubbo_apis(app_name, archive_branch_name, branch,
                                       new_agent_api_list_db)
        for new_api in new_agent_api_list_db:
            new_agent_api_list.append(
                new_api[0] + ":" + new_api[1] + ":" + new_api[2])
        logger.info("新接口：{}".format(new_agent_api_list))
        if new_agent_api_list:
            current_branch_apidoc_list = []
            ApiDocCheckUtil.get_current_branch_apidoc_list(app_name, branch, current_branch_apidoc_list)
            no_api_doc_apis = ApiDocCheckUtil.check_no_apidoc_api(app_name, archive_branch_name, branch,
                                                                  current_branch_apidoc_list, new_agent_api_list,
                                                                  warnings_result)
            if no_api_doc_apis:
                return True, no_api_doc_apis
        return False, 'APIDOC校验通过'

    @staticmethod
    def no_parse_check(iteration_id, app_name):
        with DBConnectionManagerForSqlalchemy() as db:
            first_item = db.session.query(AppMgtApidocScanLog).filter(
                AppMgtApidocScanLog.module_name == app_name,
                AppMgtApidocScanLog.pipeline_id == iteration_id,
                AppMgtInterfaceInfo.status == '扫描apidoc'
            ).order_by(AppMgtApidocScanLog.create_time.desc()).first()
            if not first_item:
                return True, '本迭代没找到「扫描apidoc」节点的日志'
            if first_item.status != "success":
                return True, '本迭代「扫描apidoc」节点的状态错误：{}'.format(first_item.status)
        return False, '本迭代apidoc解析成功'

    @staticmethod
    def get_http_apis(app_info, archive_branch_name, branch):
        with DBConnectionManagerForSqlalchemy() as db:
            http_sql = '''
                                   SELECT distinct a.interface_path, a.interface_method,a.interface_type
                                   FROM app_mgt_interface_info a
                                   LEFT JOIN app_mgt_interface_and_api_exclude ex ON a.interface_path = ex.interface_path
                                   WHERE a.branch_name = '{branch}' AND a.module_name='{module_name}' AND a.status=1 AND a.interface_path NOT LIKE '%/swagger%' AND a.interface_type='http'  AND NOT EXISTS (
                                       SELECT 1 FROM app_mgt_interface_info b WHERE b.branch_name = '{archive_branch_name}' AND b.status=1 AND b.interface_type='http' AND b.module_name='{module_name}'
                                       AND a.interface_path = b.interface_path  AND a.interface_type=b.interface_type
                                   ) and ex.interface_path IS NULL
                               '''.format(module_name=app_info, branch=branch, archive_branch_name=archive_branch_name)
            new_agent_api_list_db = db.session.execute(
                text(http_sql)).fetchall()
            return new_agent_api_list_db

    @staticmethod
    def get_all_apis(app_info, branch):
        with DBConnectionManagerForSqlalchemy() as db:
            http_sql = '''
                                       SELECT distinct a.interface_path, a.interface_method,a.interface_type
                                       FROM app_mgt_interface_info a
                                       LEFT JOIN app_mgt_interface_and_api_exclude ex ON a.interface_path = ex.interface_path
                                       WHERE a.branch_name = '{branch}' AND a.module_name='{module_name}' AND a.status=1 AND a.interface_path NOT LIKE '%/swagger%' 
                                       and ex.interface_path IS NULL
                                   '''.format(module_name=app_info, branch=branch)
            agent_api_list_db = db.session.execute(
                text(http_sql)).fetchall()
            new_agent_api_list = []
            for new_api in agent_api_list_db:
                if new_api[2] == 'http':
                    new_agent_api_list.append(
                        new_api[0].upper() + ":" + new_api[1].upper() + ":" + new_api[2].upper())
                    new_agent_api_list.append(
                        new_api[0].lower() + ":" + new_api[1].lower() + ":" + new_api[2].lower())
                new_agent_api_list.append("{}:{}:{}".format(new_api[0], new_api[1], new_api[2]))
            return new_agent_api_list

    @staticmethod
    def get_dubbo_apis(app_info, archive_branch_name, branch, new_agent_api_list_db):
        with DBConnectionManagerForSqlalchemy() as db:
            dubbo_sql = '''
                                    SELECT a.interface_path, a.interface_method,a.interface_type
                                    FROM app_mgt_interface_info a
                                    LEFT JOIN app_mgt_interface_and_api_exclude ex ON a.interface_path = ex.interface_path
                                    WHERE a.branch_name = '{branch}' AND a.module_name='{module_name}' AND a.status=1 AND a.interface_type='dubbo'  AND NOT EXISTS (
                                        SELECT 1 FROM app_mgt_interface_info b WHERE b.branch_name = '{archive_branch_name}' AND a.interface_type='dubbo' AND b.status=1 AND b.module_name='{module_name}'
                                        AND a.interface_path = b.interface_path AND a.interface_method = b.interface_method AND a.interface_type=b.interface_type
                                    ) and ex.interface_path IS NULL
                                                   '''.format(module_name=app_info, branch=branch,
                                                              archive_branch_name=archive_branch_name)
            new_agent_api_list_db.extend(db.session.execute(
                text(dubbo_sql)).fetchall())

    @staticmethod
    def check_no_apidoc_api(app_info, archive_branch_name, branch, current_branch_apidoc_list, new_agent_api_list,
                            warnings_result):
        no_apidoc_warning = []
        no_api_doc_apis = []
        no_apidoc_api = []
        if current_branch_apidoc_list:
            logger.info("new_agent_api_list：{}".format(new_agent_api_list))
            logger.info("current_branch_apidoc_list：{}".format(current_branch_apidoc_list))
            no_api_doc_api = set(
                new_agent_api_list) - set(current_branch_apidoc_list)
            logger.info("没有写apidoc的新接口：{}".format(no_api_doc_api))
            if no_api_doc_api:
                no_apidoc_warning.append("[{}]-[当前版本:{}]有如下新接口需编写apidoc:{}-[基准版本:{}]".format(
                    app_info, branch, ','.join(no_api_doc_api), archive_branch_name))
                warnings_result["no_apidoc_api_flg"] = True
                no_api_doc_apis = no_api_doc_api
        else:
            no_apidoc_warning.append("[{}]-[当前版本:{}]有如下新接口需编写apidoc:{}-[基准版本:{}]".format(
                app_info, branch, ','.join(new_agent_api_list), archive_branch_name))
            warnings_result["no_apidoc_api_flg"] = True
            no_api_doc_apis = new_agent_api_list
        if no_api_doc_apis:
            for no_api_doc_api_item in no_api_doc_apis:
                no_apidoc_api.append({"risk_item": no_api_doc_api_item,
                                      "warn_content": "[当前版本:{}]有接口，无apidoc-[基准版本:{}]".format(branch,
                                                                                                          archive_branch_name)})
        return no_apidoc_api

    @staticmethod
    def get_apidoc_list(app_info, branch):
        with DBConnectionManagerForSqlalchemy() as db:
            current_apidoc_list_db = db.session.query(AppMgtApidocInfo).filter(
                AppMgtApidocInfo.module_name == app_info,
                AppMgtApidocInfo.iter_branch == branch,
                AppMgtApidocInfo.api_type != "mq"
            ).outerjoin(AppMgtInterfaceAndApiExclude,
                        AppMgtApidocInfo.api_path != AppMgtInterfaceAndApiExclude.interface_path).all()
            apidoc_list = []
            for row in current_apidoc_list_db:
                apidoc_path = "{}:{}:{}".format(row.api_path, row.api_method, row.api_type)
                apidoc_list.append(apidoc_path)
            return apidoc_list

    @staticmethod
    def get_current_branch_apidoc_list(app_info, branch, current_branch_apidoc_list):
        with DBConnectionManagerForSqlalchemy() as db:
            current_apidoc_list_db = db.session.query(AppMgtApidocInfo).filter(
                AppMgtApidocInfo.module_name == app_info,
                AppMgtApidocInfo.iter_branch == branch
            ).outerjoin(AppMgtInterfaceAndApiExclude,
                        AppMgtApidocInfo.api_path != AppMgtInterfaceAndApiExclude.interface_path).all()
            for row in current_apidoc_list_db:
                if row.api_type.upper() == 'HTTP' and row.api_method.upper() in ["GET", "POST", "DELETE",
                                                                                 "PUT"]:
                    current_branch_apidoc_list.append(
                        row.api_path + ":" + "GET" + ":" + row.api_type)
                    current_branch_apidoc_list.append(
                        row.api_path + ":" + "POST" + ":" + row.api_type)
                    current_branch_apidoc_list.append(
                        row.api_path + ":" + "DELETE" + ":" + row.api_type)
                    current_branch_apidoc_list.append(
                        row.api_path + ":" + "PUT" + ":" + row.api_type)
                current_branch_apidoc_list.append(
                    row.api_path + ":" + row.api_method + ":" + row.api_type)

    @staticmethod
    def check_deploy_success_in_last_hour(iteration_id, app_name):
        with DBConnectionManager() as db:
            sql = '''
                SELECT *
        FROM pipeline_log_main
        WHERE iteration_id = '{}'
          AND app_name = '{}'
          AND status = 'success'
          AND suite_name IS NOT NULL
          AND LENGTH(suite_name) > 0 
          AND end_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY end_at DESC
        LIMIT 1; '''.format(iteration_id, app_name)
            db.cur.execute(sql)
        return db.cur.fetchall()


class QualityReportCheckUtil:

    @staticmethod
    def not_check(iteration_id, app_name, check_name):
        switch = GuardSwitchInfo.get(GuardSwitchInfo.guard_name == check_name)
        if switch and not switch.guard_switch:
            return True
        app_name_list = get_need_online_server([app_name])
        if not app_name_list:
            return True
        data_list = BranchesModel.select(BranchesModel.br_style).where(BranchesModel.pipeline_id == iteration_id,
                                                                       BranchesModel.br_style == 'release')
        if not data_list:
            return True
        wl_switch_id = switch.id
        guard_info = get_guard_info(iteration_id, app_name, wl_switch_id)
        if guard_info.get("pass"):
            return True
        return False

    @staticmethod
    def quality_report_check(app_name, iteration_id, br_name):
        business_name = 'risk_check'
        obj = BranchesModel.select().where(BranchesModel.pipeline_id == iteration_id).first()
        br_style = obj.br_style
        obj = BizTestAppNeed.select().where(BizTestAppNeed.module_name == app_name).first()
        check_result = False
        result_list = []
        if obj and obj.need_auto_test:
            if br_style == "release":
                app_auto_test_check_param = AppAutoTestCheckParam(business_name=business_name,
                                                                  biz_flow_name=obj.biz_flow_name,
                                                                  suite_code=None,
                                                                  pass_rate_threshold=obj.pass_rate_threshold,
                                                                  app_name=app_name,
                                                                  br_name=br_name,
                                                                  iteration_id=iteration_id,
                                                                  biz_code=obj.biz_code)
                status, msg = AutoTestChecker.check_app_auto_test_result(app_auto_test_check_param)
                logger.info('自动化测试结果，应用：{}-版本：{}-：结果{}，msg:{}'.format(app_name, br_name, status, msg))
                if status == "success":
                    result_list.append({"risk_item": '自动化测试报告', "warn_content": '自动化测试结果为通过'})
                else:
                    check_result = True
                    result_list.append({"risk_item": '自动化测试报告', "warn_content": msg})
        else:
            result_list.append({"risk_item": '自动化测试报告', "warn_content": '无需检查自动化测试报告'})

        result, msg = IterQualityReportChecker.check_iter_quality_report(iteration_id, business_name="risk_check")
        result_list.append({"risk_item": '人工测试报告', "warn_content": msg})
        check_result = check_result if check_result else not result

        return check_result, result_list


def check_merged_master(iteration_id, app_name):
    # 1 绿色 有最近归档的提交
    # 0 红色 无最近归档的提交
    # 空 黄色 最近没构建过
    with DBConnectionManager() as db:
        # 查询merged_master 同时查询git_last_update是不是一天前
        sql = '''
          SELECT
	           merged_master,
          CASE
          WHEN git_last_update IS NULL OR TRIM(git_last_update) = '' THEN 'Yes'
		  WHEN git_last_update <= DATE_SUB( NOW(), INTERVAL 1 DAY ) THEN
		  'Yes' ELSE 'No' 
	      END AS is_one_day_ago 
                FROM iter_mgt_iter_app_info 
          WHERE
                appName = '{}' 
                AND pipeline_id = '{}' limit 1; '''.format(app_name, iteration_id)
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row['merged_master'], row['is_one_day_ago']
    return None, None


def check_last_archive_info(app_name):
    with DBConnectionManager() as db:
        sql = '''
           SELECT
                i.appName,
                m.br_end_date,
                m.br_name
            FROM
                iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id and i.appName='{}'
            WHERE
                m.br_status = 'close' 
                ORDER BY  m.br_end_date DESC limit 1; '''.format(app_name)
        db.cur.execute(sql)
        last_archive_info = {"appName": app_name, "br_end_date": None, "br_name": None}
        for row in db.cur.fetchall():
            last_archive_info = row
    return last_archive_info


class SonarCheckUtil:

    @staticmethod
    def not_check(iteration_id, app_name):
        if not GuardSwitchInfo.get(GuardSwitchInfo.guard_name == 'sonar').guard_switch:
            return True
        app_name_list = get_need_online_server([app_name])
        if not app_name_list:
            return True
        sonar_switch_id = 2
        guard_info = get_guard_info(iteration_id, app_name, sonar_switch_id)
        if guard_info.get("pass"):
            return True
        return False


if __name__ == '__main__':
    # apidoc = ApiDocCheckUtil()
    # apidoc = apidoc.not_check("prod-info_1.0.0", "prod-mq-pqc-info")
    interface_list = ApiDocCheckUtil.get_current_or_achieve_branch_interface_list('release-ams-20240421-flow1.13',
                                                                                  'howbuy-ams-server')
    logger.info(len(interface_list))
