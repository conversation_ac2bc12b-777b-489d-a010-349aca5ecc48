from iter_mgt.in_transit_check.api_threshold.api_threshold_risk_state_aggregator import ApiThresholdRiskStateAggregator
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, SensorEnum


# api接口信息变化超过阀值风险探测器
class ApiThresholdRiskSensor(IRiskSensible):
    order = 5
    risk_state_agg_strategy: IRiskStateAggregator = ApiThresholdRiskStateAggregator()
    result_type: str = RiskResultShowTypeEnum.TABLE.value
    check_type: str = SensorEnum.INTERFACE.type_name
    check_type_desc: str = SensorEnum.INTERFACE.type_name

    def __init__(self, script_param):
        super().__init__(script_param)
