from iter_mgt.in_transit_check.check_utils.api_delete_check_util import ApiDelete<PERSON>heckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShow<PERSON>ype<PERSON><PERSON>, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class ApiThresholdRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.INTERFACE.type_name
    risk_check_phase = RiskCheckPhaseEnum.EXEMPT.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        if ApiDeleteCheckUtil.not_check(sensor_result_item.iteration_id, sensor_result_item.app_name):
            return RiskStateResult(True, 'API_CHECK免检', RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, None, None)
