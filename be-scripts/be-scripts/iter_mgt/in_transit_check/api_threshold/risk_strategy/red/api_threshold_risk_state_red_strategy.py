from iter_mgt.in_transit_check.check_utils.api_delete_check_util import ApiDeleteCheckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult
from settings import INTERFACE_SCAN


class ApiThresholdRiskStateRedStrategy(IRiskStateStrategy):
    check_type = SensorEnum.INTERFACE.type_name
    risk_check_phase = RiskCheckPhaseEnum.RED.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        interface_check_threshold = int(INTERFACE_SCAN['interface_check_threshold'])
        delete_api = ApiDeleteCheckUtil.api_delete_gt_threshold_check(sensor_result_item.app_name, sensor_result_item.branch, interface_check_threshold)
        if delete_api[0]:
            return RiskStateResult(True, delete_api[1], RiskResultShowTypeEnum.TABLE.value)
        return RiskStateResult(False, None, None)
