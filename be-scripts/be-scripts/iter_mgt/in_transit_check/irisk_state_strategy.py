from iter_mgt.in_transit_check.risk_sensor_enum import RiskCheckPhaseEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


# 风险状态策略
class IRiskStateStrategy:
    check_type = None
    risk_check_phase = RiskCheckPhaseEnum.EXEMPT.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        return RiskStateResult(False, None, None)
