# 检查器接口
import json
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from iter_mgt.in_transit_check.risk_sensor_result import Sen<PERSON>Result
from dao.update.mysql.service_results import update_exec_results_no_single

from iter_mgt.in_transit_check.risk_sensor_enum import RiskLevelEnum

from dao.get.mysql.service_results import get_exec_params
from settings import logger, RISK_SENSOR
import gc
import inspect
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.risk_imports import *


# 风险检查器链
class RiskSensorChain:
    def __init__(self, sid, risk_sensors):
        # 排序
        risk_sensors.sort(key=lambda sensor: sensor.order)
        self.sid = sid
        self.risk_sensors = risk_sensors
        self.sensor_result = SensorResult()

    def do_sensor(self):
        for sensor in self.risk_sensors:
            sensor.inspect(self.sensor_result)
        self._agg_result()
        self._recode_result()

    # 保存风险探测结果
    def _recode_result(self):
        logger.info(json.dumps(self.sensor_result.sensor_results, indent=4, ensure_ascii=False))
        logger.info("上报风险结果:start")
        update_exec_results_no_single(self.sid, self.sensor_result.status,
                                      self.sensor_result.sensor_results)
        logger.info("上报风险结果:end")

    # 获取远程调用参数
    def _get_params(self, sid):
        """获取调用参数"""
        return json.loads(get_exec_params(sid))

    def _agg_result(self):
        # 汇总每个应用的总风险级别
        print("汇总每个应用的总风险级别")
        agg_result = {}
        for app_name, sensor_result in self.sensor_result.sensor_results.items():
            app_risk_detail = {}
            app_risk_detail['risk_infos'] = sensor_result
            risk_level_block = None
            risk_level_waring = None
            risk_level_pass = 'green'
            for risk_detail in sensor_result:
                if risk_detail.get('risk_level') == RiskLevelEnum.RED.type_name:
                    risk_level_block = RiskLevelEnum.RED.type_name
                elif risk_detail.get('risk_level') == RiskLevelEnum.YELLOW.type_name:
                    risk_level_waring = RiskLevelEnum.YELLOW.type_name
                else:
                    risk_level_pass = RiskLevelEnum.GREEN.type_name
            if risk_level_block:
                app_risk_detail['risk_level'] = risk_level_block
            elif risk_level_waring:
                app_risk_detail['risk_level'] = risk_level_waring
            else:
                app_risk_detail['risk_level'] = risk_level_pass
            agg_result[app_name] = app_risk_detail
        self.sensor_result.set_sensor_results(agg_result)

    # 风险探测器工厂


class RiskSensorFactory(object):
    @staticmethod
    def create_all_risk_sensor(script_param):
        # 获取当前所有的对象
        all_objects = gc.get_objects()
        # 存储所有实现了IRiskSensible接口的类
        classes = []
        for obj in all_objects:
            # 检查对象是否是一个类
            if inspect.isclass(obj):
                # 检查类是否是IRiskSensible的子类
                if issubclass(obj, IRiskSensible) and obj is not IRiskSensible and obj.check_type in RISK_SENSOR[
                    'enable_risk_sensor']:
                    classes.append(obj)
        logger.info("获取所有实现类:{}".format([cls(script_param) for cls in classes]))
        # 返回所有实例
        return [cls(script_param) for cls in classes]


if __name__ == '__main__':
    logger.info("调用 {}".format(sys.argv[1:]))
    sid = sys.argv[1]
    script_param = json.loads(get_exec_params(sid))
    # 获取所有实现类
    risk_sensors = RiskSensorFactory.create_all_risk_sensor(script_param)
    # 构建检查器链
    risk_sensor_chain = RiskSensorChain(sid, risk_sensors)
    # 执行检查
    risk_sensor_chain.do_sensor()
