from dao.connect.mysql import DBConnectionManager
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class OnlineChannelRiskStateRedStrategy(IRiskStateStrategy):
    check_type = SensorEnum.ONLINE_CHANNEL.type_name
    risk_check_phase = RiskCheckPhaseEnum.RED.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        online_iter = check_online_channel(sensor_result_item.iteration_id,
                                           sensor_result_item.app_name)
        if online_iter:
            return RiskStateResult(True, "上线通道被迭代：{} 占用".format(online_iter), RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, "通过", RiskResultShowTypeEnum.TEXT.value)


def check_online_channel(iteration_id, app_name):
    with DBConnectionManager() as db:
        sql = '''
          SELECT pipeline_id 
                FROM iter_mgt_iter_app_info 
          WHERE
                sys_status = '上线中' 
                AND appName = '{}' 
                AND pipeline_id != '{}' limit 1; '''.format(app_name, iteration_id)
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row['pipeline_id']
    return None