
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.online_channel.online_channel_risk_state_aggregator import \
    OnlineChannelRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, SensorEnum


# 上线通道风险探测器
class OnlineChannelRiskSensor(IRiskSensible):
    order = 3
    risk_state_agg_strategy: IRiskStateAggregator = OnlineChannelRiskStateAggregator()
    result_type: str = RiskResultShowTypeEnum.TEXT.value
    check_type: str = SensorEnum.ONLINE_CHANNEL.type_name
    check_type_desc: str = SensorEnum.ONLINE_CHANNEL.type_desc

    def __init__(self, script_param):
        super().__init__(script_param)
