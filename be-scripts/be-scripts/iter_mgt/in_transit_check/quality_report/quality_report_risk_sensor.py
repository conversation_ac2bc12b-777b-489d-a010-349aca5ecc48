from iter_mgt.in_transit_check.quality_report.quality_report_risk_state_aggregator import QualityReportRiskStateAggregator
from iter_mgt.in_transit_check.irisk_sensible import IRiskSensible
from iter_mgt.in_transit_check.irisk_state_aggregatable import IRiskStateAggregator
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, SensorEnum


# 自动化测试超过阀值风险探测器
class QualityReportRiskSensor(IRiskSensible):
    order = 6
    risk_state_agg_strategy: IRiskStateAggregator = QualityReportRiskStateAggregator()
    result_type: str = RiskResultShowTypeEnum.TABLE.value
    check_type: str = SensorEnum.AUALITY_REPORT.type_name
    check_type_desc: str = SensorEnum.AUALITY_REPORT.type_desc

    def __init__(self, script_param):
        super().__init__(script_param)

