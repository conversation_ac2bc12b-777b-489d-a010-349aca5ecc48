from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import QualityReportCheckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowType<PERSON>num, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class QualityReportRiskStateExemptStrategy(IRiskStateStrategy):
    check_type = SensorEnum.AUALITY_REPORT.type_name
    risk_check_phase = RiskCheckPhaseEnum.EXEMPT.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        check_name = 'quality_report'
        if QualityReportCheckUtil.not_check(sensor_result_item.iteration_id, sensor_result_item.app_name, check_name):
            return RiskStateResult(True, '自动化测试免检', RiskResultShowTypeEnum.TEXT.value)
        return RiskStateResult(False, None, RiskResultShowTypeEnum.TEXT.value)
