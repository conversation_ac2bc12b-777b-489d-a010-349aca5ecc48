from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import QualityReportCheckUtil
from iter_mgt.in_transit_check.irisk_state_strategy import IRiskStateStrategy
from iter_mgt.in_transit_check.risk_sensor_enum import RiskResultShowTypeEnum, RiskCheckPhaseEnum, \
    SensorEnum
from iter_mgt.in_transit_check.risk_sensor_result import SensorResultItem, RiskStateResult


class QualityReportRiskRedStrategy(IRiskStateStrategy):
    check_type = SensorEnum.AUALITY_REPORT.type_name
    risk_check_phase = RiskCheckPhaseEnum.RED.phase_name

    def inspect(self, sensor_result_item: SensorResultItem) -> RiskStateResult:
        no_api = QualityReportCheckUtil.quality_report_check(sensor_result_item.app_name, sensor_result_item.iteration_id, sensor_result_item.branch)
        if no_api[0]:
            return RiskStateResult(True, no_api[1], RiskResultShowTypeEnum.TABLE.value)
        return RiskStateResult(False, no_api[1], RiskResultShowTypeEnum.TABLE.value)
