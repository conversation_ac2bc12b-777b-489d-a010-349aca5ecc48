#!/usr/bin/python
#-*- coding: utf-8 -*-
import subprocess
import smtplib
from email.mime.text import MIMEText    #导入MIMEText
import sys
import os
import re
#from lxml import etree
import time
#reload(sys);
#sys.setdefaultencoding('utf8')


#   [1] REPOS-PATH   (the path to this repository)
#   [2] REV          (the number of the revision just committed)
#   [3] TXN-NAME     (the name of the transaction that has become REV)


class Svntag():
    def check_contain_chinese(self,check_str):
        for ch in check_str.decode('utf-8'):
            if u'\u4e00' <= ch <= u'\u9fff':
               return True
        return False


    def shellSvn(self,command):
        proc=subprocess.Popen(command,shell=True,stdout=subprocess.PIPE,stderr=subprocess.PIPE)#打包

        mvn_log=proc.stdout.read()
        print("执行结果是什么")
        print(mvn_log)

        return mvn_log
    def svnTag(self,trunk_path,tag_path):
        command_del='del %s'%tag_path
        command_copy='svn copy %s %s'%(trunk_path,tag_path)
        self.shellSvn(command_del)
        self.shellSvn(command_copy)
    def main(self,argv):
        (repos, txn) = argv
        svnlook='/usr/bin/svnlook'
        svn_repos='svn://svn-repos.howbuy.pa/repos/'
        author=self.shellSvn('%s author -r %s %s'%(svnlook,txn,"/data/svn_repos_dir/repos/"))  #提交作者
        changeddirs=self.shellSvn('%s dirs-changed %s '%(svnlook,"/data/svn_repos_dir/repos/"))
        #changeddirs='WEB/trunk/static/tstatic/webapps'

        #message=self.shellSvn('%s log -r %s %s'%(svnlook,txn,repos))
        print ('svn log -r %s -v %s'%(txn,svn_repos))
        message2=self.shellSvn('svn log -r %s -v %s'%(txn,svn_repos))
        print message2
        #print message2
        return (changeddirs,author,message2,txn)

    def findDiff(self,svn_path,svn_version,local_path):
        svn_log_com='svn log -v %s -r %s'%(svn_path,svn_version)#获取svnlog
        print svn_log_com
        if 'svn://svn-repos.howbuy.pa/repos' in svn_path:
            svn_log_file_path=svn_path.replace('svn://svn-repos.howbuy.pa/repos','')
            print svn_log_file_path#截取svn域名之后的路径
        elif 'svn://svn-repos.howbuy.pa/repos' in svn_path:
            svn_log_file_path=svn_path.replace('svn://svn-repos.howbuy.pa/repos','')
            print svn_log_file_path#截取svn域名之后的路径

        s2='.*%s(.*)'%svn_log_file_path#匹配svn相对库的相对路径的修改日志 help库之后的日志
        svn_modify_log=self.shellSvn(svn_log_com)
        print svn_modify_log
        modify_files=re.findall(s2,self.shellSvn(svn_log_com))#获取发生更改的文件目录
        s3='(.*/)'#获取斜杠之前的信息
        s='.*/(.*)'#获取最后一个斜杠之后的信息，也是就更改的文件
        s4=' .D %s(.*)'%svn_log_file_path#找到类型为删除的文件
        print modify_files
        for i in  re.findall(s4,svn_modify_log):#删除已经删除的文件
            if '(' in i:#如果存在(就截取之前的信息
                i=re.findall('(.*)\(',i)[0]
            print local_path+i
            os.system('rm -rf %s'%local_path+i)
            os.system('svn delete %s'%local_path+i)
        #print modify_files
        for i in modify_files:#覆盖修改的文件
            if '(' in i:#如果存在(就截取之前的信息
                i=re.findall('(.*)\(',i)[0]
            modify_file=svn_path+i#svn修改文件的绝对路径
            mkdir_com='mkdir -p %s'%local_path+re.findall(s3,i)[0]#通过找到的文件，创建文件目录
            os.system(mkdir_com)
            #导出本次修改的文件到本地目录
            print local_path+i
            svn_export='svn export --force %s %s'%(modify_file,local_path+i)
            #print svn_export
            os.system(svn_export)
            os.system('svn add %s'%local_path+i)
        return svn_modify_log#返回修改的日志信息
    def eMail(self,my_msg,changeddirs,author,message):
        #mail_info={'host':"smtp.aliyun.com",'mail_from':"<EMAIL>",'mail_passwd':"howbuy123"}
        mail_info={'host':"c1.icoremail.net",'mail_from':"<EMAIL>",'mail_passwd':"howbuy!@#",'mail_from_tile':'SCM系统邮件'}
        HOST = mail_info['host']
        #HOST = "smtp.163.com"    #定义smtp主机
        SUBJECT = my_msg['subject']   #定义邮件主题
        TO = my_msg['to']  #定义邮件收件人
        #FROM = "<EMAIL>"    #定义邮件发件人
        if 'rsyc_log' in my_msg:
            msg = MIMEText("""
            <table width="800" border="0" cellspacing="0" cellpadding="4">
              <tr>
                <td bgcolor="#CECFAD" height="20" style="font-size:14px">SVN修改详情  </td>
              </tr>
              <tr>
                <td bgcolor="#EFEBDE" height="100" style="font-size:13px">
                1）修改项目:  %s<br>
                2）修改人<br>
                &nbsp;&nbsp;%s<br>
                3）提交日志<br>
                &nbsp;&nbsp;%s<br>
                4）rsyc传输日志<br>
                &nbsp;&nbsp;%s<br>


                </td>
              </tr>
            </table>"""%(changeddirs,author,message.replace('\n','<br/>'),my_msg['rsyc_log'].replace('\n','<br/>')),"html","utf-8")
        else:
            msg = MIMEText("""
                <table width="800" border="0" cellspacing="0" cellpadding="4">
                    <tr>
                <td bgcolor="#CECFAD" height="20" style="font-size:14px">SVN修改详情  </td>
                    </tr>
                      <tr>
                    <td bgcolor="#EFEBDE" height="100" style="font-size:13px">
                1）修改项目:  %s<br>
                2）修改人<br>
                &nbsp;&nbsp;%s<br>
                3）提交日志<br>
                &nbsp;&nbsp;%s<br>


                </td>
              </tr>
            </table>"""%(changeddirs,author,message.replace('\n','<br/>')),"html","utf-8")
        msg['Subject'] = SUBJECT    #邮件主题
        msg['From']=mail_info['mail_from_tile']    #邮件发件人,邮件头部可见
        msg['To']= ",".join(TO)    #邮件收件人,邮件头部可见
        try:
            server = smtplib.SMTP()    #创建一个SMTP()对象
            server.connect(HOST,"25")    #通过connect方法连接smtp主机
            server.starttls()    #启动安全传输模式
            server.login(mail_info['mail_from'],mail_info['mail_passwd'])    #邮箱账号登录校验
            server.sendmail(mail_info['mail_from'], TO, msg.as_string())      #邮件发送
            server.quit()    #断开smtp连接
            print "邮件发送成功！"
        except Exception , e:
            print "失败："+str(e)

    def get_project(self,changeddirs):
        svn_split=changeddirs.split("/")
        return svn_split[svn_split.index("OC")+1]

    def sendMail(self,argv):
        print(argv)
        (changeddirs,author,message,svn_version)=self.main(argv)
        print(message)
        my_msg={}
        print (changeddirs,author,message,svn_version)
        if "OC/" in changeddirs:
            svn_path='svn://svn-repos.howbuy.pa/repos/OC'
            svn_oc_path='/data/workspace/svn_sync/web_repos/oc'#oc检出本机路径
            rsync_logs=self.rsync_ops(svn_oc_path,svn_path,svn_version)
            if rsync_logs:
            #self.get_project(changeddirs)
                my_msg['subject'] = u"{}项目修改".format(self.get_project(changeddirs))    #定义邮件主题
           # my_msg['to']= ["<EMAIL>","<EMAIL>"]  #定义邮件收件人
                my_msg['to']= ["<EMAIL>","<EMAIL>"]  #定义邮件收件人
             #   my_msg['to']=["<EMAIL>"]
                my_msg['rsyc_log']="\n".join(rsync_logs)
                self.eMail(my_msg,changeddirs,author,message)


        if 'trunk_tag' in changeddirs and 'Sources' in changeddirs:

            my_msg['subject'] = u"trunk_tag分支发生代码修改，请进行确认"    #定义邮件主题
            my_msg['to']= ["<EMAIL>","<EMAIL>","<EMAIL>"]  #定义邮件收件人
            #my_msg['to']=["<EMAIL>"]
            self.eMail(my_msg,changeddirs,author,message)

        elif '/trunk/' in changeddirs and '/static/' in changeddirs and '/WEB/' in changeddirs:
            my_msg['subject'] = u"静态资源trunk发生修改，请确认"    #定义邮件主题
            my_msg['to']= ["<EMAIL>","<EMAIL>","<EMAIL>"]  #定义邮件收件人
            #my_msg['to']=["<EMAIL>"]

            self.eMail(my_msg,changeddirs,author,message)
            if '/tstatic' in changeddirs:
                svn_path='svn://svn-repos.howbuy.pa/repos/WEB/trunk/static/tstatic'#web_repos库静态济源地址
                svn_tstatic_repos_locate_path='/data/workspace/svn_sync/repos/tstatic'#本地repos库静态资源地址
                svn_tstatic_web_repos_locate_path='/data/workspace/svn_sync/web_repos/tstatic'#本地web_repos库静态资源地址

                svn_update_common='svn update %s'%svn_tstatic_repos_locate_path#更新repos库
                self.shellSvn(svn_update_common)
                author=self.findDiff(svn_path,svn_version,svn_tstatic_web_repos_locate_path)#找到web库修改的文件更新到本地web_repos_locate_path
                #print author
                #os.system('svn add %s'%svn_tstatic_web_repos_locate_path)
                #message='svn commit -m %s add %s'%(message,svn_tstatic_web_repos_locate_path)
                #print message
                os.system('svn commit -m "%s" %s'%(message,svn_tstatic_web_repos_locate_path))#提交本次修改的内容
                my_msg['subject']=u"tstatic静态资源发生同步从repos同步至web_repos"    #定义邮件主题
                my_msg['to']=["<EMAIL>"]
                self.eMail(my_msg,changeddirs,author,message)

        elif 'EC/design/Build/static' in message:
            print("static")
            my_msg['subject'] = u"静态资源发生修改，请确认%s"%svn_version  # 定义邮件主题
            my_msg['to'] = ["<EMAIL>","<EMAIL>","<EMAIL>",
                            "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]  # 定义邮件收件人
            #my_msg['to'] = ["<EMAIL>"]  # 定义邮件收件人
            project_name_list=[]
            if "/wap/" in changeddirs:
                project_name_list.append("wap")
            elif "/www/" in changeddirs:
                project_name_list.append("www")
            elif "/hk/" in changeddirs:
                project_name_list.append("hk")
            print(project_name_list)
            for project_name in project_name_list:
                #os.chdir("/data/h5/svndir/%s" % project_name)
                print("cd /data/h5/svndir/%s && svn update" % project_name)
                os.system("cd /data/h5/svndir/%s && svn update" % project_name)
                now_svn_version=self.shellSvn("cd /data/h5/svndir/%s && svn log -l 1 |grep r | cut -d'|' -f1" % project_name)
                #如果没有更新下来则等待一秒，重新更新
                print(now_svn_version.strip().replace("r", ""), "    ", svn_version.strip().replace("r", ""))
                while now_svn_version.strip().replace("r","")!=svn_version.strip().replace("r", ""):
                    print (now_svn_version.strip().replace("r",""),"    ",svn_version.strip().replace("r", ""))
                    time.sleep(10)
                    os.system("cd /data/h5/svndir/%s && svn update"% project_name)
                    now_svn_version = self.shellSvn("cd /data/h5/svndir/%s && svn log -l 1 |grep r | cut -d'|' -f1" % project_name)
                print('rsync -avz --delete --exclude ".svn" --exclude ".git" /data/h5/svndir/%s/ /data/h5/gitdir/h5-%s/' % (project_name, project_name))
                self.shellSvn('rsync -avz --delete --exclude ".svn" --exclude ".git" /data/h5/svndir/%s/ /data/h5/gitdir/h5-%s/' % (project_name, project_name))
                os.chdir("/data/h5/gitdir/h5-%s" % project_name)
                self.shellSvn("cd /data/h5/gitdir/h5-%s && git add --all"% project_name)
                print("cd /data/h5/gitdir/h5-%s && git commit -m ' 修改人%s 修改日志 %s'" % (project_name, author, message))
                self.shellSvn("cd /data/h5/gitdir/h5-%s && git commit -m ' 修改人%s 修改日志 %s'" % (project_name, author, message))
                #os.chdir("/data/h5/gitdir/h5-%s" % project_name)
                #os.system("git commit -m '修改日志'")
                print("cd /data/h5/gitdir/h5-%s && git push -u origin master" % project_name)
                self.shellSvn("cd /data/h5/gitdir/h5-%s && git push -u origin master" % project_name)
            self.eMail(my_msg, changeddirs, author, message)


    #外部模块
    # def find_rsync_path(self,file_name,modify_project):
    #     if self.check_contain_chinese(file_name):#存在中文
    #         return 0
    #
    #
    #     with open (r"/data/app/svn/repos/hooks/out_config.xml","r") as f:#xml文件位置
    #         tree=etree.fromstring(f.read())
    #print file_name
    #print tree.xpath('//{0}//name[contains(text(),"{1}")]/parent::*/@rsync_path'.format(modify_project,file_name))

        # if tree.xpath('//{0}//name[contains(text(),"{1}")]/parent::*/@rsync_path'.format(modify_project,file_name))==[]:
        #     return 0
        # else:
        #     return tree.xpath('//{0}//name[contains(text(),"{1}")]/parent::*/@rsync_path'.format(modify_project,file_name))[0]#找到传输路径，匹配节点的父节点

    def find_war(self,modify_file):
        svn_split=modify_file.split("/")
        rsync_path=self.find_rsync_path(svn_split[-1],svn_split[svn_split.index("OC")+1])
        if rsync_path:
            return (modify_file,rsync_path)
        else:
            return 0

    def find_diff(self,svn_path,svn_version):
        svn_rsync_path=[]#svn检出路径和对应的服务器上传路径
        svn_log_com='svn log -v %s -r %s'%(svn_path,svn_version)#获取svnlog
    #print svn_log_com
        if 'svn://svn-repos.howbuy.pa/repos' in svn_path:
            svn_log_file_path=svn_path.replace('svn://svn-repos.howbuy.pa/repos','')
        #print svn_log_file_path#截取svn域名之后的路径
        elif 'svn://svn-repos.howbuy.pa/repos' in svn_path:
            svn_log_file_path=svn_path.replace('svn://svn-repos.howbuy.pa/repos','')
        print svn_log_file_path#截取svn域名之后的路径
        s2='.*%s(.*)'%svn_log_file_path#匹配svn相对库的相对路径的修改日志 help库之后的日志
        modify_files=re.findall(s2,self.shellSvn(svn_log_com))#获取发生更改的文件目录
    #print modify_files
        for i in modify_files:
            if '(' in i:#如果存在(就截取之前的信息
                i=re.findall('(.*)\(',i)[0]
            modify_file=svn_path+i#svn修改文件的绝对路径
        #print modify_file
            if self.find_war(modify_file.strip()):
                svn_rsync_path.append(self.find_war(modify_file.strip()))
        return svn_rsync_path

    def rsync_ops(self,local_path,svn_path,svn_version):
        svn_rsync_path=self.find_diff(svn_path,svn_version)
        rsync_log_list=[]
        if svn_rsync_path==[]:
            return 0
        else:
            for one_svn_rsync_path in svn_rsync_path:
                export_out_command="svn export --force {0} {1}".format(one_svn_rsync_path[0],os.path.join(local_path,one_svn_rsync_path[1],one_svn_rsync_path[0].split("/")[-1]))
           # print one_svn_rsync_path[0].split("/")[-1]
                rsync_command="rsync -av {0} *************::{1}".format(os.path.join(local_path,one_svn_rsync_path[1],one_svn_rsync_path[0].split("/")[-1]),one_svn_rsync_path[1])
                if os.path.isdir(os.path.join(local_path,one_svn_rsync_path[1])):#本地目录是否存在

                    os.system(export_out_command)
                else:
                    os.system("mkdir -p {}".format(os.path.join(local_path,one_svn_rsync_path[1])))
                    os.system(export_out_command)
                print rsync_command
                rsync_log_list.append(rsync_command+"\n"+self.shellSvn(rsync_command))
                #rsync_log_list.append(rsync_command)
            return rsync_log_list

    def removeUpLog(self,befor_remove_up_log):#去除upload的日志
        b=[]
        for i in befor_remove_up_log.split('\n'):
            if '100%' not in i:
                b.append(i)
        return '\n'.join(b)


#if __name__ == "__main__":
reload(sys)
sys.setdefaultencoding('utf8')
    #a=sys.argv[1:]
svntag=Svntag()
svntag.sendMail(sys.argv[1:])




#REPOS="$1"
#REV="$2"
#TXN_NAME="$3"
#mailer.py commit "$REPOS" "$REV" /path/to/mailer.conf
