#!/usr/bin/python
#-*- coding: utf-8 -*-
import sys
import os
import subprocess

class LogTest():
    def shellSvn(self,command):
        proc=subprocess.Popen(command,shell=True,stdout=subprocess.PIPE,stderr=subprocess.PIPE)#打包
        mvn_log=proc.stdout.read()
        return mvn_log
    def main(self,argv):
        (repos, txn) = argv
        svnlook='/usr/bin/svnlook'
        a='%s dirs-changed -r %s %s '%(svnlook,txn,repos)
        changeddirs=self.shellSvn('%s dirs-changed -t %s %s '%(svnlook,txn,repos))
        if 'trunk_tag' in changeddirs and 'Sources' in changeddirs:
            message = "".join(os.popen("%s log '%s' -t '%s'" % (svnlook,repos, txn)).readlines()).strip()
            if len(message) < 1:
                #sys.stderr.write(changeddirs)
                sys.stderr.write("日志不能为空")
                sys.exit(1)

        else:
            message = "".join(os.popen("%s log '%s' -t '%s'" % (svnlook,repos, txn)).readlines()).strip()
            if len(message) < 1:
                #sys.stderr.write(changeddirs)
                sys.stderr.write("日志不能为空")
                sys.exit(1)
        sys.exit(0)




logtest=LogTest()
logtest.main(sys.argv[1:])

