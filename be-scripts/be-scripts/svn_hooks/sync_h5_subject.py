import os
import smtplib
from email.mime.text import MIMEText
import sys
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from settings import logger, EMAIL_BASES, CODE_STYLE


class SubjectPublisher:
    exec_ip = "***************"
    exec_user = "root"
    exec_password = "howbuy1!"
    mail_info = {'host': EMAIL_BASES["MAIL_HOST"], 'mail_from': EMAIL_BASES["SENDER"], 'mail_passwd': EMAIL_BASES["PASSWORD"],
                 'mail_from_tile': 'SCM系统邮件'}

    def __init__(self, project):
        self.code_dir = "/data/h5/svndir/{}".format(project)
        logger.info(self.code_dir)
        self.lib_repo_dir = "/data/h5/gitdir/h5-{}".format(project)
        self.project_name = project

    def code_up(self, update_cmd):
        with SSHConnectionManager(self.exec_ip, self.exec_user, self.exec_password) as ssh:
            logger.info(update_cmd)
            tdin, stdout, stderr = ssh.SSH.exec_command(update_cmd, bufsize=-1)
            logger.info(stdout.read().decode(CODE_STYLE))

    def svn_code_up(self, update_cmd):
        with SSHConnectionManager(self.exec_ip, self.exec_user, self.exec_password) as ssh:
            logger.info(update_cmd)
            tdin, stdout, stderr = ssh.SSH.exec_command(update_cmd, bufsize=-1)
            l = stdout.read().decode(CODE_STYLE)
        return l

    def check_up(self):
        pass

    def lib_repo_publish(self):
        with SSHConnectionManager(self.exec_ip, self.exec_user, self.exec_password) as ssh:
            git_add_cmd = "cd {} && git add --all".format(self.lib_repo_dir)
            git_commit_cmd = "cd {} && git commit -m '手动同步'".format(self.lib_repo_dir)
            git_push_cmd = "cd {} && git push -u origin master".format(self.lib_repo_dir)
            logger.info(git_add_cmd)
            tdin, stdout, stderr = ssh.SSH.exec_command(git_add_cmd, bufsize=-1)
            logger.info(stdout.read().decode(CODE_STYLE))
            logger.info(git_commit_cmd)
            tdin, stdout, stderr = ssh.SSH.exec_command(git_commit_cmd, bufsize=-1)
            logger.info(stdout.read().decode(CODE_STYLE))
            logger.info(git_push_cmd)
            tdin, stdout, stderr = ssh.SSH.exec_command(git_push_cmd, bufsize=-1)
            logger.info(stdout.read().decode(CODE_STYLE))

    def sync_code(self, sync_cmd):
        with SSHConnectionManager(self.exec_ip, self.exec_user, self.exec_password) as ssh:
            logger.info(sync_cmd)
            tdin, stdout, stderr = ssh.SSH.exec_command(sync_cmd, bufsize=-1)
            logger.info(stdout.read().decode(CODE_STYLE))

    def __send_mail(self,msg,svn_version):
        logger.info('send_mail')
        HOST = self.mail_info['host']
        SUBJECT = u"静态资源发生修改，请确认%s" % svn_version
        TO = ["<EMAIL>"]
        msg['Subject'] = SUBJECT  # 邮件主题
        msg['From'] = self.mail_info['mail_from_tile']
        msg['To'] = ",".join(TO)
        try:
            server = smtplib.SMTP()
            server.connect(HOST, "25")
            server.login(self.mail_info['mail_from'], self.mail_info['mail_passwd'])
            server.sendmail(self.mail_info['mail_from'], TO, msg.as_string())
            server.quit()
            logger.info("邮件发送成功！")
        except Exception as e:
            logger.error("失败：" + str(e))

    def __email_template(self,message):
        msg = MIMEText("""
                        <table width="800" border="0" cellspacing="0" cellpadding="4">
                            <tr>
                        <td bgcolor="#CECFAD" height="20" style="font-size:14px">SVN修改详情  </td>
                            </tr>
                              <tr>
                            <td bgcolor="#EFEBDE" height="100" style="font-size:13px">
                        提交日志<br>
                        &nbsp;&nbsp;%s<br>


                        </td>
                      </tr>
                    </table>""" % (message), "html")
        return msg

    def __get_commit_info(self):
        svn_commit_log = self.svn_code_up("cd /data/h5/svndir/wap && svn log -r BASE:HEAD")
        svn_commit_version = self.svn_code_up("cd /data/h5/svndir/wap && svn log -r BASE:HEAD |grep r | cut -d'|' -f1")
        return svn_commit_log, svn_commit_version

    def main(self):
        if self.project_name == 'wap':
            svn_commit_log, svn_commit_version = self.__get_commit_info()
            msg = self.__email_template(svn_commit_log)
            self.__send_mail(msg,svn_commit_version)
        update_cmd = "cd {} && svn update".format(self.code_dir)
        sync_cmd = 'rsync -avz --delete --exclude ".svn" --exclude ".git" {}/ {}/'.format(self.code_dir,
                                                                                        self.lib_repo_dir)
        self.code_up(update_cmd)

        self.sync_code(sync_cmd)
        self.lib_repo_publish()


if __name__ == '__main__':
    project = sys.argv[1]
    sp = SubjectPublisher(project)
    sp.exec_ip
    sp.main()


