import datetime
import time

from common.k8s.k8s_operation import K8sOperation
from dao.update.mysql.pipeline_log import update_pipeline_main_log
from settings import logger, TEST_PUBLISH_AIO


class PublishStatusChecker:
    def __init__(self, suite_code, app_name):
        self.suite_code = suite_code
        self.app_name = app_name
        
    def batch_check_node_status(self, app_name_list, limit_time=300, time_step=5, agent_check=True):
        """
        批量检查多个应用的节点状态
        
        Args:
            app_name_list: 应用名称列表
            limit_time: 总超时时间（秒）
            time_step: 检查间隔时间（秒）
            agent_check: 是否等待agent数据处理
            
        Returns:
            成功返回True，失败抛出异常
        """
        ko = K8sOperation()
        total_limit_time = limit_time
        success_apps = set()
        failed_apps = set()
        
        # 转换应用名称为小写，以便与返回的状态信息匹配
        app_name_list_lower = [app_name.lower() for app_name in app_name_list]
        app_name_map = {app_name.lower(): app_name for app_name in app_name_list}
        
        while limit_time > 0 and len(success_apps) < len(app_name_list):
            # 一次性获取所有应用的状态信息
            try:
                all_node_status = ko.get_node_status(self.suite_code)
                
                # 检查所有未成功的应用
                for app_name_lower in app_name_list_lower:
                    if app_name_lower in success_apps or app_name_lower in failed_apps:
                        continue
                    
                    original_app_name = app_name_map[app_name_lower]
                    
                    if app_name_lower in all_node_status:
                        node_status_info = all_node_status[app_name_lower]
                        
                        if node_status_info["STATUS"] == "Running" and int(node_status_info["READY"].split("/")[0]) > 0:
                            logger.info("{}环境的{}应用启动成功".format(self.suite_code, original_app_name))
                            success_apps.add(app_name_lower)
                        else:
                            if int(node_status_info["RESTARTS"]) > 3:
                                logger.error("{}环境的{}应用重试3次后启动失败".format(self.suite_code, original_app_name))
                                failed_apps.add(app_name_lower)
                    else:
                        logger.warn("获取{}环境{}应用状态失败: 应用不存在".format(self.suite_code, original_app_name))
            except Exception as e:
                logger.warn("获取{}环境应用状态失败: {}".format(self.suite_code, str(e)))
            
            # 检查是否所有应用都已处理完成
            if len(success_apps) + len(failed_apps) >= len(app_name_list_lower):
                break
                
            # 将小写应用名转换回原始名称用于日志显示
            success_apps_original = [app_name_map[app] for app in success_apps]
            failed_apps_original = [app_name_map[app] for app in failed_apps]
            waiting_apps_original = [app_name_map[app] for app in app_name_list_lower 
                                    if app not in success_apps and app not in failed_apps]
            
            logger.info("已成功启动应用: {}, 失败应用: {}, 等待中应用: {}".format(
                success_apps_original, 
                failed_apps_original, 
                waiting_apps_original
            ))
            
            logger.info("{}秒后重新检查应用状态，剩余检测时间: {}s".format(time_step, limit_time))
            limit_time = limit_time - time_step
            time.sleep(time_step)
        
        # 处理结果
        if len(success_apps) == len(app_name_list_lower):
            # 将小写应用名转换回原始名称用于日志显示
            success_apps_original = [app_name_map[app] for app in success_apps]
            logger.info("所有应用启动成功: {}".format(success_apps_original))
            if agent_check:
                sleep_second = TEST_PUBLISH_AIO["app_start_check_sleep_time"]
                time.sleep(int(sleep_second))
                logger.info("等待{}s，agent数据处理！".format(sleep_second))
            return True
        else:
            # 获取未成功启动的应用列表
            timeout_apps = [app for app in app_name_list_lower if app not in success_apps and app not in failed_apps]
            all_failed_apps_lower = list(failed_apps) + timeout_apps
            
            # 将小写应用名转换回原始名称用于错误消息显示
            all_failed_apps_original = [app_name_map[app] for app in all_failed_apps_lower]
            
            error_msg = "{}环境的应用 {}分钟内未全部启动成功，失败应用列表: {}".format(
                self.suite_code, 
                total_limit_time/60,
                all_failed_apps_original
            )
            logger.error(error_msg)
            raise IOError(error_msg)

    def check_node_status(self, limit_time=300, time_step=5, agent_check=True):
        ko = K8sOperation()
        total_limit_time = limit_time
        while limit_time > 0:
            try:
                node_status_info = ko.get_node_status(self.suite_code, app_name=self.app_name)
            except Exception as e:
                raise IOError("获取{}环境{}应用状态失败".format(self.suite_code, self.app_name))
            if node_status_info["STATUS"] == "Running" and int(node_status_info["READY"].split("/")[0]) > 0:
                logger.info("{}环境的{}应用启动成功".format(self.suite_code, self.app_name))
                if agent_check:
                    sleep_second = TEST_PUBLISH_AIO["app_start_check_sleep_time"]
                    time.sleep(int(sleep_second))
                    logger.info("等待{}s，agent数据处理！".format(sleep_second))
                return True
            else:
                if int(node_status_info["RESTARTS"]) > 3:
                    raise IOError("{}环境的{}应用 重试3后启动失败".format(self.suite_code, self.app_name))
            logger.warn("{}应用未启动，{}秒后重试".format(self.app_name, time_step))
            limit_time = limit_time - time_step
            time.sleep(time_step)
            logger.info("剩余检测时间: {}s".format(limit_time))
        else:
            logger.error("{}环境的{}应用 {}分钟内未启动成功，请自行排查，谢谢！".format(self.suite_code, self.app_name,
                                                                  total_limit_time/60))
            raise IOError("{}环境的{}应用 {}分钟内未启动成功，请自行排查，谢谢！".format(self.suite_code, self.app_name,
                                                                  total_limit_time/60))

    def check_node_status_once(self):
        ko = K8sOperation()
        try:
            node_status_info = ko.get_node_status(self.suite_code, app_name=self.app_name)
            if node_status_info["STATUS"] == "Running" and int(node_status_info["READY"].split("/")[0]) > 0:
                logger.info("{}应用状态正常".format(self.app_name))
                return True
            else:
                logger.info("{}应用未启动".format(self.app_name))
                return False
        except IOError as e:
            logger.info("{}应用未启动".format(self.app_name))
            return False


if __name__ == "__main__":
    publish_starts_checker = PublishStatusChecker("it29", "acc-center-web")
    publish_starts_checker.check_node_status_once()
