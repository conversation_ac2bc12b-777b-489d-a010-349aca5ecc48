from settings import logger
from ci_pipeline.ci_pipeline_models.iter_models import IterAppInfoModel
from app_mgt.models import AppBuildModel


class IterDataCompensation:
    __iter_app_info_dto = IterAppInfoModel
    __app_build_dto = AppBuildModel

    def __init__(self):
        pass

    def _get_iter_module(self, iter_id):
        module_list = []
        for row in self.__iter_app_info_dto.select().where(self.__iter_app_info_dto.pipeline_id == iter_id):
            module_list.append(row.appName)
        logger.info(module_list)
        return module_list

    def _obtain_unrecorded_modules(self, module_recorded, module_actual):
        """

        :param module_recorded: list 被记录过的module
        :param module_actual: dict 真是世界中的存在的module
        :return: .dict {module_name : pom_path}
        """
        return {module_name: module_actual[module_name] for module_name in module_actual
                if module_name in module_recorded}

    def _obtain_needless_recording(self, module_recorded, module_actual):
        """

        :param module_recorded: 被记录过的module
        :param module_actual:  真是世界中的存在的module
        :return: .list [module_name..]
        """
        return [module_name for module_name in module_recorded if module_name not in module_actual]

    def _create_iter_data(self, need_add_data):
        """

        :param need_add_data:
        :return:
        """
        iter_data_list = []
        for row in self.__app_build_dto.select().where(self.__app_build_dto.module_name in need_add_data):
            iter_data_list.append({"appName": row.module_name,
                                   "jdkVersion": row.build_jdk_version,
                                   "pom_path": need_add_data[row.module_name]})
        return iter_data_list

    def complete_data(self, iter_id, module_actual):
        """

        :param iter_id:
        :param module_actual:
        :return:
        """
        module_list = self._get_iter_module(iter_id)
        need_add_data = self._obtain_unrecorded_modules(module_list, module_actual)
        iter_data_list = self._create_iter_data(need_add_data)
        self._insert_unrecorded_data(iter_data_list)

    def _insert_unrecorded_data(self, iter_id, iter_data_list):
        """

        :param iter_id:
        :param iter_data_list:
        :return:
        """
        for row in iter_data_list:
            self.__iter_app_info_dto.create(pipeline_id=iter_id,
                                           pom_path=row["pom_path"],
                                           appName=row["appName"],
                                           sys_status="测试中",
                                           proposer="howbuyscm",
                                           jdkVersion=row["jdkVersion"])

    def _delete_needless_data(self, iter_id, module_list):
        self.__iter_app_info_dto.delete().where(self.__iter_app_info_dto.pipeline_id == iter_id,
                                                self.__iter_app_info_dto.appName in module_list).execute()

    def iter_data_compensate(self, iter_id, module_actual):
        self.complete_data(iter_id, module_actual)
        

if __name__ == "__main__":
    idc = IterDataCompensation()
    idc._get_iter_module("asset_unit0.0.0")
