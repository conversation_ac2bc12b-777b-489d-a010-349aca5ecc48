from time import sleep

from ci_pipeline.ci_pipeline_models.env_node_bind_table import EnvNodeBindTable
from settings import logger, TEST_PUBLISH_AIO as TPA
from test_mgt.test_mgt_ser import get_k8s_server
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd_by_sshpass_tomcat
from settings import PIPELINE_TEST_PUBLISH as PTP


def get_node_docker_ip(container_name, suite_code):
    k8s_server = get_k8s_server(suite_code)
    target_ip = k8s_server.get('k8s_host')
    password = k8s_server.get('k8s_password')
    cmd = "kubectl get pod -owide -l app={} -n {} --no-headers | grep 'Running' | awk '{{printf $6}}'".format(container_name, suite_code)
    completed_process_obj = exec_local_cmd_by_sshpass_tomcat(target_ip, cmd, password)
    # logger.info(completed_process_obj)
    return bytes.decode(completed_process_obj.stdout)


class NodeDockerIPRecorder:

    def __init__(self, module_name, suite_code, container_name):
        """
        制品信息记录工
        :param module_name: 应用名
        :param lib_repo_branch: 制品分支
        :param suite_code: 环境套
        """
        self.module_name = module_name
        self.suite_code = suite_code
        self.container_name = container_name

    def record_info(self):
        env_node_bind_table = EnvNodeBindTable()
        node_bind_id_list = env_node_bind_table.get_env_node_bind_id(self.module_name, suite_code=self.suite_code)
        logger.info("需要更新的node_bind list {}".format(node_bind_id_list))
        node_docker_ip = ''
        i = 0
        record_count = int(TPA['node_docker_ip_record_count'])
        while i < record_count:
            logger.info("循环检测容器启动状态，并记录pod ip（策略：每10秒轮询一次，最长不超过300秒）")
            node_docker_ip = get_node_docker_ip(self.container_name, self.suite_code)
            if node_docker_ip:
                break
            sleep(10)
            i += 1

        if node_docker_ip:
            env_node_bind_table.update_node_bind_node_dock_ip(node_bind_id_list, node_docker_ip)


if __name__ == "__main__":
    record = NodeDockerIPRecorder('schedule-ec-server', 'tms19', 'schedule-ec-server')
    record.record_info()