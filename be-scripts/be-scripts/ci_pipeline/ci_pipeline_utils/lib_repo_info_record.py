from ci_pipeline.ci_pipeline_models.env_node_bind_table import EnvNodeBindTable
from ci_pipeline.ci_pipeline_models.lib_repo_table import LibRepoTable
from settings import logger


class LibRepoInfoRecorder:

    def __init__(self, module_name, lib_repo_branch, suite_code):
        """
        制品信息记录工
        :param module_name: 应用名
        :param lib_repo_branch: 制品分支
        :param suite_code: 环境套
        """
        self.module_name = module_name
        self.lib_repo_branch = lib_repo_branch
        self.suite_code = suite_code

    def handle_info(self, env_publish_info):
        pass

    def record_info(self):
        lib_repo_table = LibRepoTable()
        lib_repo_id = lib_repo_table.get_lib_repo_id(self.module_name, self.lib_repo_branch)
        # 如果没有制品信息，将更新为0
        env_node_bind_table = EnvNodeBindTable()
        node_bind_id_list = env_node_bind_table.get_env_node_bind_id(self.module_name, suite_code=self.suite_code)
        logger.info("需要更新的node_bind list {}".format(node_bind_id_list))
        env_node_bind_table.update_node_bind_lib_repo_info(node_bind_id_list, lib_repo_id)
