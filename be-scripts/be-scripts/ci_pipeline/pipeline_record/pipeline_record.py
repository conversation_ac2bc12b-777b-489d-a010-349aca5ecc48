import os
import datetime
import traceback

from settings import logger
from ci_pipeline.ci_pipeline_models.pipeline_log_model import PipelineLogMain, PipelineLogMinor
from ci_pipeline.ci_pipeline_bo.compile_bo import CompileBo


class PipelineStatus:
    success = "success"
    failure = "failure"
    running = "running"
    aborted = "aborted"
    unstable = "unstable"


class PipelineRecorder:
    """
    流水线 执行记录器，记录流水线执行相关信息
    """
    # 依赖注入
    __pipeline_status = PipelineStatus
    __log_main = PipelineLogMain
    __log_minor = PipelineLogMinor
    end_mark = "end_pipeline"

    # 临时兼容性方案， 将job_id记录到spider调用表

    def __init__(self):
        # 主记录id
        self.__main_id = 0
        # 子记录id
        self.__item_id = 0
        # 主记录 环境属性
        self.suite_code = None

    @property
    def main_id(self):
        """
        获取子步骤id
        :return:
        """
        return self.__main_id

    @main_id.setter
    def __set_main_id(self, main_id):
        """
        设置子步骤id
        :param item_id:
        :return:
        """
        self.__main_id = main_id

    @property
    def item_id(self):
        """
        获取子步骤id
        :return:
        """
        return self.__item_id

    @item_id.setter
    def __set_item_id(self, item_id):
        """
        设置子步骤id
        :param item_id:
        :return:
        """
        self.__item_id = item_id

    def __call__(self, func):
        def inner(*args, **kwargs):
            try:
                logger.info("查询部署环境开始")
                env_publish_info = args[0].parse_data()
                self.suite_code = env_publish_info.get("suite_code")
                logger.info("suite_code:{}".format(self.suite_code))
                logger.info("查询部署环境结束")
            except Exception as err:
                traceback.print_exc()
                logger.error(str(err))
            step_name = args[1]
            data_obj = args[2]
            logger.info(step_name)
            logger.info(data_obj.__dict__)
            start_time = self.__begin_record_info(step_name, data_obj)
            try:
                if step_name == self.__pipeline_status.aborted:
                    exec_status = self.__pipeline_status.aborted
                    exec_msg = "用户取消"
                else:
                    exec_status, exec_msg = func(*args, **kwargs)
                    if exec_status == self.__pipeline_status.failure:
                        raise Exception(exec_msg)

            except Exception as err:
                logger.error(str(err))
                exec_msg = str(err)
                exec_status = self.__pipeline_status.failure
                raise Exception(str(err))
            finally:
                end_time = self.__end_record_info(step_name, exec_status, exec_msg)
                logger.info('{} 耗时: {}s'.format(step_name, str((end_time - start_time).seconds)))

        return inner

    @staticmethod
    def get_pipeline_info():
        """
         获取pipeline 执行信息， 目前通过os获取
        :return: dict
        """
        operator = ""
        if "operator" in os.environ:
            operator = os.environ["operator"]
        elif "BUILD_USER_ID" in os.environ:
            operator = os.environ["BUILD_USER_ID"]
        return {"job_url": os.environ['RUN_DISPLAY_URL'],
                "operator": operator,
                "node_name": os.environ['NODE_NAME'],
                "job_name": os.environ['JOB_NAME'],
                "build_id": os.environ['BUILD_ID'],
                "jenkins_id": os.environ['jenkins_id']}

    def __begin_record_info(self, step_name: str, data_obj: CompileBo):
        """

        :param step_name:
        :param data_obj:
        :return:
        """
        start_time = datetime.datetime.now()
        log_minor_id = self.__log_minor.insert({self.__log_minor.start_at: start_time,
                                                self.__log_minor.module_name: data_obj.module_name,
                                                self.__log_minor.step: step_name,
                                                self.__log_minor.sid: data_obj.sid,
                                                self.__log_minor.status: self.__pipeline_status.running}).execute()
        self.__set_main_id = data_obj.sid
        self.__set_item_id = log_minor_id
        return start_time

    def __end_record_info(self, step_name: str, exec_status: str, exec_msg: str):
        """

        :param step_name:
        :param exec_status:
        :param exec_msg:
        :return:
        """
        end_time = datetime.datetime.now()
        self.__log_minor.update({self.__log_minor.end_at: end_time,
                                 self.__log_minor.status: exec_status,
                                 self.__log_minor.log: exec_msg}).where(self.__log_minor.id == self.item_id).execute()
        logger.info("子id 为{}".format(self.item_id))
        if (exec_status in (self.__pipeline_status.failure, self.__pipeline_status.aborted,
                            self.__pipeline_status.unstable)
                or step_name == self.end_mark):
            logger.info("主sid 为{}".format(self.main_id))
            self.__log_main.update({self.__log_main.end_at: end_time,
                                    self.__log_main.status: exec_status}).where(
                self.__log_main.sid == self.main_id).execute()
        if self.suite_code:
            self.__log_main.update({self.__log_main.suite_name: self.suite_code}).where(
                self.__log_main.sid == self.main_id).execute()

        return end_time
