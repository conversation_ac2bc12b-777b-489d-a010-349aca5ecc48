import os
import sys
import json

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger
from ci_pipeline.pipeline_record.pipeline_record import PipelineRecorder, PipelineStatus
from ci_pipeline.ci_pipeline_bo.compile_bo import CompileBo


class BasePipeline:
    def __init__(self):
        self.pipeline_node_dict = {
            "aborted": self.abort_pipeline,
            "end_pipeline": self.end_pipeline
        }

    def end_pipeline(self):
        return PipelineStatus.success, "流水线完成"

    def abort_pipeline(self):
        return PipelineStatus.aborted, "流水线中止"

    @PipelineRecorder()
    def run_step(self, node_name, compile_bo):
        exec_status, exec_msg = self.pipeline_node_dict[node_name]()
        return exec_status, exec_msg

    def run(self, node_name, sid):
        """
        运行入口程序
        :param node_name:
        :return:
        """
        compile_bo = CompileBo.Builder().set_sid(sid).build_publish_bo()
        self.run_step(node_name, compile_bo)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    business_name = sys.argv[1]
    flag_file_dir = sys.argv[2]
    with open(flag_file_dir, "r") as f:
        json_dict = json.loads(f.read())
    sid = json_dict["sid"]
    base_pipeline = BasePipeline()
    base_pipeline.run(business_name, sid)
