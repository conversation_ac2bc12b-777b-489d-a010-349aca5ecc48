import datetime
import os
import sys
import json
import traceback
from enum import unique, Enum

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

sys.path.append(PROJECT_DIR)

from common.ztst_utils.file_hash import file_sha256
from dao.connect.mysql import DBConnectionManager
from dao.connect.oracle import OracleConnectionManager
from db_mgt.creat_sql_migrate.models import DbMgtExecSqlFileHistory, EnvMgtDbDeployInfo

from test_publish_aio.test_suite_init_impl_db import exec_dml, exec_mysql_dml

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from test_publish_aio.agent_mgt_exe_log_model import AgentMgtExecLog
from time import sleep
from ci_pipeline.ci_pipeline_utils.node_docker_ip_record import NodeDockerIPRecorder
from test_publish_aio.test_suite_init_constants import TypeEnum
from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos, get_lib_repo_path, \
    get_agent_latest_archive_version, get_db_archive_branch_list
from test_publish_aio.test_publish_aio_exec.test_suite_restart import Starter
from test_publish_aio.test_publish_aio_exec import test_publish_aio_util as publish_util
from settings import logger, PIPELINE_TEST_PUBLISH, TEST_PUBLISH_AIO, PRODUCT_STORE, PRODUCT_STORE_SQL_URL, \
    PRODUCT_STORE_SQL_BIS_URL
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import (detection_service_is_normal,
                                                                          exec_local_rsync_to_local, vm_stop,
                                                                          exec_local_cmd, pull_info_git,
                                                                          exec_local_rsync_to_remote, ssh_path_mkdir,
                                                                          exec_remote_cmd, push_local_to_other_rsync,
                                                                          exec_flyway_command, pull_sql_lib_repo)
from dao.get.mysql import branch_info, db_mgt_info
from ci_pipeline.ci_pipeline_utils.lib_repo_info_record import LibRepoInfoRecorder
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from common.ext_cmd.ssh.sftp import SCP
from common.common_tool.common_tool import exec_local_cmd
from ci_pipeline.ci_pipeline_utils.publish_utils import PublishStatusChecker
from settings import MIRROR_FACTORY, TEST_DATA_INIT
from test_pipeline.test_pipeline_utils.agent_injection import AgentInjection
from ci_pipeline.pipeline_record.pipeline_record import PipelineRecorder, PipelineStatus
from ci_pipeline.ci_pipeline_bo.compile_bo import CompileBo
from test_mgt.test_mgt_ser import get_db_info_by_app
from common.oracle.oracle import OracleHandler
from common.mysql.mysql import MysqlHandler


@unique
class DbScriptExecTypeEnum(Enum):
    INIT = ('init', "初始化")
    INCREMENT = ('increment', "增量执行")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


class TestEnvPublisher:
    def __init__(self, workspace, app_name, br_name, suite_code, cache_data_code, is_mock_agent=None,
                 biz_base_db=None, db_script_exec_type=None, iteration_id=None):
        self.workspace = workspace
        self.app_name = app_name
        self.br_name = br_name
        self.business_name = "test_publish"
        self.suite_code = suite_code
        self.cache_data_code = cache_data_code
        self.is_mock_agent = is_mock_agent
        self.biz_base_db = biz_base_db
        self.db_script_exec_type = db_script_exec_type
        self.iteration_id = iteration_id
        self.batch_no = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        self.pipeline_node_dict = {"env_check_per": self.env_check_per,
                                   "env_check_per_first_time": self.env_check_per,
                                   "pull_product": self.pull_product,
                                   "pull_git_product": self.pull_git_product,
                                   "scp_mock_package": self.scp_mock_package,
                                   "scp_package": self.scp_package,
                                   # 打镜像下线 20220311 by fwm
                                   # "make_img": self.make_img,
                                   "pull_config": self.pull_config,
                                   "replace_config": self.replace_config,
                                   "scp_config": self.scp_config,
                                   # 创建configmap下线 20220311 by fwm
                                   # "create_config_map": self.create_config_map,
                                   "get_start_file": self.get_start_file,
                                   "prepare_start_file": self.prepare_start_file,
                                   "prepare_running_agent_start_file": self.prepare_running_agent_start_file,
                                   "publish_start_file": self.publish_start_file,
                                   "agent_pull": self.agent_pull,
                                   "agent_push": self.agent_push,
                                   "db_init": self.db_init,
                                   "db_deploy": self.db_deploy,
                                   "restart_app": self.restart_app,
                                   "restart_app_again": self.restart_app,
                                   "check_result": self.check_result,
                                   "check_result_again": self.check_result,
                                   "sys_test": self.sys_test,

                                   "POD_STOP": self.pod_stop,
                                   "BEFORE_VM_STOP": self.before_vm_stop,
                                   "BEFORE_VM_BASE": self.before_vm_base,
                                   "start_wait": self.start_wait
                                   }

    def get_target_ip(self, env_publish_info):
        """
        获取目标服务器ip
        :param env_publish_info:
        :return:
        """
        if env_publish_info.deploy_type == 1:
            deploy_target_ip = env_publish_info.active_node_ip
            config_target_ip = deploy_target_ip
        elif env_publish_info.deploy_type == 2:
            deploy_target_ip = PIPELINE_TEST_PUBLISH["mirror_factory_ip"]
            config_target_ip = PIPELINE_TEST_PUBLISH["config_map_ip"]
        else:
            logger.error("{}应用，不支持的发布类型{}".format(env_publish_info.module_name, env_publish_info.deploy_type))
            sys.exit(1)
        return deploy_target_ip, config_target_ip

    def pull_product(self, env_publish_info):
        """
        拉取镜像库制品
        :param env_publish_info:
        :return:
        """
        try:
            repo_path_list = env_publish_info.lib_cache_repo_path.split("/")
            if env_publish_info.lib_cache_repo_path.endswith("/"):
                lib_cache_repo_path = "/".join(repo_path_list[:-2])
            else:
                lib_cache_repo_path = "/".join(repo_path_list[:-1])
            publish_util.pull_info_git(env_publish_info.lib_repo_path,
                                       self.br_name,
                                       lib_cache_repo_path,
                                       self.br_name)
            return PipelineStatus.success, "制品拉取结束"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)
            # sys.exit(1)

    def pull_git_product(self, env_publish_info):
        """
        拉取制品库制品
        :param env_publish_info:
        :return:
        """
        try:
            # git_repo_path: <EMAIL>:/data/git-code/xxx.git
            # env_publish_info.lib_repo_path: ************************:lib_repo/xxx.git

            lib_repo_path = env_publish_info.lib_repo_path.split("/")[-1]
            git_repo_path = PRODUCT_STORE['URL'] + '/' + lib_repo_path
            repo_path_list = env_publish_info.lib_cache_repo_path.split("/")

            if env_publish_info.lib_cache_repo_path.endswith("/"):
                lib_cache_repo_path = "/".join(repo_path_list[:-2])
            else:
                lib_cache_repo_path = "/".join(repo_path_list[:-1])
            publish_util.pull_info_git(git_repo_path,
                                       self.br_name,
                                       lib_cache_repo_path,
                                       self.br_name)
            return PipelineStatus.success, "制品拉取结束"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)
            # sys.exit(1)

    def scp_package(self, env_publish_info):
        """
        推送部署包
        :param env_publish_info:
        :return:
        """
        try:
            deploy_target_ip, config_target_ip = self.get_target_ip(env_publish_info)
            publish_util.push_local_to_other_rsync(deploy_target_ip,
                                                   env_publish_info.lib_cache_repo_path,
                                                   env_publish_info.deploy_path,
                                                   is_delete=True)
            return PipelineStatus.success, "推送部署包完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def scp_mock_package(self, env_publish_info):
        """
        复制推送mock包
        :param env_publish_info:
        :return:
        """
        try:

            deploy_target_ip, config_target_ip = self.get_target_ip(env_publish_info)

            mock_lib_cache_repo_path = os.path.join(PIPELINE_TEST_PUBLISH["mock_cache_root_path"],
                                                    self.app_name,
                                                    branch_info.get_iteration_id(self.app_name, self.br_name))

            publish_util.push_local_to_other_rsync(deploy_target_ip,
                                                   mock_lib_cache_repo_path,
                                                   env_publish_info.deploy_path,
                                                   is_delete=True)
            return PipelineStatus.success, "推送部署包完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def pull_config(self, env_publish_info):
        """
        拉取配置文件
        :param env_publish_info:
        :return:
        """
        src_cache_config_path_list = env_publish_info.selected_src_cache_config_path.split("/")
        if env_publish_info.selected_src_cache_config_path.endswith("/"):
            src_cache_config_path = "/".join(src_cache_config_path_list[:-3])
        else:
            src_cache_config_path = "/".join(src_cache_config_path_list[:-2])
        try:
            publish_util.pull_info_git(TEST_PUBLISH_AIO["test_app_resource_gitlab_ssh"],
                                       "master",
                                       src_cache_config_path,
                                       TEST_PUBLISH_AIO["test_app_resource_gitlab_name"])
            return PipelineStatus.success, "拉取配置文件完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def replace_config(self, env_publish_info):
        """
        替换宙斯配置项
        :param env_publish_info:
        :return:
        """
        try:
            publish_util.zeus_config_synchronos(self.app_name, self.br_name, self.suite_code)
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
        try:
            if os.path.isdir(env_publish_info.target_cache_config_path):
                if len(env_publish_info.target_cache_config_path.split("/")) > 3:
                    logger.info("rm -rf {}".format(env_publish_info.target_cache_config_path))
                    os.system("rm -rf {}".format(env_publish_info.target_cache_config_path))
            else:
                os.system("mkdir -p {}".format(env_publish_info.target_cache_config_path))

            exec_local_rsync_to_local(env_publish_info.selected_src_cache_config_path,
                                      env_publish_info.target_cache_config_path,
                                      is_delete=True)

            publish_util.zeus_config_replace(env_publish_info.target_cache_config_path, self.suite_code, self.br_name)
            return PipelineStatus.success, "替换宙斯配置项完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def scp_config(self, env_publish_info):
        """
        远程推送 配置文件
        :param env_publish_info:
        :return:
        """
        try:
            deploy_target_ip, config_target_ip = self.get_target_ip(env_publish_info)
            publish_util.push_local_to_other_rsync(config_target_ip,
                                                   env_publish_info.target_cache_config_path,
                                                   env_publish_info.deploy_config_path,
                                                   is_delete=True)
            return PipelineStatus.success, "远程推送 配置文件完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def get_start_file(self, env_publish_info):
        try:
            start_script_template_gitlab_name = PIPELINE_TEST_PUBLISH["start_script_template_gitlab_name"]
            logger.info("工作空间：{} ".format(env_publish_info.workspace))
            logger.info("clone的脚本仓库地址：{} ".format(env_publish_info.start_script_template_url))

            publish_util.pull_info_git(env_publish_info.start_script_template_url,
                                       "master",
                                       env_publish_info.workspace,
                                       start_script_template_gitlab_name)
            return PipelineStatus.success, "获取启动文件完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def prepare_start_file(self, env_publish_info):
        try:
            logger.info("启动脚本预处理")
            start_script_template_gitlab_name = PIPELINE_TEST_PUBLISH["start_script_template_gitlab_name"]
            def_war_script_name = PIPELINE_TEST_PUBLISH["def_war_script_name"]
            if env_publish_info.package_type == "war":
                tomcat_file_src_path = os.path.join(env_publish_info.workspace, start_script_template_gitlab_name,
                                                    'tomcat', def_war_script_name)
                if os.path.isfile(env_publish_info.start_file_local_path):
                    logger.info("rm -rf {}".format(env_publish_info.start_file_local_path))
                    os.system("rm -rf {}".format(env_publish_info.start_file_local_path))
                if not os.path.isdir(os.path.dirname(env_publish_info.start_file_local_path)):
                    logger.info("mkdir -p {}".format(os.path.dirname(env_publish_info.start_file_local_path)))
                    os.system("mkdir -p {}".format(os.path.dirname(env_publish_info.start_file_local_path)))
                logger.info("cp {} {}".format(tomcat_file_src_path, env_publish_info.start_file_local_path))
                os.system("cp {} {}".format(tomcat_file_src_path, env_publish_info.start_file_local_path))
            else:
                logger.info("git checkout -- {}".format(env_publish_info.start_file_local_path))
                os.chdir('/'.join(env_publish_info.start_file_local_path.split('/')[:-1]))
                os.system("git checkout -- {}".format(env_publish_info.start_file_local_path))
            # if self.is_mock_agent == "true":
            #     logger.info("mock_agent开关为{}，开始修改启动脚本！".format(self.is_mock_agent))
            #     script_source = env_publish_info.start_file_local_path
            #     agent_injection = AgentInjection(script_source)
            #     agent_injection.add_mock_agent_params(self.app_name, self.suite_code)
            return PipelineStatus.success, "启动脚本预处理完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def prepare_running_agent_start_file(self, env_publish_info):
        try:
            logger.info("启动脚本预处理")
            logger.info("env_publish_info:{}".format(env_publish_info))
            start_script_template_gitlab_name = PIPELINE_TEST_PUBLISH["start_script_template_gitlab_name"]
            def_war_script_name = PIPELINE_TEST_PUBLISH["def_war_script_name"]
            if env_publish_info.package_type == "war":
                tomcat_file_src_path = os.path.join(env_publish_info.workspace, start_script_template_gitlab_name,
                                                    'tomcat', def_war_script_name)
                if os.path.isfile(env_publish_info.start_file_local_path):
                    logger.info("rm -rf {}".format(env_publish_info.start_file_local_path))
                    os.system("rm -rf {}".format(env_publish_info.start_file_local_path))
                if not os.path.isdir(os.path.dirname(env_publish_info.start_file_local_path)):
                    logger.info("mkdir -p {}".format(os.path.dirname(env_publish_info.start_file_local_path)))
                    os.system("mkdir -p {}".format(os.path.dirname(env_publish_info.start_file_local_path)))
                logger.info("cp {} {}".format(tomcat_file_src_path, env_publish_info.start_file_local_path))
                os.system("cp {} {}".format(tomcat_file_src_path, env_publish_info.start_file_local_path))
            else:
                logger.info("git checkout -- {}".format(env_publish_info.start_file_local_path))
                os.chdir('/'.join(env_publish_info.start_file_local_path.split('/')[:-1]))
                os.system("git checkout -- {}".format(env_publish_info.start_file_local_path))
            agent_list = env_publish_info.agent_info_list
            script_source = env_publish_info.start_file_local_path
            agent_injection = AgentInjection(script_source)
            agent_injection.batch_no = self.cache_data_code
            for agent in agent_list:
                logger.info("添加{}的启动参数！".format(agent.get("agent_module_name")))
                agentMgtExecLog = AgentMgtExecLog(module_name=self.app_name,
                                                  branch=env_publish_info.br_name,
                                                  agent_type=agent.get("agent_module_name"),
                                                  exec_batch=self.cache_data_code,
                                                  status="running",
                                                  create_user="no_user",
                                                  create_time=datetime.datetime.now(),
                                                  )
                with DBConnectionManagerForSqlalchemy(ehco=True) as db:
                    db.session.add(agentMgtExecLog)
                    db.session.commit()
                if agent.get("agent_type") == "shard":
                    agent_injection.add_shard_agent_params(self.app_name, env_publish_info.br_name, self.suite_code,
                                                           agent)
                else:
                    agent_injection.add_agent_params(self.app_name, self.suite_code, agent_list,
                                                     env_publish_info.br_name, agent.get("agent_module_name"))

            return PipelineStatus.success, "启动脚本预处理完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def publish_start_file(self, env_publish_info):
        """
        只支持 部署在docker环境的 启动文件覆盖
        :param env_publish_info:
        :return:
        """
        try:
            logger.info("推送启动脚本ip {}".format(MIRROR_FACTORY["IP"]))
            logger.info("启动脚本本地路径 {}".format(env_publish_info.start_file_local_path))
            logger.info("启动脚本远端路径 {}".format(env_publish_info.start_script_path))

            with SSHConnectionManager(MIRROR_FACTORY["IP"], MIRROR_FACTORY["USER"], MIRROR_FACTORY["PASSWORD"]) as ssh:
                scp = SCP(ssh.SFTP)
                scp.push_file(env_publish_info.start_file_local_path, env_publish_info.start_script_path)

            """给启动脚本赋可执行权限"""
            cmd = "chmod 755 {}".format(env_publish_info.start_script_path)
            exec_remote_cmd(MIRROR_FACTORY["IP"], cmd)
            return PipelineStatus.success, "推送启动脚本完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def agent_pull(self, env_publish_info):
        try:
            agent_list = env_publish_info.agent_info_list
            if not agent_list:
                return PipelineStatus.success, "没有agent需要拉取"
            app_cache_path = os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"], 'agent')
            for starting_agent in agent_list:
                if starting_agent.get("third_party_middleware") == 0:
                    agent_name = starting_agent.get("agent_module_name")
                    lib_repo_path = get_lib_repo_path(agent_name)
                    br_name = starting_agent.get("agent_version")
                    if not br_name:
                        br_name = get_agent_latest_archive_version(agent_name)
                    publish_util.pull_info_git(lib_repo_path, br_name, app_cache_path, agent_name)
            return PipelineStatus.success, "agent脚本拉取完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def agent_push(self, env_publish_info):
        try:
            agent_list = env_publish_info.agent_info_list
            for starting_agent in agent_list:
                if starting_agent.get("third_party_middleware") == 0:
                    agent_name = starting_agent.get("agent_module_name")
                    source_path = os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"], 'agent', agent_name)
                    deploy_path = starting_agent.get("agent_deploy_path").format(
                        suite_code=env_publish_info.node_docker)
                    target_ip = starting_agent.get("agent_deploy_ip")
                    push_local_to_other_rsync(target_ip, source_path, deploy_path, is_delete=True)
            return PipelineStatus.success, "agent推送完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def restart_app(self, env_publish_info):
        """
        应用重启
        :param env_publish_info:
        :return:
        """
        try:
            starter = Starter()
            starter.restart({self.app_name: [env_publish_info.__dict__]})
            ### 启动后更新下节点绑定表的数据 by 帅 20210712
            lib_repo_info_recorder = LibRepoInfoRecorder(self.app_name, self.br_name, suite_code=self.suite_code)
            lib_repo_info_recorder.record_info()
            # 更新容器ip信息 20211015 by fwm
            if env_publish_info.deploy_type == 2:
                node_docker_ip_recorder = NodeDockerIPRecorder(self.app_name, self.suite_code,
                                                               env_publish_info.container_name)
                node_docker_ip_recorder.record_info()
            return PipelineStatus.success, "启动镜像完成"
        except Exception as e:
            logger.info(str(e))
            traceback.print_exc()
            return PipelineStatus.failure, str(e)

    def env_check_per(self, env_publish_info):
        """
        环境预检
        :param env_publish_info:
        :return:
        """
        try:
            deploy_target_ip, config_target_ip = self.get_target_ip(env_publish_info)
            # 检查部署目录
            logger.info("==================开始检测部署路径==================")
            chk_status, chk_msg = publish_util.ssh_path_whether_exist(deploy_target_ip, env_publish_info.deploy_path)
            if not chk_status and '目录' in chk_msg:
                ssh_path_mkdir(deploy_target_ip, env_publish_info.deploy_path)
                chk_status, chk_msg = publish_util.ssh_path_whether_exist(deploy_target_ip,
                                                                          env_publish_info.deploy_path)
            if not chk_status:
                raise ValueError(chk_msg)
            logger.info("==================结束检测部署路径==================")
            # 检查配置目录
            logger.info("==================开始检测配置路径==================")
            chk_status, chk_msg = publish_util.ssh_path_whether_exist(config_target_ip,
                                                                      env_publish_info.deploy_config_path)
            if not chk_status and '目录' in chk_msg:
                ssh_path_mkdir(config_target_ip, env_publish_info.deploy_config_path)
                chk_status, chk_msg = publish_util.ssh_path_whether_exist(config_target_ip,
                                                                          env_publish_info.deploy_config_path)
            if not chk_status:
                raise ValueError(chk_msg)
            logger.info("==================结束检测配置路径==================")

            # 检查启动脚本路径 20220311 by fwm
            logger.info("==================开始检测启动脚本路径==================")
            chk_status, chk_msg = publish_util.ssh_path_whether_exist(deploy_target_ip,
                                                                      env_publish_info.start_script_path_dir)
            if not chk_status and '目录' in chk_msg:
                ssh_path_mkdir(deploy_target_ip, env_publish_info.start_script_path_dir)
                chk_status, chk_msg = publish_util.ssh_path_whether_exist(deploy_target_ip,
                                                                          env_publish_info.start_script_path_dir)
            if not chk_status:
                raise ValueError(chk_msg)
            logger.info("==================结束检测启动脚本路径==================")

            # 检查启动脚本文件
            if env_publish_info.deploy_type == 1:
                logger.info("==================开始检测==================")
                chk_status, chk_msg = publish_util.ssh_path_whether_exist(config_target_ip,
                                                                          env_publish_info.start_script_path,
                                                                          is_file=True)
                if not chk_status:
                    raise ValueError(chk_msg)
                logger.info("==================结束检测==================")
            return PipelineStatus.success, "环境预检完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def check_result(self, env_publish_info):
        """
        检查启动结果
        :param env_publish_info:
        :return:
        """
        logger.info(env_publish_info.active_node_ip)
        logger.info(env_publish_info.tomcat_name)

        # 容器启动状态检测
        if env_publish_info.deploy_type == 2:
            publish_starts_checker = PublishStatusChecker(self.suite_code, self.app_name)
            publish_starts_checker.check_node_status()
        else:
            if not detection_service_is_normal(env_publish_info.active_node_ip, env_publish_info.tomcat_name):
                logger.error(
                    "进程没起来，需要研发、测试同学排查定位。可能原因有：启动脚本问题，服务配置问题，服务的依赖服务找不到导致启动失败等")
                return PipelineStatus.failure, "进程没起来，需要研发、测试同学排查定位。可能原因有：启动脚本问题，服务配置问题，服务的依赖服务找不到导致启动失败等"
        return PipelineStatus.success, "检查启动结果完成"

    def sys_test(self, env_publish_info):
        """
        调用测试接口
        :param env_publish_info:
        :return:
        """
        pass

    def pod_stop(self, env_publish_info):
        try:
            """POD停止"""
            logger.info(">>>>『POD停止』app_name = {}".format(self.app_name))
            logger.info(
                ">>>>『env_publish_info』".format(json.dumps(env_publish_info.__dict__, ensure_ascii=False, indent=4)))

            # deploy_type = env_publish_info.get('deploy_type')
            # logger.info(">>>>『虚机停止』deploy_type = {}".format(deploy_type))
            # if deploy_type == 1:
            #     active_node_ip = env_publish_info.get('active_node_ip')
            #     # 虚机部署改取tomcat_name 20220506 by fwm
            #     tomcat_name = env_publish_info.get('tomcat_name')
            #     logger.info(">>>>『虚机停止』active_node_ip = {}".format(env_publish_info.active_node_ip))
            #     logger.info(">>>>『虚机停止』tomcat_name = {}".format(env_publish_info.tomcat_name))
            #     vm_stop(active_node_ip, tomcat_name)
            # else:
            #     logger.info(">>>>非虚机应用，跳过『虚机停止』app_name = {}".format(self.app_name))
            return PipelineStatus.success, "『POD停止』完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def before_vm_stop(self, env_publish_info):
        try:
            """虚机停止 zt@2020-12-03"""
            logger.info(">>>>『虚机停止』app_name = {}".format(self.app_name))
            # logger.info(">>>>『虚机停止』br_name = {}".format(self.br_name))
            # logger.info(">>>>『虚机停止』suite_code = {}".format(self.suite_code))
            # logger.info(">>>>『虚机停止』env_publish_info = {}".format(env_publish_info))

            deploy_type = env_publish_info.get('deploy_type')
            logger.info(">>>>『虚机停止』deploy_type = {}".format(deploy_type))
            if deploy_type == 1:
                active_node_ip = env_publish_info.get('active_node_ip')
                # 虚机部署改取tomcat_name 20220506 by fwm
                tomcat_name = env_publish_info.get('tomcat_name')
                logger.info(">>>>『虚机停止』active_node_ip = {}".format(env_publish_info.active_node_ip))
                logger.info(">>>>『虚机停止』tomcat_name = {}".format(env_publish_info.tomcat_name))
                vm_stop(active_node_ip, tomcat_name)
            else:
                logger.info(">>>>非虚机应用，跳过『虚机停止』app_name = {}".format(self.app_name))
            return PipelineStatus.success, "『虚机停止』完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def before_vm_base(self, env_publish_info):
        """虚机基线处理 zt@2020-12-03"""
        try:
            logger.info(">>>>『虚机基线处理』app_name = {}".format(self.app_name))

            deploy_type = env_publish_info.get('deploy_type')
            logger.info(">>>>『虚机基线处理』deploy_type = {}".format(deploy_type))
            if deploy_type == 1:
                active_node_ip = env_publish_info.get('active_node_ip')
                package_type = env_publish_info.get('package_type')
                target_base_path = env_publish_info.get('target_base_path')
                logger.info(">>>>『虚机基线处理』active_node_ip = {}".format(active_node_ip))
                logger.info(">>>>『虚机基线处理』package_type = {}".format(package_type))
                logger.info(">>>>『虚机基线处理』target_base_path = {}".format(target_base_path))

                # 1、创建环境对应根目录
                pipeline_local_path = os.path.join(PIPELINE_TEST_PUBLISH['pipeline_local_path'])
                if not os.path.exists(pipeline_local_path):
                    cmd = "mkdir -p {}".format(pipeline_local_path)
                    exec_local_cmd(cmd)

                # 2、拉配置
                base_git_url = TEST_PUBLISH_AIO['base_app_resource_gitlab_ssh']
                base_git_name = TEST_PUBLISH_AIO['base_app_resource_gitlab_name']
                base_git_branch = TEST_PUBLISH_AIO['base_app_resource_gitlab_branch']
                pull_info_git(base_git_url, base_git_branch, pipeline_local_path, base_git_name)

                # 3、推送
                # 3-1、目标IP
                if not active_node_ip:
                    logger.warn(
                        ">>>> 应用「{}」环境「{}」没有绑定IP，跳过『虚机基线处理』。".format(self.app_name, self.suite_code))
                    return

                # 3-2、源目录（使用rsync同步，统一推应用目录下的内容）
                source_path = os.path.join(pipeline_local_path, base_git_name, self.app_name, '')
                if not os.path.exists(source_path):
                    logger.warn(
                        ">>>>base_app_resource中未配置基线数据，跳过『虚机基线处理』app_name = {}".format(self.app_name))
                    return

                # 3-3、目标目录
                if not target_base_path:
                    # 3-3、目标默认目录
                    def_vm_remote_base_path = TEST_PUBLISH_AIO['def_vm_remote_base_path']
                    def_vm_tomcat_base_path = TEST_PUBLISH_AIO['def_vm_tomcat_base_path']

                    if package_type == 'jar':
                        target_base_path = def_vm_remote_base_path
                    else:
                        target_base_path = def_vm_tomcat_base_path

                # 3-4、执行推送
                exec_local_rsync_to_remote(active_node_ip, source_path, target_base_path)

                # 3-5、打印提示
                msg = ">>>> 『虚机基线处理』：应用「{}」从执行机「{}」推送到「{}」的「{}」目录。".format(self.app_name, source_path,
                                                                                             active_node_ip,
                                                                                             target_base_path)
                logger.info(msg)
            else:
                logger.info(">>>>非虚机应用，跳过『虚机基线处理』app_name = {}".format(self.app_name))
            return PipelineStatus.success, "『虚机基线处理』完成"
        except Exception as e:
            logger.info(str(e))
            return PipelineStatus.failure, str(e)

    def start_wait(self, env_publish_info):
        seconds = int(PIPELINE_TEST_PUBLISH["start_wait_seconds"])
        logger.info("启动等待{}秒".format(seconds))
        sleep(seconds)
        return PipelineStatus.success, "启动等待完成"

    def db_init(self, env_publish_info):
        logger.info(env_publish_info.get("workspace"))
        logger.info(os.environ["WORKSPACE"])
        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(self.app_name)
        logger.info(vcs_info_list)
        db_info = get_db_info_by_app(self.app_name, self.suite_code)
        if vcs_info_list:
            if self.biz_base_db and self.biz_base_db != '0' and self.biz_base_db != 'undefined':
                if db_script_exec_type == DbScriptExecTypeEnum.INIT.type_name:
                    self.restore_dump(env_publish_info)
                    self.baseline_db_deploy(db_info)
                else:
                    logger.info("不初始化")
        return PipelineStatus.success, "成功"

    def restore_dump(self, env_publish_info):
        db_info_list = env_publish_info.get('db_info_list')
        logger.info("待恢复的DB信息：{}".format(db_info_list))
        oracle_db_info_list = []
        mysql_db_info_list = []
        for db_info in db_info_list:
            if db_info.get("db_srv_type") == 'oracle':
                oracle_db_info_list.append(db_info)
                logger.info("待恢复的oracle数据库信息：{}".format(oracle_db_info_list))
            elif db_info.get("db_srv_type") == 'mysql':
                mysql_db_info_list.append(db_info)
                logger.info("待恢复的mysql数据库信息：{}".format(mysql_db_info_list))
            logger.info('========================================================db_info:{}'.format(db_info))
            db_group_name = db_info.get('db_group_name')
        iteration_id = branch_info.get_iteration_id(self.app_name, self.br_name)
        if oracle_db_info_list:
            oh = OracleHandler(self.app_name, self.suite_code, oracle_db_info_list, self.biz_base_db)
            oh._check_dump_exist('oracle')
            # oh._db_lock()
            # oh._kill_session()
            oh._db_unlock()
            oh._drop_ddl()
            # oh._db_drop()
            # oh._user_create()
            oh._db_restore()
            # oh._db_unlock()
            oh._db_set_user_and_password()
            oh._db_update_synonym()
        elif mysql_db_info_list:
            mh = MysqlHandler(self.app_name, self.suite_code, mysql_db_info_list, self.biz_base_db)
            mh._check_dump_exist('mysql')
            mh._db_drop()
            mh._db_create()
            mh._db_restore()
        else:
            logger.info("应用无DB信息，无需处理初始化！")
            return PipelineStatus.success, "应用无DB信息，无需处理初始化！"

        return PipelineStatus.success, "成功"

    def baseline_db_deploy(self, db_info):
        jenkins_workspace = os.environ["WORKSPACE"]
        flyway_cache_path = os.path.join(jenkins_workspace,
                                         TEST_DATA_INIT.get('flyway_cache_path'))  # 加环境属性或放在workspace
        bis_sql_filesystem = os.path.join(jenkins_workspace, self.biz_base_db)

        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(self.app_name)
        iteration_id = branch_info.get_iteration_id(self.app_name, self.br_name)
        group_list = []
        db_dict = {}
        path_list = []
        for item in db_info:
            if item.get('db_name_info') not in db_dict:
                db_dict[item.get('db_name_info')] = item
        for item in db_dict:
            if db_dict[item].get('db_group_name') not in group_list:

                filesystem = "{}/{}_baseline/{}".format(jenkins_workspace, iteration_id,
                                                        db_dict[item].get('db_group_name'))
                logger.info("filesystem:{}".format(filesystem))
                # src_filesystem = os.path.join(filesystem, item['db_name_info'])
                if os.path.exists(filesystem):
                    logger.info('{}已存在'.format(filesystem))
                else:
                    logger.info("filesystem:{}".format(filesystem))
                    os.makedirs(filesystem)
                logger.info("开始克隆feature-sql master分支")
                pull_sql_lib_repo(filesystem, "master", db_dict[item].get('db_group_name'), PRODUCT_STORE_SQL_URL)
            else:
                group_list.append(db_dict[item].get('db_group_name'))
        # 拉业务SQL, 业务sql反补流帅线
        logger.info("开始克隆业务sql master分支")
        pull_sql_lib_repo(bis_sql_filesystem, "master", self.biz_base_db, PRODUCT_STORE_SQL_BIS_URL, is_delete=False)
        for item in db_dict:
            logger.info("开始处理数据库db_dict:{}".format(db_dict[item]))
            exec_flyway_cache_path = "{}/{}".format(flyway_cache_path, item)
            filesystem = "{}/{}_baseline/{}".format(jenkins_workspace, iteration_id,
                                                    db_dict[item].get('db_group_name'))
            if os.path.exists(exec_flyway_cache_path):
                logger.info('{}已存在'.format(exec_flyway_cache_path))
            else:
                logger.info("exec_flyway_cache_path:{}".format(exec_flyway_cache_path))
                os.makedirs(exec_flyway_cache_path)
            cmd = "cd {} && find {} | grep -iE .*{}/.*\.sql".format(filesystem, "archive", item)
            cmd_bis_sql = "cd {} && find {} | grep -iE .*{}/.*\.sql".format(bis_sql_filesystem, "archive", item)
            path_obj = None
            path_obj_bis_sql = None
            path_bis_sql_list = []
            try:
                # 提前申明变量
                path_obj = exec_local_cmd(cmd)
                path_obj_bis_sql = exec_local_cmd(cmd_bis_sql)
            except Exception as e:
                logger.info("devops-sql下是空目录，没找到任务sql文件")
            if path_obj:
                path_list = bytes.decode(path_obj.stdout.strip(), encoding='utf-8').split(os.linesep)
                logger.info("===================path_list================{}".format(path_list))
            if path_obj_bis_sql:
                path_bis_sql_list = bytes.decode(path_obj_bis_sql.stdout.strip(), encoding='utf-8').split(os.linesep)
                logger.info("===================path_bis_sql_list================{}".format(path_bis_sql_list))
            if not path_list and not path_bis_sql_list:
                logger.info("数据库:{} 没有任何sql".format(db_dict[item]))
                return PipelineStatus.success, "成功"
            db_info = self.prepare_db_info(db_dict[item])
            if self.db_script_exec_type == DbScriptExecTypeEnum.INIT.type_name:
                self.__cp_sql_file(filesystem, path_list, exec_flyway_cache_path)
                self.__cp_sql_file(bis_sql_filesystem, path_bis_sql_list, exec_flyway_cache_path)
            elif self.db_script_exec_type == DbScriptExecTypeEnum.INCREMENT.type_name:
                self.__cp_sql_file(filesystem, path_list, exec_flyway_cache_path)
            ora_ddl_root_dir = os.path.join(exec_flyway_cache_path, 'DDL')
            wait_log_sql_ddl_files = self.mv_overexecute_script(ora_ddl_root_dir, db_info["db_name_info"])
            exec_flyway_command(db_dict[item], 'repair', ora_ddl_root_dir)
            try:
                exec_flyway_command(db_dict[item], 'migrate', ora_ddl_root_dir)
            except Exception as ex:
                self.handle_flyway_exec_sql_file_history(False, db_dict[item].get("db_type"), db_info,
                                                         wait_log_sql_ddl_files)
                raise ex
            self.handle_flyway_exec_sql_file_history(True, db_dict[item].get("db_type"), db_info,
                                                     wait_log_sql_ddl_files)

            ora_dml_root_dir = os.path.join(exec_flyway_cache_path, 'DML')
            if not os.path.exists(ora_dml_root_dir):
                logger.info('无dml需要执行')
                return PipelineStatus.success, "成功"
            wait_log_sql_dml_files = self.mv_overexecute_script(ora_dml_root_dir, db_info["db_name_info"])
            if db_dict[item].get("db_type") == 'oracle':
                exec_dml(ora_dml_root_dir, db_info, wait_log_sql_dml_files=wait_log_sql_dml_files,
                         iteration_id=self.iteration_id,
                         batch_no=self.batch_no)
            if db_dict[item].get("db_type") == 'mysql':
                exec_mysql_dml(ora_dml_root_dir, db_info, wait_log_sql_dml_files=wait_log_sql_dml_files,
                               iteration_id=self.iteration_id,
                               batch_no=self.batch_no)
        return PipelineStatus.success, "成功"

    def prepare_db_info(self, db_dict):
        db_info = {}
        db_info["conn_url"] = db_dict['conn_url']
        db_info["db_srv_hosts"] = db_dict.get("db_hosts")
        db_info["db_user"] = db_dict.get("db_user")
        db_info["db_passwd"] = db_dict.get("db_passwd")
        db_info["username"] = db_dict.get("db_user")
        db_info["password"] = db_dict.get("db_passwd")
        db_info["suite_db_name"] = db_dict.get("db_srv_name")
        db_info["db_srv_name"] = db_dict.get("db_srv_name")
        db_info["db_srv_port"] = db_dict.get("db_port")
        db_info["db_type"] = db_dict.get("db_type")
        db_info["db_group_name"] = db_dict.get("db_group_name")
        db_info["db_name_info"] = db_dict.get("db_name_info")
        db_info["sql_ver_db"] = db_dict.get("sql_ver_db")
        db_info["sql_ver_group"] = db_dict.get("sql_ver_group")
        if 'DOCKER' in db_dict.get("db_srv_name"):
            db_info["db_srv_name"] = db_dict.get("db_name")
        if db_dict.get("db_type") == 'oracle':
            db_info["db_name"] = db_dict.get("db_user")
        if db_dict.get("db_type") == 'mysql':
            db_info["db_name"] = db_dict.get("db_name")
        return db_info

    def __cp_sql_file(self, filesystem, path_list, exec_flyway_cache_path):
        for path in path_list:
            logger.info("path===={}".format(path))
            if 'DDL' in path:
                exec_flyway_cache_path_dir = os.path.join(exec_flyway_cache_path, 'DDL')
            elif 'DML' in path:
                exec_flyway_cache_path_dir = os.path.join(exec_flyway_cache_path, 'DML')
            if exec_flyway_cache_path_dir:
                if not os.path.isdir(exec_flyway_cache_path_dir):
                    os.makedirs(exec_flyway_cache_path_dir)
                cmd = "cp -r {}/{} {}".format(filesystem, path, exec_flyway_cache_path_dir)
                logger.info(cmd)
                exec_local_cmd(cmd)

    @staticmethod
    def sort_sql_common(sql_src_path, flyway_cache_path):
        logger.info("sql源路径：{}".format(sql_src_path))
        first_child_dir_list = [f.path for f in os.scandir(sql_src_path) if f.is_dir()]
        logger.info("一级子目录列表：{}".format(first_child_dir_list))
        for first_child_dir in first_child_dir_list:
            cmd = 'cp -r {} {}'.format(first_child_dir + '/*', flyway_cache_path)
            logger.info("拷贝sql，按每个数据库来分拣sql：{}".format(cmd))
            exec_local_cmd(cmd)

    def db_deploy(self, env_publish_info):
        vcs_info_list = db_mgt_info.get_vcs_info_by_module_name(self.app_name)
        jenkins_workspace = os.environ["WORKSPACE"]
        flyway_cache_path = os.path.join(jenkins_workspace, TEST_DATA_INIT.get('flyway_cache_path'))
        db_dict = {}
        group_list = []
        path_list = []
        code_filesystem = '{}/migrate_flyway/{}/{}'
        extract_code_filesystem = '{}/migrate_flyway/{}'
        if vcs_info_list and len(vcs_info_list) > 0:
            if self.biz_base_db and self.biz_base_db != '0' and self.biz_base_db != 'undefined' and self.biz_base_db != 'False':
                iteration_id = branch_info.get_iteration_id(self.app_name, self.br_name)
                db_info_items = get_db_info_by_app(self.app_name, self.suite_code)
                logger.info("db_info_items:{}".format(db_info_items))
                for item in db_info_items:
                    filesystem = '{}/migrate_flyway/{}/{}/{}'.format(jenkins_workspace, iteration_id,
                                                                     item['db_group_name'],
                                                                     item['db_name_info'])
                    item['sql_ver_db'] = item['db_name_info']
                    item['sql_ver_group'] = item['db_group_name']
                    if os.path.exists(filesystem):
                        logger.info('{}已存在'.format(filesystem))
                    else:
                        os.makedirs(filesystem)
                    db_dict[filesystem] = item
                # 开始处理每个数据库
                logger.info("开始处理本应用关联的所有数据库")
                for cur_db_flyway_migrate_root_path in db_dict:
                    # merge mysql or oracle 数据库信息
                    cur_db_info_item = self.get_cur_db_info(db_dict, db_info_items, cur_db_flyway_migrate_root_path)
                    logger.info("当前数据库：{}".format(cur_db_info_item))
                    # 获取当前执行目录
                    exec_flyway_cache_path = self.create_db_flyway_cache_path(db_dict, flyway_cache_path,
                                                                              cur_db_flyway_migrate_root_path)
                    # 复制master sql 并按照归档顺序排序 到执行目录
                    self.copy_master_sql_to_dir(flyway_cache_path, exec_flyway_cache_path, cur_db_info_item)

                    # 复制本迭代的sql 到执行目录
                    self.copy_iteration_sql_to_dir(code_filesystem, db_dict, exec_flyway_cache_path,
                                                   extract_code_filesystem, group_list, iteration_id, jenkins_workspace,
                                                   cur_db_flyway_migrate_root_path, path_list)

                    # 清理已经自定义执行的
                    flyway_sql_historys = self.get_flyway_history(cur_db_info_item.get("db_type"),
                                                                  cur_db_info_item)
                    # migrate
                    self.migrate_sql(cur_db_info_item, exec_flyway_cache_path, flyway_sql_historys=flyway_sql_historys,
                                     iteration_id=iteration_id)
        else:
            logger.info(">> >> 警告：应用「{}」没有绑定「SQL制品」信息，跳过SQL flyway执行处理。".format(self.app_name))
        return PipelineStatus.success, "成功"

    def migrate_sql(self, cur_db_info_item, exec_flyway_cache_path, flyway_sql_historys=None, iteration_id=None):
        # 处理已经执行过的sql
        wait_log_sql_files = self.mv_overexecute_script(exec_flyway_cache_path, cur_db_info_item["db_name_info"],
                                                        flyway_sql_historys)
        exec_flyway_command(cur_db_info_item, 'repair', exec_flyway_cache_path)
        try:
            exec_flyway_command(cur_db_info_item, 'migrate', exec_flyway_cache_path)
        except Exception as ex:
            self.handle_flyway_exec_sql_file_history(False, cur_db_info_item.get("db_type"),
                                                     cur_db_info_item,
                                                     wait_log_sql_files)
            raise ex
        self.handle_flyway_exec_sql_file_history(True, cur_db_info_item.get("db_type"), cur_db_info_item,
                                                 wait_log_sql_files)
        emd, created = EnvMgtDbDeployInfo.get_or_create(db_info_id=cur_db_info_item.get("db_info_id"),
                                                        suite_code=self.suite_code,
                                                        defaults={"pipeline_id": iteration_id,
                                                                  "create_user": 'howbuyscm',
                                                                  "create_time": datetime.datetime.now()})
        if not created:
            EnvMgtDbDeployInfo.update(pipeline_id=iteration_id, update_time=datetime.datetime.now(),
                                      update_user='howbuyscm').where(
                EnvMgtDbDeployInfo.db_info_id == cur_db_info_item.get("db_info_id"),
                EnvMgtDbDeployInfo.suite_code == self.suite_code).execute()

    def get_cur_db_info(self, db_dict, db_info_items, key):
        cur_db_info_item = {}
        for db_info_item in db_info_items:
            db_info_item.update(db_dict[key])
            db_info_item_temp = self.prepare_db_info(db_info_item)
            if db_info_item_temp["db_name_info"].lower() == db_dict[key].get("sql_ver_db").lower():
                cur_db_info_item = db_info_item_temp
        return cur_db_info_item

    def copy_iteration_sql_to_dir(self, code_filesystem, db_dict, exec_flyway_cache_path, extract_code_filesystem,
                                  group_list, iteration_id, jenkins_workspace, key, path_list):
        if db_dict[key].get('sql_ver_group') not in group_list:

            logger.info('===============code_filesystem:{}'.format(
                code_filesystem.format(jenkins_workspace, iteration_id,
                                       db_dict[key].
                                       get('sql_ver_group'))))
            try:
                # 拉取当前迭代的sql
                pull_sql_lib_repo(
                    code_filesystem.format(jenkins_workspace, iteration_id, db_dict[key].get('sql_ver_group')),
                    self.br_name, db_dict[key].get('sql_ver_group'), PRODUCT_STORE_SQL_URL)
            except Exception as e:
                logger.error(e)
                logger.info("{} {} 分支不存在 ".format(db_dict[key].get('sql_ver_group'), self.br_name))
                return
        else:
            group_list.append(db_dict[key].get('sql_ver_group'))
        cmd = "cd {} && find {}/{} | grep -iE .*{}.*\.sql". \
            format(extract_code_filesystem.format(jenkins_workspace, iteration_id),
                   db_dict[key].get('sql_ver_group'), db_dict[key].get('sql_ver_db'),
                   db_dict[key].get('sql_ver_db'))
        try:
            path_obj = exec_local_cmd(cmd)
        except Exception as e:
            path_obj = ''
            logger.info("devops-sql下是空目录，没找到任务sql文件")
        if path_obj:
            path_list = bytes.decode(path_obj.stdout.strip(), encoding='utf-8').split(os.linesep)
        for path in path_list:
            exec_local_cmd("cp {}/{} {}".format(extract_code_filesystem.format(jenkins_workspace,
                                                                               iteration_id),
                                                path, exec_flyway_cache_path))

    def create_db_flyway_cache_path(self, db_dict, flyway_cache_path, key):
        exec_flyway_cache_path = "{}/{}/iter".format(flyway_cache_path, db_dict[key].get('sql_ver_db'))
        if os.path.exists(exec_flyway_cache_path):
            logger.info('{}已存在'.format(exec_flyway_cache_path))
        else:
            logger.info("exec_flyway_cache_path:{}".format(exec_flyway_cache_path))
            os.makedirs(exec_flyway_cache_path)
        return exec_flyway_cache_path

    def copy_master_sql_to_dir(self, flyway_cache_path, exec_flyway_cache_path, cur_db_info_item):
        exec_flyway_old_cache_path = "{}/{}/master".format(flyway_cache_path,
                                                           cur_db_info_item.get('sql_ver_db'))
        # if self.db_script_exec_type != DbScriptExecTypeEnum.INCREMENT.type_name:
        #     return
        # 拉取历史到此目录
        logger.info("开始克隆feature-sql master分支")
        pull_sql_lib_repo(exec_flyway_old_cache_path, "master", cur_db_info_item['sql_ver_group'],
                          PRODUCT_STORE_SQL_URL)
        # 检出最近归档且没有执行的sql 按照
        logger.info("exec_flyway_old_cache_path:{}".format(os.path.exists(exec_flyway_old_cache_path)))
        if os.path.exists(exec_flyway_old_cache_path):
            # 查询当前应用历史迭代，按归档时间顺序
            db_archive_branchs_order_asc = get_db_archive_branch_list(cur_db_info_item.get('db_group_name'))
            logger.info("数据库归档迭代【按时间升序】：{}".format(db_archive_branchs_order_asc))
            files = os.listdir(os.path.join(exec_flyway_old_cache_path, 'archive'))
            # 遍历文件列表并移动文件
            branchs = []
            other_branchs = []
            for file in files:
                if file in db_archive_branchs_order_asc:
                    branchs.append(file)
                else:
                    other_branchs.append(file)
                    # 开始排序各个迭代的sql文件
            # 执行迭代外的sql
            index_br = 1
            self.sort_his_sql(cur_db_info_item, exec_flyway_cache_path, exec_flyway_old_cache_path, index_br,
                              other_branchs)

            # 执行迭代内的sql
            sorted_branchs = sorted(branchs, key=lambda x: db_archive_branchs_order_asc.index(x))

            self.sort_his_sql(cur_db_info_item, exec_flyway_cache_path, exec_flyway_old_cache_path, index_br,
                              sorted_branchs)

    def sort_his_sql(self, cur_db_info_item, exec_flyway_cache_path, exec_flyway_old_cache_path, index_br,
                     other_branchs):
        for branch in other_branchs:
            source_path = os.path.join(exec_flyway_old_cache_path, 'archive', branch,
                                       cur_db_info_item['db_name_info'])
            self.reorder_sql(source_path)
            target_path = os.path.join(exec_flyway_cache_path, str(index_br), branch)
            if not os.path.exists(target_path):
                os.makedirs(target_path)
            cmd = 'mv {}/** {}'.format(source_path, target_path)
            exec_local_cmd(cmd)
            index_br = index_br + 1
            sleep(1)

    def parse_data(self):
        """
        解析数据
        :return:
        """
        app_dict_list = [{self.app_name: ""}]
        ptr = PublishTestRepos(self.suite_code,
                               self.workspace,
                               # business_name=self.business_name,
                               type_enum=TypeEnum.BUILD,
                               br_name=self.br_name,
                               cache_data_code=self.cache_data_code,
                               app_dict_list=app_dict_list)
        for row in ptr:
            logger.info(row)
            return row
            # {row.__dict__["module_name"]:[row.__dict__]}
        return None

    @PipelineRecorder()
    def run_step(self, node_name, compile_bo):
        env_publish_info = self.parse_data()
        logger.info(env_publish_info)
        exec_status, exec_msg = self.pipeline_node_dict[node_name](env_publish_info)
        return exec_status, exec_msg

    def run(self, node_name, sid):
        """
        运行入口程序
        :param node_name:
        :return:
        """
        compile_bo = CompileBo.Builder().set_app_name(self.app_name).set_sid(sid).build_publish_bo()
        self.run_step(node_name, compile_bo)

    # 移除已经执行过的sql，避免重复执行
    def mv_overexecute_script(self, exec_flyway_cache_path_dir, db_name, flyway_sql_historys=None):

        logger.info("exec_flyway_cache_path_dir:{},db_name:{}".format(exec_flyway_cache_path_dir, db_name))
        new_sql_files = []
        if os.path.exists(exec_flyway_cache_path_dir):
            for root, dirs, files in os.walk(exec_flyway_cache_path_dir):
                for sql_file in files:
                    if not sql_file.endswith('.sql'):
                        continue
                    # 获取文件的完整路径
                    sql_file_path = os.path.join(root, sql_file)
                    sql_file_sha256 = file_sha256(sql_file_path)
                    sql_his = {
                        "iteration_id": self.iteration_id,
                        "batch_no": self.batch_no,
                        "suite_code": self.suite_code,
                        "db_name": db_name,
                        "sql_ver_name": sql_file,
                        "sql_file_hash": sql_file_sha256,
                    }
                    exex_his = DbMgtExecSqlFileHistory.select(DbMgtExecSqlFileHistory.sql_file_hash,
                                                              DbMgtExecSqlFileHistory.sql_ver_name).where(
                        DbMgtExecSqlFileHistory.db_name == db_name,
                        DbMgtExecSqlFileHistory.suite_code == self.suite_code,
                        DbMgtExecSqlFileHistory.sql_file_hash == sql_file_sha256
                    ).get_or_none()
                    if exex_his:
                        logger.info("排除已经执行过【{},{}】的sql脚本:{}".format(self.suite_code, db_name, sql_file))
                        init_sql_file_path = os.path.join(root, exex_his.sql_ver_name)
                        if sql_file_path != init_sql_file_path:
                            # 文件改名文件
                            cmd = "mv  {} {}".format(sql_file_path, init_sql_file_path)
                            exec_local_cmd(cmd)
                            sql_file_path = init_sql_file_path
                        # 查是否在fly history中
                        if flyway_sql_historys:
                            exit = False
                            for fly_his_script in flyway_sql_historys:
                                if fly_his_script == exex_his.sql_ver_name:
                                    exit = True
                            if not exit:
                                # 删除文件
                                cmd = "rm  {}".format(sql_file_path)
                                exec_local_cmd(cmd)
                                continue
                    else:
                        new_sql_files.append(sql_his)

        new_sql_files = sorted(new_sql_files, key=lambda p: p['sql_ver_name'])
        return new_sql_files

    def handle_flyway_exec_sql_file_history(self, flyway_exec_success, db_type, db_info, wait_log_sql_ddl_files):
        if flyway_exec_success:
            # 记录所有 wait_log_sql_ddl_files
            self.batch_log_exec_sql_file(wait_log_sql_ddl_files)
        else:
            # 查询flyway_schema_history 已经执行成功的sql脚本 同时在wait_log_sql_ddl_files中的
            sql_historys = self.get_flyway_history(db_type, db_info)
            sql_files = [item['sql_ver_name'] for item in wait_log_sql_ddl_files]
            common_elements = list(set(sql_historys) & set(sql_files))
            common_files = []
            for file in wait_log_sql_ddl_files:
                for common_element in common_elements:
                    if file['sql_ver_name'] == common_element:
                        common_files.append(file)
            self.batch_log_exec_sql_file(common_files)

    def batch_log_exec_sql_file(self, wait_log_sql_files):
        if not wait_log_sql_files:
            logger.info("无 sql执行需要记录")
            return
        for sql_file in wait_log_sql_files:
            DbMgtExecSqlFileHistory.get_or_create(iteration_id=self.iteration_id,
                                                  batch_no=self.batch_no,
                                                  suite_code=sql_file['suite_code'],
                                                  db_name=sql_file['db_name'],
                                                  sql_ver_name=sql_file['sql_ver_name'],
                                                  sql_file_hash=sql_file['sql_file_hash'],
                                                  defaults={'create_user': "scm",
                                                            'create_time': datetime.datetime.now(),
                                                            'update_user': "scm",
                                                            'update_time': datetime.datetime.now(),
                                                            })

    # 查询指定数据库flyway的执行历史
    def get_flyway_history(self, db_type, db_info):
        if db_type.lower() == 'mysql':
            return self.get_flyway_mysql_history(db_info)
        else:
            return self.get_flyway_oracle_history(db_info)

    def get_flyway_mysql_history(self, db_info):
        logger.info("db_info:{}".format(db_info))
        with DBConnectionManager(host=db_info.get("db_srv_hosts"), port=3306, user=db_info.get("username"),
                                 password=db_info.get("password"), db=db_info.get("suite_db_name")) as db:
            sql = ''' select script from flyway_schema_history where success=1;'''
            db.cur.execute(sql)
            results = db.cur.fetchall()
            logger.info("get_flyway_mysql_history:{}".format(results))
            return [item['script'] for item in results]

    def get_flyway_oracle_history(self, db_info):
        host = None
        sid = None
        db_user = None
        db_passwd = None
        if db_info.get("db_srv_hosts"):
            host = db_info.get("db_srv_hosts")
            sid = db_info.get("db_srv_name")
            db_user = db_info.get("username")
            db_passwd = db_info.get("password")
        else:
            host = db_info.get("db_hosts")
            sid = db_info.get("db_name")
            db_user = db_info.get("db_user")
            db_passwd = db_info.get("db_passwd")
        logger.info("db_user:{},db_passwd:{},host:{},sid:{}".format(db_user, db_passwd, host, sid))
        with OracleConnectionManager(db_user, db_passwd, host,
                                     sid) as db:
            sql = ''' 
               select "script" from "flyway_schema_history" where "success"=1
            '''

            db.cur.execute(sql)
            results = db.cur.fetchall()
            logger.info("get_flyway_mysql_history:{}".format(results))
            return [item[0] for item in results]

    def reorder_sql(self, source_path):
        br_sql_index = 1
        for root, dirs, files in os.walk(source_path):
            for file in files:
                if not file.endswith(".sql"):
                    continue
                current_datetime = datetime.datetime.now()
                # 创建一个表示一天的增量对象
                one_day = datetime.timedelta(minutes=5)
                # 将一天的增量对象反向应用于当前日期和时间，得到前一天的日期和时间
                yesterday_datetime = current_datetime - one_day
                db_ver_prefix = yesterday_datetime.strftime('%Y.%m.%d.%H.%M.%S')
                sql_file_idx = '%03d' % br_sql_index
                logger.info("old file is:{}".format(file))
                logger.info("old file origin file name:{}".format(str(file).split("__")))
                sql_ver_str = 'V{}.{}__{}'.format(db_ver_prefix, sql_file_idx, str(file).split("__")[1])
                br_sql_index = br_sql_index + 1
                mv_cmd = 'mv {} {} '.format(os.path.join(root, file), os.path.join(root, sql_ver_str))
                exec_local_cmd(mv_cmd)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    workspace = sys.argv[1]
    business_name = sys.argv[2]
    app_name = sys.argv[3]
    br_name = sys.argv[4]
    cache_data_code = sys.argv[5]
    suite_code = sys.argv[6]
    is_mock_agent = sys.argv[7]
    flag_file_dir = sys.argv[8]
    biz_base_db = sys.argv[9]
    # spider传入
    db_script_exec_type = sys.argv[10]
    iteration_id = ''
    parts = workspace.split("/")[-1].split("_")
    if len(parts) >= 2:
        second_part = "_".join(parts[:2])
        iteration_id = second_part
    else:
        logger.info("workspace={}".format(workspace))
        raise Exception("迭代ID获取失败")
    with open(flag_file_dir, "r") as f:
        json_dict = json.loads(f.read())
    sid = json_dict["sid"]
    tep = TestEnvPublisher(workspace, app_name, br_name, suite_code, cache_data_code, is_mock_agent, biz_base_db,
                           db_script_exec_type=db_script_exec_type, iteration_id=iteration_id)
    tep.run(business_name, sid)

    # pas = PublishTestRepos("tms18","E:\\test",business_name= "test_publish",br_name="1.0.1",app_list=["order-plan-center-remote"])

    # lib_repo_info_recorder = LibRepoInfoRecorder("ftx-console-web", "0.0.0001", suite_code="tms02")
    # lib_repo_info_recorder.record_info()
