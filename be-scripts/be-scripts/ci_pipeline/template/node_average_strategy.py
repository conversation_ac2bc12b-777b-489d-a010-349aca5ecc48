from ci_pipeline.template.group_publish_pipeline_constant import get_node_three_operate_str
from ci_pipeline.template.node_pipline_context import CreateNodePiplineStrategy, CreateNodePiplineBaseObject
from ci_pipeline.template.publish_pipeline_constant import get_average_list


class NodeAverageStrategy(CreateNodePiplineStrategy):
    # 节点均分
    def create_node_pipeline_groovy(self, data_bo: CreateNodePiplineBaseObject):
        suite_app = data_bo.suite_app
        node_data = data_bo.node_data
        exec_id = data_bo.exec_id
        # ip_num = len(node_data)
        # avg_list = get_average_list(ip_num)
        # [2, 2, 2]
        key_list = list(node_data.keys())
        stage_groovy_dict = {}
        index = 0
        groups = [key_list[i:i + 3] for i in range(0, len(key_list), 3)]
        suite_stage = ''
        for group in groups:
            app_pipeline = ''
            for node in group:
                node_three_operate_str = get_node_three_operate_str(node, exec_id)
                app_pipeline += node_three_operate_str +'\n'
            app_stages_str = "stages {{\n{}\n}}".format(app_pipeline)
            index += 1
            suite_stage += "\nstage('{}') {{\n{}\n}}\n".format(suite_app + '-' + str(index),
                                                               app_stages_str)

        stage_groovy_dict[1] = suite_stage
        # part1_stage = ''
        # part2_stage = ''
        # part3_stage = ''
        # index = 0
        # part_num = 1
        # for num in avg_list:
        #     suite_stage = ''
        #     for i in range(num):
        #         ip = key_list[index]
        #         node_three_operate_str = get_node_three_operate_str(ip, exec_id)
        #         node_stages_str = "stages {{\n{}\n}}".format(node_three_operate_str)
        #         suite_stage += "\nstage('{}') {{\n{}\n}}\n".format(suite_app + '-' + str(ip),
        #                                                            node_stages_str)
        #         index += 1
        #     if part_num == 1:
        #         part1_stage += suite_stage
        #     elif part_num == 2:
        #         part2_stage += suite_stage
        #     else:
        #         part3_stage += suite_stage
        #     part_num += 1
        # if part1_stage:
        #     stage_groovy_dict[1] = part1_stage
        # if part2_stage:
        #     stage_groovy_dict[2] = part2_stage
        # if part3_stage:
        #     stage_groovy_dict[3] = part3_stage

        return stage_groovy_dict
