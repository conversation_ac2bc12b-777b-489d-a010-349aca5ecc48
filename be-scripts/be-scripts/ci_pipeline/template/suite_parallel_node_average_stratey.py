from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineStrategy, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.publish_pipeline_constant import get_node_three_operate_str, get_average_list


class SuiteParallelNodeAverageStrategy(CreatePublishPipelineStrategy):
    # 环境并行，节点均分
    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        publish_stage = data_bo.publish_stage
        publish_data = data_bo.publish_node_data
        publish_stage_template = "\nstage('{publish_name}') {{\nstages{{{parallel_stage}}}\n}}"
        parallel_stage_part1_template = "\nstage('{publish_name}-Part1') {{\nparallel{{{part1_stage}}}\n}}"
        parallel_stage_part2_template = "\nstage('{publish_name}-Part2') {{\nparallel{{{part2_stage}}}\n}}"
        parallel_stage_part3_template = "\nstage('{publish_name}-Part3') {{\nparallel{{{part3_stage}}}\n}}"
        part1_stage = ''
        part2_stage = ''
        part3_stage = ''
        publish_stage_groovy_str = ''
        for suite_code, v in publish_data.items():

            if suite_code in ['parallel', 'node_parallel']:
                continue
            ip_num = len(v)
            avg_list = get_average_list(ip_num)
            # [2, 2, 2]
            key_list = list(v.keys())

            index = 0
            part_num = 1
            for num in avg_list:
                suite_stage = ''
                for i in range(num):
                    node_three_operate_str = get_node_three_operate_str(key_list[index])
                    node_stages_str = "stages {{\n{}\n}}".format(node_three_operate_str)
                    suite_stage += "\nstage('{}') {{\n{}\n}}\n".format(suite_code + '-' + str(index),
                                                                       node_stages_str)
                    index += 1
                if part_num == 1:
                    part1_stage += suite_stage
                elif part_num == 2:
                    part2_stage += suite_stage
                else:
                    part3_stage += suite_stage
                part_num += 1
        if part1_stage:
            publish_stage_groovy_str += parallel_stage_part1_template.format(publish_name=publish_stage,part1_stage=part1_stage)
        if part2_stage:
            publish_stage_groovy_str += parallel_stage_part2_template.format(publish_name=publish_stage,part2_stage=part2_stage)
        if part3_stage:
            publish_stage_groovy_str += parallel_stage_part3_template.format(publish_name=publish_stage,part3_stage=part3_stage)
        publish_stage_groovy = publish_stage_template.format(publish_name=publish_stage,
                                                             parallel_stage=publish_stage_groovy_str)
        return publish_stage_groovy
