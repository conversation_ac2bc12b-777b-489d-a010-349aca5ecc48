import json
import os
import sys
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from common.routers.router import Router
from settings import logger, JENKINS_INFO, TEST_PUBLISH_AIO
from jenkins import <PERSON>
from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineContext, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.publish_pipeline_constant import PublishPipelineConstant, \
    SuiteParallelStrategy
from ci_pipeline.template.suite_parallel_strategy import ParallelSuiteStrategy
from ci_pipeline.template.suite_seriall_strategy import SerialSuiteStrategy


class BatchDeployPipline(Router):

    def __init__(self, sys_param):
        self.sys_param = sys_param
        self.jenkins_url = JENKINS_INFO["URL"]
        self.username = JENKINS_INFO["USER"]
        self.password = JENKINS_INFO["PASSWORD"]
        self.jenkins_server = Jenkins(self.jenkins_url, username=self.username, password=self.password)

    def make_up_param(self, param_dict, job_name):
        params_list = []
        stage_param_dict = {"job_name": job_name}
        exec_id = None
        try:
            for k1, v1 in param_dict.items():
                # k1 = 2-产线发布
                for suite_code, v2 in v1.items():
                    # suite_code = prod
                    if suite_code in ['parallel', 'node_parallel']:
                        continue
                    for node, v3 in v2.items():
                        if node in ['sequence', 'node_parallel']:
                            continue
                        for ip in v3:
                            if not params_list:
                                data_param = PublishPipelineConstant.DATA_PARAM.format(json.dumps(v3[ip]["update"]))
                                params_list.append(data_param)
                            stage_param_dict["CONFIG_UPDATE_PARAM_" + ip.replace('.', '_')] = v3[ip]["update"]
                            stage_param_dict["PUBLISH_PARAM_" + ip.replace('.', '_')] = v3[ip]["deploy"]
                            stage_param_dict["SERVICE_VERIFY_PARAM_" + ip.replace('.', '_')] = v3[ip]["verify"]
                            if not exec_id:
                                exec_id = v3[ip]["update"]["exec_id"]

            params = PublishPipelineConstant.parameter.format('\n'.join(params_list))
            logger.info("待写入缓存的数据：{}".format(stage_param_dict))
            # param_json = json.dumps(stage_param_dict)
            file_name = job_name + "_" + exec_id + ".json"
            logger.info("文件名：{}".format(file_name))
            with open(os.path.join(TEST_PUBLISH_AIO['nfs_batch_publish'], file_name), "w",
                      encoding='utf-8') as f:
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(TEST_PUBLISH_AIO['nfs_batch_publish'], file_name)))
                f.write(json.dumps(stage_param_dict))

        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, params, stage_param_dict, exec_id

    def make_up_pipeline_node(self, param_dict, exec_id):
        try:
            sorted_dict = sorted(param_dict.items(), key=lambda x: x[0].split('-')[0])
            result_dict = {k: v for k, v in sorted_dict}
            suite_code_stage_list = []
            strategy = None
            context = CreatePublishPipelineContext()
            for k1, v1 in result_dict.items():
                # k1 = 2-产线发布
                # v1 = "bs-zb":{"***********":{}}
                # v1 = "wgq-zb|howbuy-wechat-message-remote":{"node":{"***********":{}},"node_parallel":"1+N"}
                bo = CreatePublishPipelineBaseObject(k1, v1, exec_id)
                if int(v1.get("parallel")) == SuiteParallelStrategy.SERIAL_STRATEGY.strategy_code:
                    logger.info('环境串行')
                    strategy = SerialSuiteStrategy()
                elif int(v1.get("parallel")) == SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_code:
                    logger.info('环境并行')
                    strategy = ParallelSuiteStrategy()
                else:
                    raise Exception('不支持的环境策略:{}'.format(v1.get("parallel")))
                context.set_strategy(strategy)
                context.set_data_bo(bo)
                pipline_groovy = context.do_strategy()
                if pipline_groovy:
                    suite_code_stage_list.append(pipline_groovy)
            pipeline_groovy = "\n".join(suite_code_stage_list)
            print(pipeline_groovy)
        except Exception as e:
            logger.error(traceback.format_exc())
            raise Exception(e)
        return True, pipeline_groovy

    def get_deploy_jenkins_config(self):
        try:
            job_name = 'deploy_pipeline_模板'
            job_config = self.jenkins_server.get_job_config(job_name)
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, job_config

    def reconstruct_deploy_jenkins_config(self, job_config, params, node, job_name):
        try:
            job_config = job_config.replace("//parameters", params)
            job_config = job_config.replace("//stages", node)
            self.jenkins_server.reconfig_job(job_name, job_config)
        except Exception as e:
            print(e)
            logger.error(e)
            raise Exception(e)
        return True

    @staticmethod
    def _get_job_name(exec_param):
        job_name = ''
        try:
            for k1, v1 in exec_param.items():
                # k1 = 2-产线发布
                if k1 in ['job_name']:
                    job_name = v1
                    continue
            if not job_name:
                raise Exception('没有获取到job_name')
            del exec_param["job_name"]
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, job_name

    def main(self):
        try:
            exec_param = self._get_params(self.sys_param)

            exec_param = json.loads(exec_param)
            logger.info(exec_param)

            status, job_name = self._get_job_name(exec_param)
            # job_name = "ztst_group_publish_pipeline"
            status, params, stage_param_list, exec_id = self.make_up_param(exec_param, job_name)
            status, node = self.make_up_pipeline_node(exec_param, exec_id)
            status, template_job_config = self.get_deploy_jenkins_config()
            status = self.reconstruct_deploy_jenkins_config(template_job_config, params, node, job_name)
        except Exception as e:
            logger.error(traceback.format_exc())
            sys.exit(1)
        return True, "success"


if __name__ == '__main__':
    exec_param2 = """
    {
    "1-灾备发布": {
        "parallel": 0,
        "bs-zb|howbuy-wechat-message-console": {
            "node": {
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                }
            },
            "node_parallel": 0,
            "sequence": 0
        },
        "bs-zb|howbuy-wechat-message-remote": {
            "node": {
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 218283,
                        "end_ver": "",
                        "iteration_id": "ztst_patest-group-publish-20240711",
                        "exec_id": "20240823110842",
                        "username": "wenlong.zhang"
                    }
                }
            },
            "node_parallel": 0,
            "sequence": 0
        }
    }
}
    """

    exec_param1 = """
{
    "1-灾备发布": {
        "wgq-zb|howbuy-wechat-message-remote": {
            "node": {
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "wgq-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 0,
             "sequence": 1
        },
        "bs-zb|howbuy-wechat-message-remote": {
            "node": {
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 1,
            "sequence": 1
        },
        "parallel": 1
    },
    "2-灾备发布": {
        "bs-zb|howbuy-wechat-message-console": {
            "node": {
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 0,
             "sequence": 1
        },
        "bs-zb|howbuy-wechat-message-executor": {
            "node": {
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-executor",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-executor",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-executor",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-executor",
                        "suite_code": "bs-zb",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-executor",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-executor",
                        "suite_code": "bs-zb",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 2,
            "sequence": 1
        },
        "parallel": 1
    },
    "3-产线发布": {
        "prod|howbuy-wechat-message-remote": {
            "node": {
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "prod",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "prod",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "prod",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "************": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "prod",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "prod",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "prod",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "************",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 0,
            "sequence": 1
        },
        "parallel": 0
    },
    "4-产线发布": {
        "pd-prod|howbuy-wechat-message-console": {
            "node": {
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "pd-prod",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "pd-prod",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-console",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 2,
            "sequence": 1
        },
        "pd-prod|howbuy-wechat-message-remote": {
            "node": {
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "pd-prod",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                },
                "***********": {
                    "update": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "pd-prod",
                        "res_type": "config",
                        "opt_type": "update",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "deploy": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "deploy",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    },
                    "verify": {
                        "app_name": "howbuy-wechat-message-remote",
                        "suite_code": "pd-prod",
                        "res_type": "pkg",
                        "opt_type": "verify",
                        "ip": "***********",
                        "action_id": 236407,
                        "end_ver": "",
                        "iteration_id": "ftx_2.0.1",
                        "exec_id": "20240318214400",
                        "username": "haiguang.chen"
                    }
                }
            },
            "node_parallel": 1,
            "sequence": 1
        },
        "parallel": 0
    }
}
"""

try:
    sys_param = sys.argv[1]
    # sys_param = 6252257
    logger.info(sys_param)
    batch_deploy = BatchDeployPipline(sys_param)
    batch_deploy.main()
    # exec_param = json.loads(exec_param2)
    # batch_deploy.make_up_param(exec_param, 'group_publish_demo')
    # batch_deploy.make_up_pipeline_node(exec_param, '20240823110842')

    sys.exit(0)
except Exception as e:
    logger.error(e)
    sys.exit(1)
