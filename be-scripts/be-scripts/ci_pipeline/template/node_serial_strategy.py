from ci_pipeline.template.node_pipline_context import CreateNodePiplineStrategy, CreateNodePiplineBaseObject
from ci_pipeline.template.group_publish_pipeline_constant import get_node_three_operate_str


class NodeSerialStrategy(CreateNodePiplineStrategy):
    # 节点串行
    def create_node_pipeline_groovy(self, data_bo: CreateNodePiplineBaseObject):
        suite_app = data_bo.suite_app
        node_data = data_bo.node_data
        exec_id = data_bo.exec_id
        stage_groovy_dict = {}
        app_pipeline = ''
        for index, ip in enumerate(node_data.keys()):
            node_three_operate_str = get_node_three_operate_str(ip, exec_id)
            app_pipeline += node_three_operate_str + '\n'
        app_stages_str = "stages {{\n{}\n}}".format(app_pipeline)
        suite_stage = "\nstage('{}') {{\n{}\n}}\n".format(suite_app + '-1', app_stages_str)
        stage_groovy_dict[1] = suite_stage

        return stage_groovy_dict
