from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineStrategy, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.publish_pipeline_constant import get_node_three_operate_str


class SuiteSerialNodeOneNStrategy(CreatePublishPipelineStrategy):
    # 环境串行，节点1+N并行
    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        publish_stage = data_bo.publish_stage
        publish_data = data_bo.publish_node_data
        publish_stage_template = "\nstage('{publish_name}') {{\nstages{{{parallel_stage}}}\n}}"
        parallel_stage_part1_template = "\nstage('{suite_code}-Part1') {{\nparallel{{{part1_stage}}}\n}}"
        parallel_stage_part2_template = "\nstage('{suite_code}-Part2') {{\nparallel{{{part2_stage}}}\n}}"

        publish_stage_groovy_str = ''
        for suite_code, v in publish_data.items():
            part1_stage = ''
            part2_stage = ''
            if suite_code in ['parallel', 'node_parallel']:
                continue
            for index, ip in enumerate(v.keys()):
                node_three_operate_str = get_node_three_operate_str(ip)
                node_stages_str = "stages {{\n{}\n}}".format(node_three_operate_str)
                suite_stage = "\nstage('{}') {{\n{}\n}}\n".format(suite_code + '-' + str(index),
                                                                  node_stages_str)
                if index == 0:
                    part1_stage += suite_stage
                else:
                    part2_stage += suite_stage

            parallel_stage_part1 = parallel_stage_part1_template.format(suite_code=suite_code,
                part1_stage=part1_stage) if part1_stage else ''
            parallel_stage_part2 = parallel_stage_part2_template.format(suite_code=suite_code,
                part2_stage=part2_stage) if part2_stage else ''
            publish_stage_groovy_str += parallel_stage_part1 + parallel_stage_part2
        publish_stage_groovy = publish_stage_template.format(publish_name=publish_stage,
                                                             parallel_stage=publish_stage_groovy_str)
        return publish_stage_groovy
