import os
import sys
import json

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from common.routers.router import Router
from settings import logger, J<PERSON><PERSON><PERSON>S_INFO
from jenkins import <PERSON>
from ci_pipeline.template.suite_serial_node_serial_strategy import SuiteSerialNodeSerialStrategy
from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineContext, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.suite_parallel_node_one_n_strategy import SuiteParallelNodeOneNStrategy
from ci_pipeline.template.suite_parallel_node_serial_stratey import Suite<PERSON>arallelNodeSerialStrategy
from ci_pipeline.template.publish_pipeline_constant import PublishPipelineConstant, SuiteParallelStrategy, \
    NodeParallelStrategy
from ci_pipeline.template.suite_parallel_node_average_stratey import SuiteParallelNodeAverageStrategy
from ci_pipeline.template.suite_serial_node_average_stratey import SuiteSerialNodeAverageStrategy
from ci_pipeline.template.suite_serial_node_one_n_strategy import SuiteSerialNodeOneNStrategy


class BatchDeployPipline(Router):

    def __init__(self, sys_param):
        self.sys_param = sys_param
        self.jenkins_url = JENKINS_INFO["URL"]
        self.username = JENKINS_INFO["USER"]
        self.password = JENKINS_INFO["PASSWORD"]
        self.jenkins_server = Jenkins(self.jenkins_url, username=self.username, password=self.password)

    def make_up_param(self, param_dict):
        params_list = []
        stage_param_list = {}
        try:
            for k1, v1 in param_dict.items():
                # k1 = 2-产线发布
                for suite_code, v2 in v1.items():
                    # suite_code = prod
                    if suite_code in ['parallel', 'node_parallel']:
                        continue
                    for ip in v2:
                        if not params_list:
                            data_param = PublishPipelineConstant.DATA_PARAM.format(json.dumps(v2[ip]["update"]))
                            params_list.append(data_param)
                        config_update_param = PublishPipelineConstant.CONFIG_UPDATE_PARAM.format(ip.replace('.', '_'),
                                                                                                 json.dumps(
                                                                                                     v2[ip]["update"]))
                        publish_param = PublishPipelineConstant.PUBLISH_PARAM.format(ip.replace('.', '_'),
                                                                                     json.dumps(v2[ip]["deploy"]))
                        service_verify_param = PublishPipelineConstant.SERVICE_VERIFY_PARAM.format(ip.replace('.', '_'),
                                                                                                   json.dumps(v2[ip][
                                                                                                                  "verify"]))
                        params_list.append(
                            "{}\n{}\n{}\n".format(config_update_param, publish_param, service_verify_param))
                        stage_param_list[ip] = ["CONFIG_UPDATE_PARAM_{}".format(ip),
                                                "PUBLISH_PARAM_{}".format(ip),
                                                "SERVICE_VERIFY_PARAM_{}".format(ip)]

            params = PublishPipelineConstant.parameter.format('\n'.join(params_list))
            print('params={}'.format(params))
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, params, stage_param_list

    def make_up_pipeline_node(self, param_dict):
        try:
            sorted_dict = sorted(param_dict.items(), key=lambda x: x[0].split('-')[0])
            result_dict = {k: v for k, v in sorted_dict}
            pipeline_groovy = ''
            strategy = None
            context = CreatePublishPipelineContext()
            for k1, v1 in result_dict.items():
                # k1 = 2-产线发布
                bo = CreatePublishPipelineBaseObject(k1, v1)

                if int(v1.get("parallel")) == SuiteParallelStrategy.SERIAL_STRATEGY.strategy_code and int(
                        v1.get("node_parallel")) == NodeParallelStrategy.SERIAL_STRATEGY.strategy_code:
                    logger.info("当前执行策略为： {}, {}".format(SuiteParallelStrategy.SERIAL_STRATEGY.strategy_desc,
                                                                NodeParallelStrategy.SERIAL_STRATEGY.strategy_desc))
                    strategy = SuiteSerialNodeSerialStrategy()

                elif int(v1.get("parallel")) == SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_code and int(
                        v1.get("node_parallel")) == NodeParallelStrategy.ONE_N_STRATEGY.strategy_code:
                    logger.info("当前执行策略为： {}, {}".format(SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_desc,
                                                                NodeParallelStrategy.ONE_N_STRATEGY.strategy_desc))
                    strategy = SuiteParallelNodeOneNStrategy()
                elif int(v1.get("parallel")) == SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_code and int(
                        v1.get("node_parallel")) == NodeParallelStrategy.SERIAL_STRATEGY.strategy_code:
                    logger.info("当前执行策略为： {}, {}".format(SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_desc,
                                                                NodeParallelStrategy.SERIAL_STRATEGY.strategy_desc))
                    strategy = SuiteParallelNodeSerialStrategy()
                elif int(v1.get("parallel")) == SuiteParallelStrategy.SERIAL_STRATEGY.strategy_code and int(
                        v1.get("node_parallel")) == NodeParallelStrategy.ONE_N_STRATEGY.strategy_code:
                    logger.info("当前执行策略为： {}, {}".format(SuiteParallelStrategy.SERIAL_STRATEGY.strategy_desc,
                                                                NodeParallelStrategy.ONE_N_STRATEGY.strategy_desc))
                    strategy = SuiteSerialNodeOneNStrategy()
                elif int(v1.get("parallel")) == SuiteParallelStrategy.SERIAL_STRATEGY.strategy_code and int(
                        v1.get("node_parallel")) == NodeParallelStrategy.AVERAGE_STRATEGY.strategy_code:
                    logger.info("当前执行策略为： {}, {}".format(SuiteParallelStrategy.SERIAL_STRATEGY.strategy_desc,
                                                                NodeParallelStrategy.AVERAGE_STRATEGY.strategy_desc))
                    strategy = SuiteSerialNodeAverageStrategy()
                elif int(v1.get("parallel")) == SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_code and int(
                        v1.get("node_parallel")) == NodeParallelStrategy.AVERAGE_STRATEGY.strategy_code:
                    logger.info("当前执行策略为： {}, {}".format(SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_desc,
                                                                NodeParallelStrategy.AVERAGE_STRATEGY.strategy_desc))
                    strategy = SuiteParallelNodeAverageStrategy()
                context.set_strategy(strategy)
                context.set_data_bo(bo)
                pipeline_groovy += context.do_strategy()
            print(pipeline_groovy)
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, pipeline_groovy

    def get_deploy_jenkins_config(self, params, node):
        try:
            job_name = 'deploy_pipeline_模板'
            job_config = self.jenkins_server.get_job_config(job_name)
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, job_config

    def reconstruct_deploy_jenkins_config(self, job_config, params, node, job_name):
        try:
            job_config = job_config.replace("//parameters", params)
            job_config = job_config.replace("//stages", node)
            self.jenkins_server.reconfig_job(job_name, job_config)
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True

    @staticmethod
    def _get_job_name(exec_param):
        app_name = ''
        try:
            for k1, v1 in exec_param.items():
                # k1 = 2-产线发布
                for suite_code, v2 in v1.items():
                    if suite_code in ['parallel', 'node_parallel']:
                        continue
                    for ip, v3 in v2.items():
                        for action, action_param in v3.items():
                            app_name = action_param.get('app_name')
                            break
                        break
                    break
                break
            job_name = '{}_publish_pipeline'.format(app_name)
            if not app_name:
                raise Exception('没有获取到job_name')
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, job_name

    def main(self):
        try:
            exec_param = self._get_params(self.sys_param)

            exec_param = json.loads(exec_param)
            logger.info(exec_param)

            status, job_name = self._get_job_name(exec_param)
            status, params, stage_param_list = self.make_up_param(exec_param)
            status, node = self.make_up_pipeline_node(exec_param)
            status, template_job_config = self.get_deploy_jenkins_config(params, node)
            status = self.reconstruct_deploy_jenkins_config(template_job_config, params, node, job_name)
        except Exception as e:
            sys.exit(1)
        return True, "success"


if __name__ == '__main__':

    try:
        sys_param = sys.argv[1]
        # sys_param = 1204336
        logger.info(type(sys_param))
        batch_deploy = BatchDeployPipline(sys_param)
        batch_deploy.main()
        sys.exit(0)
    except Exception as e:
        logger.error(e)
        sys.exit(1)

# exec_param = '''
#                         {
#                                     	"2-产线发布":
#                                     	{
#                                     		"prod":
#                                     			{
#
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#             										"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#             										"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				}
#                                     			},
#                                     		"pd-prod":
#                                     			{
#                                     				"**************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "**************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "**************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "**************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#                                     				"**************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "**************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "**************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "**************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				}
#                                     			}
#             									,
#             									"parallel":1,
#             									"node_parallel":"1+N"
#
#                                     	},
#                                     	"1-灾备发布":
#                                     	{
#                                     		"wgq-zb":
#                                     			{
#
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				}
#                                     			},
#                                     		"bs-zb":
#                                     			{
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				},
#                                     				"************": {
#                                     					"update": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "config",
#                                     						"opt_type": "update",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"deploy": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "deploy",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					},
#                                     					"verify": {
#                                     						"app_name": "prod-mq-pqc-info",
#                                     						"suite_code": "prod",
#                                     						"res_type": "pkg",
#                                     						"opt_type": "verify",
#                                     						"ip": "************",
#                                     						"action_id": 236407,
#                                     						"end_ver": "",
#                                     						"iteration_id": "ftx_2.0.1",
#                                     						"exec_id": "20240318214400",
#                                     						"username": "haiguang.chen"
#                                     					}
#                                     				}
#                                     			},
#             								"parallel":1,
#             								"node_parallel":"1+N"
#
#                                     	}
#                                     }
#                                      '''
