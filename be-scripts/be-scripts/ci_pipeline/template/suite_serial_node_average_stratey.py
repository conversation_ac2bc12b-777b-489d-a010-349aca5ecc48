from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineStrategy, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.publish_pipeline_constant import get_node_three_operate_str, get_average_list


class SuiteSerialNodeAverageStrategy(CreatePublishPipelineStrategy):
    # 环境串行，节点均分
    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        publish_stage = data_bo.publish_stage
        publish_data = data_bo.publish_node_data
        publish_stage_template = "\nstage('{publish_name}') {{\nstages{{{parallel_stage}}}\n}}"
        parallel_stage_part_template = "\nstage('{suite_code}-Part{part_num}') {{\nparallel{{{part1_stage}}}\n}}"

        publish_stage_groovy_str = ''
        for suite_code, v in publish_data.items():
            parallel_stage_part = ''

            if suite_code in ['parallel', 'node_parallel']:
                continue
            ip_num = len(v)
            avg_list = get_average_list(ip_num)
            # [2, 2, 2]
            key_list = list(v.keys())

            index = 0
            part_num = 1
            for num in avg_list:
                suite_stage = ''
                for i in range(num):
                    node_three_operate_str = get_node_three_operate_str(key_list[index])
                    node_stages_str = "stages {{\n{}\n}}".format(node_three_operate_str)
                    suite_stage += "\nstage('{}') {{\n{}\n}}\n".format(suite_code + '-' + str(index),
                                                                       node_stages_str)
                    index += 1
                parallel_stage_part += parallel_stage_part_template.format(suite_code=suite_code, part_num=part_num,
                                                                           part1_stage=suite_stage)
                part_num += 1
            publish_stage_groovy_str += parallel_stage_part
        publish_stage_groovy = publish_stage_template.format(publish_name=publish_stage,
                                                             parallel_stage=publish_stage_groovy_str)
        return publish_stage_groovy
