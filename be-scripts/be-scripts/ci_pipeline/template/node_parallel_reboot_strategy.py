from ci_pipeline.template.group_publish_pipeline_constant import get_node_reboot_operate_str
from ci_pipeline.template.node_pipline_context import CreateNodePiplineStrategy, CreateNodePiplineBaseObject


class NodeParallelRebootStrategy(CreateNodePiplineStrategy):
    # 节点并发重启
    def create_node_pipeline_groovy(self, data_bo: CreateNodePiplineBaseObject):
        node_key = data_bo.suite_app
        node_data = data_bo.node_data
        exec_id = data_bo.exec_id
        node_reboot_operate_str = get_node_reboot_operate_str(node_key, exec_id)

        return node_reboot_operate_str
