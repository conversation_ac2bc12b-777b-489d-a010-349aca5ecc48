import json
import os
import sys
import traceback


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from common.routers.router import Router
from settings import logger, JENKINS_INFO, TEST_PUBLISH_AIO
from jenkins import <PERSON>
from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineContext, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.stage_seriall_strategy import StageSerialStrategy
from dao.update.mysql.service_results import update_exec_results
from ci_pipeline.template.publish_pipeline_constant import PublishPipelineConstant


class BatchRebootPipline(Router):

    def __init__(self, sys_param):
        self.sys_param = sys_param
        self.jenkins_url = JENKINS_INFO["URL"]
        self.username = JENKINS_INFO["USER"]
        self.password = JENKINS_INFO["PASSWORD"]
        self.jenkins_server = <PERSON>(self.jenkins_url, username=self.username, password=self.password)

    def make_up_param(self, param_dict, job_name, exec_id):
        stage_param_dict = {"job_name": job_name}
        params_list = []
        try:
            for k1, v1 in param_dict.items():
                for app_key, v2 in v1.items():
                    v2['exec_id'] = exec_id
                    stage_param_dict[app_key] = v2
                    if not params_list:
                        data_param = PublishPipelineConstant.DATA_PARAM.format(json.dumps(v2))
                        params_list.append(data_param)
            params = PublishPipelineConstant.parameter.format('\n'.join(params_list))
            logger.info("待写入缓存的数据：{}".format(stage_param_dict))
            file_name = job_name + "_" + str(exec_id) + ".json"
            logger.info("文件名：{}".format(file_name))
            with open(os.path.join(TEST_PUBLISH_AIO['nfs_batch_publish'], file_name), "w",
                      encoding='utf-8') as f:
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(TEST_PUBLISH_AIO['nfs_batch_publish'], file_name)))
                f.write(json.dumps(stage_param_dict))

        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, params

    def make_up_pipeline_node(self, param_dict, exec_id):
        try:
            sorted_dict = sorted(param_dict.items(), key=lambda x: x[0].split('_')[1])
            result_dict = {k: v for k, v in sorted_dict}
            suite_code_stage_list = []
            strategy = StageSerialStrategy()
            context = CreatePublishPipelineContext()
            for stage, v1 in result_dict.items():
                bo = CreatePublishPipelineBaseObject(stage, v1, exec_id)
                context.set_strategy(strategy)
                context.set_data_bo(bo)
                pipline_groovy = context.do_strategy()
                if pipline_groovy:
                    suite_code_stage_list.append(pipline_groovy)
            pipeline_groovy = "\n".join(suite_code_stage_list)
            # print(pipeline_groovy)
        except Exception as e:
            traceback.format_exc()
            raise Exception(e)
        return True, pipeline_groovy

    def get_deploy_jenkins_config(self):
        try:
            job_name = 'reboot_pipeline_模板'
            job_config = self.jenkins_server.get_job_config(job_name)
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, job_config

    def reconstruct_deploy_jenkins_config(self, job_config, node, params, job_name):
        try:
            job_config = job_config.replace("//parameters", params)
            job_config = job_config.replace("//stages", node)
            self.jenkins_server.reconfig_job(job_name, job_config)
        except Exception as e:
            print(e)
            logger.error(e)
            raise Exception(e)
        return True

    @staticmethod
    def _get_job_name(exec_param):
        job_name = ''
        try:
            for k1, v1 in exec_param.items():
                if k1 in ['job_name']:
                    job_name = v1
                    continue
            if not job_name:
                raise Exception('没有获取到job_name')
            del exec_param["job_name"]
        except Exception as e:
            logger.error(e)
            raise Exception(e)
        return True, job_name

    def main(self):
        try:
            exec_id = self.sys_param
            exec_param = self._get_params(exec_id)

            exec_param = json.loads(exec_param)
            logger.info(exec_param)

            status, job_name = self._get_job_name(exec_param)
            status, params = self.make_up_param(exec_param, job_name, exec_id)
            status, node = self.make_up_pipeline_node(exec_param, exec_id)
            status, template_job_config = self.get_deploy_jenkins_config()
            status = self.reconstruct_deploy_jenkins_config(template_job_config, node, params, job_name)
        except Exception as e:
            logger.error(traceback.format_exc())
            sys.exit(1)
        return True, "success"


if __name__ == '__main__':
    exec_result = "running"
    msg = '执行成功'
    sys_param = sys.argv[1]
    # sys_param = 6252257
    logger.info(sys_param)
    try:
        update_exec_results(sys_param, exec_result, "")
        batch_deploy = BatchRebootPipline(sys_param)
        batch_deploy.main()
        exec_result = "success"
    except Exception as e:
        exec_result = "failure"
        msg = str(e)
        traceback.print_exc()
        raise Exception(e)
    finally:
        update_exec_results(sys_param, exec_result, msg)
        sys.exit(0)
