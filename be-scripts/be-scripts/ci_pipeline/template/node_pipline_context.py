from abc import ABCMeta, abstractmethod


class CreateNodePiplineBaseObject:

    def __init__(self, suite_app=None, node_data=None, suite_parallel=None, exec_id=None):
        self.suite_app = suite_app
        self.node_data = node_data
        self.suite_parallel = suite_parallel
        self.exec_id = exec_id


class CreateNodePiplineStrategy(metaclass=ABCMeta):

    @abstractmethod
    def create_node_pipeline_groovy(self, data_bo: CreateNodePiplineBaseObject):
        pass


class CreateNodePipelineContext:
    def __init__(self, strategy=None, nodePiplineBaseObject: CreateNodePiplineBaseObject=None):
        self.strategy = strategy
        self.data_bo = nodePiplineBaseObject

    def set_strategy(self, strategy):
        self.strategy = strategy

    def set_data_bo(self, data_bo):
        self.data_bo = data_bo

    def do_strategy(self):
        return self.strategy.create_node_pipeline_groovy(self.data_bo)
