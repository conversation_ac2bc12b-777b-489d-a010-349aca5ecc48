import enum


@enum.unique
class NodeParallelStrategy(enum.Enum):
    SERIAL_STRATEGY = (0, "节点串行")
    ONE_N_STRATEGY = (1, "节点1+N并行")
    AVERAGE_STRATEGY = (2, "节点均分并行")

    def __init__(self, strategy_code, strategy_desc):
        self.strategy_code = strategy_code
        self.strategy_desc = strategy_desc


@enum.unique
class SuiteParallelStrategy(enum.Enum):
    SERIAL_STRATEGY = (0, "环境串行")
    PARALLEL_STRATEGY = (1, "环境并行")

    def __init__(self, strategy_code, strategy_desc):
        self.strategy_code = strategy_code
        self.strategy_desc = strategy_desc


class PublishPipelineConstant:
    DATA_PARAM = '''string(name: 'DATA_PARAM', defaultValue: '{}', description: 'handle data param')'''
    CONFIG_UPDATE_PARAM = '''string(name: 'CONFIG_UPDATE_PARAM_{}', defaultValue: '{}', description: 'Parameter for the  config update')'''
    PUBLISH_PARAM = '''string(name: 'PUBLISH_PARAM_{}', defaultValue: '{}', description: 'Parameter for the  publish update')'''
    SERVICE_VERIFY_PARAM = '''string(name: 'SERVICE_VERIFY_PARAM_{}', defaultValue: '{}', description: 'Parameter for the verify update')'''

    parameter = "parameters {{{}}}"

    CONFIG_UPDATE = '''
                stage("配置更新 {}") {{
                    steps {{
                        script {{
                            try {{
                                def defaultValue = new JsonSlurper().parseText(params.CONFIG_UPDATE_PARAM_{})
                                defaultValue = new HashMap(defaultValue)
                                defaultValue.build_id = currentBuild.id
                                defaultValue.job_name = env.JOB_NAME
                                def updatedValue = JsonOutput.toJson(defaultValue)
                                echo "demo:" + updatedValue
                                echo "demo: ${{params.CONFIG_UPDATE_PARAM_{}}}"
                                sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/publish_jenkins_pipeline_main.py run_publish_step_main \\'' + updatedValue + '\\''
                            }} catch (Exception e) {{
                                def logContent = currentBuild.rawBuild.getLog(100)
                                logContent = "这一步是在应用配置更新时，输出调用salt服务器的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下：\\n\\n" + logContent
                                def params = [
                                    "content": "要给出最有可能得结果（一般不要超过3条），这个日志是调用salt服务返回的：\\n- 首先，这是通过调用salt服务器返回结果判断执行是否成功。应当以http的结果来回答。比如返回结果中，result字段的值为“False”\\n- 其次，ERROR或Exception附近如果有类似“[{}]、版本不一致、平台调用salt命令返回码：500，salt操作失败，请及时联系运维排查解决、Failed to establish a new connection”等的提示，也可能是问题的主要原因\\n- 无法给予提示的问题可回答找张怀天，冯伟敏帮助排查", 
                                    "question": logContent
                                    ]
                                def response = httpRequest(
                                    contentType: 'APPLICATION_JSON',
                                    httpMode: 'POST',
                                    requestBody: JsonOutput.toJson(params),
                                    url: 'http://mantis.howbuy.pa/mantis/assistant/question_answer/question_store/'
                                )
                                def json = readJSON text: response.content
                                def key = json.key
                                println "看不懂日志？移步这里试试：http://mantis.howbuy.pa/mantis/assistant/question_answer/question_answer/?key=$key"
                                throw e
                            }}
                        }}
                    }}
                }}'''

    PUBLISH = '''stage('发布 {}') {{
                   steps {{
                       script {{
                           try {{
                                def defaultValue = new JsonSlurper().parseText(params.PUBLISH_PARAM_{})
                                defaultValue = new HashMap(defaultValue)
                                defaultValue.build_id = currentBuild.id
                                defaultValue.job_name = env.JOB_NAME
                                def updatedValue = JsonOutput.toJson(defaultValue)
                                echo "demo:" + updatedValue
                                echo "demo: ${{params.PUBLISH_PARAM_{}}}"
                                sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/publish_jenkins_pipeline_main.py run_publish_step_main \\'' + updatedValue + '\\''
                           }} catch (Exception e) {{
                              def logContent = currentBuild.rawBuild.getLog(100)
                              logContent = "这一步是在应用发布时，输出调用salt服务器的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下：\\n\\n" + logContent
                              def params = [
                                    "content": "要给出最有可能得结果（一般不要超过3条），这个日志是调用salt服务返回的：\\n- 首先，调用salt服务器返回结果判断执行是否成功。应当以http的结果来回答。比如脚本返回中出现“Starting”等内容，可能启动成功\\n- 其次，其次，ERROR或Exception附近如果有类似“Failed to establish a new connection、版本不一致、[{}]、版本不一致”的提示，也可能是问题的主要原因，这类问题一般联系运维处理\\n- 如果错误中明确有以下内容的可以参考。无法给予提示的问题可回答找张怀天，冯伟敏帮助排查", 
                                    "question": logContent
                                    ]
                              def response = httpRequest(
                                    contentType: 'APPLICATION_JSON',
                                    httpMode: 'POST',
                                    requestBody: JsonOutput.toJson(params),
                                    url: 'http://mantis.howbuy.pa/mantis/assistant/question_answer/question_store/'
                                )
                              def json = readJSON text: response.content
                              def key = json.key
                              println "看不懂日志？移步这里试试：http://mantis.howbuy.pa/mantis/assistant/question_answer/question_answer/?key=$key"
                              throw e
                            }}
                        }}
                   }}
               }}'''

    SERVICE_VERIFY = '''stage('服务验证 {}') {{
                    steps {{
                        script {{
                            try {{
                                def defaultValue = new JsonSlurper().parseText(params.SERVICE_VERIFY_PARAM_{})
                                defaultValue = new HashMap(defaultValue)
                                defaultValue.build_id = currentBuild.id
                                defaultValue.job_name = env.JOB_NAME
                                def updatedValue = JsonOutput.toJson(defaultValue)
                                echo "demo:" + updatedValue
                                echo "demo: ${{params.SERVICE_VERIFY_PARAM_{}}}"
                                sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/publish_jenkins_pipeline_main.py run_publish_step_main \\'' + updatedValue + '\\''
                            }} catch(Exception e) {{
                                def logContent = currentBuild.rawBuild.getLog(100)
                                logContent = "这一步是在应用启动时，输出spring actuator打印的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下：\\n\\n" + logContent
                                def params = [
                                    "content": "要给出最有可能得结果（一般不要超过3条），这个日志是调用salt服务返回的：\\n- 首先，这是通过spring actuator来判断服务是否启动正常。应当以spring actuator的结果来回答。比如spring actuator监测中出现“DOWN”\\n- 其次，ERROR附近如果有类似“版本不一致”的提示，也可能是问题的主要原因\\n- 如果错误中明确有以下内容的可以参考。如果不是以下错误，那不应当往这方面回答\\n1. 如果报错中提到“返回的json中不包含component”，应该就是报文不符合规范，直接告知报文内容并告知不符合规范；\\n2. 如果返回的json字符串是空的，那么除了告知问题外，可回答找张怀天，冯伟敏帮助排查", 
                                    "question": logContent
                                    ]
                                def response = httpRequest(
                                    contentType: 'APPLICATION_JSON',
                                    httpMode: 'POST',
                                    requestBody: JsonOutput.toJson(params),
                                    url: 'http://mantis.howbuy.pa/mantis/assistant/question_answer/question_store/'
                                )
                                def json = readJSON text: response.content
                                def key = json.key
                                println "看不懂日志？移步这里试试：http://mantis.howbuy.pa/mantis/assistant/question_answer/question_answer/?key=$key"
                                throw e
                            }}
                        }}
                    }}
                }}'''


def get_node_three_operate_str(ip):
    node_three_operate_str = ''
    CONFIG_UPDATE = PublishPipelineConstant.CONFIG_UPDATE.format(ip.replace('.', '_'), ip.replace('.', '_'),
                                                                 ip.replace('.', '_'), ip.replace('.', '_'))
    PUBLISH = PublishPipelineConstant.PUBLISH.format(ip.replace('.', '_'), ip.replace('.', '_'),
                                                     ip.replace('.', '_'))
    SERVICE_VERIFY = PublishPipelineConstant.SERVICE_VERIFY.format(ip.replace('.', '_'), ip.replace('.', '_'),
                                                                   ip.replace('.', '_'))
    node_three_operate_str += "{}\n{}\n{}\n".format(CONFIG_UPDATE, PUBLISH, SERVICE_VERIFY)
    return node_three_operate_str


def get_average_list(n):
    if n == 1:
        return [1]
    elif n == 2:
        return [1, 1]

    x, y = divmod(n, 3)

    if y == 0:
        return [x] * 3
    elif y == 1:
        return [x] * 2 + [x + 1]
    else:
        return [x] + [x + 1] * 2


if __name__ == '__main__':
    print(get_average_list(8))
