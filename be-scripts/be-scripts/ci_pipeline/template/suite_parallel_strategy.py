from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineStrategy, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.node_average_strategy import NodeAverageStrategy
from ci_pipeline.template.node_one_n_strategy import NodeOneNStrategy
from ci_pipeline.template.node_pipline_context import CreateNodePiplineBaseObject, CreateNodePipelineContext
from ci_pipeline.template.node_serial_strategy import NodeSerialStrategy
from ci_pipeline.template.publish_pipeline_constant import NodeParallelStrategy
from settings import logger


class ParallelSuiteStrategy(CreatePublishPipelineStrategy):
    # 环境并行
    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        publish_stage = data_bo.publish_stage
        publish_data = data_bo.publish_node_data
        exec_id = data_bo.exec_id
        # k1 = 2-产线发布
        # v1 = "bs-zb":{"***********":{}},"parallel":"0", "node_parallel":"0"
        # v1 = {"wgq-zb|howbuy-wechat-message-remote":{"node":{"***********":{}},"node_parallel":"1+N"}, "wgq-zb|howbuy-wechat-message-console":{"node":{"***********":{}},"node_parallel":"1+N"},"parallel":"0"}
        publish_stage_groovy = ''
        suite_code_stage = {}
        context = CreateNodePipelineContext()
        for suite_app, v in publish_data.items():
            logger.info('suite_app:{}'.format(suite_app))
            if suite_app in ['parallel', 'node_parallel']:
                continue
            node_parallel = v.get('node_parallel')
            logger.info('node_parallel:{}'.format(node_parallel))
            node_list = v.get('node')
            bo = CreateNodePiplineBaseObject(suite_app, node_list, int(node_parallel), exec_id)
            if int(node_parallel) == NodeParallelStrategy.SERIAL_STRATEGY.strategy_code:
                logger.info('环境并行，节点串行')
                strategy = NodeSerialStrategy()
            elif int(node_parallel) == NodeParallelStrategy.AVERAGE_STRATEGY.strategy_code:
                logger.info('环境并行，节点均分并行')
                strategy = NodeAverageStrategy()
            elif int(node_parallel) == NodeParallelStrategy.ONE_N_STRATEGY.strategy_code:
                logger.info('环境并行，节点1+N并行')
                strategy = NodeOneNStrategy()
            else:
                raise Exception('不支持的环境策略:{}'.format(node_parallel))
            context.set_strategy(strategy)
            context.set_data_bo(bo)
            suite_code_stage_dict = context.do_strategy()
            for k, v in suite_code_stage_dict.items():
                new_stage_groovy = suite_code_stage.get(k, '') + " \n " + v
                suite_code_stage[k] = new_stage_groovy

        parallel_stage_template = "\nstage('{publish_stage}-Part{sequence}') {{\nparallel{{{part_stage}}}\n}}"
        serial_stage_groovy = ""
        for k, v in suite_code_stage.items():
            serial_stage_groovy += parallel_stage_template.format(publish_stage=publish_stage, sequence=k, part_stage=v)
        if serial_stage_groovy:
            suite_stage = "stages {\n" + serial_stage_groovy + "\n}"
            publish_stage_groovy += "\nstage('{}') {{\n failFast false \n{}\n}}\n".format(publish_stage, suite_stage)

        return publish_stage_groovy
