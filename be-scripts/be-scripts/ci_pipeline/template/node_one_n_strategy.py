from ci_pipeline.template.node_pipline_context import CreateNodePiplineStrategy, CreateNodePiplineBaseObject
from ci_pipeline.template.group_publish_pipeline_constant import get_node_three_operate_str


class NodeOneNStrategy(CreateNodePiplineStrategy):
    # 节点1+N并行
    def create_node_pipeline_groovy(self, data_bo: CreateNodePiplineBaseObject):
        suite_app = data_bo.suite_app
        node_data = data_bo.node_data
        exec_id = data_bo.exec_id
        part1_stage = ''
        part2_stage = ''
        stage_groovy_dict = {}
        for index, ip in enumerate(node_data.keys()):
            node_three_operate_str = get_node_three_operate_str(ip, exec_id)
            node_stages_str = "stages {{\n{}\n}}".format(node_three_operate_str)
            suite_stage = "\nstage('{}') {{\n{}\n}}\n".format(suite_app + '-' + str(index),
                                                              node_stages_str)
            if index == 0:
                part1_stage += suite_stage
            else:
                part2_stage += suite_stage
        stage_groovy_dict[1] = part1_stage
        if part2_stage:
            stage_groovy_dict[2] = part2_stage
        return stage_groovy_dict
