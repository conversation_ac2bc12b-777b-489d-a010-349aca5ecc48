import random

from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos
from settings import logger
from test_publish_aio.test_suite_init_constants import TypeEnum


class PublishPipelineTemplate:
    script_path = "${BEP}/ci_pipeline/test_env_publish/test_env_publish_main.py"
    node_stage = """stage('{stage_name}') {{
                    when{{
                        expression  {{analysis_flag == true}}
                    }}
                    steps {{
                        {stage_name}()
                    }}
                }}"""

    note_stage_method = """
        void {stage_name}() {{
            sh 'python3.x {script_path} '+cache_data_path+' {node_name} {app_name} {br_name} {cache_data_code} '+suite_code+' '+is_mock_agent+' '+flagPath+' '+dump_bis_code+' '+db_exec_type
        }}
    """

    node_dict = {"env_check_per": "env_check",
                 "env_check_per_first_time": "env_check_first",
                 "pull_product": "pull_image_repo",
                 "pull_git_product": "pull_repo",
                 "scp_package": "push_repo",
                 "pull_config": "pull_config",
                 "replace_config": "replace_zeus_config",
                 "scp_config": "push_config",
                 "get_start_file": "pull_start_file",
                 "prepare_start_file": "prepare_start_file",
                 "publish_start_file": "push_start_file",
                 "prepare_starting_agent_start_file": "prepare_starting_agent_start_file",
                 "prepare_running_agent_start_file": "prepare_running_agent_start_file",

                 "agent_pull": "pull_agent_repo",
                 "agent_push": "push_agent_repo",

                 "db_init": "restore_db",
                 "db_deploy": "publish_sql",

                 "restart_app": "restart_app",
                 "restart_app_again": "restart_app_again",
                 "check_result": "check_result",
                 "check_result_again": "check_result_again",
                 "sys_test": "run_testset",

                 "BEFORE_VM_STOP": "stop_vm",
                 "BEFORE_VM_BASE": "baseline_vm",

                 "start_wait": "wait_after_start"
                 }

    publish_pipeline_template = """
        stage("Test publish"){{
            when{{
                expression  {{analysis_flag == true}}
            }}
            stages {{
                {env_check_per}
                {BEFORE_VM_STOP}
                {BEFORE_VM_BASE}
                stage('Preparation stage'){{
                    {pre_template}
                }}
                {restart_app}
                {check_result}
            }}
        }}   
    """

    publish_pipeline_docker_template = """
        stage("Test publish"){{
            when{{
                expression  {{analysis_flag == true}}
            }}
            stages {{
                {env_check_per}
                stage('Preparation stage'){{
                    {pre_template}
                }}
                {prepare_running_agent_start_file}
                {restart_app}
                {check_result}
            }}
        }}   
    """

    publish_pipeline_template_no_vm = """
        stage("Test publish"){{
            when{{
                expression  {{analysis_flag == true}}
            }}
            stages {{
                {env_check_per}
                stage('Preparation stage'){{
                    {pre_template}
                }}
                {restart_app}
                {check_result}
            }}
        }}   
    """

    publish_pipeline_template_only_production = """
        stage("Test publish"){{
            when{{
                expression  {{analysis_flag == true}}
            }}
            stages {{
                {env_check_per}
                {BEFORE_VM_STOP}
                {BEFORE_VM_BASE}
                {pre_template}
                {restart_app}
                {check_result}
            }}
        }}   
        """

    app_template = """stage('1-app') {{
                            when{{
                                expression  {{analysis_flag == true}}
                            }}
                            stages {{
                                {pull_product}
                                {scp_package}
                            }}
                        }}"""

    app_mock_template = """stage('1-app') {{
                            when{{
                                expression  {{analysis_flag == true}}
                            }}
                            stages {{
                                {scp_mock_package}
                            }}
                        }} """

    config_template = """stage('2-config') {{
                            when{{
                                expression  {{analysis_flag == true}}
                            }}
                            stages {{
                                {pull_config}
                                {replace_config}
                                {scp_config}
                            }}
                        }}"""

    start_script_template = """stage('3-start_script') {{
                                when{{
                                    expression  {{analysis_flag == true}}
                                }}
                                stages {{
                                    {get_start_file}
                                    {prepare_start_file}
                                    {publish_start_file}
                                }}
                            }}"""

    agent_template = """stage('4-agent') {{
                                    when{{
                                        expression  {{analysis_flag == true}}
                                    }}
                                    stages {{
                                        {agent_pull}
                                        {agent_push}
                                    }}
                                }}"""

    db_template = """stage('5-db_handle') {{
                                    when{{
                                        expression  {{analysis_flag == true}}
                                    }}
                                    stages {{
                                        {db_init}
                                        {db_deploy}
                                    }}
                                }}"""

    pre_template = """    
     parallel {              
              
            }
    """

    running_agent_pre_template_for_docker = """parallel {{
            {app_template}
            {config_template}
            {start_script_template}
            {agent_template}
            {db_template}
        }}"""

    no_agent_pre_template_for_docker = """parallel {{
        {app_template}
        {config_template}
        {start_script_template}
        {db_template}
    }}"""
    no_zeus_pre_template_for_docker = """parallel {{
        {app_template}
        {start_script_template}
        {mock_agent_template}
        {db_template}
    }}"""
    no_zeus_no_agent_pre_template_for_docker = """parallel {{
        {app_template}
        {start_script_template}
        {db_template}
    }}"""
    zeus_for_vm = """parallel {{
        {app_template}
        {config_template}
    }}"""

    def __init__(self, workspace, app_name, br_name, suite_code, need_mock, is_mock_agent=None, dump_bis_code=None):
        self.app_name = app_name
        self.br_name = br_name
        self.suite_code = suite_code
        self.need_mock = need_mock
        self.is_mock_agent = is_mock_agent
        self.dump_bis_code = dump_bis_code
        app_dict_list = [{app_name: br_name}]
        ptr = PublishTestRepos(self.suite_code,
                               workspace,
                               type_enum=TypeEnum.BUILD,
                               br_name=self.br_name,
                               app_dict_list=app_dict_list)
        for row in ptr:
            logger.info(row)
            self.env_publish_info = row
        self.cache_data_code = ptr.cache_data_code

    def set_up_pipeline(self, ref_method_list):
        # 生产各个节点
        stage_dict = self.product_stage(ref_method_list)

        if self.env_publish_info.deploy_type:
            check_result = stage_dict["check_result"]
        else:
            check_result = ""
        start_script_template = self.start_script_template.format(get_start_file=stage_dict["get_start_file"],
                                                                  prepare_start_file=stage_dict["prepare_start_file"],
                                                                  publish_start_file=stage_dict["publish_start_file"])

        app_template = self.app_template.format(pull_product=stage_dict["pull_product"],
                                                scp_package=stage_dict["scp_package"])

        agent_template = self.agent_template.format(agent_pull=stage_dict["agent_pull"],
                                                    agent_push=stage_dict["agent_push"])

        db_template = self.db_template.format(db_init=stage_dict["db_init"],
                                              db_deploy=stage_dict["db_deploy"])

        agent_pipeline_dict = {"start_script_template": start_script_template,
                               "app_template": app_template, "agent_template": agent_template,
                               "check_result": check_result,
                               "db_template": db_template}
        # 接入宙斯的应用需要替换外移文件
        zeus_type = self.env_publish_info.selected_zeus_type \
            if self.env_publish_info.selected_zeus_type or self.env_publish_info.selected_zeus_type == 0 \
            else self.env_publish_info.zeus_type
        if zeus_type == 1:
            config_template = self.config_template.format(pull_config=stage_dict["pull_config"],
                                                          replace_config=stage_dict["replace_config"],
                                                          scp_config=stage_dict["scp_config"])
            if self.env_publish_info.deploy_type == 2:
                agent_pipeline_dict.update({"config_template": config_template})
                logger.info("agent_pipeline_dict===={}".format(agent_pipeline_dict))
                publish_pipeline_template = self.get_docker_agent_pipeline(agent_pipeline_dict, ref_method_list)
                return publish_pipeline_template
            pre_template = self.zeus_for_vm.format(app_template=app_template, config_template=config_template)
            publish_pipeline_template = self.publish_pipeline_template.format(
                env_check_per=stage_dict["env_check_per"],
                BEFORE_VM_STOP=stage_dict["BEFORE_VM_STOP"],
                BEFORE_VM_BASE=stage_dict["BEFORE_VM_BASE"],
                pre_template=pre_template,
                restart_app=stage_dict["restart_app"],
                check_result=check_result)
        else:
            if self.env_publish_info.deploy_type == 2:
                logger.info("agent_pipeline_dict===={}".format(agent_pipeline_dict))
                publish_pipeline_template = self.get_docker_agent_pipeline(agent_pipeline_dict, ref_method_list)
                return publish_pipeline_template
            else:
                publish_pipeline_template = self.publish_pipeline_template_only_production.format(
                    env_check_per=stage_dict["env_check_per"],
                    BEFORE_VM_STOP=stage_dict["BEFORE_VM_STOP"],
                    BEFORE_VM_BASE=stage_dict["BEFORE_VM_BASE"],
                    pre_template=app_template,
                    restart_app=stage_dict["restart_app"],
                    check_result=check_result)
        return publish_pipeline_template

    def get_docker_agent_pipeline(self, agent_pipeline_dict, ref_method_list):
        stage_dict = self.product_stage(ref_method_list)
        app_template = agent_pipeline_dict.get("app_template")
        logger.info("app_template===={}".format(app_template))
        config_template = agent_pipeline_dict.get("config_template") if agent_pipeline_dict.get(
            "config_template") else ""
        agent_template = agent_pipeline_dict.get("agent_template")
        logger.info("agent_template===={}".format(agent_template))
        check_result = agent_pipeline_dict.get("check_result")
        db_template = agent_pipeline_dict.get("db_template")
        start_script_template = self.start_script_template.format(
            get_start_file=stage_dict["get_start_file"],
            prepare_start_file=stage_dict["prepare_running_agent_start_file"],
            publish_start_file=stage_dict["publish_start_file"])
        pre_template = self.running_agent_pre_template_for_docker.format(
            app_template=app_template, config_template=config_template,
            start_script_template=start_script_template,
            agent_template=agent_template, db_template=db_template)
        publish_pipeline_template = self.publish_pipeline_template_no_vm.format(
            env_check_per=stage_dict["env_check_per"],
            pre_template=pre_template,
            restart_app=stage_dict["restart_app"],
            check_result=check_result)
        return publish_pipeline_template

    def product_stage(self, ref_method_list):
        stage_dict = {}

        for node_name in self.node_dict:
            stage_name = "{}{}{}".format(self.node_dict[node_name], random.randint(100, 999), self.cache_data_code[-4:])
            stage_dict[node_name] = self.node_stage.format(stage_name=stage_name)

            method_str = self.note_stage_method.format(stage_name=stage_name,
                                                       script_path=self.script_path,
                                                       node_name=node_name,
                                                       app_name=self.app_name,
                                                       br_name=self.br_name,
                                                       suite_code=self.suite_code,
                                                       cache_data_code=self.cache_data_code)

            if method_str not in ref_method_list:
                ref_method_list.append(method_str)

        return stage_dict


if __name__ == "__main__":
    # ppt = PublishPipelineTemplate("D:\\test", "crm-td-server", "1.2.5", "it31", True)
    # ref_method_list = []
    # ppt.set_up_pipeline(ref_method_list)
    url = "124345_1212_23143"
    print(url[-4:])
