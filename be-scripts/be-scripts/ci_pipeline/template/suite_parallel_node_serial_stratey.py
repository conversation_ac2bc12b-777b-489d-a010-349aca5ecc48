from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineStrategy, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.publish_pipeline_constant import PublishPipelineConstant


class SuiteParallelNodeSerialStrategy(CreatePublishPipelineStrategy):
    # 环境并行，节点串行
    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        publish_stage = data_bo.publish_stage
        publish_data = data_bo.publish_node_data
        first_stage = ''
        suite_stage = ''
        for suite_code, v in publish_data.items():
            if suite_code in ['parallel', 'node_parallel']:
                continue
            same_suite_code_stage_str = ''
            for ip in v:
                CONFIG_UPDATE = PublishPipelineConstant.CONFIG_UPDATE.format(ip.replace('.', '_'), ip.replace('.', '_'),
                                                          ip.replace('.', '_'), ip.replace('.', '_'))
                PUBLISH = PublishPipelineConstant.PUBLISH.format(ip.replace('.', '_'), ip.replace('.', '_'), ip.replace('.', '_'))
                SERVICE_VERIFY = PublishPipelineConstant.SERVICE_VERIFY.format(ip.replace('.', '_'), ip.replace('.', '_'),
                                                            ip.replace('.', '_'))
                same_suite_code_stage_str += "{}\n{}\n{}\n".format(CONFIG_UPDATE, PUBLISH, SERVICE_VERIFY)
            same_suite_code_stage_str = "stages {{\n{}\n}}".format(same_suite_code_stage_str)
            suite_stage += "\nstage('{}') {{\n{}\n}}\n".format(suite_code, same_suite_code_stage_str)
        parallel_stage = "parallel {{\n{}\n}}".format(suite_stage)
        first_stage += "\nstage('{}') {{\n{}\n}}\n".format(publish_stage, parallel_stage)
        return first_stage

