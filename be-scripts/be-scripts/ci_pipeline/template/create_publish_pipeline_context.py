from abc import ABCMeta, abstractmethod


class CreatePublishPipelineBaseObject:

    def __init__(self, publish_stage=None, publish_node_data=None, exec_id=None):
        self.publish_stage = publish_stage
        self.publish_node_data = publish_node_data
        self.exec_id = exec_id


class CreatePublishPipelineStrategy(metaclass=ABCMeta):

    @abstractmethod
    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        pass


class CreatePublishPipelineContext:
    def __init__(self, strategy=None, createPublishPipelineBaseObject: CreatePublishPipelineBaseObject=None):
        self.strategy = strategy
        self.data_bo = createPublishPipelineBaseObject

    def set_strategy(self, strategy):
        self.strategy = strategy

    def set_data_bo(self, data_bo):
        self.data_bo = data_bo

    def do_strategy(self):
        return self.strategy.create_pipeline_groovy(self.data_bo)
