from ci_pipeline.template.create_publish_pipeline_context import CreatePublishPipelineStrategy, \
    CreatePublishPipelineBaseObject
from ci_pipeline.template.node_parallel_reboot_strategy import NodeParallelRebootStrategy
from ci_pipeline.template.node_pipline_context import CreateNode<PERSON><PERSON>lineBaseObject, CreateNodePipelineContext
from ci_pipeline.template.publish_pipeline_constant import SuiteParallelStrategy


class StageSerialStrategy(CreatePublishPipelineStrategy):

    def create_pipeline_groovy(self, data_bo: CreatePublishPipelineBaseObject):
        publish_stage = data_bo.publish_stage
        publish_data = data_bo.publish_node_data
        exec_id = data_bo.exec_id
        publish_stage_groovy = ''
        context = CreateNodePipelineContext()
        serial_stage_groovy = ""
        for app_key, v in publish_data.items():
            strategy = NodeParallelRebootStrategy()
            bo = CreateNodePiplineBaseObject(app_key, [v], SuiteParallelStrategy.PARALLEL_STRATEGY.strategy_code, exec_id)
            context.set_strategy(strategy)
            context.set_data_bo(bo)
            app_node_groovy = context.do_strategy()
            stages_step = "stages {\n" + app_node_groovy + "\n}"
            stage_groovy = "\nstage('{}') {{\n {}\n }} \n".format(app_key, stages_step)
            serial_stage_groovy += stage_groovy + "\n"

        if serial_stage_groovy:
            parallel_groovy = "\nparallel {\n" + serial_stage_groovy + "\n}"
            stage2_groovy = "\nstage('{}') {{\n {}\n }} \n".format(publish_stage, parallel_groovy)
            stages_groovy = "\nstages {\n" + stage2_groovy + "\n}"
            publish_stage_groovy += "\nstage('{}') {{\n failFast false \n {}\n }} \n".format(publish_stage,
                                                                                             stages_groovy)

        return publish_stage_groovy

