from settings import AI_HELP


class GroupPublishPipelineConstant:
    DATA_PARAM = '''string(name: 'DATA_PARAM', defaultValue: '{}', description: 'handle data param')'''
    CONFIG_UPDATE_PARAM = '''string(name: 'CONFIG_UPDATE_PARAM_{}', defaultValue: '{}', description: 'Parameter for the  config update')'''
    PUBLISH_PARAM = '''string(name: 'PUBLISH_PARAM_{}', defaultValue: '{}', description: 'Parameter for the  publish update')'''
    SERVICE_VERIFY_PARAM = '''string(name: 'SERVICE_VERIFY_PARAM_{}', defaultValue: '{}', description: 'Parameter for the verify update')'''

    parameter = "parameters {{{}}}"

    CONFIG_UPDATE = '''
                stage("配置更新 {ip}") {{
                    steps {{
                        script {{
                            try {{
                                sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/group_publish_jenkins_pipeline_main.py run_publish_step_main {job_name} {build_id} CONFIG_UPDATE_PARAM_{ip} {exec_id}'
                            }} catch(Exception e) {{
                                logContent = "这一步是在应用配置更新时，输出调用salt服务器的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下："
                                verifyLogs(logContent, "{config_prompt}")
                                throw e
                            }}
                        }}
                    }}
                }}'''

    PUBLISH = '''stage('发布 {ip}') {{
                   steps {{
                       script {{
                            try {{
                                sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/group_publish_jenkins_pipeline_main.py run_publish_step_main {job_name} {build_id} PUBLISH_PARAM_{ip} {exec_id}'
                            }} catch(Exception e) {{
                                logContent = "这一步是在应用发布时，输出调用salt服务器的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下："
                                verifyLogs(logContent, "{publish_prompt}")
                                throw e
                            }}
                        }}
                   }}
               }}'''

    SERVICE_VERIFY = '''stage('服务验证 {ip}') {{
                    steps {{
                        script {{
                            try {{
                                sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/group_publish_jenkins_pipeline_main.py run_publish_step_main {job_name} {build_id} SERVICE_VERIFY_PARAM_{ip} {exec_id}'
                            }} catch(Exception e) {{
                                logContent = "这一步是在应用启动时，输出spring actuator打印的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下："
                                verifyLogs(logContent, "{verify_prompt}")
                                throw e
                            }}
                        }}
                    }}
                }}'''

    NODE_REBOOT = '''stage('{node_key}') {{
                        steps {{
                            script {{
                                try {{
                                    sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/group_publish_jenkins_pipeline_main.py run_node_reboot {job_name} {build_id} {node_key} {exec_id}'
                                }} catch(Exception e) {{
                                    logContent = "这一步是在节点重启时，输出spring actuator打印的日志，来判断是否有问题。日志中有没有报出问题？主要是什么问题？报文如下："
                                    verifyLogs(logContent, "{verify_prompt}")
                                    throw e
                                }}
                            }}
                        }}
                    }}'''


def get_node_three_operate_str(ip, exec_id):
    node_three_operate_str = ''
    CONFIG_UPDATE = GroupPublishPipelineConstant.CONFIG_UPDATE.format(ip=ip.replace('.', '_'), job_name="${JOB_NAME}",
                                                                      build_id="${BUILD_ID}", exec_id=exec_id, config_prompt=AI_HELP['config_prompt'])
    PUBLISH = GroupPublishPipelineConstant.PUBLISH.format(ip=ip.replace('.', '_'), job_name="${JOB_NAME}",
                                                          build_id="${BUILD_ID}", exec_id=exec_id, publish_prompt=AI_HELP['publish_prompt'])
    SERVICE_VERIFY = GroupPublishPipelineConstant.SERVICE_VERIFY.format(ip=ip.replace('.', '_'), job_name="${JOB_NAME}",
                                                                        build_id="${BUILD_ID}", exec_id=exec_id, verify_prompt=AI_HELP['verify_prompt'])
    node_three_operate_str += "{}\n{}\n{}\n".format(CONFIG_UPDATE, PUBLISH, SERVICE_VERIFY)
    return node_three_operate_str

def get_node_reboot_operate_str(node_key, exec_id):
    node_reboot_str = GroupPublishPipelineConstant.NODE_REBOOT.format(node_key=node_key, job_name="${JOB_NAME}",
                                                                      build_id="${BUILD_ID}", exec_id=exec_id, verify_prompt="重启")
    node_reboot_operate_str = "{}\n".format(node_reboot_str)
    return node_reboot_operate_str