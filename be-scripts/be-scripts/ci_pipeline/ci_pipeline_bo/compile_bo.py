

class CompileBo:
    def __init__(self, builder):
        self.__flag_file_dir = builder.flag_file_dir
        self.__iteration_id = builder.iteration_id
        self.__app_name = builder.app_name
        self.__pom_path = builder.pom_path
        self.__module_name = builder.module_name
        self.__sid = builder.sid

    @property
    def flag_file_dir(self):
        return self.__flag_file_dir

    @property
    def iteration_id(self):
        return self.__iteration_id

    @property
    def app_name(self):
        return self.__app_name

    @property
    def pom_path(self):
        return self.__pom_path

    @property
    def module_name(self):
        return self.__module_name

    @property
    def sid(self):
        return self.__sid

    class Builder:
        flag_file_dir: str = ""
        iteration_id: str = ""
        app_name: str = ""
        pom_path: str = ""
        module_name: str = ""
        sid: int = None

        def set_flag_file_dir(self, flag_file_dir: str):
            self.flag_file_dir = flag_file_dir
            return self

        def set_iteration_id(self, iteration_id: str):
            self.iteration_id = iteration_id
            return self

        def set_app_name(self, app_name: str):
            self.app_name = app_name
            return self

        def set_pom_path(self, pom_path: str):
            self.pom_path = pom_path
            return self

        def set_module_name(self, module_name: bool):
            self.module_name = module_name
            return self

        def set_sid(self, sid: int):
            self.sid = sid
            return self

        def verify_basic_property(self):
            if self.app_name == "":
                raise Exception("app_name 不可为空")
            if self.iteration_id == "":
                raise Exception("iteration_id 不可为空")
            if self.sid is None:
                raise Exception("sid 不可为空")
            if self.flag_file_dir == "":
                raise Exception("flag_file_dir 不可为空")

        def build_product_bo(self):
            self.verify_basic_property()
            return CompileBo(self)

        def build_publish_bo(self):
            if self.sid is None:
                raise Exception("sid 不可为空")
            return CompileBo(self)

        def build_bo(self):
            self.verify_basic_property()
            if self.pom_path == "":
                raise Exception("pom_path 不可为空")
            if self.module_name == "":
                raise Exception("module_name 不可为空")
            return CompileBo(self)


