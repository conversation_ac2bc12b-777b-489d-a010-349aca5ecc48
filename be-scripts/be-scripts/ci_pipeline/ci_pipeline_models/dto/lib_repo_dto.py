import datetime


class LibRepoDto:
    def __init__(self):
        self.module_name = ""
        self.lib_repo_url = ""
        self.lib_repo_branch = ""
        self.lib_repo_version = ""
        self.lib_repo_size = datetime.datetime.now()
        self.create_time = datetime.datetime.now()
        self.iteration_id = ""
        self.lib_repo_version_log = ""
        self.suite_code = ""
        self.package_stamp = ""

    def set_module_name(self, module_name):
        self.module_name = module_name

    def get_module_name(self):
        return self.module_name

    def set_lib_repo_url(self, lib_repo_url):
        self.lib_repo_url = lib_repo_url

    def get_lib_repo_url(self):
        return self.lib_repo_url

    def set_lib_repo_branch(self, lib_repo_branch):
        self.lib_repo_branch = lib_repo_branch

    def get_lib_repo_branch(self):
        return self.lib_repo_branch

    def set_lib_repo_version(self, lib_repo_version):
        self.lib_repo_version = lib_repo_version

    def get_lib_repo_version(self):
        return self.lib_repo_version

    def set_lib_repo_size(self, lib_repo_size):
        self.lib_repo_size = lib_repo_size

    def get_lib_repo_size(self):
        return self.lib_repo_size

    def set_create_time(self, create_time):
        self.create_time = create_time

    def get_create_time(self):
        return self.create_time

    def set_iteration_id(self, iteration_id):
        self.iteration_id = iteration_id

    def get_iteration_id(self):
        return self.iteration_id

    def set_lib_repo_version_log(self, lib_repo_version_log):
        self.lib_repo_version_log = lib_repo_version_log

    def get_lib_repo_version_log(self):
        return self.lib_repo_version_log

    def set_suite_code(self, suite_code):
        self.suite_code = suite_code

    def get_suite_code(self):
        return self.suite_code

    def set_package_stamp(self, package_stamp):
        self.package_stamp = package_stamp

    def get_package_stamp(self):
        return self.package_stamp
