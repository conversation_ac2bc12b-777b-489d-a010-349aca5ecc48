from peewee import *
from sqlalchemy import Column, String, JSON, TIMESTAMP
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

from utils.public_utils import auto_str
from typing import Any
from dao.base_model import BaseModel


class IterAppInfoModel(BaseModel):
    pipeline_id = CharField(verbose_name='流水线ID', max_length=50, default=None)
    appName = CharField(verbose_name='应用名', max_length=100)
    sys_status_in_choice = (
        ('测试中', '测试中'), ('已推送制品库', '已推送制品库'), ('发布私服', '发布私服'), ('计划上线', '计划上线'),
        ('仿真通过', '仿真通过'), ('已上线', '已上线'), ('已归档', '已归档')
    )
    sys_status = CharField(verbose_name='应用状态', max_length=20, choices=sys_status_in_choice, default='测试中')
    sys_duedate = CharField(verbose_name='发布日期', max_length=50, null=True, default=None)
    proposer = CharField(verbose_name='申请人', max_length=50, null=True, default="")
    simulate_identifier = CharField(verbose_name='仿真验证人', max_length=20, null=True, default=None)
    config_content = CharField(verbose_name='配置文件', max_length=100, null=True, default=None)
    pom_path = CharField(verbose_name='pom文件路径', max_length=100, default=None)
    git_last_version = CharField(verbose_name='最后一次编译版本', max_length=50, null=True, default=None)
    jdkVersion = CharField(verbose_name='指定jdk版本', max_length=20, null=True, default="",
                           choices=(('1.6', '1.6'), ('1.7', '1.7'), ('1.8', '1.8'),))
    git_repo_version = CharField(verbose_name='分支制品版本', max_length=50, null=True, default=None)
    git_repos_time = DateTimeField(null=True, verbose_name='制品时间')
    create_date = DateTimeField(verbose_name='创建时间')
    package_type = CharField(max_length=10, verbose_name='应用类型')
    git_path = TextField(verbose_name='git路径')
    user_name = CharField(verbose_name='用户名', max_length=100)
    need_online = BooleanField(verbose_name='jar是否需要上线')
    build_cmd = CharField(verbose_name='编译命令', max_length=128)
    zeus_type = IntegerField(verbose_name='配置类型')
    git_last_update = DateTimeField(verbose_name='git最后更新时间')
    merged_master = IntegerField(verbose_name='是否合并master')

    class Meta:
        db_table = "iter_mgt_iter_app_info"
        verbose_name = '迭代应用信息表'


class BranchesModel(BaseModel):
    pipeline_id = CharField(verbose_name='迭代版本', max_length=500)
    br_name = CharField(verbose_name='分支版本', max_length=500)
    project_group = CharField(verbose_name='代码分组', max_length=50)
    br_style = CharField(verbose_name='分支类型', max_length=20)
    br_start_date = CharField(verbose_name='分支开始时间', max_length=50)
    br_end_date = CharField(verbose_name='预期上线时间', max_length=50)
    duedate = CharField(verbose_name='预期上线时间', max_length=50)
    br_status = CharField(verbose_name='迭代状态', max_length=20)
    is_new = IntegerField(verbose_name='是否新创建分支')
    samulation_mail_status = IntegerField(verbose_name='邮件状态')
    description = CharField(verbose_name='功能描述·', max_length=200)
    releaseNotic = CharField(verbose_name='注意事项', max_length=200)
    file_ccms_config = CharField(verbose_name='CCMS配置文件名称', max_length=1000)
    tapd_id = CharField(verbose_name='tpad id', max_length=50)
    archive_msg = TextField(verbose_name="归档信息")
    create_date = DateTimeField(verbose_name='创建时间')
    test_end_date = DateTimeField(verbose_name='测试结束时间')
    update_test_end_date_user = CharField(verbose_name='更新测试结束操作人', max_length=50)

    class Meta:
        db_table = 'iter_mgt_iter_info'
        verbose_name = '分支信息表'


class EnvMgtNodeBindDynamic(BaseModel):
    bind_id = BigIntegerField(verbose_name='绑定表ID')
    bind_dynamic_desc = CharField(verbose_name='动态绑定说明', max_length=255)
    node_docker_service_ip = CharField(verbose_name='k8s容器中的service_ip', max_length=50)
    node_docker_dubbo_port = IntegerField(verbose_name='k8s容器中的dubbo端口')
    node_docker_service_hosts = CharField(verbose_name='k8s容器的hosts', max_length=100)
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')

    class Meta:
        db_table = 'env_mgt_node_bind_dynamic'
        verbose_name = '环境绑定动态表'


class IterWhitelistApp(BaseModel):
    wl_group_id = IntegerField(verbose_name='组级别白名单ID')
    wl_type = CharField(verbose_name='白名单类型：1-应用、2-迭代', max_length=20)
    wl_name = CharField(verbose_name='白名主体：迭代名或者应用名', max_length=100)
    wl_pass = IntegerField(verbose_name='白名单是否通过')
    wl_value = IntegerField(verbose_name='白名单阈值')
    wl_opt_user = CharField(verbose_name='白名单操作人', max_length=20)
    wl_opt_time = DateTimeField(verbose_name='白名单操作时间')
    wl_desc = CharField(verbose_name='白名单描述', max_length=255)
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length=20)
    update_time = DateTimeField(verbose_name='更新时间')
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'iter_whitelist_app'
        verbose_name = '迭代白名单应用表'


class IterWhitelistGroup(BaseModel):
    wl_group_name = CharField(verbose_name='白名单git分组名称', max_length=100)
    wl_switch_id = IntegerField(verbose_name='白名单开关ID')
    wl_group_pass = IntegerField(verbose_name='白名单组级别是否放行')
    wl_group_value = IntegerField(verbose_name='白名单组级别阈值')
    wl_group_opt_user = CharField(verbose_name='白名单组级别操作人', max_length=20)
    wl_group_opt_time = DateTimeField(verbose_name='白名单组级别操作时间')
    wl_group_desc = CharField(verbose_name='白名单组级别描述', max_length=255)
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='更新人', max_length=20)
    update_time = DateTimeField(verbose_name='更新时间')
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'iter_whitelist_group'
        verbose_name = '迭代白名单分组表'




Base = declarative_base()
metadata = Base.metadata


@auto_str
class TaskMgtServiceResults(Base):
    __tablename__ = 'task_mgt_service_results'

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True)
    business_name = Column(String(50), nullable=False)
    status = Column(String(10), nullable=False)
    result_json = Column(JSON, comment='规则类型 SHARDING')
    end_at = Column(TIMESTAMP, comment='更新时间')
