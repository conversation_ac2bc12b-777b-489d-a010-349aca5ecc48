from peewee import fn,JO<PERSON>
from datetime import datetime
import copy
from ci_pipeline.ci_pipeline_models.iter_models import IterAppInfoModel, BranchesModel
from app_mgt.models import MiniVersionUpgradeModule
from settings import logger


class UpgradeMiniVersionModuleInfo:
    __iter_app_info_dto = IterAppInfoModel
    __branches_dto = BranchesModel
    __mini_version_upgrade_module_dto = MiniVersionUpgradeModule

    def __init__(self):
        pass

    def _get_mini_version_upgrade_module(self):
        mini_upgrade_module_list = []
        for row in self.__mini_version_upgrade_module_dto.select(
            self.__mini_version_upgrade_module_dto.module_name):
            mini_upgrade_module_list.append(row.module_name)
        return mini_upgrade_module_list

    def get_module_upgrade_info(self):
        mini_upgrade_module_list = self._get_mini_version_upgrade_module()
        logger.info(mini_upgrade_module_list)
        query = self.__iter_app_info_dto\
            .select(self.__iter_app_info_dto.appName, self.__branches_dto.br_name, self.__branches_dto.br_end_date)\
            .join(self.__branches_dto, JOIN.LEFT_OUTER, on=(
                self.__iter_app_info_dto.pipeline_id == self.__branches_dto.pipeline_id))\
            .where(self.__branches_dto.br_status == "close", self.__iter_app_info_dto.appName.in_(mini_upgrade_module_list))
        logger.info(query.sql())
        module_upgrade_info_dict = {}
        for row in query.objects():
            # logger.info(row.appName)
            # logger.info(row.br_name)
            # logger.info(row.br_end_date)
            if row.appName in module_upgrade_info_dict:
                br_name_dict = copy.deepcopy(module_upgrade_info_dict[row.appName])
                is_add_br_name = False
                for br_name in module_upgrade_info_dict[row.appName]:
                    # 版本号首位相同，取归档日期大的版本
                    if row.br_name.split(".")[0] == br_name.split(".")[0]:
                        old_date = datetime.strptime(module_upgrade_info_dict[row.appName][br_name], "%Y年%m月%d日 %H时%M分")
                        new_date = datetime.strptime(row.br_end_date,  "%Y年%m月%d日 %H时%M分")
                        if new_date > old_date:
                            br_name_dict.pop(br_name)
                            br_name_dict[row.br_name] = row.br_end_date

                    else:
                        is_add_br_name = True
                if is_add_br_name:
                    br_name_dict[row.br_name] = row.br_end_date
                module_upgrade_info_dict[row.appName] = br_name_dict

            else:
                module_upgrade_info_dict[row.appName] = {row.br_name: row.br_end_date}
        logger.info(module_upgrade_info_dict)
        return module_upgrade_info_dict



if __name__ == "__main__":
    mui = UpgradeMiniVersionModuleInfo()
    module_upgrade_info_dict = mui.get_module_upgrade_info()
