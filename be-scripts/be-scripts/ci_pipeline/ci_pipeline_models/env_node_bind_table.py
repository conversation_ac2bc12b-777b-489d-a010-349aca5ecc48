import datetime
from dao.connect.mysql import DBConnectionManager
from settings import logger


class EnvNodeBindTable:
    def get_env_node_bind_id(self, module_name, node_ip=None, node_docker=None, suite_code=None):
        """
        获取节点绑定表得id
        :param module_name: 应用名
        :param node_ip: ip
        :param node_docker: docker环境标识
        :return: list [] 表示没获取到
        """
        node_bind_id_list = []
        if node_ip:
            if suite_code:
                sql = '''SELECT b.id FROM env_mgt_node_bind b LEFT JOIN env_mgt_node n ON b.node_id = n.id
                        LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
                        WHERE n.node_ip="{}" AND b.module_name = "{}" AND s.suite_code="{}"
                           '''.format(node_ip, module_name, suite_code)
            else:
                sql = '''SELECT b.id FROM env_mgt_node_bind b LEFT JOIN env_mgt_node n ON b.node_id = n.id
                        WHERE n.node_ip="{}" AND b.module_name = "{}"
                   '''.format(node_ip, module_name)
        elif node_docker:
            sql = '''SELECT b.id FROM env_mgt_node_bind b WHERE b.node_docker="{}" AND b.module_name = "{}"
                       '''.format(node_docker, module_name)
        elif suite_code:
            sql = '''SELECT b.id FROM env_mgt_node_bind b LEFT JOIN env_mgt_suite n ON b.suite_id = n.id
             WHERE n.suite_code="{}" AND b.module_name = "{}"
                        '''.format(suite_code, module_name)
        else:
            raise IOError("node_ip 和 node_docker 至少有一个变量")
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            for row in db.cur.fetchall():
                node_bind_id_list.append(row["id"])

        return node_bind_id_list

    def update_node_bind_lib_repo_info(self, node_bind_id_list, lib_repo_info_id):
        """
        更新节点绑定表中 制品得信息
        :param node_bind_id_list: 节点绑定表id
        :param lib_repo_info_id:  lib_repo_info
        :return:
        """
        if len(node_bind_id_list) == 1:
            sql = '''update env_mgt_node_bind set lib_repo_info_id = {},node_lib_repo_update_time="{}" where 
              id = {}'''.format(lib_repo_info_id, datetime.datetime.now(), node_bind_id_list[0])
        elif len(node_bind_id_list) > 1:
            sql = '''update env_mgt_node_bind set lib_repo_info_id = {},node_lib_repo_update_time="{}" where 
                         id in {}'''.format(lib_repo_info_id, datetime.datetime.now(), tuple(node_bind_id_list))
        else:
            raise IOError("未获取到应用绑定环境的信息")
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            db.connection.commit()

    def update_node_bind_node_dock_ip(self, node_bind_id_list, node_docker_ip):
        """
        更新节点绑定表中的容器ip
        :param node_bind_id_list: 节点绑定表id
        :param node_docker_ip:  node_docker_ip
        :return:
        """
        if len(node_bind_id_list) == 1:
            sql = '''update env_mgt_node_bind set node_docker_ip = "{}" where id = {} '''.format(node_docker_ip,
                                                                                                 node_bind_id_list[0])
        elif len(node_bind_id_list) > 1:
            sql = '''update env_mgt_node_bind set node_docker_ip = "{}" where id in {} '''.format(node_docker_ip,
                                                                                                  tuple(
                                                                                                      node_bind_id_list))
        else:
            raise IOError("未获取到应用绑定环境的信息")
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            db.connection.commit()

    def get_bind_id_by_app_env(self, app_name, suite_code):
        sql = '''
                select id from env_mgt_node_bind where 										
                LOWER(module_name) = "{app_name}" 
                and suite_id = (select id from env_mgt_suite s where s.suite_code = '{suite_code}') 
                and node_docker = '{suite_code}'
            '''.format(app_name=app_name, suite_code=suite_code)
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            for row in db.cur.fetchall():
                return row["id"]

    def update_node_bind_service_ip_port(self, app_name, suite_code, ip, port):
        """暂时废弃 by fwm@20221008"""
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if port:
            sql = '''
                        update env_mgt_node_bind_dynamic set node_docker_service_ip = "{ip}", 
                        node_docker_dubbo_port = "{port}",
                        update_time = "{update_time}"
                        where bind_id = (select id from env_mgt_node_bind where 										
                        LOWER(module_name) = "{app_name}" 
                        and suite_id = (select id from env_mgt_suite s where s.suite_code = '{suite_code}') 
                        and node_docker = '{suite_code}')
                    '''.format(app_name=app_name, suite_code=suite_code, ip=ip, port=port, update_time=cur_time)
        else:
            sql = '''
                    update env_mgt_node_bind_dynamic set node_docker_service_ip = "{ip}", 
                    update_time = "{update_time}"
                    where bind_id = (select id from env_mgt_node_bind where 										
                    LOWER(module_name) = "{app_name}" 
                    and suite_id = (select id from env_mgt_suite s where s.suite_code = '{suite_code}') 
                    and node_docker = '{suite_code}')
                '''.format(app_name=app_name, suite_code=suite_code, ip=ip, update_time=cur_time)
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            a = db.connection.commit()
            print(a)

    def update_node_bind_hosts(self, app_name, suite_code, hosts):
        """暂时废弃 by fwm@20221008"""

        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        sql = '''
                update env_mgt_node_bind_dynamic 
                set node_docker_service_hosts = "{hosts}", update_time = "{update_time}"
                where bind_id = (select id from env_mgt_node_bind where 										
                LOWER(module_name) = "{app_name}" 
                and suite_id = (select id from env_mgt_suite s where s.suite_code = '{suite_code}') 
                and node_docker = '{suite_code}')
            '''.format(app_name=app_name, suite_code=suite_code, hosts=hosts, update_time=cur_time)
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            db.connection.commit()