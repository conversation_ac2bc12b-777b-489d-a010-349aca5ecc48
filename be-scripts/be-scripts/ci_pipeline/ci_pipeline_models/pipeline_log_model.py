from peewee import *
from dao.base_model import BaseModel


class PipelineLogMain(BaseModel):
    (NONEXEC, RUNNING, SUCCESS, FAILURE) = ('nonexec', 'running', 'success', 'failure')
    STATUS_CHOICE = (
        (NONEXEC, '未执行'),
        (RUNNING, '执行中'),
        (SUCCESS, '执行成功'),
        (FAILURE, '执行失败')
    )

    sid = AutoField(primary_key=True, verbose_name='脚本执行ID')
    exec_jenkins = CharField(verbose_name='执行jenkins')
    exec_parameter = TextField(verbose_name='执行参数')
    start_at = DateTimeField(null=True, verbose_name='执行开始时间')
    iteration_id = CharField(verbose_name='迭代号', max_length=50)
    app_name = CharField(verbose_name='应用名称', max_length=50)
    end_at = DateTimeField(null=True, verbose_name='执行结束时间')
    status = CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    suite_name = CharField(verbose_name='环境套', max_length=50)
    update_memo = CharField(verbose_name='更新回忆', max_length=100)
    job_number = IntegerField(verbose_name='任务号')

    class Meta:
        table_name = "pipeline_log_main"


class PipelineLogMinor(BaseModel):

    STATUS_CHOICE = (
        ('running', '执行中'),
        ('success', '执行成功'),
        ('failure', '执行失败')
    )
    id = AutoField(primary_key=True, verbose_name='脚本执行ID')
    sid = IntegerField(verbose_name='脚本执行ID')
    step = CharField(max_length=50, verbose_name='执行步骤名称')
    status = CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    log = TextField(verbose_name='详细日志', null=True,)
    start_at = DateTimeField(null=True, verbose_name='执行开始时间')
    end_at = DateTimeField(null=True, verbose_name='执行结束时间')
    module_name = CharField(verbose_name='模块名', max_length=50)

    class Meta:
        db_table = 'pipeline_log_minor'
        verbose_name = '流水线步骤日志'
