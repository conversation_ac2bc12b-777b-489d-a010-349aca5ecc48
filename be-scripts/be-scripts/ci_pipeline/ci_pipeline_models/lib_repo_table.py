from dao.connect.mysql import DBConnectionManager
from settings import logger


class LibRepoTable:
    def insert_lib_repo_info(self, lib_repo_dto):
        """
        插入制品信息
        :param lib_repo_dto:
        :return:
        """
        with DBConnectionManager() as db:
            sql = '''
                    INSERT INTO product_mgt_product_info (module_name, lib_repo_url, lib_repo_branch, lib_repo_version,
                                                          lib_repo_size, iteration_id, lib_repo_version_log, suite_code,
                                                          package_stamp)
                    VALUES ("{module_name}", "{lib_repo_url}", "{lib_repo_branch}",
                     "{lib_repo_version}", "{lib_repo_size}", "{iteration_id}", "{lib_repo_version_log}", 
                     "{suite_code}", "{package_stamp}")
                  '''.format(**lib_repo_dto.__dict__)
            logger.info(sql)
            db.cur.execute(sql)
            insert_id = db.connection.insert_id()
            logger.info(insert_id)
            db.connection.commit()
        return insert_id

    def get_lib_repo_id(self, module_name, lib_repo_branch, suite_code=None):
        """
        获取制品信息
        :param module_name: 应用名
        :param lib_repo_branch: 制品分支
        :param suite_code: 环境套
        :return: num 0 表示没有
        """
        lib_repo_id = 0
        sql = 'SELECT id FROM product_mgt_product_info WHERE module_name="{}" AND lib_repo_branch="{}"'.\
            format(module_name, lib_repo_branch)
        if suite_code:
            sql = sql + ' and suite_code = {}'.format(suite_code)
        sql = sql + ' ORDER BY create_time DESC'
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            for row in db.cur.fetchall():
                return row["id"]
        return lib_repo_id



