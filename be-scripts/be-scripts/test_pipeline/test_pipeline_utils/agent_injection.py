import json
import os
from settings import TEST_SYSTEM_INTERFACE, MOCK_SYSTEM_INTERFACE, SHARDING, INTERFACE_URL
from settings import logger


class AgentInjection:
    test_system_interface = TEST_SYSTEM_INTERFACE
    agent_base_path = "/data/logs"
    mark_value = "JAVA_OPTS"
    mark_value_shard = "CLASS_PATH"
    mock_system_interface = MOCK_SYSTEM_INTERFACE
    batch_no: str = ""

    def __init__(self, file_path):
        self.file_path = file_path

    def get_mark_value(self):
        return self.mark_value

    def get_mark_value_shard(self):
        return self.mark_value_shard

    def get_file_path(self):
        return self.file_path

    def alter_start_file(self, add_params):
        """往脚本中新增一行即可"""
        mark_value = self.get_mark_value()
        file_path = self.get_file_path()
        with open(file_path, "r+") as f:
            rows = f.readlines()
            """追加的时候添加的格式为JAVA_OPTS="$JAVA_OPTS -javaagent:xxxx  20220429 by fwm"""
            add_content = '{mark_value}="${mark_value} {add_params}" \n'.format(mark_value=mark_value,
                                                                                add_params=add_params)
            logger.info("追加一行 {}".format(add_content))
            rows.insert(1, add_content)
        with open(file_path, "w+") as w:
            w.writelines(rows)
        logger.info("处理后脚本内容：")
        with open(file_path, "r+") as f:
            for line in f.readlines():
                print(line)

    def alter_start_file_for_shard(self, add_params):
        """往脚本中新增一行即可"""
        if add_params:
            file_path = self.get_file_path()
            with open(file_path, "r+") as f:
                rows = f.readlines()
                add_content = add_params+'\n'
                logger.info("追加一行 {}".format(add_content))
                rows.insert(1, add_content)
            with open(file_path, "w+") as w:
                w.writelines(rows)
            logger.info("处理后脚本内容：")
            with open(file_path, "r+") as f:
                for line in f.readlines():
                    print(line)

    def interface_collect_agent_params(self, jdk_version, app_name, suite_code, branch_name):
        """
        :param jdk_version:
        :param app_name:
        :param suite_code:
        :param branch_name:
        :return: .str
         返回加入到启动脚本中的参数， 如下：
        "-javaagent:/data/logs/howbuy-interface-scan-agent/1.8/howbuy-interface-scan-agent.jar
        ='http://192.168.222.48:8088/mring/api/importAgentReportInterfaces.do&adviser-batch-center-remote&it01&3.4.41'"
        """
        agent_abs_path = os.path.join(self.agent_base_path, self.test_system_interface["interface_agent_package_name"],
                                      jdk_version, self.test_system_interface["interface_agent_package_name"] + ".jar")
        request_url = "{base_api_url}{interface_name}&{app_name}&{suite_code}&{branch_name}".format \
            (base_api_url=self.test_system_interface["base_api_url"],
             interface_name=self.test_system_interface["interface_collect_api_name"],
             app_name=app_name,
             suite_code=suite_code,
             branch_name=branch_name)
        return "-javaagent:{agent_abs_path}='{request_url}'".format(agent_abs_path=agent_abs_path,
                                                                    request_url=request_url)

    def add_interface_collect_agent_params(self, jdk_version, app_name, suite_code, branch_name):
        agent_params = self.interface_collect_agent_params(jdk_version, app_name, suite_code, branch_name)
        self.alter_start_file(agent_params)

    def jacoco_agent_params(self, output, address, port, append, includes):
        """
        :param output:
        :param address:
        :param port:
        :param append:
        :param includes:
        :return: .str
         返回加入到启动脚本中的参数， 如下：
        "-javaagent:/data/logs/jacoco/lib/jacocoagent.jar=output=tcpserver,address=0.0.0.0,port=6305,append=false,includes=com.howbuy.*"
        """
        jacoco_agent_path = '{}/jacoco/lib/jacocoagent.jar'.format(self.agent_base_path)
        return "-javaagent:{}=output={},address={},port={},append={},includes={}".format(jacoco_agent_path, output,
                                                                                         address, port, append,
                                                                                         includes)

    def add_jacoco_agent_params(self, output='tcpserver', address='0.0.0.0', port='6305', append='false',
                                includes='com.howbuy.*'):
        agent_params = self.jacoco_agent_params(output, address, port, append, includes)
        self.alter_start_file(agent_params)

    def mock_agent_params(self, app_name, suite_code, mock_env, qa_info_api):
        mock_agent_path = os.path.join(self.agent_base_path, 'howbuy-mock-agent/lib/howbuy-mock-agent.jar')
        return "-javaagent:{}={},{},{},{} ".format(mock_agent_path, app_name, suite_code, mock_env, qa_info_api)

    def add_mock_agent_params(self, app_name, suite_code):
        mock_env = self.mock_system_interface["default_mock_env"]
        qa_info_api = "{}{}".format(self.mock_system_interface["qa_info_url"],
                                    self.mock_system_interface["get_mock_data_api"])
        agent_params = self.mock_agent_params(app_name, suite_code, mock_env, qa_info_api)
        self.alter_start_file(agent_params)

    def get_agent_params(self, module_name, suite_code, agent_list, br_name):
        # logger.info("env_publish_info的类型是{}".format(type(env_publish_info)))
        # logger.info("env_publish_info==={}".format(env_publish_info))
        # agent_list = env_publish_info.agent_info_dict.get("starting_agent_list") + env_publish_info.agent_info_dict.get(
        #     "running_agent_list")
        # agent_list = env_publish_info.get("agent_info_dict").get("starting_agent_list") + env_publish_info.get("agent_info_dict").get("running_agent_list")
        params_dict = {}
        mock_env = self.mock_system_interface["default_mock_env"]
        qa_info_url = self.mock_system_interface["qa_info_url"]
        get_mock_data_api = self.mock_system_interface["get_mock_data_api"]
        base_api_url = self.test_system_interface["base_api_url"]
        interface_collect_api_name = self.test_system_interface["interface_collect_api_name"]
        branch_name = br_name
        # branch_name = env_publish_info.get("br_name")
        for agent in agent_list:
            params_dict.update({agent.get("agent_module_name"):
                                    " -javaagent:" + agent.get("agent_cmd").format(
                                        agent_package_name=agent.get("agent_package_name"),
                                        module_name=module_name,
                                        suite_code=suite_code,
                                        mock_env=mock_env,
                                        qa_info_url=qa_info_url,
                                        get_mock_data_api=get_mock_data_api,
                                        base_api_url=base_api_url,
                                        interface_collect_api_name=interface_collect_api_name,
                                        upload_url=INTERFACE_URL['spider'] + SHARDING["upload_url"],
                                        branch_name=branch_name,
                                        exec_batch=self.batch_no,
                                        arex_service_name=agent.get("arex_service_name"),
                                        pinpoint_agent_id=agent.get("pinpoint_agent_id")
                                    ) + " "})

        return params_dict

    def add_agent_params(self, module_name, suite_code, agent_list, br_name, agent_module_name):
        agent_params_dict = self.get_agent_params(module_name, suite_code, agent_list, br_name)
        self.alter_start_file(agent_params_dict.get(agent_module_name))

    def create_agent_param_content(self, module_name, suite_code, agent_list, br_name, agent_module_name):
        add_params = self.get_agent_params(module_name, suite_code, agent_list, br_name)
        agent_param = '{mark_value}="${mark_value} {add_params}" \n'.format(mark_value=self.mark_value,
                                                                            add_params=add_params.get(agent_module_name))
        logger.info("追加一行 {}".format(agent_param))
        return agent_param

    def add_shard_agent_params(self, module_name, br_name, suite_code, agent):
        agent_param = agent.get("agent_cmd")
        self.alter_start_file_for_shard(agent_param)
        agent_param = self.get_shard_agent_opt_params(module_name, br_name, suite_code, agent)
        self.alter_start_file_for_shard(agent_param)

    def create_shard_agent_param_content(self, module_name, br_name, suite_code, agent):
        agent_param = agent.get("agent_cmd")
        add_content = agent_param + '\n'
        agent_param = self.get_shard_agent_opt_params(module_name, br_name, suite_code, agent)
        add_content += agent_param + '\n'
        return add_content

    def get_shard_agent_opt_params(self, module_name, br_name, suite_code, agent):
        param = 'S_JAVA_OPTS="'
        if agent.get("sharding_agent_param"):
            for key, value in json.loads(agent.get("sharding_agent_param")).items():
                param = param + " -D" + key + "=" + value + " "
        param = param + " -D" + "report_shard_tables_api_path=" + INTERFACE_URL['spider'] + INTERFACE_URL[
            'spider_context'] + \
                INTERFACE_URL[
                    'report_shard_tables_api_name']
        param = param + '"'
        return param


if __name__ == "__main__":
    a = AgentInjection("D:\\GITProject\\start-script\\startup.sh")
    env_publish_info = {

        "active_node_ip": "",

        "agent_info_dict": {

            "running_agent_list": [

                {

                    "agent_cmd": "/data/logs/jacoco/lib/{agent_package_name}=output=tcpserver,address=0.0.0.0,port=6305,append=false,includes=com.howbuy.*",

                    "agent_deploy_path": "/data/logs/{suite_code}/jacoco/lib",

                    "agent_module_name": "JaCoCo",

                    "agent_package_name": "jacocoagent.jar",

                    "agent_type": 2,

                    "third_party_middleware": 1

                },

                {

                    "agent_cmd": "/data/logs/howbuy-mock-agent/lib/{agent_package_name}={module_name},{suite_code},{mock_env},{qa_info_url}{get_mock_data_api}",

                    "agent_deploy_path": "/data/logs/{suite_code}/howbuy-mock-agent/lib",

                    "agent_module_name": "howbuy-mock-agent",

                    "agent_package_name": "howbuy-mock-agent.jar",

                    "agent_type": 2,

                    "third_party_middleware": 0

                }

            ],

            "starting_agent_list": [

                {

                    "agent_cmd": "/data/logs/howbuy-interface-scan-agent/1.8/{agent_package_name}='{base_api_url}{interface_collect_api_name}&{module_name}&{suite_code}&{branch_name}'",

                    "agent_deploy_path": "/data/logs/{suite_code}/howbuy-interface-scan-agent/1.8",

                    "agent_module_name": "howbuy-interface-scan-agent",

                    "agent_package_name": "howbuy-interface-scan-agent.jar",

                    "agent_type": 1,

                    "third_party_middleware": 0

                },

                {

                    "agent_cmd": "/data/logs/shardingsphere-agent/{agent_package_name}={module_name}",

                    "agent_deploy_path": "/data/logs/{suite_code}/shardingsphere-agent ",

                    "agent_module_name": "shardingsphere-agent",

                    "agent_package_name": "shardingsphere-agent.jar",

                    "agent_type": 1,

                    "third_party_middleware": 0

                }

            ]

        },

        "br_name": "patest-2709",

        "container_name": "acc-center-web",

        "deploy_config_path": "/home/<USER>/config/it29/tomcat/acc-center-web/",

        "deploy_path": "/home/<USER>/img-factory-online/it29/acc-center-web/acc-center-web/",

        "deploy_type": 2,

        "gitlab_path": "acc/acc-center",

        "lib_cache_repo_path": "/data/pipeline_repo/acc-center-web/patest-2709/",

        "lib_repo_config_path": "tp/acc-center-web/pro-w",

        "lib_repo_path": "************************:lib_repo/acc-center.git",

        "module_name": "acc-center-web",

        "nacos_conf_name": "",

        "nacos_namespace": "acc-center-web",

        "node_docker": "it29",

        "online_br_name": "master",

        "package_full": 1,

        "package_name": "acc-center.war",

        "package_type": "war",

        "platform_type": 1,

        "script_name": "catalina.sh",

        "src_cache_config_path": "/data/test_publish_aio/it29/test_app_resource/acc-center-web/",

        "start_file_local_path": "/home/<USER>/dump/acc_patest-2709_acc-center-web/start_script_template/acc-center-web/catalina.sh",

        "start_level": 1,

        "start_script_path": "/home/<USER>/img-factory-online/it29/acc-center-web/catalina.sh",

        "start_script_path_dir": "/home/<USER>/img-factory-online/it29/acc-center-web",

        "start_script_template_url": "*************************:app-ops/start_script_template.git",

        "suite_code": "it29",

        "svn_container_path": "",

        "target_base_path": "",

        "target_cache_config_path": "/data/pipeline_repo/acc-center-web/config/patest-2709/",

        "third_party_middleware": 0,

        "tomcat_name": "acc-center-web",

        "workspace": "/home/<USER>/dump/acc_patest-2709_acc-center-web",

        "zeus_type": 1

    }
    a.add_agent_params('howbuy-middleware-remote', 'it29', env_publish_info, 'shardingsphere-agent')
    a.add_agent_params('howbuy-middleware-remote', 'it29', env_publish_info, 'howbuy-mock-agent')
    a.add_agent_params('howbuy-middleware-remote', 'it29', env_publish_info, 'howbuy-interface-scan-agent')
    a.add_agent_params('howbuy-middleware-remote', 'it29', env_publish_info, 'JaCoCo')
