import os
import datetime
from settings import logger
from common.ext_cmd.shell_cmd import shell_cmd_exit_found2notfound
from utils.compile.mvn import compiler
from dao.get.mysql import app_info
from test_pipeline.test_pipeline_models.test_pipeline_dao.unit_test import TestReportModel
from ci_pipeline.ci_pipeline_models.iter_models import  BranchesModel


class JunitTest(compiler.Compiler):

    def exec_junit_test(self):
        """执行测试"""
        package_info = self._get_package_info(self.app_name)
        pom_type = package_info['package_type']

        if pom_type == 'pom':
            # 删除m
            self.del_pom_module(self.pom_path)

        jdk_path = self._get_jdk_path(app_name=self.app_name, iteration_id=self.iteration_id)
        command = 'export JAVA_HOME={} && {} clean test -Dautoconfig.skip=true ' \
                  '--settings {} -U -Dmaven.test.skip=false -Dmaven.test.failure.ignore=true  -f {}'.format(jdk_path,
                                                                                        self.default_mvn,
                                                                                        self.mvn_settings,
                                                                                        self.pom_path)

        logger.info('-----------开始编译-----------')
        logger.info('编译命令:{}'.format(command))
        exit_code, output = shell_cmd_exit_found2notfound(command, exit_conditions='BUILD FAILURE,Malformed POM',
                                                          exit_conditions_not_found='BUILD SUCCESS', read_line=True,
                                                          all_log=True)
        if exit_code == 1:
            return "failure", output, None
        # 判断是否是上线应用.(原: 判断是不是dubbo或war. 如果是,返回包路径)
        result = None

        return "success", "执行测试成功", result

    @staticmethod
    def record_report_info(iteration_id, module_name, lib_repo_url, lib_repo_size):
        # fixme by shuai 20220414
        trm = TestReportModel()
        logger.info(trm.Meta.db_table)
        for row in BranchesModel.select(BranchesModel.br_name).\
                where(BranchesModel.pipeline_id == iteration_id):
            trm.lib_repo_branch = row.br_name

        # trm.iteration_id = "_".join(os.environ['JOB_NAME'].split("_")[:-1])
        #         # trm.module_name = os.environ['JOB_NAME'].split("_")[-1]
        trm.iteration_id = iteration_id
        trm.module_name = module_name
        #trm.lib_repo_branch = lib_repo_branch
        trm.lib_repo_size = lib_repo_size
        trm.lib_repo_url = lib_repo_url
        trm.jenkins_job_id = os.environ['BUILD_ID']
        trm.report_url = os.environ['RUN_DISPLAY_URL']
        trm.create_time = datetime.datetime.now()
        trm.insert()
        return "success", "录入成功"
