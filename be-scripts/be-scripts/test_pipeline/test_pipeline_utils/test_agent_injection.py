import pytest
import os
from settings import logger
from test_pipeline.test_pipeline_utils.agent_injection import AgentInjection
from settings import TEST_SYSTEM_INTERFACE


jdk_version_list = ["1.7", "1.8"]
app_name = ["adviser-batch-center-remote", "acc-center-web", "acc-console-web"]
suite_code = ["it01", "it02", "it03"]
branch_name = ["1.0.2", "9.9.9", "master", "fasfsdfsdafdsf4twegfasdgsdfagasdfsdfasdfdsf"]


class TestAgentInjection:

    file_path = "./temp_start.sh"

    def setup_class(self):
        logger.info("准备实例化AgentInjection类")
        if not os.path.isfile(self.file_path):
            logger.info(self.file_path)
            open(self.file_path, "w+")

        self.agent_injection = AgentInjection(self.file_path)

    def test_alter_start_file(self, agent_params):
        self.alter_start_file(agent_params)
        with open(self.file_path, "r") as f:
            while True:
                line = f.readline()
                if not line:
                    break
                if "=" in line and line.split("=")[0].strip() == self.agent_injection.mark_value:
                    assert agent_params in "=".join(line.split("=")[1:]).strip()



    @pytest.mark.parametrize("jdk_version", jdk_version_list)
    @pytest.mark.parametrize("app_name", app_name)
    @pytest.mark.parametrize("suite_code", suite_code)
    @pytest.mark.parametrize("branch_name", branch_name)
    def test_interface_collect_agent_params(self, jdk_version, app_name, suite_code, branch_name):
        """
        :param jdk_version:
        :param app_name:
        :param suite_code:
        :param branch_name:
        :return: .str
         返回加入到启动脚本中的参数， 如下：
        "-javaagent:/data/logs/howbuy-interface-scan-agent/1.8/howbuy-interface-scan-agent.jar
        ='http://**************:8088/mring/api/importAgentReportInterfaces.do&adviser-batch-center-remote&it01&3.4.41'"
        """

        agent_params = self.agent_injection.interface_collect_agent_params(jdk_version,
                                                                           app_name,
                                                                           suite_code,
                                                                           branch_name)
        logger.info(agent_params)
        agent_abs_path = os.path.join(self.agent_injection.agent_base_path,
                                      self.agent_injection.test_system_interface["interface_agent_package_name"],
                                      jdk_version,
                                      self.agent_injection.test_system_interface["interface_agent_package_name"] + ".jar")
        request_url = "{base_api_url}{interface_name}&{app_name}&{suite_code}&{branch_name}".format \
            (base_api_url=self.agent_injection.test_system_interface["base_api_url"],
             interface_name=self.agent_injection.test_system_interface["interface_collect_api_name"],
             app_name=app_name,
             suite_code=suite_code,
             branch_name=branch_name)
        test_agent_params = "-javaagent:{agent_abs_path}='{request_url}'".format(agent_abs_path=agent_abs_path,
                                                                                 request_url=request_url)
        logger.info(test_agent_params)
        assert agent_params == test_agent_params
    #
    # def test_add_interface_collect_agent_params(self):
    #     assert 1 == 3
    #
    # def test_jacoco_agent_params(self):
    #     assert 1 == 1
    #
    # def test_add_jacoco_agent_params(self):
    #     assert 1 == 1
