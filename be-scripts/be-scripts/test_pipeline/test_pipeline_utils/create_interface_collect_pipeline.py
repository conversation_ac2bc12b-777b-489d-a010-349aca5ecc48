import os


from settings import logger
from test_pipeline.test_pipeline_models.test_pipeline_dao.get.app_iterface_test import get_need_interface_collection_info
from test_pipeline.templates.interface_collect_template import InterfaceCollectTemplate


class InterfaceCollectionPipelineGenerator:

    def __init__(self, workspace, publish_suite):
        self.publish_suite = publish_suite
        self.workspace = workspace

    def create_single_publish_pipeline(self, app_name, br_name, suite_code, src_suite_code, package_type,
                                       build_jdk_version):
        ppt = InterfaceCollectTemplate(self.workspace, app_name, br_name,
                                       suite_code, src_suite_code=src_suite_code,
                                       package_type=package_type, build_jdk_version=build_jdk_version)
        publish_env_xml = ppt.product_loop_stage()
        logger.info(publish_env_xml)
        return publish_env_xml

    def batch_app(self):
        batch_dict = {}
        for row in get_need_interface_collection_info():
            if row["module_name"] in batch_dict:
                batch_dict[row["module_name"]].append(row)
            else:
                batch_dict[row["module_name"]] = [row]
        logger.info(batch_dict)
        return batch_dict

    @staticmethod
    def get_need_collect_app():
        app_list = []
        for row in get_need_interface_collection_info():
            if row["module_name"] in app_list:
                continue
            app_list.append(row["module_name"])
        return app_list

    def batch_publish_pipeline(self):
        batch_publish_template = """
           stage("{app_name}发布阶段"){{
                when{{
                    expression  {{analysis_flag == true}}
                }}          
               steps{{
               {single_app_publish_pipeline}
               }}
           }}
           """
        batch_dict = self.batch_app()
        batch_publish_pipeline_dict_tem = {}
        for app_name in batch_dict:
            for row in batch_dict[app_name]:
                if row["module_name"] in batch_publish_pipeline_dict_tem:
                    batch_publish_pipeline_dict_tem[app_name] = batch_publish_pipeline_dict_tem[app_name] \
                                                       + "\n" + self.create_single_publish_pipeline(row["module_name"],
                                                                                           row["lib_repo_branch"],
                                                                                           self.publish_suite,
                                                                                            row["suite_code"],
                                                                                            row["package_type"],
                                                                                            row["build_jdk_version"])
                else:
                    batch_publish_pipeline_dict_tem[app_name] = self.create_single_publish_pipeline(row["module_name"],
                                                                                           row["lib_repo_branch"],
                                                                                            self.publish_suite,
                                                                                            row["suite_code"],
                                                                                            row["package_type"],
                                                                                            row["build_jdk_version"])
        batch_publish_pipeline_dict = {}
        for app_name in batch_publish_pipeline_dict_tem:
            batch_publish_pipeline_dict[app_name] = batch_publish_template.format(app_name=app_name,
                                          single_app_publish_pipeline=batch_publish_pipeline_dict_tem[app_name])
        logger.info(batch_publish_pipeline_dict)
        return batch_publish_pipeline_dict

    def create_publish_pipeline(self):

        batch_publish_pipeline_dict = self.batch_publish_pipeline()
        if len(batch_publish_pipeline_dict) == 1:
            return list(batch_publish_pipeline_dict.values())[0]
        else:
            return '''stage("多应用并发"){{
                  when{{
                        expression  {{analysis_flag == true}}
                    }}      
                    parallel {{{batch_publish_pipeline_dict}}}
                    }}'''.\
                format(batch_publish_pipeline_dict="\n".join(list(batch_publish_pipeline_dict.values())))


if __name__ == "__main__":
    ge = InterfaceCollectionPipelineGenerator("D:\\scm_code\\be-scripts\\Jenkinsfile", "it01")
    ge.batch_publish_pipeline()
    #logger.info(ge.create_publish_pipeline())