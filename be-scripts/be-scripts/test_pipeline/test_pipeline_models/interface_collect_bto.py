import os
from settings import PIPELINE_TEST_PUBLISH


class InterfaceCollectBto:
    __config_info = PIPELINE_TEST_PUBLISH
    __start_file_name = "start.sh"
    __tomcat_start_file_name = "catalina.sh"
    __tomcat_start_file_dir = "tomcat"

    def __init__(self, params_info):
        self.workspace = params_info["workspace"]
        self.marking_path = params_info["marking_path"]
        self.suite_code = params_info["suite_code"]
        self.config_map_ip = self.__config_info["config_map_ip"]
        self.config_map_user = self.__config_info["config_map_user"]
        self.config_map_password = self.__config_info["config_map_password"]
        self.config_map_conf_path = self.__config_info["config_map_conf_path"]
        self.start_script_template_url = self.__config_info["start_script_template_url"]
        start_script_repos_name = self.start_script_template_url.split("/")[-1].replace(".git", "")
        if "app_name" in params_info:
            self.app_name = params_info["app_name"]
            self.br_name = params_info["br_name"]
            self.package_type = params_info["package_type"]
            if self.package_type == "war":
                self.tomcat_file_src_path = os.path.join(self.workspace, start_script_repos_name,
                                                         self.__tomcat_start_file_dir,
                                                         self.__tomcat_start_file_name)
                self.start_file_local_path = os.path.join(self.workspace, start_script_repos_name, self.app_name,
                                                          self.__tomcat_start_file_name)
            else:
                self.start_file_local_path = os.path.join(self.workspace, start_script_repos_name, self.app_name,
                                                    self.__start_file_name)

            self.deploy_config_path = self.get_deploy_config_path(self.suite_code)
            self.src_suite_code = params_info["src_suite_code"]
            self.src_config_path = self.get_deploy_config_path(self.src_suite_code)
            self.build_jdk_version = params_info["build_jdk_version"]

    def get_deploy_config_path(self, suite_code):

        if self.package_type == "jar":
            return os.path.join(self.config_map_conf_path,
                                suite_code,
                                'remote',
                                self.app_name, "conf")

        else:
            tomcat_container_path = 'tomcat/{}/WEB-INF/classes/'.format(self.app_name)
            return os.path.join(self.config_map_conf_path,
                                suite_code,
                                tomcat_container_path)
        return deploy_config_path

