from peewee import *
from dao.base_model import BaseModel


class IntegrationTestList(BaseModel):
    create_time = DateTimeField( null=True, verbose_name='创建时间')
    test_set_id = IntegerField(verbose_name='测试集ID')

    class Meta:
        db_table = 'test_mgt_integration_test_list'
        verbose_name = '集成测试执行清单'


class IntegrationTestListDetail(BaseModel):

    app_name = CharField(verbose_name='应用名', max_length=100)
    br_name = CharField(verbose_name='分支名', max_length=100)
    create_time = DateTimeField(null=True, verbose_name='创建时间')
    create_user = CharField(verbose_name='创建用户', max_length=100)
    main_id = IntegerField(verbose_name='批次号')
    update_time = DateTimeField(null=True, verbose_name='更新时间')
    update_user = CharField(verbose_name='更新用户', max_length=100)

    class Meta:
        db_table = 'test_mgt_integration_test_list_detail'
        verbose_name = '集成测试详细执行清单'


class TestMgtPublishApp(BaseModel):
    suite_code = CharField(verbose_name='环境套编码', max_length=100)
    module_name = CharField(verbose_name='应用名', max_length=100)

    class Meta:
        db_table = 'test_mgt_publish_app'
        verbose_name = '测试环境用发布版本的应用列表'