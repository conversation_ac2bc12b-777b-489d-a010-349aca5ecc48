from dao.connect.mysql import BaseModel
from settings import logger
import datetime


class TestReportModel(BaseModel):

    def __init__(self):
        self.module_name = None
        self.jenkins_job_id = None
        self.report_url = None
        self.lib_repo_url = None
        self.lib_repo_branch = None
        self.lib_repo_size = None
        self.create_time = None
        self.iteration_id = None

    class Meta:
        db_table = 'product_mgt_test_report_info'


if __name__ == "__main__":
    tr = TestReportModel()
    logger.info(tr.Meta.db_table)
    tr.iteration_id = ""
    tr.module_name = ""
    tr.jenkins_job_id = 1
    tr.report_url = ""
    tr.create_time = datetime.datetime.now()
    tr.insert()

