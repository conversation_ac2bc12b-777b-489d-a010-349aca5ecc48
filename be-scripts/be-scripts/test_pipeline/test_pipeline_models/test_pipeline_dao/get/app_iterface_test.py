from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_need_interface_collection_info(region_group="test", br_status = "open"):
    """
     筛选的业务条件
     1、部署在测试环境上的
     2、在开发中的
    :param region_group:
    :param br_status:
    :return: iter obj
    """

    sql = '''SELECT b.module_name,zz.iteration_id,zz.lib_repo_branch,s.suite_code,d.package_type,d.build_jdk_version 
FROM env_mgt_node_bind b RIGHT JOIN 
env_mgt_suite s ON s.id = b.suite_id
RIGHT JOIN app_mgt_app_build d ON b.module_name=d.module_name
RIGHT JOIN (
SELECT b.module_name,p.iteration_id,p.lib_repo_branch,MAX(b.node_lib_repo_update_time) AS node_lib_repo_update_time FROM env_mgt_node_bind b 
LEFT JOIN product_mgt_product_info p ON b.lib_repo_info_id = p.id
LEFT JOIN iter_mgt_iter_info iter ON iter.pipeline_id = p.iteration_id
LEFT JOIN test_mgt_app_test a ON a.module_name = b.module_name
LEFT JOIN env_mgt_suite s ON s.id = b.suite_id
LEFT JOIN env_mgt_region r ON r.id=s.region_id
WHERE p.id IS NOT NULL AND iter.br_status="{}" AND r.region_group = "{}" 
AND a.need_interface_agent=1 GROUP BY b.module_name,p.iteration_id)
 zz ON  b.node_lib_repo_update_time = zz.node_lib_repo_update_time AND zz.module_name=b.module_name
'''. format(br_status, region_group)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_is_need_unit(module_name):
    """
    获取应用是否需要进行单元测试
    :param module_name:
    :return:
    """
    sql = 'select need_unit_test from test_mgt_app_test where module_name = "{}"'\
        .format(module_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    need_unit_test = 0
    for row in db.cur.fetchall():
        need_unit_test = row["need_unit_test"]
    return need_unit_test


def get_suite_code_id(suite_name):
    sql = 'SELECT id FROM env_mgt_suite WHERE suite_code = "{}"' \
        .format(suite_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["id"]
