from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_need_unit_app(exclude_group=[]):
    """

    :param exclude_group:
    :return:
    """
    sql = '''SELECT i.git_url,CONCAT(i.git_url, i.git_path) as repos_path, m.module_name 
FROM app_mgt_app_info i RIGHT JOIN 
app_mgt_app_module m ON m.app_id = i.id
RIGHT JOIN app_mgt_app_build b ON b.module_name = m.module_name 
 WHERE CONCAT(i.git_url, i.git_path) IS NOT NULL AND m.need_online = 1 AND b.package_type IN ("jar","war") AND 
 i.third_party_middleware = 0 '''
    if len(exclude_group) > 0:
        sql = sql +' and i.git_url not in ("{}")'.format('","'.join(exclude_group))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_iter_app_info(br_name):
    """

    :param br_name:
    :return:
    """
    sql = '''SELECT i.pipeline_id,i.appName, CONCAT(ap.git_url, ap.git_path) as repos_path
 FROM iter_mgt_iter_app_info i LEFT JOIN app_mgt_app_module m
 ON i.appName=m.module_name left join  app_mgt_app_info ap on  m.app_id = ap.id
LEFT JOIN iter_mgt_iter_info a ON a.pipeline_id = i.pipeline_id
 WHERE a.br_name = "{}" AND  m.need_online = 1 '''.format(br_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()
