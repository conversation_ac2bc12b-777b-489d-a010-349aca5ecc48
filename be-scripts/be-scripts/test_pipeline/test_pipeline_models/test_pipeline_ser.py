from settings import logger
from peewee import fn
import os, sys
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
logger.info(PROJECT_DIR)
sys.path.append(PROJECT_DIR)
from test_pipeline.test_pipeline_models.test_pipeline_models import IntegrationTestList, IntegrationTestListDetail


class IntegrationTestListBo:
    __integration_test_list_dto = IntegrationTestList
    __integration_test_list_detail_dto = IntegrationTestListDetail

    def obtain_integration_test_list_last(self, test_set_id):
        """
        获取该测试集 最近一次需要执行的应用清单
        :param test_set_id:
        :return:
        """
        integration_test_dict = {}
        integration_test_list = self.__integration_test_list_detail_dto.select().where(
            self.__integration_test_list_detail_dto.main_id == self.__integration_test_list_dto.select(
                fn.MAX(self.__integration_test_list_dto.id)).where(
                self.__integration_test_list_dto.test_set_id == test_set_id).get())
        for row in integration_test_list:
            integration_test_dict[row.app_name] = row.br_name
        logger.info(integration_test_dict)
        return integration_test_dict


if __name__ == "__main__":
    itb = IntegrationTestListBo()
    itb.obtain_integration_test_list_last(408)


