pipeline {
    agent any
     environment {
        script_path='/home/<USER>/be-scripts/be-scripts/test_pipeline/test_pipeline_business/app_interface_collect_pepeline.py'
        PYTHON_CMD="python3.x"
        args = "{\"workspace\":\"${workspace}\",\"marking_path\":\"/home/<USER>/dump/${env.JOB_NAME}_test_skip.txt\"}"
    }
    stages{
        stage("判断是否需要解析"){

                steps{
                    echo "${env.JOB_NAME}"
                    script {
                       cache_data_path="/home/<USER>/dump/${env.JOB_NAME}"
                       flagPath="/home/<USER>/dump/${env.JOB_NAME}.txt"
                       analysis_flag = fileExists flagPath
                       testSkipPath="/home/<USER>/dump/${env.JOB_NAME}_test_skip.txt"
                       //bind_suite
                        suite_code = "tms18"
                       //bind_suite
                       analysis_test_skip = fileExists testSkipPath
                    }
                }
        }
         stage("解析"){
               when{
                        expression  {analysis_flag == false}
                }
                steps {
                   sh label: '', script: '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} "create_pipeline"'
                }
         }

         //interface_collect
          stage("环境发布"){
                   when{
                        expression  {analysis_flag == true}
                        expression  {analysis_test_skip == false}
                   }
                   parallel {

                    stage('none') {
                        steps {
                            echo ''
                        }
                    }

                   }
             }
        //interface_collect
    }
    post{
        always {
            script {
                sh 'echo ' + analysis_flag
                sh 'echo ' + analysis_test_skip
                if ( analysis_flag && analysis_test_skip ) {
                    sh 'rm -f ' + testSkipPath
                }
                if (analysis_flag){
                    sh 'rm -rf '+flagPath
                }
            }
        }
        failure{
            echo '运行失败'
            sh label: '', script: 'rm -rf '+flagPath
        }
        aborted {
            echo '取消运行'
            sh label: '', script: 'rm -rf '+flagPath
        }
        success{
            echo "运行成功"
        }
    }
}