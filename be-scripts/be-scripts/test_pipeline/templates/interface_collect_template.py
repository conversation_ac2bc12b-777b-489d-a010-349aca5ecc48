from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos
from settings import logger
from test_publish_aio.test_suite_init_constants import TypeEnum


class InterfaceCollectTemplate:
    script_path = "~/be-scripts/be-scripts/ci_pipeline/test_env_publish/test_env_publish_main.py"

    test_publish_node_stage = """stage('{stage_name}') {{
                    when{{
                        expression  {{analysis_flag == true}}
                    }}
                    steps {{
                        sh 'python3.x {script_path} '+cache_data_path+' {node_name} {app_name} {br_name} {cache_data_code} '+suite_code
                    }}
                }}"""

    cover_config = """stage('{stage_name}') {{
                    when{{
                        expression  {{analysis_flag == true}}
                    }}
                    steps {{
                        script {{                       
                                def argsMap = new JsonSlurper().parseText(args)
                                argsMap["src_suite_code"] = "{src_suite_code}"
                                argsMap["package_type"] = "{package_type}"
                                argsMap["build_jdk_version"] = "{build_jdk_version}"
                                argsMap["app_name"] = "{app_name}"
                                argsMap["br_name"] = "{br_name}"
                                println argsMap
                                env.newargs_{src_suite_code} = new JsonOutput().toJson(argsMap)
                                println env.newargs_{src_suite_code}
                               
                        }}
                         sh label: '', script: '${{PYTHON_CMD}} ${{SCRIPT_PATH}} ${{params}} ${{newargs_{src_suite_code}}} "{node_name}"'
                    }}
                }}"""

    loop_stage = """
        script{{
           for (interface_node_name in interface_node_stages){{
                stage(interface_node_name) {{ 
            if (interface_node_name=="cover_config" || interface_node_name=="alert_start_file")
            {{
            def argsMap = new JsonSlurperClassic().parseText(args)
                                argsMap["src_suite_code"] = "{src_suite_code}"
                                argsMap["package_type"] = "{package_type}"
                                argsMap["build_jdk_version"] = "{build_jdk_version}"
                                argsMap["app_name"] = "{app_name}"
                                argsMap["br_name"] = "{br_name}"
                                println argsMap
                                env.newargs_{src_suite_code} = new JsonOutput().toJson(argsMap)
                                env.newargs_{src_suite_code} = env.newargs_{src_suite_code}.trim().replaceAll("\\n","")
                                println env.newargs_{src_suite_code}
            
       
            sh label: '', script: '''${{PYTHON_CMD}} ${{SCRIPT_PATH}} ${{newargs_{src_suite_code}}} ${{pipeline_params}} '''+interface_node_name               
            }}
            else{{           
                    sh "python3.x {script_path} ${{cache_data_path}} ${{interface_node_name}} {app_name} {br_name} {cache_data_code} {suite_code}"
                    }}
                }}
           }}
        }}
    """

    test_publish_node_dict = {"env_check_per": "环境预检",
                 "pull_product": "拉制品",
                 "scp_package": "推制品",
                 "make_img": "打镜像",
                 "create_config_map": "创建ConfigMap",

                 "get_start_file": "获取启动文件",
                 "publish_start_file": "发布启动文件",

                 "restart_app": "重启app",
                 "check_result": "验证结果",
                 }

    interface_collect_node_dict = {
                 "cover_config": "覆盖configMap",
                 "alert_start_file": "修改启动文件",
                 }

    publish_pipeline_template = """
        stage("{branch_name}"){{
            when{{
                expression  {{analysis_flag == true}}
            }}
            stages {{
                {env_check_per}
                {pre_template}
                {restart_app}
                {check_result}
            }}
        }}   
    """
    app_template = """stage('{branch_name}制品发布') {{
                            when{{
                                expression  {{analysis_flag == true}}
                            }}
                            stages {{
                                {pull_product}
                                {scp_package}
                                {make_img}
                                {cover_config}                               
                                {create_config_map}
                                {get_start_file}
                                {alert_start_file}
                                {publish_start_file}
                            }}
                        }}"""

    def __init__(self, workspace, app_name, br_name, suite_code, src_suite_code, package_type, build_jdk_version):
        self.app_name = app_name
        self.br_name = br_name
        self.suite_code = suite_code
        self.src_suite_code = src_suite_code

        ptr = PublishTestRepos(self.suite_code,
                               workspace,
                               type_enum=TypeEnum.BUILD,
                               br_name=self.br_name,
                               app_list=[self.app_name])
        for row in ptr:
            logger.info(row)
            self.env_publish_info = row
        self.cache_data_code = ptr.cache_data_code
        self.package_type = package_type
        self.build_jdk_version = build_jdk_version

    def set_up_pipeline(self):
        # 生产各个节点
        stage_dict = self.product_stage()
        app_template = self.app_template.format(branch_name=self.br_name, pull_product=stage_dict["pull_product"],
                                                    scp_package=stage_dict["scp_package"],
                                                    make_img=stage_dict["make_img"],
                                                cover_config=stage_dict["cover_config"],
                                                create_config_map=stage_dict["create_config_map"],
                                                get_start_file=stage_dict["get_start_file"],
                                                alert_start_file=stage_dict["alert_start_file"],
                                                publish_start_file=stage_dict["publish_start_file"]
                                                )

        publish_pipeline_template = self.publish_pipeline_template.format(
            branch_name=self.br_name,
            env_check_per=stage_dict["env_check_per"],
            pre_template=app_template,
            restart_app=stage_dict["restart_app"],
            check_result=stage_dict["check_result"])
        return publish_pipeline_template

    def product_loop_stage(self):
        return self.loop_stage.format(script_path=self.script_path,
                               app_name=self.app_name,
                               br_name=self.br_name,
                               suite_code=self.suite_code,
                               cache_data_code=self.cache_data_code,
                               src_suite_code=self.src_suite_code,
                               package_type=self.package_type,
                               build_jdk_version=self.build_jdk_version )

    def product_stage(self):
        stage_dict = {}
        for node_name in self.test_publish_node_dict:
            stage_dict[node_name] = self.test_publish_node_stage.format(stage_name=self.test_publish_node_dict[node_name],
                                                           script_path=self.script_path,
                                                           node_name=node_name,
                                                           app_name=self.app_name,
                                                           br_name=self.br_name,
                                                           suite_code=self.suite_code,
                                                           cache_data_code=self.cache_data_code)
        for node_name in self.interface_collect_node_dict:
            stage_dict[node_name] = self.cover_config.format(stage_name=self.interface_collect_node_dict[node_name],
                                                             app_name=self.app_name,
                                                             src_suite_code=self.src_suite_code,
                                                             package_type=self.package_type,
                                                             build_jdk_version=self.build_jdk_version,
                                                             br_name=self.br_name,
                                                             node_name=node_name)

        return stage_dict


if __name__ == "__main__":
    ppt = PublishPipelineTemplate("E:\\test", "otc-batch-remote", "0.0.1112", "tms17", True)
    ppt.set_up_pipeline()
