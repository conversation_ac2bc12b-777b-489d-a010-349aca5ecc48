from utils.call_external_interface.call_http_api import SpiderHttpCaller
from common.call_api.gitlab.merge import Merger

from settings import logger
import json
import datetime
import time
from common.call_api.be_jenkins.jenkins_api import Jenkins<PERSON><PERSON>
from test_pipeline.test_pipeline_models.test_pipeline_dao.get.unit_test import get_need_unit_app, get_iter_app_info


class IterApplyBo:
    def __init__(self):
        self.branch_name = "unit0.0.0"
        self.branch_type = "bugfix"
        self.is_update_out_dep = 0
        self.deadline = "2023-01-30"
        self.gitlab_group = ""
        self.desc = "用来单元测试"
        self.tapd_id = ""
        self.repos_str = []

    def set_gitlab_group(self, gitlab_group):
        self.gitlab_group = gitlab_group

    def set_repos_str(self, repos_str):
        self.repos_str = repos_str


class BatchUnitTester:
    __iter_apply_bo = IterApplyBo
    __jenkins_api = JenkinsApi
    _exclude_group_list = ["mojie", "scm_test_project"]

    def __init__(self):
        pass

    @staticmethod
    def __get_need_unit_app(*args):
        return get_need_unit_app(*args)

    @staticmethod
    def __get_iter_app_info(*args):
        return get_iter_app_info(*args)

    def _get_unit_job(self):
        job_name_list = []
        iter_apply_bo = self.__iter_apply_bo()
        for row in self.__get_iter_app_info(iter_apply_bo.branch_name):
            job_name_list.append("{}_{}".format(row["pipeline_id"], row["appName"]))
        return job_name_list

    def _create_branch_data(self):
        iter_apply_data_dict = {}

        for row in self.__get_need_unit_app(self._exclude_group_list):
            if row["git_url"] in iter_apply_data_dict:
                iter_apply_data_dict[row["git_url"]]["repos_str"].append({'repos_path': row['repos_path'],
                                                                          'module_name': row['module_name']})
            else:
                iter_apply_bo = self.__iter_apply_bo()
                iter_apply_bo.set_gitlab_group(row["git_url"])
                iter_apply_bo.set_repos_str([{'repos_path': row['repos_path'], 'module_name': row['module_name']}])
                iter_apply_data_dict[row["git_url"]] = iter_apply_bo.__dict__
        logger.info(iter_apply_data_dict)
        return iter_apply_data_dict

    def _delete_unit_test_pipeline(self, data):
        http_caller = SpiderHttpCaller()
        http_caller.spider_login()

        res = http_caller.post_request(http_caller.business_dict["del_repos"], json=json.dumps(data))
        #res = http_caller.post_request(http_caller.business_dict["iter_apply"], data=data)
        logger.info(res.__dict__)

    def _create_delete_data(self):
        delete_iter_data = {}
        iter_apply_bo = self.__iter_apply_bo()
        for row in self.__get_iter_app_info(iter_apply_bo.branch_name):
            if row["pipeline_id"] in delete_iter_data:
                delete_iter_data[row["pipeline_id"]]["repos_str"].append({"repos_path": row['repos_path'],
                                                             "module_name": row["appName"]})
            else:
                delete_iter_data[row["pipeline_id"]] = {"repos_str": [{"repos_path": row['repos_path'],
                                                         "module_name": row["appName"]}],
                                                         "iteration_id": row["pipeline_id"]}
        logger.info(delete_iter_data)
        return delete_iter_data

    def batch_delete_unit_test_pipeline(self):
        for iter_id, data in self._create_delete_data().items():
            self._delete_unit_test_pipeline(data)
            time.sleep(1)
            # {
            #     "repos_str": [{"repos_path": params.row.gitRepo, "module_name": row["appName"]}],
            #     "iteration_id": row["pipeline_id"]
            #}
        # iter_apply_data_dict = self._create_branch_data()
        # for group, data in iter_apply_data_dict.items():
        #     logger.info(data)
        #     self._delete_unit_test_pipeline(data)

    def _create_unit_test_pipeline(self, data):
        http_caller = SpiderHttpCaller()
        http_caller.spider_login()

        res = http_caller.post_request(http_caller.business_dict["iter_apply"], json=json.dumps(data))
        #res = http_caller.post_request(http_caller.business_dict["iter_apply"], data=data)
        logger.info(res.__dict__)

    def batch_create_unit_test_pipeline(self):
        iter_apply_data_dict = self._create_branch_data()
        for group, data in iter_apply_data_dict.items():
            logger.info(data)
            self._create_unit_test_pipeline(data)
            time.sleep(1)
            #break

    def _execute_unit_test_pipeline(self, job_name):
        jenkins_api = self.__jenkins_api.instance()
        jenkins_api.server.build_job(job_name)

    def batch_execute_unit_test_pipeline(self):
        for job_name in self._get_unit_job():
            logger.info(job_name)
            self._execute_unit_test_pipeline(job_name)
            time.sleep(30)

    def bath_merge(self):
        repos_path_list = []
        for key, values in self._create_branch_data().items():
            #logger.info(values["branch_name"])
            for row in values["repos_str"]:
                if row["repos_path"] in repos_path_list:
                    continue
                logger.info(row["repos_path"])
                repos_path_list.append(row["repos_path"])
        me = Merger(values["branch_name"], repos_path_list)
        me.merge_master_to_branch()

    def bath_delete_git_branch(self):
        repos_path_list = []
        for key, values in self._create_branch_data().items():
            #logger.info(values["branch_name"])
            for row in values["repos_str"]:
                if row["repos_path"] in repos_path_list:
                    continue
                logger.info(row["repos_path"])
                repos_path_list.append(row["repos_path"])
        me = Merger(values["branch_name"], repos_path_list)
        # "FPC/calc-offline-fund"
        #me = Merger(values["branch_name"], ["FPC/calc-offline-fund"])
        me.delete_branch()

    def bath_create_git_branch(self):
        repos_path_list = []
        for key, values in self._create_branch_data().items():
            #logger.info(values["branch_name"])
            for row in values["repos_str"]:
                if row["repos_path"] in repos_path_list:
                    continue
                logger.info(row["repos_path"])
                repos_path_list.append(row["repos_path"])
        me = Merger(values["branch_name"], repos_path_list)
        # "FPC/calc-offline-fund"
        #me = Merger(values["branch_name"], ["FPC/calc-offline-fund"])
        me.create_branch()

if __name__ == "__main__":
    bch = BatchUnitTester()
    # 批量创建
    #bch.batch_create_unit_test_pipeline()
    # 批量执行
    bch.batch_execute_unit_test_pipeline()
    # 批量删除
    #bch.batch_delete_unit_test_pipeline()
    # 批量合并
    #bch.bath_merge()
    # 删除git分支
    #bch.bath_delete_git_branch()
    # 创建git分支
    #bch.bath_create_git_branch()