import sys
import os
import jenkins
import json
import requests
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger, JENKINS_INFO, INTERFACE_URL
from test_pipeline.test_pipeline_models.interface_collect_bto import InterfaceCollectBto
from test_pipeline.test_pipeline_utils.create_interface_collect_pipeline import InterfaceCollectionPipelineGenerator
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from test_pipeline.test_pipeline_utils.agent_injection import AgentInjection
from utils.call_external_interface.call_http_api import SpiderHttpCaller
from test_pipeline.test_pipeline_models.test_pipeline_dao.get.app_iterface_test import get_suite_code_id


class InterfaceCollectionPipeline:
    interface_collection_pipeline_name = "interface_collection_pipeline"
    replace_marking = "//interface_collect"
    __jenkins_info = JENKINS_INFO

    def __init__(self):
        self.pipeline_node_dict = {
            "create_pipeline": self.create_pipeline,
            "cover_config": self.cover_config,
            "alert_start_file": self.alert_start_file,
            "env_recover": self.env_recover
        }

    def env_recover(self, data_obj):
        """
        接口统计完毕后 调用环境初始化接口 恢复环境
        :param data_obj:
        :return:
        """
        app_list = InterfaceCollectionPipelineGenerator.get_need_collect_app()
        env_id = get_suite_code_id(data_obj.suite_code)
        http_caller = SpiderHttpCaller()
        http_caller.spider_login()
        params = {"env_name": data_obj.suite_code, "tms_list": ",".join(app_list), "clean_cache": 1, "test_set_id": 0,
                  "env_id": env_id}
        res = http_caller.post_request(http_caller.business_dict["env_init"], params=params)
        logger.info(res)

    def alert_start_file(self, data_obj):
        logger.info("启动文件路径： {}".format(data_obj.start_file_local_path))
        logger.info("应用的jdk版本：{}".format(data_obj.build_jdk_version))
        logger.info("应用名： {}".format(data_obj.app_name))
        logger.info("运行环境： {}".format(data_obj.suite_code))
        logger.info("分支名称： {}".format(data_obj.br_name))

        # war 类型的应用先从 模板里复制到本地目录，再修改， 因为存在相同应用不同分支 串行部署的情况，
        # 需要避免 第二次修改的不是原模板，故而war类型需要先删除再复制模板，jar类型需要 revert上次修改
        # by 帅 20211105
        if data_obj.package_type == "war":
            if os.path.isfile(data_obj.start_file_local_path):
                os.system("rm -rf {}".format(data_obj.start_file_local_path))
            if not os.path.isdir(os.path.dirname(data_obj.start_file_local_path)):
                os.system("mkdir -p {}".format(os.path.dirname(data_obj.start_file_local_path)))
            logger.info("cp {} {}".format(data_obj.tomcat_file_src_path, data_obj.start_file_local_path))
            os.system("cp {} {}".format(data_obj.tomcat_file_src_path, data_obj.start_file_local_path))
        else:
            logger.info("git checkout -- {}".format(data_obj.start_file_local_path))
            os.system("git checkout -- {}".format(data_obj.start_file_local_path))
        agent_injection = AgentInjection(data_obj.start_file_local_path)

        agent_injection.add_interface_collect_agent_params(data_obj.build_jdk_version, data_obj.app_name,
                                                           data_obj.suite_code, data_obj.br_name)

    def cover_config(self, data_obj):
        """
        覆盖配置 从一套环境到另一套环境
        :param data_obj:
        :return:
        """
        try:
            with SSHConnectionManager(data_obj.config_map_ip,
                                      data_obj.config_map_user,
                                      data_obj.config_map_password) as ssh:
                if data_obj.src_config_path.endswith("/"):
                    src_config_path = data_obj.src_config_path
                else:
                    src_config_path = "{}/".format(data_obj.src_config_path)

                if data_obj.deploy_config_path.endswith("/"):
                    deploy_config_path = data_obj.deploy_config_path
                else:
                    deploy_config_path = "{}/".format(data_obj.deploy_config_path)

                cover_cmd= 'rsync -avz --delete {} {}'.format(src_config_path, deploy_config_path)
                logger.info("覆盖配置命令 {}".format(cover_cmd))
                tdin, stdout, stderr = ssh.SSH.exec_command(cover_cmd, bufsize=-1)
                logger.info(stdout.read())
                logger.info(stderr.read())
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)

    def get_publish_pipeline(self, data_obj):
        ig = InterfaceCollectionPipelineGenerator(data_obj.workspace, data_obj.suite_code)
        publish_pipeline_str = ig.create_publish_pipeline()
        return publish_pipeline_str

    def create_pipeline(self, data_obj):
        if len(data_obj.workspace) > 10:
            logger.info("清理工作空间 rm -rf {}/*".format(data_obj.workspace))
            os.system("rm -rf {}/*".format(data_obj.workspace))
        self.server = jenkins.Jenkins(self.__jenkins_info["URL"],
                                      username=self.__jenkins_info["USER"],
                                      password=self.__jenkins_info["PASSWORD"])

        initial_pipeline = self.server.get_job_config(self.interface_collection_pipeline_name)
        publish_pipeline_str = self.get_publish_pipeline(data_obj)
        initial_pipeline_list = initial_pipeline.split(self.replace_marking)
        new_pipeline = self.replace_marking.join([initial_pipeline_list[0], "\n{}\n".format(publish_pipeline_str),
                                   initial_pipeline_list[2]])
        logger.info(new_pipeline)
        self.server.reconfig_job(self.interface_collection_pipeline_name, new_pipeline)
        # 记录解析标记
        with open(data_obj.marking_path, "w+") as f:
            f.write(json.dumps(data_obj.__dict__))
        self.server.build_job(self.interface_collection_pipeline_name,
                              parameters={"params": json.dumps({"suite_code": data_obj.suite_code}).replace(" ", "")})
        return new_pipeline

    def parsing_params(self, params_info):

        return InterfaceCollectBto(params_info)

    def run_step(self, node_name, data_obj, params_info):
        self.pipeline_node_dict[node_name](data_obj)

    def run(self, node_name, params_info):
        """
        运行入口程序
        :param node_name: e.g 'pull_product' ...
        :return:
        """

        data_obj = self.parsing_params(params_info)
        self.run_step(node_name, data_obj, params_info)


if __name__ == "__main__":

    pipeline_params = sys.argv[1]
    input_params = sys.argv[2]
    node_name = sys.argv[3]
    #
    # pipeline_params = '{"workspace":"D:/scm_code/204","marking_path":"D:/scm_code/204/flag.txt"}'
    # input_params = '{"suite_code":"tms18"}'
    # node_name = "create_pipeline"
    logger.info("<流水线默认参数>: {}".format(pipeline_params))
    logger.info("<构建传入的参数>: {}".format(input_params))
    logger.info("<node_name>: {}".format(node_name))

    interface_c_pipeline = InterfaceCollectionPipeline()
    interface_c_pipeline.run(node_name, dict(json.loads(pipeline_params), **json.loads(input_params)))
