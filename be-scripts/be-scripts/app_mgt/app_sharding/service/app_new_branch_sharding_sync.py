import datetime

from app_mgt.app_sharding.sharding_dao.sharding_dao_impl import add_sharding_rule, sync_sharding_ds, \
    sync_sharding_binding_table, sync_sharding_table_config, query_sharding_rule, prepare_rules_sharding_algorithms
from app_mgt.app_sharding.sharding_model.app_mgt_sharding_config import AppMgtShardingConfig
from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_sharding_algorithms_config_model import \
    DbMgtAppShardingRuleShardingAlgorithmsConfig
from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_sharding_algorithms_props_model import \
    DbMgtAppShardingRuleShardingAlgorithmsProp
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.get.mysql import app_info
from settings import logger


class AppNewBranchShardingSyncService:
    @classmethod
    def sync_app_sharding_config_for_new_branch(cls, module_name, branch_name, archive_branch_name):
        try:
            logger.info("查询{},{} sharding_config".format(module_name, archive_branch_name))
            with DBConnectionManagerForSqlalchemy() as db:
                archive_branch_config = db.session.query(AppMgtShardingConfig).filter(
                    AppMgtShardingConfig.module_name == module_name,
                    AppMgtShardingConfig.branch_name == archive_branch_name).first()
                if not archive_branch_config:
                    logger.warn(
                        "应用:{}新开分支:{}同步sharding_config失败,原因:{}".format(module_name, branch_name,
                                                                                   "没有历史sharding 配置"))
                    return
                sharding_agent_param = archive_branch_config.sharding_agent_param
                sharding_agent_param["branch"]= branch_name
                branch_config = AppMgtShardingConfig(module_name=module_name, branch_name=branch_name,
                                                     sharding_logic_db_name=archive_branch_config.sharding_logic_db_name,
                                                     sharding_agent_module_name=archive_branch_config.sharding_agent_module_name,
                                                     sharding_agent_branch_name=archive_branch_config.sharding_agent_branch_name,
                                                     sharding_agent_param=sharding_agent_param,
                                                     create_time=datetime.datetime.now(),
                                                     create_user="pa",
                                                     stamp=0)
                db.session.add(branch_config)
                db.session.commit()
        except Exception as e:
            logger.error("应用:{}新开分支:{}同步sharding_config失败,原因:{}".format(module_name, branch_name, e))
            return False

    @classmethod
    def sync_sharding_config_for_new_branch(cls, module_name, branch_name):
        try:
            cls.sync_app_sharding_config_for_new_branch(module_name, branch_name,
                                                        app_info.get_latest_archive_version(module_name))
            # 老的sharding逻辑下线 202050318 by fwm
            # archive_branch_name = app_info.get_latest_and_has_sharding_archive_version(module_name)
            # logger.info("应用:{},新开分支：{},最新归档分支:{}".format(module_name, branch_name, archive_branch_name))
            # if not archive_branch_name:
            #     logger.info("应用:{},最新归档分支:{} 没有sharding_config".format(module_name, archive_branch_name))
            #     return True
            # archive_rule_config_id = cls.get_archive_rule_config_id(module_name, archive_branch_name)
            # if not archive_rule_config_id:
            #     logger.info("应用:{},最新归档分支:{} 没有Sharding_RuleConfig".format(module_name, archive_branch_name))
            #     return True
            #     # 同步数据源
            # cls.syncDs(module_name, branch_name, archive_branch_name)
            # add_sharding_rule(module_name, branch_name, archive_branch_name)
            # rule_config_id = cls.get_archive_rule_config_id(module_name, branch_name)
            # cls.sync_sharding_algorithms_config(module_name, rule_config_id, archive_branch_name)
            # cls.syncTableConfig(rule_config_id, archive_rule_config_id)
            # cls.syncBindTable(module_name, rule_config_id, archive_branch_name)
        except Exception as ex:
            logger.error(ex)

    @classmethod
    def syncDs(cls, module_name, branch_name, archive_branch_name):
        sync_sharding_ds(module_name, branch_name, archive_branch_name)

    @classmethod
    def syncTableConfig(cls, rule_config_id, archive_rule_config_id):
        sync_sharding_table_config(rule_config_id, archive_rule_config_id)

    @classmethod
    def syncBindTable(cls, module_name, rule_config_id, archive_branch_name):
        sync_sharding_binding_table(module_name, rule_config_id, archive_branch_name)

    @classmethod
    def sync_sharding_algorithms_config(cls, module_name, rule_config_id, archive_branch_name):
        algorithms = prepare_rules_sharding_algorithms(module_name, archive_branch_name, None)
        with DBConnectionManagerForSqlalchemy() as db:
            for algorithm in algorithms:
                algorithmConfig = DbMgtAppShardingRuleShardingAlgorithmsConfig(
                    rule_config_id=rule_config_id,
                    sharding_algorithm_name=algorithm.sharding_algorithm_name,
                    type=algorithm.type,
                    create_time=algorithm.create_time,
                    update_time=algorithm.update_time,
                    create_user=algorithm.create_user,
                    update_user=algorithm.update_user)
                db.session.add(algorithmConfig)
                db.session.commit()
                id = algorithmConfig.id
                logger.info("id:{}".format(id))
                props = []
                for prop in algorithm.props():
                    dbAlgorithmsProp = DbMgtAppShardingRuleShardingAlgorithmsProp(
                        algorithms_id=id,
                        props_key=prop.props_key,
                        props_value=prop.props_value,
                        create_time=algorithm.create_time,
                        update_time=algorithm.update_time,
                        create_user=algorithm.create_user,
                        update_user=algorithm.update_user
                    )
                    props.append(dbAlgorithmsProp)
                db.session.add_all(props)
                db.session.commit()

    @classmethod
    def get_archive_rule_config_id(cls, module_name, archive_branch_name):
        seq_all = query_sharding_rule(module_name, archive_branch_name)
        if seq_all:
            return seq_all[0].id
        return None


if __name__ == '__main__':
    data = AppNewBranchShardingSyncService.sync_sharding_config_for_new_branch("acc-center-server", "test1")
