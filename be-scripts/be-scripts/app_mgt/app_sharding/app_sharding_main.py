import os
import sys

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from typing import Dict
import datetime
import itertools
import logging
import yaml
from app_mgt.app_sharding.sharding_dao.sharding_dao_impl import prepare_sharding_ds, \
    prepare_rules_sharding_binding_table, prepare_rules_sharding_tables, prepare_rules_sharding_algorithms, \
    get_app_env_db_info
from settings import logger as log, SHARDING, MOCK_SYSTEM_INTERFACE
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import ssh_path_whether_exist, \
    exec_local_cmd_by_sshpass_tomcat, push_local_to_other_rsync
from urllib.parse import urlparse, parse_qs


# 组装应用分片规则 并部署到指定环境
class AppShardingMain:
    # 模块名（应用名）
    module_name: str
    # 分支
    branch: str
    # 环境
    env: str
    # 批次号每次执行时自动生成
    batch_no: str
    # 当前sharding应用配置根目录
    work_dir: str

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    # /data/app/sharding_conf_base
    def prepare_sharding_conf_dir(self):
        # 创建当前批次/data/app/sharding_{batch_no}/{module_name}
        self.work_dir = "/data/app/sharding_{}/{}".format(self.batch_no, self.module_name)
        os.system("mkdir -p {}".format(self.work_dir))
        # cp -r /data/app/sharding_conf_base/** /data/app/sharding_{batch_no}/{module_name}/
        os.system("cp -r {} {}".format(SHARDING["base_conf"],
                                       self.work_dir))
        log.info("work_dir:{}".format(self.work_dir))
        log.info(self.get_sharding_ds_and_rule_file())

    def get_sharding_ds_and_rule_file(self):
        dataSources = ""
        rules = ""
        for root, dirs, files in os.walk(self.work_dir):
            for file in files:
                try:
                    if file == "dataSources":
                        dataSources = root + os.sep + file
                    elif file == "rules":
                        rules = root + os.sep + file
                except Exception as e:
                    logging.info(e)
        return dataSources, rules

    def get_ds_conf(self):
        db_info_list = get_app_env_db_info(self.module_name, self.env)
        # db_info_list = get_app_env_db_info("acc-center-server", self.env)
        log.info("app:{},branch:{},env:{}".format(self.module_name, self.branch, self.env))
        ds_config_seq = prepare_sharding_ds(self.module_name, self.branch, self.env)
        ds_config_seq = sorted(ds_config_seq, key=lambda x: x.ds_name)
        ds_dict = {}
        for key, group in itertools.groupby(ds_config_seq,
                                            key=lambda x: x.ds_name):
            ds_props: Dict[str, str] = {}
            db_info, group_list = self.get_db(db_info_list, group)
            for props in group_list:
                value = self.get_type_value(props.ds_props_value)
                if props.ds_props_key == "username":
                    value = self.replace_username(props.ds_props_value, db_info)
                if props.ds_props_key == "url":
                    value = self.replace_url(props.ds_props_value, db_info)
                if props.ds_props_key == "rawJdbcUrl":
                    value = self.replace_url(props.ds_props_value, db_info)
                if props.ds_props_key == "password":
                    value = self.replace_password(props.ds_props_value, db_info)
                ds_props[props.ds_props_key] = value
            # key：数据源名 val
            ds_dict[key] = ds_props
        log.info(ds_dict)
        return ds_dict

    # 持久化到本地缓存目录
    def get_type_value(self, value):
        if value.isdigit() or value.isnumeric():
            return int(value)
        if value.lower() == "false":
            return False
        elif value.lower() == "true":
            return True
        else:
            try:
                return round(float(value))
            except ValueError:
                return value

    # 持久化到本地缓存目录
    def persist_ds_conf(self):
        yaml_dir = self.get_sharding_ds_and_rule_file()[0]
        yaml_dict = self.get_ds_conf()
        with open(yaml_dir, 'w', encoding='utf-8', ) as f:
            yaml.dump(yaml_dict, f, encoding='utf-8', default_flow_style=False, Dumper=NoQuoteDumper)

    def publish_app_sharding_conf(self):
        target_ip = MOCK_SYSTEM_INTERFACE['agent_deploy_path_ip']
        deploy_path: str = SHARDING["sharding_agent_deploy_path"]
        deploy_path = deploy_path.format(self.env, self.module_name)
        local_path = self.work_dir
        log.info("target_ip:{}".format(target_ip))
        log.info("deploy_path:{}".format(deploy_path))
        chk_status, chk_msg = ssh_path_whether_exist(target_ip, deploy_path)
        log.info("chk_status:{}".format(chk_status))
        log.info("chk_msg:{}".format(chk_msg))
        if not chk_status:
            cmd = "mkdir -p {}".format(deploy_path)
            exec_local_cmd_by_sshpass_tomcat(target_ip, cmd)
        if len(local_path) < 10:
            raise Exception("local_path :{}".format(local_path))
        push_local_to_other_rsync(target_ip, local_path, deploy_path, is_delete=True)

    def get_rules_conf(self):
        sharding = {}

        binding_table_seq = self.handle_binding_tables()
        sharding["bindingTables"] = binding_table_seq

        tables_seq = self.handle_tables()
        sharding["tables"] = tables_seq

        algorithms_seq = self.handle_algorithms()
        sharding["shardingAlgorithms"] = algorithms_seq

        rules = [{"!SHARDING": sharding}]
        return rules

    def handle_algorithms(self):
        algorithms = prepare_rules_sharding_algorithms(self.module_name, self.branch, self.env)
        algorithms_seq = []
        for algorithm in algorithms:
            algorithms_item = {}
            algorithms_prop_dict = {}
            for prop in algorithm.props():
                algorithms_prop_dict[prop.props_key] = prop.props_value
            algorithms_item[algorithm.sharding_algorithm_name] = {"props": algorithms_prop_dict, "type": algorithm.type}
            algorithms_seq.append(algorithms_item)
        return algorithms_seq

    def handle_tables(self):
        tables = prepare_rules_sharding_tables(self.module_name, self.branch, self.env)
        tables_seq = []
        for table in tables:
            table_item = {"actualDataNodes": table.actual_data_nodes, "logicTable": table.logic_table}
            table_strategy_props = {'shardingAlgorithmName': table.table_sharding_algorithm_name,
                                    'shardingColumn': table.table_sharding_column}
            table_item["tableStrategy"] = {table.table_strategy: table_strategy_props}
            database_strategy_props = {'shardingAlgorithmName': table.database_sharding_algorithm_name,
                                       'shardingColumn': table.database_sharding_column}
            table_item["databaseStrategy"] = {table.database_strategy: database_strategy_props}
            tables_seq.append({table.table_name: table_item})
        return tables_seq

    def handle_binding_tables(self):
        binding_tables = prepare_rules_sharding_binding_table(self.module_name, self.branch, self.env)
        binding_table_seq = []
        for binding_table in binding_tables:
            binding_table_seq.append(str(binding_table.binding_table))
        return binding_table_seq

    def persist_rules_conf(self):
        yaml_dir = self.get_sharding_ds_and_rule_file()[1]
        yaml_dict = self.get_rules_conf()
        with open(yaml_dir, 'w', encoding='utf-8', ) as f:
            yaml.dump(yaml_dict, f, encoding='utf-8', default_flow_style=False, Dumper=NoQuoteDumper)

        new_lines = []
        with open(yaml_dir, 'r', encoding='utf-8', ) as f:
            lines = []
            lines.extend(f.readlines())
            new_lines.append(lines[0].replace("- '!SHARDING':", "  - !SHARDING"))
            start_replace = False
            for line in lines:
                if "SHARDING" in line:
                    pass
                elif "shardingAlgorithms" in line:
                    new_lines.append(line)
                    start_replace = True
                elif start_replace:
                    new_lines.append(line.replace("- ", "  "))
                elif not start_replace:
                    new_lines.append(line)

        with open(yaml_dir, 'w', encoding='utf-8', ) as f:
            f.writelines(new_lines)

    def replace_username(self, old_username, db_info):
        log.info("old_username:{}".format(old_username))
        log.info("new_username:{}".format(db_info.get("username")))
        return db_info.get("username")

    def replace_url(self, old_url: str, db_info):
        log.info("old_url:{}".format(old_url))
        parsed_url = urlparse(old_url)
        # 获取 IP 和端口号
        path = parsed_url.path
        port = path.split("/")[-2].split(":")[1]
        ip = path.split("/")[-2].split(":")[0]
        # 获取数据库名称
        db_name = path.split("/")[-1]
        new_url = old_url.replace(ip, db_info.get("db_srv_hosts"))
        new_url = new_url.replace(str(port), str(db_info.get("db_srv_port")))
        new_url = new_url.replace(db_name, db_info.get("db_name"))
        log.info("new_url:{}".format(new_url))
        return new_url

    def replace_password(self, old_pwd, db_info):
        log.info("old_pwd:{}".format(old_pwd))
        log.info("new_pwd:{}".format(db_info.get("password")))
        return db_info.get("password")

    def get_db(self, db_info_list, group):
        group_list = []
        db_result = None
        for props in group:
            if props.ds_props_key == "url":
                parsed_url = urlparse(props.ds_props_value)
                # 获取 IP 和端口号
                path = parsed_url.path
                port = path.split("/")[-2].split(":")[1]
                ip = path.split("/")[-2].split(":")[0]
                # 获取数据库名称
                db_name = path.split("/")[-1]
                for db in db_info_list:
                    if db_name.endswith(db.get("db_info_suffix_name")):
                        db_result = db
            group_list.append(props)
        return db_result, group_list


class NoQuoteDumper(yaml.Dumper):
    """
    自定义的 YAML Dumper 类，用于禁止在字符串值中添加引号
    """

    def represent_scalar(self, tag, value, style=None):
        if isinstance(value, str):
            style = ""  # 将字符串样式设置为空字符串，即不使用引号
        return super().represent_scalar(tag, value, style)


if __name__ == '__main__':
    app_sharding_main = AppShardingMain(module_name="howbuy-qa-info-remote", branch="test-agent", env="it02",
                                        batch_no=datetime.datetime.now().strftime('%Y%m%d%H%M%S'))
    app_sharding_main.prepare_sharding_conf_dir()
    app_sharding_main.persist_ds_conf()
    app_sharding_main.persist_rules_conf()
    app_sharding_main.publish_app_sharding_conf()
