# coding: utf-8
from sqlalchemy import Column, String, TIMESTAMP
from sqlalchemy.dialects.mysql import BIGIN<PERSON>
from sqlalchemy.ext.declarative import declarative_base

from utils.public_utils import auto_str
from typing import Any
Base = declarative_base()
metadata = Base.metadata


# @auto_str
# class DbMgtAppShardingEnvDbConfig(Base):
#     __tablename__ = 'db_mgt_app_sharding_env_db_config'
#     __table_args__ = {'comment': '应用sharding环境逻辑数据源配置'}
#
#     def __init__(self, *args: Any, **kwargs: Any) -> None:
#         super().__init__(*args, **kwargs)
#
#     id = Column(BIGINT(11), primary_key=True)
#     module_name = Column(String(50), nullable=False, comment='应用名')
#     branch = Column(String(50), nullable=False, comment='分支')
#     suite_code = Column(String(20), nullable=False, comment='环境')
#     db = Column(String(50), nullable=False, comment='逻辑数据库')
#     suffix = Column(String(50), nullable=False, comment='后缀')
#     create_user = Column(String(20), nullable=False, comment='创建人')
#     create_time = Column(TIMESTAMP, nullable=False, comment='创建时间')
#     update_user = Column(String(20), nullable=False, comment='更新人')
#     update_time = Column(TIMESTAMP, nullable=False, comment='更新时间')
