# coding: utf-8
from sqlalchemy import Column, String, TIMES<PERSON>MP
from sqlalchemy.dialects.mysql import BIGINT, LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_sharding_algorithms_props_model import \
    DbMgtAppShardingRuleShardingAlgorithmsProp
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from utils.public_utils import auto_str
from typing import Any

Base = declarative_base()
metadata = Base.metadata


@auto_str
class DbMgtAppShardingRuleShardingAlgorithmsConfig(Base):
    __tablename__ = 'db_mgt_app_sharding_rule_sharding_algorithms_config'
    __table_args__ = {'comment': '应用sharding规则-分片算法配置'}

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True)
    rule_config_id = Column(BIGINT(11), nullable=False, comment='规则ID')
    sharding_algorithm_name = Column(LONGTEXT, nullable=False, comment='算法名')
    type = Column(String(50), nullable=False, comment='算法类型')
    create_time = Column(TIMESTAMP, nullable=False, comment='创建时间')
    update_time = Column(TIMESTAMP, comment='更新时间')
    create_user = Column(String(20), nullable=False, comment='创建人')
    update_user = Column(String(20), comment='更新人')

    def props(self):
        props = []
        with DBConnectionManagerForSqlalchemy() as db:
            props = db.session.query(DbMgtAppShardingRuleShardingAlgorithmsProp) \
                .filter(DbMgtAppShardingRuleShardingAlgorithmsProp.algorithms_id == self.id).all()
        return props
