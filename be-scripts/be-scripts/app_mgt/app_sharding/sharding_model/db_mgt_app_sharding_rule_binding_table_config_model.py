# coding: utf-8
from sqlalchemy import Column, String, TIMESTAMP, Text
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

from utils.public_utils import auto_str
from typing import Any
Base = declarative_base()
metadata = Base.metadata


@auto_str
class DbMgtAppShardingRuleBindingTableConfig(Base):
    __tablename__ = 'db_mgt_app_sharding_rule_binding_table_config'
    __table_args__ = {'comment': '应用sharding规则-绑定表配置'}

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True)
    rule_config_id = Column(BIGINT(11), nullable=False, comment='规则ID')
    binding_table = Column(String(2000), nullable=False, comment='绑定表')
    create_time = Column(TIMESTAMP, nullable=False, comment='创建时间')
    update_time = Column(TIMESTAMP, comment='更新时间')
    create_user = Column(String(20), nullable=False, comment='创建人')
    update_user = Column(String(20), comment='更新人')
