# coding: utf-8
from sqlalchemy import Column, String
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

from utils.public_utils import auto_str
from typing import Any
Base = declarative_base()
metadata = Base.metadata


@auto_str
class DbMgtAppShardingRule(Base):
    __tablename__ = 'db_mgt_app_sharding_rule'
    __table_args__ = {'comment': '应用sharding规则表'}

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True)
    module_name = Column(String(50), nullable=False)
    branch = Column(String(50), nullable=False)
    rule_config = Column(String(20), nullable=False, comment='规则类型 SHARDING')
