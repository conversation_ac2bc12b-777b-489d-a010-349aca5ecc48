# coding: utf-8
from sqlalchemy import Column, Index, JSON, String, TIMESTAMP
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class AppMgtShardingConfig(Base):
    __tablename__ = 'app_mgt_sharding_config'
    __table_args__ = (
        Index('idx_unique', 'module_name', 'branch_name', 'sharding_agent_module_name', 'sharding_agent_branch_name', unique=True),
        {'comment': '应用分片配置表'}
    )

    id = Column(BIGINT(11), primary_key=True)
    module_name = Column(String(100), nullable=False, comment='应用名')
    branch_name = Column(String(100), nullable=False, comment='分支')
    sharding_logic_db_name = Column(String(100), comment='分片逻辑库名')
    sharding_agent_module_name = Column(String(100), nullable=False, comment='品控分片agent名称')
    sharding_agent_branch_name = Column(String(100), nullable=False, comment='品控分片agent分支')
    sharding_agent_param = Column(JSON, nullable=False, comment='品控分片agent参数')
    create_user = Column(String(20), nullable=False)
    create_time = Column(TIMESTAMP, nullable=False)
    update_user = Column(String(20))
    update_time = Column(TIMESTAMP)
    stamp = Column(TINYINT(2), nullable=False)
