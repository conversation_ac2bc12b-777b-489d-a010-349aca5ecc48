# coding: utf-8
from typing import Any

from sqlalchemy import Column, String, TIMESTAMP
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

from utils.public_utils import auto_str

Base = declarative_base()
metadata = Base.metadata


@auto_str
class DbMgtAppShardingDatasource(Base):
    __tablename__ = 'db_mgt_app_sharding_datasource'
    __table_args__ = {'comment': '应用sharding数据源表'}

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True, comment='主键')
    module_name = Column(String(50), nullable=False, comment='应用名')
    branch = Column(String(100), nullable=False, comment='分支')
    ds_name = Column(String(50), nullable=False, comment='数据源名')
    ds_props_key = Column(String(100), nullable=False, comment='数据源配置key名')
    ds_props_value = Column(String(255), nullable=False, comment='数据源配置属性值')
    create_user = Column(String(20), nullable=False, comment='创建人')
    create_time = Column(TIMESTAMP, nullable=False, comment='创建时间')
    update_user = Column(String(20), comment='更新人')
    update_time = Column(TIMESTAMP, comment='更新时间')
