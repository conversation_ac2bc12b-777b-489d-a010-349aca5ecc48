# coding: utf-8
from typing import Any

from sqlalchemy import Column, String, TIMESTAMP
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

from utils.public_utils import auto_str

Base = declarative_base()
metadata = Base.metadata


@auto_str
class DbMgtAppShardingRuleTableConfig(Base):
    __tablename__ = 'db_mgt_app_sharding_rule_table_config'
    __table_args__ = {'comment': '应用sharding规则-表配置'}

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True)
    rule_config_id = Column(BIGINT(11), nullable=False, comment='规则ID')
    table_name = Column(String(50), nullable=False, comment='表名')
    logic_table = Column(String(100), nullable=False, comment='逻辑表')
    actual_data_nodes = Column(String(255), nullable=False, comment='真实节点')
    database_strategy = Column(String(50), comment='分库策略类型')
    database_sharding_algorithm_name = Column(String(50), comment='分库算法别名')
    database_sharding_column = Column(String(50), comment='分库分片键')
    table_strategy = Column(String(50), comment='分表策略类型')
    table_sharding_algorithm_name = Column(String(50), comment='分表算法别名')
    table_sharding_column = Column(String(50), comment='分表分片键')
    create_time = Column(TIMESTAMP, nullable=False, comment='创建人')
    update_time = Column(TIMESTAMP, comment='更新时间')
    create_user = Column(String(20), nullable=False, comment='创建人')
    update_user = Column(String(20), comment='更新人')
