# coding: utf-8
from sqlalchemy import Column, DateTime, String
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base
from typing import Any

from sqlalchemy.orm import relationship

from utils.public_utils import auto_str

Base = declarative_base()
metadata = Base.metadata


@auto_str
class DbMgtAppShardingRuleShardingAlgorithmsProp(Base):
    __tablename__ = 'db_mgt_app_sharding_rule_sharding_algorithms_props'
    __table_args__ = {'comment': '应用sharding规则-分片算法配置扩展属性'}

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)

    id = Column(BIGINT(11), primary_key=True)
    algorithms_id = Column(BIGINT(11), nullable=False)
    props_key = Column(String(100), nullable=False)
    props_value = Column(String(255), nullable=False)
    create_user = Column(String(50), nullable=False)
    create_time = Column(DateTime, nullable=False)
    update_user = Column(String(50))
    update_time = Column(DateTime)

