from typing import List

from sqlalchemy import text

from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_datasource_model import DbMgtAppShardingDatasource
from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_binding_table_config_model import \
    DbMgtAppShardingRuleBindingTableConfig
from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_model import DbMgtAppShardingRule
from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_sharding_algorithms_config_model import \
    DbMgtAppShardingRuleShardingAlgorithmsConfig
from app_mgt.app_sharding.sharding_model.db_mgt_app_sharding_rule_table_config_model import \
    DbMgtAppShardingRuleTableConfig
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.get.mysql import db_mgt_bind_view


def sync_sharding_ds(module_name, branch, archive_branch_name):
    with DBConnectionManagerForSqlalchemy() as db:
        sql_str = '''
             INSERT INTO `db_mgt_app_sharding_datasource` (
	                 `module_name`,
	                 `branch`,
	                 `ds_name`,
	                 `ds_props_key`,
	                 `ds_props_value`,
	                 `create_user`,
	                 `create_time`,
	                 `update_user`,
	                 `update_time` 
                      ) SELECT
                      `module_name`,
                      '{branch}',
                      `ds_name`,
                      `ds_props_key`,
                      `ds_props_value`,
                      `create_user`,
                      `create_time`,
                      `update_user`,
                      `update_time` 
                      FROM
                      	db_mgt_app_sharding_datasource 
                      WHERE
                      	module_name = '{module_name}' 
                      	AND branch = '{archive_branch_name}'
        '''.format(branch=branch, module_name=module_name, archive_branch_name=archive_branch_name)
        db.session.execute(text(sql_str))
        db.session.commit()


def sync_app_sharding_config(module_name, branch, archive_branch_name):
    with DBConnectionManagerForSqlalchemy() as db:
        sql_str = '''
             INSERT INTO `app_mgt_sharding_config` (
	                 `module_name`,
	                 `branch_name`,
	                 `sharding_logic_db_name`,
	                 `sharding_agent_module_name`,
	                 `sharding_agent_branch_name`,
	                 `sharding_agent_param`,
	                 `create_user`,
	                 `create_time`,
	                 `update_user`,
	                 `update_time`,
	                  `stamp`
                      ) SELECT
                      `module_name`,
                      '{branch}',
                      `sharding_logic_db_name`,
	                 `sharding_agent_module_name`,
	                 `sharding_agent_branch_name`,
	                 `sharding_agent_param`,
                      `create_user`,
                      `create_time`,
                      `update_user`,
                      `update_time`,
                       `stamp`
                      FROM
                      	app_mgt_sharding_config 
                      WHERE
                      	module_name = '{module_name}' 
                      	AND branch_name = '{archive_branch_name}'
        '''.format(branch=branch, module_name=module_name, archive_branch_name=archive_branch_name)
        db.session.execute(text(sql_str))
        db.session.commit()


def sync_sharding_binding_table(module_name, rule_config_id, archive_branch_name):
    with DBConnectionManagerForSqlalchemy() as db:
        sql_str = '''
            INSERT INTO db_mgt_app_sharding_rule_binding_table_config (
                   `rule_config_id`,
                   `binding_table`,
                   `create_time`,
                   `update_time`,
                   `create_user`,
                   `update_user` 
                   ) SELECT
                   {rule_config_id},
                   config.`binding_table`,
                   config.`create_time`,
                   config.`update_time`,
                   config.`create_user`,
                   config.`update_user` 
                   FROM
                   	db_mgt_app_sharding_rule_binding_table_config config
                   	JOIN db_mgt_app_sharding_rule rule ON rule.id = config.rule_config_id 
                   WHERE
                   	rule.module_name = '{module_name}' 
                   	AND rule.branch = '{archive_branch_name}'
                           '''.format(rule_config_id=rule_config_id, module_name=module_name,
                                      archive_branch_name=archive_branch_name)
        db.session.execute(text(sql_str))
        db.session.commit()


def sync_sharding_table_config(rule_config_id, archive_rule_config_id):
    with DBConnectionManagerForSqlalchemy() as db:
        sql_str = '''
                INSERT INTO `db_mgt_app_sharding_rule_table_config` (
                  `rule_config_id`,
                	`table_name`,
                	`logic_table`,
                	`actual_data_nodes`,
                	`database_strategy`,
                	`database_sharding_algorithm_name`,
                	`database_sharding_column`,
                	`table_strategy`,
                	`table_sharding_algorithm_name`,
                	`table_sharding_column`,
                	`create_time`,
                	`update_time`,
                	`create_user`,
                	`update_user` 
                ) 
                SELECT
                {rule_config_id},
                `table_name`,
                `logic_table`,
                `actual_data_nodes`,
                `database_strategy`,
                `database_sharding_algorithm_name`,
                `database_sharding_column`,
                `table_strategy`,
                `table_sharding_algorithm_name`,
                `table_sharding_column`,
                `create_time`,
                `update_time`,
                `create_user`,
                `update_user` 
                FROM
                	db_mgt_app_sharding_rule_table_config 
                WHERE
                	rule_config_id = {archive_rule_config_id}
                                           '''.format(rule_config_id=rule_config_id,
                                                      archive_rule_config_id=archive_rule_config_id)
        db.session.execute(text(sql_str))
        db.session.commit()


def add_sharding_rule(module_name, branch, archive_branch_name):
    with DBConnectionManagerForSqlalchemy() as db:
        sql_str = '''
                        INSERT INTO `db_mgt_app_sharding_rule` (
                        module_name,
                        branch,
                        rule_config) select module_name,'{branch}',rule_config from db_mgt_app_sharding_rule where 
                         	module_name = '{module_name}' 
                       	AND branch = '{archive_branch_name}'
                        '''.format(branch=branch, module_name=module_name,
                                   archive_branch_name=archive_branch_name)
        db.session.execute(text(sql_str))
        db.session.commit()


def query_sharding_rule(module_name, branch):
    with DBConnectionManagerForSqlalchemy() as db:
        data_seq = db.session.query(DbMgtAppShardingRule).filter(
            DbMgtAppShardingRule.module_name == module_name,
            DbMgtAppShardingRule.branch == branch).all()
        return data_seq


# 查询数据源配置
def prepare_sharding_ds(module_name, branch, env) -> List[DbMgtAppShardingDatasource]:
    with DBConnectionManagerForSqlalchemy() as db:
        data_seq = db.session.query(DbMgtAppShardingDatasource).filter(
            DbMgtAppShardingDatasource.module_name == module_name,
            DbMgtAppShardingDatasource.branch == branch).all()
        return data_seq


# 查询分片规则绑定表配置
def prepare_rules_sharding_binding_table(module_name, branch, env) -> List[DbMgtAppShardingRuleBindingTableConfig]:
    with DBConnectionManagerForSqlalchemy() as db:
        data_seq = db.session.query(DbMgtAppShardingRuleBindingTableConfig).join(DbMgtAppShardingRule,
                                                                                 DbMgtAppShardingRuleBindingTableConfig.rule_config_id == DbMgtAppShardingRule.id).filter(
            DbMgtAppShardingRule.module_name == module_name,
            DbMgtAppShardingRule.branch == branch).all()
        return data_seq


# 查询分片表配置
def prepare_rules_sharding_tables(module_name, branch, env) -> List[DbMgtAppShardingRuleTableConfig]:
    with DBConnectionManagerForSqlalchemy() as db:
        data_seq = db.session.query(DbMgtAppShardingRuleTableConfig).join(DbMgtAppShardingRule,
                                                                          DbMgtAppShardingRuleTableConfig.rule_config_id == DbMgtAppShardingRule.id).filter(
            DbMgtAppShardingRule.module_name == module_name,
            DbMgtAppShardingRule.branch == branch).all()
        return data_seq


# 查询分片算法配置
def prepare_rules_sharding_algorithms(module_name, branch, env) -> List[DbMgtAppShardingRuleShardingAlgorithmsConfig]:
    with DBConnectionManagerForSqlalchemy() as db:
        data_seq = db.session.query(DbMgtAppShardingRuleShardingAlgorithmsConfig).join(DbMgtAppShardingRule,
                                                                                       DbMgtAppShardingRuleShardingAlgorithmsConfig.rule_config_id == DbMgtAppShardingRule.id).filter(
            DbMgtAppShardingRule.module_name == module_name,
            DbMgtAppShardingRule.branch == branch).all()
        return data_seq


def get_app_env_db_info(module_name, suite_code) -> List[DbMgtAppShardingRuleShardingAlgorithmsConfig]:
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
    """
    # with DBConnectionManagerForSqlalchemy() as db:
    #     sql = '''
    #         SELECT DISTINCT dob.db_info_id, i.db_info_suffix_name,
    #            g.db_group_name, s.db_srv_type, s.db_srv_hosts, s.db_srv_port, s.db_srv_username,
    #            s.db_srv_password,s.db_srv_name, i.db_info_username, i.db_info_password
    #         FROM  db_mgt_app_bind b
    #         INNER JOIN db_mgt_domain_bind dob ON b.db_domain_id = dob.db_domain_id
    #         INNER JOIN db_mgt_info i ON dob.db_info_id = i.id
    #         INNER JOIN db_mgt_group g ON i.db_group_id = g.id
    #         INNER JOIN db_mgt_srv s ON i.db_srv_id = s.id
    #         WHERE b.app_module_name = "{}"
    #    '''.format(module_name)
    #
    #     db_info_list = []
    #
    #     for db_info in db.session.execute(text(sql)).fetchall():
    #         db_info = dict(zip(["db_info_id", "db_info_suffix_name",
    #                             "db_group_name", "db_srv_type", "db_srv_hosts", "db_srv_port", "db_srv_username",
    #                             "db_srv_password", "db_srv_name", "db_info_username", "db_info_password"], db_info))
    #         env_db_info = db_mgt_bind_view.get(suite_code=suite_code,
    #                                            db_info_id=db_info.get("db_info_id"))[0]
    #         db_info["db_name"] = env_db_info.get("suite_db_name")
    #         db_info["username"] = env_db_info.get("username")
    #         db_info["password"] = env_db_info.get("password")
    #         db_info_list.append(db_info)
    db_info_list = []
    env_db_info_list = db_mgt_bind_view.get(suite_code=suite_code, module_name=module_name)
    for env_db_info in env_db_info_list:
        db_info = {'db_info_id': env_db_info.get("db_info_id"), "db_info_suffix_name": env_db_info.get("db_name"),
                   'db_group_name': env_db_info.get("db_group_name"), 'db_srv_type': env_db_info.get("db_srv_type"),
                   'db_srv_hosts': env_db_info.get("db_srv_hosts"), 'db_srv_port': env_db_info.get("db_srv_port"),
                   'db_srv_username': env_db_info.get("db_srv_username"),
                   'db_info_username': env_db_info.get("username"),
                   'db_info_password': env_db_info.get("password"), 'db_name': env_db_info.get("suite_db_name"),
                   'username': env_db_info.get("username"), 'password': env_db_info.get("password")}
        db_info_list.append(db_info)
    return db_info_list


if __name__ == '__main__':
    db_info_list = get_app_env_db_info("acc-center-server", "it10")
    print(db_info_list)
