from peewee import *
from dao.base_model import BaseModel


class AppBuildModel(BaseModel):

    app_id = IntegerField(verbose_name='应用ID')
    module_name = CharField(verbose_name='模块名', max_length=100)
    module_code = CharField(verbose_name='模块编码', max_length=100)
    module_version = CharField(verbose_name='模块版本', max_length=100)
    package_type = CharField(verbose_name='包类型：pom、war、jar、tar', max_length=100)
    package_name = CharField(verbose_name='包名', max_length=255)
    package_full = BooleanField(verbose_name='是否完整包')
    build_jdk_version = CharField(verbose_name='编译JDK版本', max_length=100)
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')
    stamp = IntegerField(verbose_name='版本')
    need_mock = BooleanField(verbose_name='是否存在mock类型')
    mock_build_cmd = CharField(verbose_name='mock编译命令', max_length=100)
    build_cmd = CharField(verbose_name='编译命令', max_length=128)

    class Meta:
        table_name = 'app_mgt_app_build'


class MiniVersionUpgradeModule(BaseModel):
    module_name = CharField(verbose_name='标准统一模块名', max_length=255)

    class Meta:
        db_table = 'app_mgt_mini_version_upgrade_module'
        verbose_name = '需要小版本更新的模块'


class AppMgtAppModule(BaseModel):
    app_id = IntegerField(verbose_name='应用ID')
    module_name = CharField(verbose_name='模块名', max_length=100)
    module_code = CharField(verbose_name='模块编码', max_length=100)
    module_status = IntegerField(verbose_name='模块状态')
    module_desc = CharField(verbose_name='模块说明', max_length=255)
    module_svn_path = CharField(verbose_name='模块SVN路径', max_length=999)
    module_jdk_version = CharField(verbose_name='指定JDK版本', max_length=100)
    need_online = IntegerField(verbose_name='是否需要上线')
    need_check = IntegerField(verbose_name='是否需要维护')
    app_port = IntegerField(verbose_name='应用端口')
    container_name = CharField(verbose_name='容器名', max_length=255)
    create_path = CharField(verbose_name='打包路劲', max_length=100)
    lib_repo = CharField(verbose_name='制品库', max_length=100)
    deploy_path = CharField(verbose_name='发布路径', max_length=999)
    extend_attr = CharField(verbose_name='发布路径', max_length=999)
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')
    stamp = IntegerField(verbose_name='版本')
    zeus_type = IntegerField(verbose_name='配置方式')
    need_ops = IntegerField(verbose_name='是否上线标记')
    nacos_namespace = CharField(verbose_name='nacos配置的命名空间', max_length=255)
    nacos_conf_name = CharField(verbose_name='nacos配置配置文件名', max_length=255)
    share_module_name = CharField(verbose_name='公共module的名字', max_length=100)
    is_agent = IntegerField(verbose_name='是否是agent')
    is_component = IntegerField(verbose_name='是否是组件')
    batch_publish = IntegerField(verbose_name='是否批量发布')
    jenkins_batch_publish = IntegerField(verbose_name='是否jenkins批量发布')
    spring_version = IntegerField(verbose_name='spring版本， 0:默认没设置;1:spring 2.3.12;2:spring 2.1.8')

    class Meta:
        db_table = 'app_mgt_app_module'
        verbose_name = '应用模块信息表'

class AppMgtDepModuleName(BaseModel):
    module_name = CharField(verbose_name='上线应用名', max_length=100)
    dep_module_name = CharField(verbose_name='依赖应用名', max_length=100)

    class Meta:
        db_table = 'app_mgt_dep_module'
        verbose_name = '应用模块依赖表'

class MobileBuild(BaseModel):
    """
     移动端构建表
    """
    module_name = CharField(verbose_name='模块名', max_length=100)
    build_path = CharField(verbose_name='构建路径', max_length=100)
    build_product_path = CharField(verbose_name='构建产物', max_length=100)
    repo_product_path = CharField(verbose_name='制品库路径', max_length=255)
    node_module_src_path = BooleanField(verbose_name='模块源')
    node_module_tgt_path = CharField(verbose_name='模块目标', max_length=100)
    entrance_file = CharField(verbose_name='entrance_file路径', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    node_version = CharField(verbose_name='node版本', max_length=20)
    entrance_file_tgt = DateTimeField(verbose_name='组装后的entrance文件')

    class Meta:
        db_table = 'app_mgt_h5_build'
        verbose_name = '应用构建表'