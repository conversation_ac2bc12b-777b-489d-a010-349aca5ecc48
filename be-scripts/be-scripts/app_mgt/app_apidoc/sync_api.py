import os
import sys

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.service.app_mgt_api_service import sync_qa_info
from app_mgt.app_apidoc.scan_api_doc_base import ScanApiDocBase
import logging
from utils.test_env.test_env_lib import step_desc
from app_mgt.app_apidoc.dump_api_to_json import ApiDump


# @Time : 20230407
# <AUTHOR> yang.zhan
class ApiSync(ScanApiDocBase):

    def __init__(self, app_name, iter_branch, jenkins_work_dir, pipeline_id):
        super(ApiSync, self).__init__(app_name, iter_branch, jenkins_work_dir, pipeline_id)

    @step_desc("同步api到QA操作")
    def sync_api(self):
        try:
            # 生成接口文档
            ApiDump.dump_api_doc_api(self.module_name, self.iter_branch)
        except Exception as ex:
            logging.info(ex)
        try:
            # 触发qa_info同步接口
            sync_qa_info(self.module_name, self.iter_branch)
        except Exception as ex:
            logging.info(ex)


if __name__ == '__main__':
    params = sys.argv[1:]
    logging.info("参数列表 {}".format(params))
    logging.info("job_name is {}".format(params[1]))
    logging.info("jenkins_work_dir is {}".format(params[2]))
    param_list = params[1].split("_")
    module_name = param_list[2]
    branch_name = param_list[1]
    pipeline_id = params[1]
    logging.info("module_name is {}".format(module_name))
    logging.info("branch_name is {}".format(branch_name))
    apiSync = ApiSync(module_name, branch_name, params[2], pipeline_id)
    apiSync.sync_api()
