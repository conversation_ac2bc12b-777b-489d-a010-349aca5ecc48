#!/bin/bash

#base_path=/data/apidoc/cgi-ehowbuy-container/test-apidoc-jiasu
#branch_name="test-apidoc-jiasu"
#module_name="module_name"

base_path=$1
module_name=$2
branch_name=$3

file_names=$(ls $base_path/assets)
echo $file_names
FS=$'\r\n' read -d '' -a file_names_array <<< "$file_names"

handle_replace_exec() {
    for asFile in "${file_names_array[@]}"; do
        if [[ $asFile != $1 ]]; then
            cmd="sed -i 's/${asFile}/${asFile}?module_name=${module_name}\&branch_name=${branch_name}/g' ${base_path}/assets/${1}"
            echo $cmd
            eval $cmd
        fi
    done
}

handle_replace() {
    max_concurrent_jobs=10  # 设置最大并发作业数量
    running_jobs=0
    for file_name in "${file_names_array[@]}"; do
        handle_replace_exec $file_name &
        ((running_jobs++))
        if ((running_jobs >= max_concurrent_jobs)); then
            wait  # 等待后台作业完成
            running_jobs=0
        fi
    done
    wait  # 等待剩余的后台作业完成
}

start_time=$(date +%s)
handle_replace
end_time=$(date +%s)
execution_time=$((end_time - start_time))
echo "Execution time: $execution_time seconds"