const path = require('path');
const {createDoc} = require('apidoc');
const fs = require('fs');

//获取参数
const arguments = process.argv.splice(2)
console.log("arguments:%o", arguments)

//参数校验
if (!arguments || arguments.length != 2) {
    throw new Error('arguments not right,must len 2 ');
}

const params = JSON.parse(arguments[0])

console.log("params:%o", params)

const apis = []
params.forEach(module => {
    try {
//生成apiInfo
        const doc = createDoc({
            src: path.resolve(module[1]),
            dest: path.resolve(""), // can be omitted if dryRun is true
            // if you don't want to generate the output files:
            dryRun: false,
            // if you don't want to see any log output:
            silent: false,
        })

//持久化接口数据到指定文件
        if (typeof doc !== 'boolean') {
            console.log(' typeof doc.data %o', typeof doc.data)
            if (typeof doc.data === 'string') {
                try {
                    const dataArray = JSON.parse(doc.data);
                    dataArray.forEach(dataObj => {
                        dataObj.module_name = module[0];
                    });
                    doc.data = JSON.stringify(dataArray);
                } catch (e) {
                    console.error('Error parsing doc.data:', e);
                }
            }

            // Documentation was generated!
            console.log('%s api documentation was generated!.', module[0]);
            console.log("doc data %o", apis)
            apis.push(...JSON.parse(doc.data))
            // 具有文件名，内容和回调函数的writeFile函数
        } else {
            console.log('no createDoc for  %o', module[0]);
        }
    } catch (error) {
        console.error(error); // Manually log the error.
    }
})

console.log("doc datas length %o", apis.length)
fs.writeFile(arguments[1], JSON.stringify(apis), function (err) {
    if (err) throw err;
    console.log(' output file created successfully.');
});

