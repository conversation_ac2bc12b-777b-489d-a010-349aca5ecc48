import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.util.obj_ext import ObjExt


class AppMgtApidocInfoTempBO(ObjExt):
    id = None
    module_name: str = None
    iter_branch: str = None
    api_name: str = None
    api_path: str = None
    api_method: str = None
    api_type: str = None
    api_method_signature: str = None
    api_request_sample: str = None
    api_response_sample: str = None
    create_time = None
    create_user = None
    update_time = None
    update_user = None
    # apidoc增加组和描述 zt@2023-12-27
    api_group = None
    api_description = None
    api_doc_module_name = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def __eq__(self, other: object) -> bool:
        if isinstance(other, AppMgtApidocInfoTempBO):
            return self.module_name == other.module_name and self.iter_branch == other.iter_branch \
                and self.api_path == other.api_path and self.api_method == other.api_method and self.api_type == other.api_type and self.api_method_signature == other.api_method_signature
        return False

    def __hash__(self) -> int:
        return hash((self.module_name, self.iter_branch, self.api_path, self.api_method, self.api_type,
                     self.api_method_signature))
