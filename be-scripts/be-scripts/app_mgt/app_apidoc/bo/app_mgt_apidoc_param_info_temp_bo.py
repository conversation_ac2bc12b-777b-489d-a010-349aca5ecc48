import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.util.obj_ext import ObjExt


class AppMgtApidocParamInfoTempBO(ObjExt):
    id = None
    api_id = None
    param_type: str = None
    param_name: str = None
    param_value_type: str = None
    param_desc: str = None
    param_default_value: str = None
    param_size = None
    param_allowed_values = None
    optional = None
    create_user = None
    create_time = None
    update_user = None
    update_time = None
    # apidoc增加组和描述 zt@2023-12-27
    api_group = None
    api_description = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def __eq__(self, other: object) -> bool:
        if isinstance(other, AppMgtApidocParamInfoTempBO):
            return self.api_id == other.api_id and self.param_type == other.param_type and self.param_name == other.param_name
        return False

    def __hash__(self) -> int:
        return hash((self.api_id, self.param_type, self.param_name))