import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.bo.app_mgt_apidoc_info_temp_bo import AppMgtApidocInfoTempBO
from app_mgt.app_apidoc.bo.app_mgt_apidoc_param_info_temp_bo import AppMgtApidocParamInfoTempBO


class ApidocBO:
    apidoc_info_temp_bo: AppMgtApidocInfoTempBO = None
    apidoc_param_info_bo_list: list[AppMgtApidocParamInfoTempBO] = list()

    def __init__(self, apidoc, param_list):
        self.apidoc_info_temp_bo = apidoc
        self.apidoc_param_info_bo_list = param_list
