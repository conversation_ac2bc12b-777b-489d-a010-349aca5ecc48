import os
import sys
from concurrent.futures import ThreadPoolExecutor

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.service.app_new_branch_api_sync import AppNewBranchApiSyncService
from common.common_tool.common_tool import exec_local_cmd
from settings import API_DOC
import json
import logging


# @Time : 20230407
# <AUTHOR> yang.zhan
class ApiDump:
    @classmethod
    def replace_assets_url(cls, base_path, module_name, branch_name):
        cmd = 'sed -i \'s/{}/{}/g\' {}'.format(
            "\.js", ".js?module_name={}\&branch_name={}".format(module_name, branch_name),
            base_path + os.sep + "apidoc.html")
        exec_local_cmd(cmd)

        cmd = 'sed -i \'s/{}/{}/g\' {} '.format(
            "\.css", ".css?module_name={}\&branch_name={}".format(module_name, branch_name),
            base_path + os.sep + "apidoc.html")
        exec_local_cmd(cmd)

        cmd = 'sed -i \'s/{}/{}/g\' {}'.format(
            "\.js", ".js?module_name={}\&branch_name={}".format(module_name, branch_name),
            base_path + os.sep + "index.html")
        exec_local_cmd(cmd)

        cmd = 'sed -i \'s/{}/{}/g\' {} '.format(
            "\.css", ".css?module_name={}\&branch_name={}".format(module_name, branch_name),
            base_path + os.sep + "index.html")
        exec_local_cmd(cmd)

        cmd = 'sed -i  \'s/\/continuous-testing-frontend//g\' {}'.format(base_path + os.sep + "assets" + os.sep + "*")
        exec_local_cmd(cmd)

        cmd = 'sed -i  \'s/\/continuous-testing-frontend//g\' {}'.format(base_path + os.sep + "apidoc.html")
        exec_local_cmd(cmd)

        cmd = 'sed -i  \'s/\/continuous-testing-frontend//g\' {}'.format(base_path + os.sep + "index.html")
        exec_local_cmd(cmd)

        cmd = 'sh {} {} {} {}'.format(API_DOC['replace_assets_file_path'], base_path, module_name, branch_name)
        exec_local_cmd(cmd)
        # file_names = os.listdir(base_path + os.sep + "assets")
        # logging.info(file_names)
        # with ThreadPoolExecutor(max_workers=20) as t:
        #     for file_name_list in [file_names[i:i + 20] for i in range(0, len(file_names), 20)]:
        #         t.submit(cls.handle_replace, base_path, branch_name, file_name_list, file_names, module_name)

    @classmethod
    def handle_replace(cls, base_path, branch_name, file_names, all_file_names, module_name):
        for asFile in file_names:
            with ThreadPoolExecutor(max_workers=3) as t:
                for file_name_list in [all_file_names[i:i + 20] for i in range(0, len(all_file_names), 20)]:
                    cls.handle_replace_exec(file_name_list, asFile, base_path, branch_name, module_name)

    @classmethod
    def handle_replace_exec(cls, all_file_names, asFile, base_path, branch_name, module_name):
        for asFile2 in all_file_names:
            if asFile2 != asFile:
                cmd = 'sed -i \'s/{}/{}/g\' {}'.format(
                    asFile2, asFile2 + "?module_name={}\&branch_name={}".format(module_name, branch_name),
                             base_path + os.sep + "assets" + os.sep + asFile)
                exec_local_cmd(cmd)

    @classmethod
    def is_dir_empty(cls, path):
        if not os.path.exists(path):
            print("{}目录不存在".format(path))
            return False
        if len(os.listdir(path)) == 0:
            print("{}目录为空".format(path))
            return False
        else:
            print("{}目录不为空".format(path))
            return True

    @classmethod
    def dump_api_doc_api(cls, module_name: str, branch_name: str):
        logging.info("应用：{}，分支：{} 同步api_doc_api开始".format(module_name, branch_name))
        # 查询归档分支接口信息
        api_info_list = AppNewBranchApiSyncService.get_api_info(module_name, branch_name)
        logging.info("应用：{}，分支：{} api_doc size {}".format(module_name, branch_name, len(api_info_list)))
        if len(api_info_list) > 0:
            apis = []
            apis_params = {}
            for api_info, api_param in api_info_list:
                api_exist = False
                for api in apis:
                    if api['id'] == api_info.id:
                        api_exist = True
                if not api_exist:
                    logging.info(api_info.api_path)
                    api_request_sample = None
                    api_response_sample = None
                    if api_info.api_request_sample:
                        try:
                            api_request_sample = str(api_info.api_request_sample, encoding='utf-8')
                        except Exception as ex:
                            api_request_sample = api_info.api_request_sample
                    if api_info.api_request_sample:
                        try:
                            api_response_sample = str(api_info.api_response_sample, encoding='utf-8')
                        except Exception as ex:
                            api_response_sample = api_info.api_response_sample
                    apis.append({"id": api_info.id,
                                 "module_name": api_info.module_name,
                                 "iter_branch": api_info.iter_branch,
                                 "api_name": api_info.api_name,
                                 "api_path": api_info.api_path,
                                 "api_method": api_info.api_method,
                                 "api_type": api_info.api_type,
                                 "api_group": api_info.api_group,
                                 "api_description": api_info.api_description,
                                 "api_method_signature": api_info.api_method_signature,
                                 "api_request_sample": api_request_sample,
                                 "api_response_sample": api_response_sample})

                if not api_param:
                    continue
                if api_param.optional:
                    optional = "可选"
                else:
                    optional = "必选"
                param_desc = None
                if api_param.param_desc:
                    try:
                        param_desc = str(api_param.param_desc, encoding='utf-8')
                    except Exception as ex:
                        param_desc = api_param.param_desc
                param_api = {
                    "id": api_param.id,
                    "api_id": api_param.api_id,
                    "param_type": api_param.param_type,
                    "param_name": api_param.param_name,
                    "param_value_type": api_param.param_value_type,
                    "param_desc": param_desc,
                    "param_default_value": api_param.param_default_value,
                    "param_size": api_param.param_size,
                    "param_allowed_values": api_param.param_allowed_values,
                    "optional": optional
                }
                if apis_params.get(api_param.api_id):
                    if api_param.param_type == "response_param" and apis_params[api_info.id].get("response_param"):
                        apis_params[api_info.id].get("response_param").append(param_api)
                    elif api_param.param_type == "request_param" and apis_params[api_info.id].get("request_param"):
                        apis_params[api_info.id].get("request_param").append(param_api)
                    elif api_param.param_type == "header_param" and apis_params[api_info.id].get("header_param"):
                        apis_params[api_info.id].get("header_param").append(param_api)
                    else:
                        apis_params[api_info.id][api_param.param_type] = [param_api]
                else:
                    apis_params[api_info.id] = {api_param.param_type: [param_api]}
            for api in apis:
                if apis_params.get(api["id"]):
                    api["response_param"] = apis_params.get(api["id"]).get("response_param", [])
                    api["request_param"] = apis_params.get(api["id"]).get("request_param", [])
                    api["header_param"] = apis_params.get(api["id"]).get("header_param", [])
            apidoc_data = {"module_name": module_name, "branch_name": branch_name, "apis": apis}
            js_obj = json.dumps(apidoc_data)
            apidoc_path = API_DOC['apis_dump_dir'] + os.sep + module_name
            basic_apidoc_path = API_DOC['apis_dump_dir'] + os.sep + 'basic'
            if not cls.is_dir_empty(apidoc_path + os.sep + branch_name):
                if not cls.is_dir_empty(basic_apidoc_path):
                    # 获取html 制品
                    cmd = 'git clone -b {} {} {}'.format(API_DOC['apis_doc_html_version'],
                                                         API_DOC['apis_doc_html_git_lib_url'],
                                                         basic_apidoc_path)
                    exec_local_cmd(cmd)
                exec_local_cmd("rm -rf {}".format(apidoc_path))
                exec_local_cmd("mkdir -p {}/{}".format(apidoc_path, branch_name))
                # 获取html 制品
                cmd = 'cp -r {}/** {}/{}'.format(basic_apidoc_path, apidoc_path, branch_name)
                exec_local_cmd(cmd)
                cls.replace_assets_url(apidoc_path + os.sep + branch_name, module_name, branch_name)
            # 放json
            apidoc_json_root = apidoc_path + os.sep + branch_name + os.sep + "data"
            exec_local_cmd("mkdir -p {}".format(apidoc_json_root))

            apidoc_file = apidoc_json_root + os.sep + "apidocs.json"
            exec_local_cmd("rm -rf {}".format(apidoc_file))
            with open(apidoc_file, 'w') as file_obj:
                file_obj.write(js_obj)

            sshpass_cmd = "sshpass -p howbuy2015 ssh tomcat@192.168.209.43 {}".format(
                "mkdir -p {}".format(API_DOC['apis_dump_dir']))
            exec_local_cmd(sshpass_cmd)

            sshpass_cmd = "sshpass -p howbuy2015 ssh tomcat@192.168.209.43 {}".format(
                "rm -rf {}".format(apidoc_path + os.sep + branch_name))
            exec_local_cmd(sshpass_cmd)
            sshpass_cmd = "sshpass -p howbuy2015 ssh tomcat@192.168.209.43 {}".format(
                "mkdir -p {}".format(apidoc_path + os.sep + branch_name))
            exec_local_cmd(sshpass_cmd)

            remote_cmd = "sshpass -p howbuy2015 scp -r {apidoc_file} tomcat@192.168.209.43:{apidoc_path}".format(
                apidoc_file=(apidoc_path + os.sep + branch_name + os.sep + "**"),
                apidoc_path=(apidoc_path + os.sep + branch_name))
            exec_local_cmd(remote_cmd)

            logging.info("应用：{}，分支：{} api_doc 同步到ng完成".format(module_name, branch_name))
        doc_link = API_DOC['apis_doc_root_url'] + "?module_name=" + module_name + "&branch_name=" + branch_name
        # 插入新迭代接口信息
        logging.info("应用：{}，分支：{}api_doc dump结束,文档链接:{}".format(module_name, branch_name, doc_link))


if __name__ == '__main__':
    ApiDump.dump_api_doc_api("o32-pre-web", "3.2.24")
