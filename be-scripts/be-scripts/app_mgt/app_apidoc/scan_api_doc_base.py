import os
import sys
# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import API_DOC
from utils.test_env.test_env_lib import step_desc


# @Time : 20230407
# <AUTHOR> yang.zhan
class ApiDocError(Exception):
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return repr(self.value)


class ScanApiDocBase(object):
    # 定义一些常量
    api_file_suffix = "_apidoc.json"
    api_file_path_spread = "_"
    generate_api_work_sub_dir = "apidoc"

    def __init__(self, app_name, iter_branch, work_dir,pipeline_id):
        self.pipeline_id: str = pipeline_id
        self.module_name: str = app_name
        self.iter_branch: str = iter_branch
        self.work_dir: str = work_dir + os.sep + self.generate_api_work_sub_dir
        self.prepare_generate_log_path = '{}/generate_apidoc.log'.format(self.work_dir)
        self.prepare_generate_error_log_path = '{}/generate_apidoc_error.log'.format(self.work_dir)

    @step_desc("获取api文档路径")
    def get_api_doc_file_path(self):
        return self.work_dir + os.sep + self.module_name + self.api_file_path_spread \
            + self.iter_branch + self.api_file_suffix

    def check_api_doc_file(self):
        if not self.check_api_doc_file_exist():
            if API_DOC['block_when_no_api'].lower() == 'true':
                # 强制阻断
                raise ApiDocError("apidoc 未生成，强制要求必须要有apidoc")
            else:
                # 人工创建apidoc文件 并写入空列表
                open(self.get_api_doc_file_path(), 'w').write("[]")

    def check_api_doc_file_exist(self):
        return os.path.exists(self.get_api_doc_file_path())
