import datetime
import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.service.app_mgt_api_service import get_src_path, save_scan_log
import json
from settings import API_DOC
from app_mgt.app_apidoc.scan_api_doc_base import ScanApiDocBase
import logging
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd
from utils.test_env.test_env_lib import step_desc

from app_mgt.app_apidoc.model.app_mgt_apidoc_scan_log import AppMgtApidocScanLog


# @Time : 20230407
# <AUTHOR> yang.zhan
class ApiDocGenerate(ScanApiDocBase):
    # 定义一些常量
    __nvm_script = API_DOC['nvm_script']
    __node_version = API_DOC['node_version']
    __generate_api_script_dir = PROJECT_DIR + os.sep + "app_mgt" + os.sep + "app_apidoc"
    __generate_api_script = API_DOC['generate_api_script']
    __generate_api_script_path = __generate_api_script_dir + os.sep + __generate_api_script

    def __init__(self, app_name, iter_branch, jenkins_work_dir, pipeline_id):
        super(ApiDocGenerate, self).__init__(app_name, iter_branch, jenkins_work_dir, pipeline_id)
        self.root_dir: str = jenkins_work_dir

    @step_desc("生成apidoc")
    def generate_api_doc(self):
        # 准备工作目录
        self.prepare_work_dir()
        # 生成应用apidoc文档
        cmd = self.get_generate_api_doc_cmd()
        logging.info("开始扫描apidoc")
        # 同步执行命令
        exec_local_cmd(cmd)
        # 检出error日志
        try:
            exec_local_cmd(self.get_error_log_cmd())
        except Exception as ex:
            logging.info("插件解析apidoc时没有error日志")
        logging.info("扫描apidoc结束")

        # apidoc解析出错的发布风险。zt@2024-01-15
        log_pipeline_id = ""
        if self.pipeline_id and self.module_name:
            log_pipeline_id = self.pipeline_id.replace("_"+self.module_name, '')

        scan_log = AppMgtApidocScanLog(
            module_name=self.module_name,
            pipeline_id=log_pipeline_id,
            phase='扫描apidoc',
            status='success',
            create_time=datetime.datetime.now(),
            create_user='pa'
        )
        try:
            exec_local_cmd(self.get_encountered_cmd())
            scan_log.status = 'error'
            logging.info("encountered error")
        except Exception as ex:
            logging.info("success")
        logging.info("扫描apidoc结束")
        save_scan_log(scan_log)
        # 校验
        self.check_api_doc_file()
        if not self.check_api_doc_file_exist():
            logging.info(
                '【应用：{} 分支：{}】apidoc未生成  请检查是否编写apidoc!!'.format(self.module_name, self.iter_branch))
        else:
            logging.info('【应用：{} 分支：{}】apidoc生成成功'.format(self.module_name, self.iter_branch))

    @step_desc("获取应用源码目录路径")
    def get_app_src_path(self):
        src_path_tuple_list_temp = get_src_path(self.module_name, self.pipeline_id)
        src_path_tuple_list = list()
        for src_path_tuple in src_path_tuple_list_temp:
            src_path_tuple_list.append([src_path_tuple[0], self.root_dir + os.sep + src_path_tuple[1],
                                        self.root_dir + os.sep + self.generate_api_work_sub_dir + os.sep +
                                        src_path_tuple[
                                            0] + self.api_file_suffix])
        return src_path_tuple_list

    @step_desc("获取生成apidoc的命令")
    def get_generate_api_doc_cmd(self):
        prepare_node_env_cmd = ' {} && nvm use {}'.format(self.__nvm_script,
                                                          self.__node_version)
        prepare_generate_work_dir_cmd = 'cp {} {} && cd {} '.format(self.__generate_api_script_path,
                                                                    self.work_dir,
                                                                    self.work_dir)
        logging.info('生成apidoc的日志文件路径:{}'.format(self.prepare_generate_log_path))
        # prepare_generate_npm_module_cmd = 'npm install apidoc'
        prepare_generate_api_cmd = "npm link apidoc && node {} '{}' {}".format(
            self.__generate_api_script,
            json.dumps(self.get_app_src_path()),
            self.get_api_doc_file_path())
        cmd = ' {} && {} && {} > {}'.format(prepare_node_env_cmd,
                                            prepare_generate_work_dir_cmd,
                                            prepare_generate_api_cmd, self.prepare_generate_log_path)

        return cmd

    def get_error_log_cmd(self):
        return "grep -i -B 1 -A 1 'error\|ERROR' {}".format(self.prepare_generate_log_path)

    def get_encountered_cmd(self):
        return "grep -i -B 1 -A 1 'encountered' {}".format(self.prepare_generate_log_path)

    def prepare_work_dir(self):
        # 检测工作目录是否存在
        is_exists = os.path.exists(self.work_dir)
        if not is_exists:
            os.makedirs(self.work_dir)
            logging.info('创建:{} 目录成功'.format(self.work_dir))
        # 清理历史文件
        if self.check_api_doc_file_exist():
            os.remove(self.get_api_doc_file_path())
            logging.info('清理:{} 成功'.format(self.get_api_doc_file_path()))


if __name__ == '__main__':
    params = sys.argv[1:]
    logging.info("参数列表 {}".format(params))
    logging.info("job_name is {}".format(params[1]))
    logging.info("jenkins_work_dir is {}".format(params[2]))
    param_list = params[1].split("_")
    module_name = param_list[2]
    branch_name = param_list[1]
    pipeline_id = "_".join(param_list[0:-1])
    logging.info("module_name is {}".format(module_name))
    logging.info("branch_name is {}".format(branch_name))
    logging.info("pipeline_id is {}".format(pipeline_id))
    apiDocParse = ApiDocGenerate(module_name, branch_name, params[2], pipeline_id)
    try:
        apiDocParse.generate_api_doc()
    except Exception as ex:
        logging.error(ex)
    finally:
        logging.info("排错文档链接:{url}".format(
            url="http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=70488092"))
