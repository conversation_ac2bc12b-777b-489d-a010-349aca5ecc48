# coding: utf-8
from sqlalchemy import Column, Index, String, TIMESTAMP
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class AppMgtApidocScanLog(Base):
    __tablename__ = 'app_mgt_apidoc_scan_log'
    __table_args__ = (
        Index('normal_idx', 'module_name', 'pipeline_id'),
        {'comment': 'apidoc扫描日志表'}
    )

    id = Column(BIGINT(11), primary_key=True)
    module_name = Column(String(100), nullable=False, index=True, comment='应用名')
    pipeline_id = Column(String(100), nullable=False, comment='迭代ID')
    phase = Column(String(20), nullable=False, comment='阶段【扫描apidoc、解析apidoc】')
    status = Column(String(20), nullable=False, comment='状态')
    create_time = Column(TIMESTAMP, nullable=False)
    update_time = Column(TIMESTAMP)
    create_user = Column(String(50), nullable=False)
    update_user = Column(String(50))
