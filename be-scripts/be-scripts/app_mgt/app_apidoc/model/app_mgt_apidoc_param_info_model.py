# coding: utf-8

import sys
import os
import datetime

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.bo.app_mgt_apidoc_param_info_temp_bo import AppMgtApidocParamInfoTempBO
from sqlalchemy import Column, DateTime, Index, String
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.ext.declarative import declarative_base

from app_mgt.app_apidoc.util.obj_ext import ObjExt

Base = declarative_base()
metadata = Base.metadata


class AppMgtApidocParamInfo(Base, ObjExt):
    __tablename__ = 'app_mgt_apidoc_param_info'
    __table_args__ = (
        Index('api_id', 'param_type', 'param_name', unique=True),
        {'comment': '应用api参数表'}
    )

    id = Column(BIGINT(11), primary_key=True, comment='主键')
    api_id = Column(BIGINT(11), nullable=False, comment='接口主键')
    param_type = Column(String(20), nullable=False, comment='参数类型（header/requestParam/responseParam）')
    param_name = Column(String(20), nullable=False, comment='参数名称')
    param_value_type = Column(String(50), nullable=False, comment='参数值类型（基本类型+自定义类型）')
    param_desc = Column(String(50), comment='参数描述')
    param_default_value = Column(String(255), comment='参数默认值')
    param_size = Column(String(50), comment='Information about the size of the variable.')
    param_allowed_values = Column(String(255), comment='Information about allowed values of the variable.')
    optional = Column(TINYINT(1), nullable=False, comment='是否可选(是1/否0)')
    create_user = Column(String(20), nullable=False, comment='创建人')
    create_time = Column(DateTime, nullable=False, comment='创建时间')
    update_user = Column(String(20), comment='更新人')
    update_time = Column(DateTime, comment='更新时间')

    @classmethod
    def from_bo(cls, bo_obj: AppMgtApidocParamInfoTempBO) -> 'AppMgtApiParamInfoTemp':
        bo_obj.create_time = datetime.datetime.now()
        return cls(**bo_obj.__dict__)

    def __eq__(self, other: object) -> bool:
        if isinstance(other, AppMgtApidocParamInfo):
            return self.api_id == other.api_id and self.param_type == other.param_type and self.param_name == other.param_name
        return False

    def __hash__(self) -> int:
        return hash((self.api_id, self.param_type, self.param_name))