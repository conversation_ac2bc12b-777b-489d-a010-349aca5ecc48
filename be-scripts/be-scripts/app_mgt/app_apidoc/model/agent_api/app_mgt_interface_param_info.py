# coding: utf-8
from sqlalchemy import Column, DateTime, Index, String
from sqlalchemy.dialects.mysql import BIGINT, TINYINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class AppMgtInterfaceParamInfo(Base):
    __tablename__ = 'app_mgt_interface_param_info'
    __table_args__ = (
        Index('uni_idx0_param', 'module_name', 'interface_path', 'interface_method', 'interface_type', 'field_name', 'branch_name', unique=True),
    )

    id = Column(BIGINT(11), primary_key=True, comment='ID')
    module_name = Column(String(50), comment='应用名')
    branch_name = Column(String(100), comment='分支名称')
    interface_path = Column(String(255), comment='接口路径')
    interface_method = Column(String(100), comment='接口方法')
    interface_type = Column(String(10), comment='接口类型')
    field_name = Column(String(100), comment='参数名称')
    field_type = Column(String(100), comment='参数类型（Integer,String等）')
    is_required = Column(TINYINT(1), comment='是否必填参数：1，必填，0，选填')
    enum_values = Column(String(500), comment='枚举值')
    create_time = Column(DateTime, comment='创建时间')
    create_user = Column(String(50), comment='创建人')
    update_time = Column(DateTime, comment='更新时间')
    update_user = Column(String(50), comment='更新人')
