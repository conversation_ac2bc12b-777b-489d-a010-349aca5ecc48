# coding: utf-8
from sqlalchemy import Column, DateTime, Index, JSON, String
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class AppMgtInterfaceInfo(Base):
    __tablename__ = 'app_mgt_interface_info'
    __table_args__ = (
        Index('uni_idx_interface', 'module_name', 'branch_name', 'interface_path', 'interface_method', 'interface_type', unique=True),
    )

    id = Column(BIGINT(11), primary_key=True, comment='ID')
    module_name = Column(String(50), comment='应用名')
    branch_name = Column(String(100), comment='分支名')
    interface_name = Column(String(100), comment='接口名称')
    interface_path = Column(String(255), comment='接口路径')
    interface_method = Column(String(100), comment='接口方法')
    interface_type = Column(String(10), comment='接口类型')
    content_type = Column(String(50), comment='请求参数类型')
    encryption = Column(String(50), comment='加密类型')
    status = Column(BIGINT(1), comment='状态：0，不可用，1，可用')
    create_version = Column(String(100), comment='接口创建版本')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='更新时间')
    request_params = Column(JSON, comment='接口请求参数')
    response_params = Column(JSON, comment='接口返回参数')
    defines_params = Column(JSON, comment='http的自定义对象参数')
    interface_name_dev = Column(String(100), comment='接口名称-供开发编辑')
