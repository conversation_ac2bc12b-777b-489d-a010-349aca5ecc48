# coding: utf-8
import sys
import os


# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
import datetime
from app_mgt.app_apidoc.bo.app_mgt_apidoc_info_temp_bo import AppMgtApidocInfoTempBO
from sqlalchemy import Column, DateTime, Index, String, Text
from sqlalchemy.dialects.mysql import BIGINT
from sqlalchemy.ext.declarative import declarative_base

from app_mgt.app_apidoc.util.obj_ext import ObjExt

Base = declarative_base()
metadata = Base.metadata


class AppMgtApidocInfoTemp(Base, ObjExt):
    __tablename__ = 'app_mgt_apidoc_info_temp'
    __table_args__ = (
        Index('api_unique', 'module_name', 'iter_branch', 'api_path', 'api_method', 'api_type', 'api_method_signature',
              unique=True),
        {'comment': '应用api信息预存储表'}
    )

    id = Column(BIGINT(11), primary_key=True, comment='主键')
    module_name = Column(String(50), nullable=False, comment='应用名')
    iter_branch = Column(String(50), nullable=False, comment='应用分支')
    api_name = Column(String(50), comment='api名称')
    api_path = Column(String(255), nullable=False, comment='api路径')
    api_method = Column(String(50), nullable=False, comment='api方法')
    api_type = Column(String(20), nullable=False, comment='api类型（HTTP/DUBBO/MQ）')
    api_method_signature = Column('api_method_signature', String(255), nullable=False,
                                  comment='api方法签名（取参数类型 多个用｜分隔）')
    api_request_sample = Column(Text, comment='api参数样例')
    api_response_sample = Column(Text, comment='api响应样例')
    create_time = Column(DateTime, nullable=False)
    create_user = Column(String(50), nullable=False, comment='创建人')
    update_time = Column(DateTime, comment='更新时间')
    update_user = Column(String(50), comment='更新人')
    # apidoc增加组和描述 zt@2023-12-27
    api_group = Column(String(255), comment='api组')
    api_description = Column(String(999), comment='api描述')
    api_doc_module_name = Column(String(100), comment='api所属模块')

    @classmethod
    def from_bo(cls, bo_obj: AppMgtApidocInfoTempBO) -> 'AppMgtApidocInfoTemp':
        bo_obj.create_time = datetime.datetime.now()
        return cls(**bo_obj.__dict__)

    def __eq__(self, other: object) -> bool:
        if isinstance(other, AppMgtApidocInfoTemp):
            return self.module_name == other.module_name and self.iter_branch == other.iter_branch \
                and self.api_path == other.api_path and self.api_method == other.api_method and self.api_type == other.api_type and self.api_method_signature == other.api_method_signature
        return False

    def __hash__(self) -> int:
        return hash((self.module_name, self.iter_branch, self.api_path, self.api_method, self.api_type,
                     self.api_method_signature))

