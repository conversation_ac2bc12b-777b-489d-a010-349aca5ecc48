# 自动将数据库表转成model 在目录下命令行执行即可
# sqlacodegen mysql+pymysql://scm:123456@192.168.209.63:3306/spider --tables 表名  > model.py
# sqlacodegen mysql+pymysql://scm:123456@192.168.209.63:3306/spider --tables app_mgt_api_info  > app_mgt_api_info_bo.py
# sqlacodegen mysql+pymysql://scm:123456@192.168.209.63:3306/spider --tables app_mgt_interface_info  > app_mgt_interface_info.py
# sqlacodegen mysql+pymysql://scm:123456@192.168.209.63:3306/spider --tables app_mgt_interface_param_info  > app_mgt_interface_param_info.py


