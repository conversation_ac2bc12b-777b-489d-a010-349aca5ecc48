import os
import sys
import threading
import time
import traceback
import json
import datetime
import logging

from app_mgt.app_apidoc.service.api_doc_diff_service import get_api_doc_diff_size

PROJECT_DIR = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from settings import API_DOC, ROCKETMQ
from app_mgt.models import AppMgtDepModuleName
from dao.get.mysql.app_info import get_group_module_name
from dao.get.mysql.relation import get_iter_module_name
from common.email.send_email import SendMail
from app_mgt.app_apidoc.model.app_mgt_apidoc_param_info_temp_model import AppMgtApidocParamInfoTemp
from app_mgt.app_apidoc.model.app_mgt_apidoc_param_info_model import AppMgtApidocParamInfo
from app_mgt.app_apidoc.model.app_mgt_apidoc_info_temp_model import AppMgtApidocInfoTemp
from app_mgt.app_apidoc.model.app_mgt_apidoc_info_model import AppMgtApidocInfo, AppMgtInterfaceAndApiExclude
from app_mgt.app_apidoc.bo.apidoc_bo import ApidocBO
from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
# 设置项目目录 解决依赖问题
from iter_mgt.in_transit_check.check_utils.in_transit_entrance_guard_check import ApiDocCheckUtil
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy, SustainablePool
from app_mgt.app_apidoc.scan_api_doc_base import ApiDocError
from job.iter_mgt.models import TaskMgtRocketmqInfoLog
from rocketmq.client import Producer, Message
from sqlalchemy import text
from concurrent.futures import ThreadPoolExecutor

sustainablePool = SustainablePool(pool_size=10)


# 处理api信息
def check_all_api(module_name: str, iter_branch: str, apis: list[ApidocBO], pipeline_id: str):
    # 查询基准
    new_size = len(apis)
    need_check_apis = 0
    interface_list = ApiDocCheckUtil.get_current_or_achieve_branch_interface_list(iter_branch, module_name)
    with DBConnectionManagerForSqlalchemy() as db:
        need_check_apis = db.session.query(AppMgtApidocInfo).filter(AppMgtApidocInfo.module_name == module_name,
                                                                    AppMgtApidocInfo.iter_branch == iter_branch,
                                                                    AppMgtApidocInfo.retain != 'Y').outerjoin(
            AppMgtInterfaceAndApiExclude,
            AppMgtApidocInfo.api_path != AppMgtInterfaceAndApiExclude.interface_path).all()
        if interface_list and need_check_apis:
            logging.info('应用:{},分支:{},需要校验的接口数量:{}'.format(module_name, iter_branch, len(need_check_apis)))
            for need_check_api in need_check_apis:
                need_check_api_exist = False
                for interface in interface_list:
                    if need_check_api.api_path == interface.interface_path and need_check_api.api_method == interface.interface_method and need_check_api.api_type == interface.interface_type:
                        need_check_api_exist = True
                if not need_check_api_exist:
                    for apiBo in apis:
                        if need_check_api.api_path == apiBo.apidoc_info_temp_bo.api_path and need_check_api.api_method == apiBo.apidoc_info_temp_bo.api_method and need_check_api.api_type == apiBo.apidoc_info_temp_bo.api_type and need_check_api.api_method_signature == apiBo.apidoc_info_temp_bo.api_method_signature:
                            need_check_api_exist = True
                status = 0
                if need_check_api_exist:
                    status = 1
                logging.info(
                    '应用:{},分支:{},接口{}, method:{}, api_type: {},状态置为{}'.format(module_name, iter_branch,
                                                                                        need_check_api.api_path,
                                                                                        need_check_api.api_method,
                                                                                        need_check_api.api_type,
                                                                                        status))
                db.session.query(AppMgtApidocInfo).filter(AppMgtApidocInfo.id == need_check_api.id).update(
                    {"status": status})
            db.session.commit()
            need_check_apis = db.session.query(AppMgtApidocInfo).filter(AppMgtApidocInfo.module_name == module_name,
                                                                        AppMgtApidocInfo.iter_branch == iter_branch,
                                                                        AppMgtApidocInfo.retain != 'Y',
                                                                        AppMgtApidocInfo.status == 1).outerjoin(
                AppMgtInterfaceAndApiExclude,
                AppMgtApidocInfo.api_path != AppMgtInterfaceAndApiExclude.interface_path).all()
    old_size = len(need_check_apis)
    logging.info('''
    应用:{}
    分支:{}
    本次扫描接口数量:{}
    需要校验的接口数量:{}
    diff阈值:{}
    '''.format(module_name, iter_branch, new_size, old_size, API_DOC['warn_when_diff_api_size']))
    # 当前迭代下的同组模块

    diff_size = get_api_doc_diff_size(pipeline_id, module_name, apis, need_check_apis)

    if diff_size > int(API_DOC['warn_when_diff_api_size']):
        warning_content = '''
            应用:{}
            分支:{}
            本次扫描接口数量:{}
            需要校验的接口数量:{}
            diff阈值:{}
            触发diff告警:{}！！！'''
        logging.info(
            warning_content.format(module_name, iter_branch, new_size, old_size, API_DOC['warn_when_diff_api_size'],
                                   diff_size))
        try:
            logging.info("==== 准备发送邮件to {} ====".format(
                API_DOC['warn_email_sent_to']))
            send_mail = SendMail()
            send_mail.set_to(API_DOC['warn_email_sent_to'])
            send_mail.set_subject(API_DOC['warn_email_title'])
            send_mail.set_content(
                warning_content.format(module_name, iter_branch, new_size, old_size, API_DOC['warn_when_diff_api_size'],
                                       diff_size))
            send_mail.send()
        except Exception as e:
            logging.warning("==== ！！！！邮件发送出错！！！！ ====")
            logging.error(e)
            # 异常返回
            sys.exit(1)

        raise ApiDocError("apidoc 入库失败，原因：触发diff告警")


# 处理api信息
def update_retain_api(module_name: str, iter_branch: str, apis: list[ApidocBO]):
    with DBConnectionManagerForSqlalchemy() as db:
        need_check_apis = db.session.query(AppMgtApidocInfo).filter(AppMgtApidocInfo.module_name == module_name,
                                                                    AppMgtApidocInfo.iter_branch == iter_branch,
                                                                    AppMgtApidocInfo.retain == 'Y').outerjoin(
            AppMgtInterfaceAndApiExclude,
            AppMgtApidocInfo.api_path != AppMgtInterfaceAndApiExclude.interface_path).all()
        need_update_count = 0
        update_count = 0
        for need_check_api in need_check_apis:
            for api in apis:
                if need_check_api.api_path == api.apidoc_info_temp_bo.api_path and need_check_api.api_method == api.apidoc_info_temp_bo.api_method and need_check_api.api_type == api.apidoc_info_temp_bo.api_type and need_check_api.api_method_signature == api.apidoc_info_temp_bo.api_method_signature:
                    need_update_count = need_update_count + 1
                    res = db.session.query(AppMgtApidocInfo).filter(AppMgtApidocInfo.id == need_check_api.id).update(
                        {"retain": 'N'})
                    if res == 1:
                        update_count = update_count + 1
                    db.session.flush()
        print("==== 需要取消强制保留的api状态成功 ====:{}个".format(need_update_count))

        print("==== 取消强制保留的api状态成功 ====:{}个".format(update_count))
        db.session.commit()


def persist_api_info(module_name, iter_branch, apis: list[ApidocBO], pipeline_id):
    try:
        logging.info("==== 保存至temp表中 ====")
        save_api_info_temp(module_name, iter_branch, apis)
        logging.info("==== 较上一次扫描的api数量做对比，差异大于阈值则告警，并终止入库 ====")
        check_all_api(module_name, iter_branch, apis, pipeline_id)
        logging.info("更改以前是人工处理的、强制保留的,但是当前迭代扫出来了的接口的retain状态改为N")
        update_retain_api(module_name, iter_branch, apis)
        logging.info("保存到api和api_param表中")
        save_api_info(module_name, iter_branch, apis)
        logging.info("api处理结束")
    except Exception as ex:
        traceback.print_exc()
        logging.error(ex)

    return len(apis)


def save_api_info_temp(module_name, iter_branch, apis: list[ApidocBO]):
    with DBConnectionManagerForSqlalchemy() as db:
        logging.info("clear_his_temp")
        clear_his_temp(db, iter_branch, module_name)
        logging.info("save temp表")
        db.session.commit()
    # apis 按50个一组分成多个list 小于50个就不分组
    with ThreadPoolExecutor(max_workers=20) as t:
        batch = 0
        for apis_list in [apis[i:i + 50] for i in range(0, len(apis), 50)]:
            batch = batch + 1
            t.submit(save_api_info_temp_handler, apis_list, batch)
    # 改成多线程处理apis
    logging.info("提交temp表")


def save_api_info_temp_handler(apis, batch):
    logging.info("api批次:{} {}开始处理".format(batch, len(apis)))
    try:
        save_api_info_temp_handler_exec(apis, batch)
    except Exception as ex:
        traceback.print_exc()
        logging.error(ex)
        logging.info("api批次:{} {}重试处理开始".format(batch, len(apis)))
        try:
            save_api_info_temp_handler_exec(apis, batch)
        except Exception as e:
            traceback.print_exc()
            logging.error("api批次:{} {}处理失败".format(batch, len(apis)))


def save_api_info_temp_handler_exec(apis, batch):
    session = sustainablePool.get_db_session()
    appMgtApiParamInfoTempList = set()
    for api in apis:
        if api.apidoc_info_temp_bo is not None:
            # 保存所有接口信息
            appMgtApiInfoTemp = AppMgtApidocInfoTemp.from_bo(
                api.apidoc_info_temp_bo)
            session.add(appMgtApiInfoTemp)
            session.flush()
            session.commit()
            session.query(AppMgtApidocParamInfoTemp).filter(
                AppMgtApidocParamInfoTemp.api_id == appMgtApiInfoTemp.id
            ).delete()
            for api_param in api.apidoc_param_info_bo_list:
                appMgtApiParamInfoTemp = AppMgtApidocParamInfoTemp.from_bo(
                    api_param)
                appMgtApiParamInfoTemp.api_id = appMgtApiInfoTemp.id
                appMgtApiParamInfoTempList.add(appMgtApiParamInfoTemp)
    logging.info("session.add_all start:{}".format(batch))
    session.add_all(appMgtApiParamInfoTempList)
    logging.info("session.add_all end:{}".format(batch))
    session.commit()
    session.close()
    logging.info("api批次:{} {}处理完毕".format(batch, len(apis)))


def clear_his_temp(db, iter_branch, module_name):
    # 清除ApiInfoTemp数据
    api_ids_tuple = db.session.query(AppMgtApidocInfoTemp.id).filter(AppMgtApidocInfoTemp.module_name == module_name,
                                                                     AppMgtApidocInfoTemp.iter_branch == iter_branch).all()
    api_ids = list()
    for api_id_tuple in api_ids_tuple:
        api_ids.append(api_id_tuple.id)
    if len(api_ids) == 0:
        return
        # 清除ApiParamInfoTemp数据
    db.session.query(AppMgtApidocParamInfoTemp).filter(
        AppMgtApidocParamInfoTemp.api_id.in_(api_ids)).delete()
    db.session.query(AppMgtApidocInfoTemp).filter(AppMgtApidocInfoTemp.module_name == module_name,
                                                  AppMgtApidocInfoTemp.iter_branch == iter_branch).delete()


def save_api_info(module_name, iter_branch, apis: list[ApidocBO]):
    with DBConnectionManagerForSqlalchemy() as db:
        # 查询历史非强制保留的ApiInfo数据
        api_ids_tuple = db.session.query(AppMgtApidocInfo.id).filter(AppMgtApidocInfo.module_name == module_name,
                                                                     AppMgtApidocInfo.iter_branch == iter_branch,
                                                                     AppMgtApidocInfo.retain != 'Y').all()
        api_ids = list()
        for api_id_tuple in api_ids_tuple:
            api_ids.append(api_id_tuple.id)
        db.session.query(AppMgtApidocParamInfo).filter(AppMgtApidocParamInfo.api_id.in_(api_ids),
                                                       ).delete()
        db.session.query(AppMgtApidocInfo).filter(AppMgtApidocInfo.module_name == module_name,
                                                  AppMgtApidocInfo.iter_branch == iter_branch,
                                                  AppMgtApidocInfo.status == 1
                                                  ).delete()
        db.session.commit()
        logging.info("清除历史ApiParamInfo数据 end")
        with ThreadPoolExecutor(max_workers=20) as t:
            batch = 0
            for apis_list in [apis[i:i + 50] for i in range(0, len(apis), 50)]:
                batch = batch + 1
                t.submit(save_api_info_handler, apis_list, batch)
            t.shutdown()
        clear_his_temp(db, iter_branch, module_name)
        db.session.commit()
    return len(apis)


def save_api_info_handler(apis, batch):
    logging.info("api批次:{} {}开始处理".format(batch, len(apis)))
    try:
        save_api_info_handler_exec(apis, batch)
    except Exception as e:
        traceback.print_exc()
        logging.error("api批次:{} {}处理失败".format(batch, len(apis)))
        logging.info("api批次:{} {}重试开始处理".format(batch, len(apis)))
        try:
            save_api_info_handler_exec(apis, batch)
        except Exception as e:
            traceback.print_exc()
            logging.error("api批次:{} {}处理失败".format(batch, len(apis)))


def save_api_info_handler_exec(apis, batch):
    session = sustainablePool.get_db_session()
    appMgtApiParamInfoList = set()
    for api in apis:
        appMgtApiInfoTemp = AppMgtApidocInfo.from_bo(api.apidoc_info_temp_bo)
        appMgtApiInfoTemp.retain = 'N'
        appMgtApiInfoTemp.status = 1
        session.add(appMgtApiInfoTemp)
        session.flush()
        session.commit()
        session.query(AppMgtApidocParamInfo).filter(
            AppMgtApidocParamInfo.api_id == appMgtApiInfoTemp.id,
        ).delete()
        for api_param in api.apidoc_param_info_bo_list:
            appMgtApiParamInfo = AppMgtApidocParamInfo.from_bo(api_param)
            appMgtApiParamInfo.api_id = appMgtApiInfoTemp.id
            appMgtApiParamInfoList.add(appMgtApiParamInfo)
    session.add_all(appMgtApiParamInfoList)
    session.commit()
    session.close()
    logging.info("api批次:{} {}处理完毕".format(batch, len(apis)))


def save_api_info_param(api_params: list[AppMgtApidocParamInfo]):
    return len(api_params)


def save_scan_log(log):
    with DBConnectionManagerForSqlalchemy() as db:
        db.session.add(log)
        db.session.commit()


def sync_qa_info(module_name, iter_branch):
    if API_DOC['sync_qa_info_open'].lower() == "true":
        meg_body = dict(module_name=module_name, branch_name=iter_branch,
                        idempotent_no=time.strftime("%Y%m%d%H%M%S"))
        logging.info("qa_info 同步TOPIC:{}".format(
            ROCKETMQ['scan_interface_topic']))
        t = threading.Thread(target=send_to_rmq, args=(meg_body, ROCKETMQ['scan_interface_topic']))
        t.start()
    else:
        logging.info("api同步开关未打开,请联系平台")


def send_to_rmq(meg_body, topic):
    logging.info("发送到rmq的同步信息为：{}".format(meg_body))
    namesrv = ROCKETMQ['namesrv'] if ROCKETMQ['namesrv'] else ""
    namesrv_domain = ROCKETMQ['namesrv_domain'] if ROCKETMQ['namesrv_domain'] else ""
    group_id = topic + "_GROUP"
    try:
        pd = Producer(group_id, timeout=6000)
        if namesrv:
            logging.info("namesrv:{}".format(namesrv))
            pd.set_namesrv_addr(namesrv)
        else:
            logging.info("namesrv_domain:{}".format(namesrv_domain))
            pd.set_namesrv_domain(namesrv_domain)
        pd.start()

        msg_body = json.dumps(meg_body).encode('utf-8')
        msg = Message(topic)
        msg.set_body(msg_body)

        ret = pd.send_sync(msg)

        rmq_id = TaskMgtRocketmqInfoLog(name_server=namesrv, name_server_domain=namesrv_domain,
                                        group=group_id, topic=topic, message=msg_body, status=ret.status,
                                        msg_id=ret.msg_id, offset=ret.offset, creator="be-script",
                                        create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        rmq_id.save()
        logging.info("同步信息发送成功 msgId:{}".format(ret.msg_id))
        pd.shutdown()
    except Exception as e:
        logging.error(str(e))
        rmq_id = TaskMgtRocketmqInfoLog(name_server=namesrv, name_server_domain=namesrv_domain,
                                        group=group_id, topic=topic, message="RMQ连接超时，超时时间6s", status="",
                                        msg_id="", offset="", creator="be-script",
                                        create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        logging.info("同步消息发送失败")
        # 人工发送同步消息｜再扫描一次
        rmq_id.save()


def get_src_path(module_name, pipeline_id):
    with DBConnectionManagerForSqlalchemy() as db:
        # values = db.session.execute(text('''select iter_app.appName as module_name,REPLACE(iter_app.pom_path,'pom.xml','') src_path from (
        #            select dep_app as artifact_id, dep_pipeline_id as cur_pipeline_id from iterative_pipeline_relations where  cur_pipeline_id = '{}' and iter_app_name in
        #                (
        # 		          select module_name from app_mgt_app_module where app_id in (
        #                           select app.id  from app_mgt_app_module module
        #                                          join app_mgt_app_info app on module.app_id=app.id
        #                                          where module.module_name='{}')
        #                )
        #           union select DISTINCT appName as artifact_id,cur_pipeline_id from iterative_pipeline_relations where iter_app_name='{}' and cur_pipeline_id = '{}'
        #     ) app_iter join iter_mgt_iter_app_info iter_app on app_iter.artifact_id=iter_app.appName and   app_iter.cur_pipeline_id=iter_app.pipeline_id'''.format(
        #     pipeline_id, module_name, module_name, pipeline_id))).fetchall()

        # 改成只取当前迭代下的模块 20241014 by fwm
        values = db.session.execute(text('''SELECT iter_app.appName AS module_name,REPLACE(iter_app.pom_path,'pom.xml','') src_path 
                        FROM (
                           SELECT DISTINCT appName AS artifact_id,app_pipeline_id FROM iterative_pipeline_relations 
                           WHERE iter_app_name='{module_name}' AND cur_pipeline_id = '{pipeline_id}' AND app_group = '{app_group}'
                           
                           UNION 
                           
                           SELECT DISTINCT dep_app AS artifact_id, dep_pipeline_id AS app_pipeline_id FROM iterative_pipeline_relations r
                           INNER JOIN app_mgt_dep_module dm ON r.iter_app_name = dm.module_name AND r.dep_app = dm.dep_module_name
                           WHERE iter_app_name='{module_name}' AND cur_pipeline_id = '{pipeline_id}' 
                        ) app_iter JOIN iter_mgt_iter_app_info iter_app ON app_iter.artifact_id=iter_app.appName AND app_iter.app_pipeline_id=iter_app.pipeline_id  '''.format(
            module_name=module_name, pipeline_id=pipeline_id, app_group=pipeline_id.split("_")[0]))).fetchall()
        return values


if __name__ == '__main__':
    sustainablePool = SustainablePool(pool_size=20)
    session = sustainablePool.get_db_session()
    lists = [
        AppMgtApidocInfo(api_name="test", create_user='pa', create_time=datetime.datetime.now(), iter_branch="test",
                         api_method_signature="s", module_name="test", api_method="test", api_type="test",
                         api_path="/ss")]
    session.add_all(lists)
    session.commit()
