import datetime
from copy import deepcopy
import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.model.agent_api.app_mgt_interface_info import AppMgtInterfaceInfo
from app_mgt.app_apidoc.model.agent_api.app_mgt_interface_param_info import AppMgtInterfaceParamInfo
from app_mgt.app_apidoc.model.app_mgt_apidoc_info_model import AppMgtApidocInfo, AppMgtInterfaceAndApiExclude
from app_mgt.app_apidoc.model.app_mgt_apidoc_param_info_model import AppMgtApidocParamInfo
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.get.mysql import app_info
from settings import logger


class AppNewBranchApiSyncService:
    @classmethod
    def sync_api_for_new_branch(cls, module_name: str, branch_name: str):
        archive_branch_name = app_info.get_latest_and_has_api_archive_version(
            module_name)
        logger.info("应用:{},新开分支：{},最新归档分支:{}".format(
            module_name, branch_name, archive_branch_name))
        if not archive_branch_name:
            logger.info("应用:{},最新归档分支:{} 没有api_doc api".format(
                module_name, archive_branch_name))
            return True
        # 同步api_doc接口信息
        cls.sync_api_doc_api(module_name, branch_name, "test1.2.1.1.1.3")

    @classmethod
    def sync_api_doc_api(cls, module_name: str, branch_name: str, archive_branch_name: str):
        logger.info("应用：{}，分支：{} 同步api_doc_api开始".format(
            module_name, branch_name))
        # 查询归档分支接口信息
        api_info_list = cls.get_api_info(module_name, archive_branch_name)
        if len(api_info_list) > 0:
            new_api_param_list = []
            with DBConnectionManagerForSqlalchemy() as db:
                for api_info, api_param in api_info_list:
                    new_api_info = AppMgtApidocInfo(module_name=api_info.module_name,
                                                    iter_branch=branch_name,
                                                    api_type=api_info.api_type,
                                                    api_path=api_info.api_path,
                                                    api_name=api_info.api_name,
                                                    api_method=api_info.api_method,
                                                    api_method_signature=api_info.api_method_signature,
                                                    retain=api_info.retain,
                                                    create_user="pa",
                                                    create_time=datetime.datetime.now()
                                                    )
                    exist_list = db.session.query(AppMgtApidocInfo).filter(
                        AppMgtApidocInfo.module_name == api_info.module_name,
                        AppMgtApidocInfo.iter_branch == branch_name,
                        AppMgtApidocInfo.api_type == api_info.api_type,
                        AppMgtApidocInfo.api_path == api_info.api_path,
                        AppMgtApidocInfo.api_name == api_info.api_name,
                        AppMgtApidocInfo.api_method == api_info.api_method,
                        AppMgtApidocInfo.api_method_signature == api_info.api_method_signature).outerjoin(
                        AppMgtInterfaceAndApiExclude,
                        AppMgtApidocInfo.api_path != AppMgtInterfaceAndApiExclude.interface_path).all()
                    if exist_list and len(exist_list) > 0:
                        new_api_info = exist_list[0]
                    else:
                        db.session.add(new_api_info)
                        db.session.flush()
                    if api_param:
                        new_api_param = AppMgtApidocParamInfo(param_type=api_param.param_type,
                                                              param_name=api_param.param_name,
                                                              param_desc=api_param.param_desc,
                                                              param_default_value=api_param.param_default_value,
                                                              param_value_type=api_param.param_value_type,
                                                              optional=api_param.optional,
                                                              create_user="pa",
                                                              create_time=datetime.datetime.now(),
                                                              api_id=new_api_info.id
                                                              )
                        new_api_param_list.append(new_api_param)
                if new_api_param_list:
                    db.session.add_all(new_api_param_list)
                    db.session.commit()
        # 插入新迭代接口信息
        logger.info("应用：{}，分支：{} 同步api_doc_api结束".format(
            module_name, branch_name))

    @classmethod
    def sync_agent_api(cls, module_name: str, branch_name: str, archive_branch_name: str):
        logger.info("应用：{}，分支：{} 同步agent_api开始".format(
            module_name, branch_name))
        # 查询归档分支接口信息
        interface_info_list = cls.get_interface_info(
            module_name, archive_branch_name)
        interface_param_info_list = cls.get_interface_param_info(
            module_name, archive_branch_name)
        if interface_info_list and len(interface_info_list) > 0:
            new_interface_info_list = []
            for interface_info in interface_info_list:
                new_interface_info_list.append(AppMgtInterfaceInfo(module_name=interface_info.module_name,
                                                                   branch_name=branch_name,
                                                                   interface_name=interface_info.interface_name,
                                                                   interface_path=interface_info.interface_path,
                                                                   interface_method=interface_info.interface_method,
                                                                   interface_type=interface_info.interface_type,
                                                                   content_type=interface_info.content_type,
                                                                   encryption=interface_info.encryption,
                                                                   status=interface_info.status,
                                                                   create_version=archive_branch_name,
                                                                   create_user=interface_info.create_user,
                                                                   create_time=datetime.datetime.now(),
                                                                   request_params=interface_info.request_params,
                                                                   response_params=interface_info.response_params,
                                                                   defines_params=interface_info.defines_params,
                                                                   interface_name_dev=interface_info.interface_name_dev,
                                                                   ))
            if len(new_interface_info_list) > 0:
                with DBConnectionManagerForSqlalchemy() as db:
                    db.session.add_all(new_interface_info_list)
                    db.session.commit()

        if interface_param_info_list and len(interface_param_info_list) > 0:
            new_interface_param_info_list = []
            for interface_param_info in interface_param_info_list:
                new_interface_param_info_list.append(AppMgtInterfaceParamInfo(
                    module_name=interface_param_info.module_name,
                    branch_name=branch_name,
                    interface_path=interface_param_info.interface_path,
                    interface_method=interface_param_info.interface_method,
                    interface_type=interface_param_info.interface_type,
                    field_name=interface_param_info.field_name,
                    field_type=interface_param_info.field_type,
                    is_required=interface_param_info.is_required,
                    enum_values=interface_param_info.enum_values,
                    create_user=interface_param_info.create_user,
                    create_time=datetime.datetime.now()
                ))
            if len(new_interface_param_info_list) > 0:
                with DBConnectionManagerForSqlalchemy() as db:
                    db.session.add_all(new_interface_param_info_list)
                    db.session.commit()

        # 插入新迭代接口信息
        logger.info("应用：{}，分支：{} 同步agent_api结束".format(
            module_name, branch_name))

    @classmethod
    def get_interface_info(cls, module_name, archive_branch_name):
        with DBConnectionManagerForSqlalchemy() as db:
            return db.session.query(AppMgtInterfaceInfo).filter(AppMgtInterfaceInfo.module_name == module_name,
                                                                AppMgtInterfaceInfo.branch_name == archive_branch_name).all()

    @classmethod
    def get_interface_param_info(cls, module_name, archive_branch_name):
        with DBConnectionManagerForSqlalchemy() as db:
            return db.session.query(AppMgtInterfaceParamInfo).filter(
                AppMgtInterfaceParamInfo.module_name == module_name,
                AppMgtInterfaceParamInfo.branch_name == archive_branch_name).all()

    @classmethod
    def get_api_info(cls, module_name, archive_branch_name):
        with DBConnectionManagerForSqlalchemy() as db:
            api_info_list = db.session.query(AppMgtApidocInfo, AppMgtApidocParamInfo).outerjoin(
                AppMgtApidocParamInfo,
                AppMgtApidocInfo.id == AppMgtApidocParamInfo.api_id
            ).filter(
                AppMgtApidocInfo.module_name == module_name,
                AppMgtApidocInfo.iter_branch == archive_branch_name,
                AppMgtApidocInfo.status == 1
            ).all()
            result = []
            for api_info, api_param in api_info_list:
                result.append((deepcopy(api_info), deepcopy(api_param)))
            db.session.commit()
            return result
