import logging

from app_mgt.models import AppMgtDepModuleName
from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
from dao.get.mysql.app_info import get_group_module_name

from dao.get.mysql.relation import get_iter_module_name


def get_api_doc_diff_size(pipeline_id, module_name, apis, need_check_apis):
    project_group = pipeline_id.split('_')[0]
    # pipeline_id = project_group + '_' + iter_branch
    special_module_list = add_special_module(module_name)
    cur_pipeline_module_list = get_dept_module_name(project_group, pipeline_id, module_name, special_module_list)
    logging.info("==== 当前迭代下的同组模块:{} ====".format(cur_pipeline_module_list))

    group_module_list = get_group_module_name(project_group)
    if special_module_list:
        group_module_list.extend(special_module_list)
    logging.info("==== 当前分组模块和特殊依赖模块:{} ====".format(group_module_list))
    difference_set = [x for x in group_module_list if x not in cur_pipeline_module_list]

    diff_size = count_reduce_api(apis, difference_set, group_module_list, need_check_apis)
    return diff_size


def add_special_module(module_name):
    special_module_list = []
    module_name = AppMgtDepModuleName.select().where(AppMgtDepModuleName.module_name == module_name)
    if module_name:
        for result in module_name:
            special_module_list.append(result.dep_module_name)
    return special_module_list


def get_dept_module_name(project_group, pipeline_id, module_name, special_module_list):
    tree_module_name = get_iter_module_name(pipeline_id, module_name)
    cur_pipeline_module_list = []
    logging.info("==== 当前迭代{},应用{} 的深度依赖:{}".format(pipeline_id, module_name, tree_module_name))
    for result in tree_module_name:
        if result['app_pipeline_id'] == pipeline_id or result['app_group'] == project_group:
            if result['appName'] not in cur_pipeline_module_list:
                cur_pipeline_module_list.append(result['appName'])
                continue
        else:
            logging.warning("当前module非同组module{},分组{}".format(result['appName'], result['app_group']))
            if special_module_list and result['appName'] in special_module_list:
                # 判断迭代是否关闭
                obj = BranchesModel.select().filter(
                    BranchesModel.pipeline_id == result['app_pipeline_id']).get_or_none()
                if 'close' != obj.br_status:
                    cur_pipeline_module_list.append(result['appName'])
    return cur_pipeline_module_list


def count_reduce_api(apis, archive_pipeline_module_list, group_module_list, need_check_apis):
    diff_size = 0
    for need_check_api in need_check_apis:
        need_check_api_exist = False
        # 先判断是否在同组模块
        if need_check_api.api_doc_module_name:
            if need_check_api.api_doc_module_name not in group_module_list:
                logging.info("module不在当前分组中:{}".format(need_check_api.api_doc_module_name))
                diff_size = diff_size + 1
                continue
            if need_check_api.api_doc_module_name in archive_pipeline_module_list:
                continue
        else:
            if need_check_api.module_name not in group_module_list:
                logging.info("module不在当前分组中:{}".format(need_check_api.module_name))
                diff_size = diff_size + 1
                continue
            if need_check_api.module_name in archive_pipeline_module_list:
                continue
        for apiBo in apis:
            if need_check_api.api_path == apiBo.apidoc_info_temp_bo.api_path \
                    and need_check_api.api_method == apiBo.apidoc_info_temp_bo.api_method \
                    and need_check_api.api_type == apiBo.apidoc_info_temp_bo.api_type \
                    and need_check_api.api_method_signature == apiBo.apidoc_info_temp_bo.api_method_signature:
                need_check_api_exist = True
        if not need_check_api_exist:
            logging.warning("本次删除了apidoc:{}.{}".format(need_check_api.api_path, need_check_api.api_method))
            diff_size = diff_size + 1
    return diff_size
