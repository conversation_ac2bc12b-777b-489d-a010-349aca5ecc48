

class CreatePipelineBo:
    """建造者模式"""

    def __init__(self, builder):
        self.__app_name = builder.app_name
        self.__iteration_id = builder.iteration_id
        self.__pipeline_name = builder.pipeline_name
        self.__pipeline_params = builder.pipeline_params
        self.__template_name = builder.template_name
        self.__entrance = builder.entrance
        self.__tapd_id = builder.tapd_id

    @property
    def app_name(self):
        return self.__app_name

    @property
    def pipeline_name(self):
        return self.__pipeline_name

    @property
    def iteration_id(self):
        return self.__iteration_id

    @property
    def pipeline_params(self):
        return self.__pipeline_params

    @property
    def template_name(self):
        return self.__template_name

    @property
    def entrance(self):
        return self.__entrance

    @property
    def tapd_id(self):
        return self.__tapd_id

    class Builder:
        pipeline_name: str
        app_name: str
        iteration_id: str
        pipeline_params: str = None
        template_name: str = ""
        entrance: bool = True
        tapd_id: int = 0

        def set_app_name(self, app_name: str):
            self.app_name = app_name
            return self

        def set_iteration_id(self, iteration_id: str):
            self.iteration_id = iteration_id
            return self

        def set_pipeline_params(self, pipeline_params: str):
            self.pipeline_params = pipeline_params
            return self

        def set_template_name(self, template_name: str):
            self.template_name = template_name
            return self

        def set_entrance(self, entrance: bool):
            self.entrance = entrance
            return self

        def set_tapd_id(self, tapd_id: int):
            self.tapd_id = tapd_id
            return self

        def set_pipeline_name(self):
            self.pipeline_name = "{}_{}".format(self.iteration_id, self.app_name)
            return self

        def verify_basic_property(self):
            if self.app_name == "":
                raise Exception("app_name 不可为空")
            if self.iteration_id == "":
                raise Exception("iteration_id 不可为空")
            if self.template_name == "":
                raise Exception("template_name 不可为空")

        def build_bo(self):
            self.verify_basic_property()
            self.set_pipeline_name()
            return CreatePipelineBo(self)
