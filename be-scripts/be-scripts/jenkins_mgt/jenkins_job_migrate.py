
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from jenkins_mgt.bo.create_pipeline_bo import CreatePipelineBo
from settings import logger
from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import branch_apply as branch_apply_get


class JenkinsJobMigration:
    def __init__(self):
        pass

    def create_jenkins_pipeline(self, iteration_id, app_name, tapd_id):
        """
        创建流水线
        :param iteration_id:
        :param :
        :return:
        """
        # 使用动态创建jenkins
        create_pipeline_builder = CreatePipelineBo.Builder().set_iteration_id(iteration_id).\
            set_template_name("pipeline_模板") \
            .set_tapd_id(tapd_id)
        jenkins_job_mgt = JenkinsJobMgt()

        create_pipeline_bo = create_pipeline_builder.set_app_name(app_name).build_bo()
        logger.info(create_pipeline_bo.__dict__)
        jenkins_job_mgt.migrate_jenkins_pipeline(create_pipeline_bo)

    def run_migrate(self):

        for row in self.get_migration_info():
            logger.info(row)
            self.create_jenkins_pipeline(row["pipeline_id"], row["appName"], row["tapd_id"])

    def run_mobile_migrate(self, type_list):

        for row in self.get_mobile_migration_info(type_list):
            logger.info(row)
            self._create_mobile_jenkins_pipeline(row["pipeline_id"], row["br_name"], row["tapd_id"], {row["appName"]: row["package_type"]})


    def _create_mobile_jenkins_pipeline(self, iteration_id, branch_name, tapd_id, non_mvn_module_info):
        """
        创建流水线
        :param iteration_id:
        :param module_name_list:
        :return:
        """

        h5_app_list = []
        static_list = []
        android_list = []
        ios_list = []
        param_remote_list = []
        for app_name in non_mvn_module_info:
            if non_mvn_module_info[app_name] in ["remote", "dist"]:
                h5_app_list.append(app_name)
            elif non_mvn_module_info[app_name] in ["static"]:
                static_list.append(app_name)
            # 拉分支时创建流水线调用
            elif non_mvn_module_info[app_name] in ["android"]:
                android_list.append(app_name)
            elif non_mvn_module_info[app_name] in ["ios"]:
                ios_list.append(app_name)
            elif non_mvn_module_info[app_name] in ["param-remote", "mini-program", "ssr-static", "ssr-remote"]:
                param_remote_list.append(app_name)
        if len(h5_app_list) > 0:
            self._create_job(iteration_id, branch_name, tapd_id, "h5_ci_pipeline_template", h5_app_list)
        if len(static_list) > 0:
            self._create_job(iteration_id, branch_name, tapd_id, "h5_ci_pipeline_pc_template", static_list)
        # 新增的 安卓和ios流水线模板
        if len(android_list) > 0:
            self._create_job(iteration_id, branch_name, tapd_id, "app_android_template", android_list)
        if len(ios_list) > 0:
            self._create_job(iteration_id, branch_name, tapd_id, "app_ios_template", ios_list)
        if len(param_remote_list) > 0:
            self._create_job(iteration_id, branch_name, tapd_id, "mini_remote_pipeline_template", param_remote_list, True)

    def _create_job(self, iteration_id, branch_name, tapd_id, template_name, h5_app_list, is_mini_pipeline=None):
        compile_info = branch_apply_get.get_compile_info(h5_app_list)
        jenkins_job_mgt = JenkinsJobMgt()
        for app_name in h5_app_list:
            compile_params = dict(**{"br_name": branch_name, "app_name": app_name}, **compile_info[app_name])
            # 生成编译流水线需要的参数
            pipeline_params = 'args = "{{\\"app_name\\":\\"{app_name}\\",\\"br_name\\":\\"{br_name}\\",' \
                              '\\"git_repo_path\\":\\"{git_repo_path}\\",\\"workspace\\":\\"${{workspace}}\\"' \
                              ',\\"lib_repo\\":\\"{lib_repo}\\"}}"'.format(**compile_params)
            # 新增小程序参数模板
            if is_mini_pipeline:
                pipeline_params = 'args = "{{\\"app_name\\":\\"{app_name}\\",\\"br_name\\":\\"{br_name}\\",' \
                                  '\\"git_repo_path\\":\\"{git_repo_path}\\",\\"workspace\\":\\"${{workspace}}\\"' \
                                  ',\\"lib_repo\\":\\"{lib_repo}\\",\\"jenkins_id\\":\\"${{jenkins_id}}\\",' \
                                  '\\"datetime\\":\\"${{datetime}}\\"}}"'.format(**compile_params)
            # 修改 创建job 实现 动态分发 2022/10/18 刘帅
            create_pipeline_builder = CreatePipelineBo.Builder().set_iteration_id(iteration_id).set_app_name(
                app_name).set_template_name(template_name) \
                .set_tapd_id(tapd_id).set_pipeline_params(pipeline_params)
            if compile_info[app_name]["package_type"] == "dist":
                create_pipeline_bo = create_pipeline_builder.set_entrance(True).build_bo()
            else:
                create_pipeline_bo = create_pipeline_builder.set_entrance(False).build_bo()
            logger.info(create_pipeline_bo.__dict__)
            jenkins_job_mgt.migrate_jenkins_pipeline(create_pipeline_bo)

    def get_mobile_migration_info(self, pkg_type_list):
        sql = '''
SELECT DISTINCT CONCAT(i.pipeline_id,"_",i.appName) AS job_name,j.tapd_id,j.pipeline_id,i.appName ,j.create_date,j.br_name, b.package_type
FROM  hm_optlog_main mn LEFT JOIN 
 iter_mgt_iter_app_info i ON  mn.job_name = CONCAT(i.pipeline_id,"_",i.appName) 
 LEFT JOIN iter_mgt_iter_info j ON i.pipeline_id = j.pipeline_id
LEFT JOIN app_mgt_app_module m ON i.appName = m.module_name
LEFT JOIN app_mgt_app_build b ON b.module_name = m.module_name
LEFT JOIN jenkins_mgt_jenkins_job_info jb ON jb.job_name =  CONCAT(i.pipeline_id,"_",i.appName)
WHERE m.need_online = 1 AND j.br_status = "open" AND b.package_type IN
 ("{}") AND jb.jenkins_info_id =2
      AND mn.start_time >"2022-11-29 00:00:00" ORDER BY mn.id DESC
            '''.format('","'.join(pkg_type_list))
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            job_list = []
            for row in db.cur.fetchall():
                # logger.info(row["job_name"])
                job_list.append(row)
        logger.info("迁移个数{}".format(len(job_list)))
        return job_list

    def get_migration_info(self, pkg_type_list):
#         sql = '''SELECT CONCAT(i.pipeline_id,"_",i.appName) AS job_name,j.tapd_id,j.pipeline_id,i.appName ,j.create_date
#  FROM iter_mgt_iter_app_info i LEFT JOIN iter_mgt_iter_info j ON i.pipeline_id = j.pipeline_id
# LEFT JOIN app_mgt_app_module m ON i.appName = m.module_name
# LEFT JOIN app_mgt_app_build b ON b.module_name = m.module_name
# LEFT JOIN jenkins_mgt_jenkins_job_info jb ON jb.job_name =  CONCAT(i.pipeline_id,"_",i.appName)
# WHERE m.need_online = 1 AND j.br_status = "open" AND b.package_type IN
#  ("jar","war","tar") AND j.create_date >"2022-09-01 00:00:00" AND jb.jenkins_info_id =2'''
        sql = '''
        
SELECT DISTINCT mn.iteration_id,mn.app_name, CONCAT(i.pipeline_id,"_",i.appName) AS job_name,j.tapd_id,j.pipeline_id,i.appName ,j.create_date,
j.br_name
FROM  pipeline_log_main mn LEFT JOIN 
 iter_mgt_iter_app_info i ON i.pipeline_id=mn.iteration_id AND i.appName = mn.app_name 
 LEFT JOIN iter_mgt_iter_info j ON i.pipeline_id = j.pipeline_id
LEFT JOIN app_mgt_app_module m ON i.appName = m.module_name
LEFT JOIN app_mgt_app_build b ON b.module_name = m.module_name
LEFT JOIN jenkins_mgt_jenkins_job_info jb ON jb.job_name =  CONCAT(i.pipeline_id,"_",i.appName)
WHERE m.need_online = 1 AND j.br_status = "open" AND b.package_type IN
 ("{}") AND jb.jenkins_info_id =2
  AND mn.start_at >"2022-11-29 00:00:00" ORDER BY mn.sid DESC
        '''.format('","'.format(pkg_type_list))
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            job_list = []
            for row in db.cur.fetchall():
                #logger.info(row["job_name"])
                job_list.append(row)

        return job_list


if __name__ == "__main__":
    ["remote", "dist"]
    type_list = ["ios","android"]
    job_migration = JenkinsJobMigration()
    job_migration.run_mobile_migrate(type_list)


