import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from jenkins_mgt.models import Jenkins<PERSON>gtJobComposition, JenkinsMgtJobCompositionDetail

from jenkins_mgt.jenkins_job_handlers import JenkinsCompositionParent

from settings import logger


class JenkinsCompositionMgt:

    def __init__(self, build_id, batch_no, stage_name, job_type):
        self.build_id = build_id
        self.batch_no = batch_no
        self.stage_name = stage_name
        self.job_type = job_type

    def exec(self):
        logger.info(self.batch_no)
        logger.info(self.stage_name)
        logger.info(self.build_id)
        subclasses = JenkinsCompositionParent.__subclasses__()
        for subclass in subclasses:
            if subclass.handler_name == self.job_type:
                logger.info("匹配到流水线处理器:{}".format(subclass))
                instance = subclass(self.batch_no, self.stage_name)
                instance.start_jenkins()
                instance.update_jenkins_info()
                instance.watch_jenkins_status()

    def parse(self):
        logger.info("解析")
        with DBConnectionManagerForSqlalchemy() as db:
            db.session.query(JenkinsMgtJobComposition).filter(
                JenkinsMgtJobComposition.batch_no == self.batch_no).update({"job_build_id": self.build_id})
            db.session.commit()

    def end(self):
        status = "success"
        with DBConnectionManagerForSqlalchemy() as db:
            err_objs = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                JenkinsMgtJobCompositionDetail.status == "failure").all()
            if err_objs:
                for err_obj in err_objs:
                    logger.info("执行失败的流水线【{}】:{}".format(err_obj.job_name, err_obj.job_url))
                status = "failure"
            waiting_objs = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                JenkinsMgtJobCompositionDetail.status == "waiting").all()
            if waiting_objs:
                for err_obj in waiting_objs:
                    logger.info("丢弃执行的流水线【{}".format(err_obj.job_name))
                db.session.query(JenkinsMgtJobCompositionDetail).filter(
                    JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                    JenkinsMgtJobCompositionDetail.status == "waiting").update({"status": "aborted"})

            db.session.query(JenkinsMgtJobComposition).filter(
                JenkinsMgtJobComposition.batch_no == self.batch_no).update({"status": status})
            db.session.commit()
        logger.info("组合流水线执行完成:状态【{}】".format(status))

    def call(self, stage):
        acceptor = {
            "parse": self.parse,
            "end": self.end,
        }
        if acceptor.get(stage, None):
            acceptor[stage]()
        else:
            status = "success"
            try:
                self.exec()
            except Exception as ex:
                logger.error(ex)
                status = "failure"
                raise Exception("组合流水线执行失败！")
            finally:
                self.end_detail(status)

    def end_detail(self, status):
        with DBConnectionManagerForSqlalchemy() as db:
            update_obj = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                JenkinsMgtJobCompositionDetail.job_name == self.stage_name).update({"status": status})
            logger.info("状态更新:{}".format("完成" if update_obj > 0 else "失败"))
            db.session.commit()


if __name__ == '__main__':
    if len(sys.argv) < 4:
        logger.error("参数不完整！")
        sys.exit(1)
    if sys.argv[3] != "parse" and sys.argv[3] != "end":
        jenkinsComposition = JenkinsCompositionMgt(sys.argv[1], sys.argv[2],
                                                   sys.argv[3], sys.argv[3].split("_job_type_")[0])
    else:
        jenkinsComposition = JenkinsCompositionMgt(sys.argv[1], sys.argv[2], sys.argv[3],
                                                   sys.argv[3])

    jenkinsComposition.call(sys.argv[3])
    sys.exit(0)
