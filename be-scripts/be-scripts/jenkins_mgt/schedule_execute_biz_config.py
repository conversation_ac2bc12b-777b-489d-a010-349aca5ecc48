import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)
from job.jenkins.test_data_dev.models import BizBaseDbBind, BizBaseInfo, BizBaseType, BizTestIter, BizTestFlowInfo, \
    BizTestFlowTestsetDetail

from jenkins_mgt.models import BizTestFlowScheduleConfig, BizTestFlowGlobalParam
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import http_request_post

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from settings import logger, INTERFACE_URL


class ScheduleExeBizConfigMgt:

    def __init__(self, build_id, schedule_config_id):
        self.build_id = build_id
        self.schedule_config_id = schedule_config_id
        self.batch_no = None

    def exec(self):
        logger.info(self.schedule_config_id)
        logger.info(self.build_id)
        logger.info("准备触发")
        with DBConnectionManagerForSqlalchemy() as db:
            scheduleConfigsGlobalParams = db.session.query(BizTestFlowScheduleConfig, BizTestFlowGlobalParam).join(
                BizTestFlowGlobalParam,
                BizTestFlowScheduleConfig.id == BizTestFlowGlobalParam.schedule_config_id).filter(
                BizTestFlowScheduleConfig.id == self.schedule_config_id).all()
            scheduleConfig = None
            globalParams = []
            app_dict_list = []
            just_init_db = 0
            for scheduleConfigItem, globalParamItem in scheduleConfigsGlobalParams:
                scheduleConfig = scheduleConfigItem
                globalParams.append(globalParamItem)
                if globalParamItem.param_type == "test_apps":
                    for param in globalParamItem.param:
                        app_dict_list.append({"app_name": param["app_module_name"], "branch_name": param["br_name"]})
                if globalParamItem.param_type == "ext_config":
                    just_init_db = globalParamItem.param.get("just_init_db", 0)
            biz_base_info = db.session.query(BizBaseInfo).filter(BizBaseInfo.biz_code == scheduleConfig.biz_code).one()
            biz_type_info = db.session.query(BizBaseType).filter(BizBaseType.id == biz_base_info.biz_type).one()
            # bis_info_param = db.session.query(BizBaseDbBind).filter(
            #     BizBaseDbBind.biz_code == scheduleConfig.biz_code).first()
            # 兼容从希娜同步业务的新逻辑 for怀天  20241223 by fwm
            if biz_base_info.biz_scenario_name:
                suffix_suffix = biz_type_info.biz_type_department + biz_type_info.biz_type_transaction + "-" + biz_base_info.biz_name + "-" + biz_base_info.biz_scenario_name
            else:
                suffix_suffix = biz_base_info.biz_name
            suffix = suffix_suffix + "_" + scheduleConfig.biz_flow_name
            biz_job_name = "biz_job_job_type_{}".format(suffix)

            biz_test_flow_info = db.session.query(BizTestFlowInfo).filter(
                BizTestFlowInfo.biz_pipeline_name == suffix).one()
            biz_test_flow_testset_detail = db.session.query(BizTestFlowTestsetDetail).filter(
                BizTestFlowTestsetDetail.flow_id == biz_test_flow_info.id).all()
            logger.info("biz_test_flow_testset_detail={}".format(biz_test_flow_testset_detail))
            testset_detail_list = []
            version_type = ""
            for item in biz_test_flow_testset_detail:
                version_type = item.version_type
                testset_detail_list.append(
                    {"testSetId": str(item.testset_id), "appName": item.app_name, "script_branch": item.script_branch})

            # TRADE-BATCH_0.0.13_清算--批处理_T0ANDT1-1
            biz_flow_job_name = scheduleConfig.biz_code + "_" + scheduleConfig.biz_iter_branch + "_" + suffix
            param = {"exec_action_type": "flow_schedule", "suite_code": scheduleConfig.suite_code,
                     "jobs_info": [

                         {"job_name": "mult_init_job_type_环境初始化", "param": {
                             "app_dict_list": app_dict_list,
                             "bis_pipeline_id": scheduleConfig.biz_code + "_" + scheduleConfig.biz_iter_branch,
                             "biz_db_init_flag": True,
                             "just_init_db": just_init_db,
                             # "db_env": bis_info_param.biz_base_db_code,
                             "env_name": scheduleConfig.suite_code
                         }}
                         ,
                         {"job_name": biz_job_name,
                          "param": {
                              "biz_pipeline_name": suffix,
                              "biz_test_iter_id": scheduleConfig.biz_code + "_" + scheduleConfig.biz_iter_branch,
                              "suite_code": scheduleConfig.suite_code,
                              "testset_detail_list": testset_detail_list,
                              "version_type": version_type if version_type else "REMOTE",
                          }}
                     ]}
            logger.info(param)
            biz_test_iter_id = scheduleConfig.biz_code + "_" + scheduleConfig.biz_iter_branch
            biz_test_iter_obj = db.session.query(BizTestIter).filter(
                BizTestIter.biz_test_iter_id == biz_test_iter_id).one()
            if biz_test_iter_obj.br_status == 'close':
                db.session.query(BizTestFlowScheduleConfig).filter(
                    BizTestFlowScheduleConfig.biz_iter_branch == scheduleConfig.biz_iter_branch,
                    BizTestFlowScheduleConfig.biz_flow_name == scheduleConfig.biz_flow_name,
                    BizTestFlowScheduleConfig.suite_code == scheduleConfig.suite_code).update({"enable_schedule": 0})
                db.session.commit()
                logger.info("业务迭代{}已经归档，已自动将定时任务停止！".format(biz_test_iter_id))
                return
            self.execute_start_job(param, biz_flow_job_name)
            logger.info("触发完成")

    def parse(self):
        logger.info("解析")
        with DBConnectionManagerForSqlalchemy() as db:
            bizTestFlowScheduleConfig = db.session.query(BizTestFlowScheduleConfig).filter(
                BizTestFlowScheduleConfig.id == self.schedule_config_id).one()
            job_name = bizTestFlowScheduleConfig.jenkins_url.split("/")[-2]
            pipeline_url = bizTestFlowScheduleConfig.jenkins_url.replace(
                "job/" + job_name + "/", "blue/organizations/jenkins/{}/detail/{}/{}/pipeline/".format(job_name,
                                                                                                       job_name,
                                                                                                       self.build_id))
            db.session.query(BizTestFlowScheduleConfig).filter(
                BizTestFlowScheduleConfig.id == self.schedule_config_id).update({"pipeline_url": pipeline_url})
            db.session.commit()
            logger.info("pipeline_url:{}".format(pipeline_url))

    def end(self):
        status = "success"
        logger.info("定时流水线执行完成:状态【{}】".format(status))

    def call(self, stage):
        acceptor = {
            "parse": self.parse,
            "end": self.end,
        }
        if acceptor.get(stage, None):
            acceptor[stage]()
        else:
            status = "success"
            try:
                self.exec()
            except Exception as ex:
                logger.error(ex)
                status = "failure"
                raise Exception("定时流水线执行失败！")
            finally:
                logger.info("ok")

    def execute_start_job(self, request_param, biz_job_name):
        logger.info("{} 启动中。。。".format(request_param))
        url = INTERFACE_URL['spider'] + INTERFACE_URL['spider_context'] + INTERFACE_URL[
            'start_jenkins_composition_job_url']
        try:
            res = http_request_post(url, request_param, headers={'Content-Type': 'application/json',
                                                                 "Authorization": INTERFACE_URL[
                                                                     'spider_api_Authorization']})
            result = res.json()
            logger.info(result)
            if "status" in result and result["status"] == "success":
                logger.info(result["data"])
                self.batch_no = result["data"]["batch_no"]
                self.execute_history({"exec_action_type": "flow_schedule", "exec_detail_param": result["data"],
                                      "biz_type": biz_job_name, "exec_suite_code": request_param['suite_code']})
                return True
            else:
                return "启动发生错误"
        except Exception as ex:
            logger.error(ex)
            return "启动发生错误"

    def execute_history(self, request_param):
        logger.info("{} 启动中。。。".format(request_param))
        url = INTERFACE_URL['spider'] + INTERFACE_URL['spider_context'] + INTERFACE_URL['execute_history']
        try:
            res = http_request_post(url, request_param, headers={'Content-Type': 'application/json',
                                                                 "Authorization": INTERFACE_URL[
                                                                     'spider_api_Authorization']})
            result = res.json()
            logger.info(result)
            if "status" in result and result["status"] == "success":
                logger.info(result["data"])
                return True
            else:
                return "启动发生错误"
        except Exception as ex:
            logger.error(ex)
            return "启动发生错误"


if __name__ == '__main__':
    if len(sys.argv) < 4:
        logger.error("参数不完整！")
        sys.exit(1)
    else:
        scheduleExeBizConfigMgt = ScheduleExeBizConfigMgt(sys.argv[1], sys.argv[2])
        scheduleExeBizConfigMgt.call(sys.argv[3])
        sys.exit(0)
