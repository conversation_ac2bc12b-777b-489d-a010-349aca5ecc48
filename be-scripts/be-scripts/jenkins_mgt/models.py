from peewee import *
from dao.base_model import SpiderBaseModels

# coding: utf-8
from sqlalchemy import Column, String, TIMESTAMP, JSON, DateTime
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYINT
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class JenkinsInfo(SpiderBaseModels):
    """
     jenkins 信息表
    """
    jenkins_url = Char<PERSON><PERSON>(verbose_name='jenkins_url', max_length=128)
    username = Cha<PERSON><PERSON><PERSON>(verbose_name='用户名', max_length=64)
    password = Cha<PERSON><PERSON><PERSON>(verbose_name='密码', max_length=64)
    jenkins_state = BooleanField(verbose_name='应用状态：0-不可用，1-可用', default=1)
    jenkins_identity = Char<PERSON><PERSON>(verbose_name='jenkins_身份', max_length=32, default="slave")

    class Meta:
        db_table = 'jenkins_mgt_jenkins_info'
        verbose_name = 'jenkins信息表'


class JenkinsJobInfo(SpiderBaseModels):
    """
     jenkins job信息表
    """
    job_name = Char<PERSON><PERSON>(verbose_name='job_name', max_length=512)
    iteration_id = CharField(verbose_name='迭代号', max_length=256)
    app_name = CharField(verbose_name='应用名', max_length=128)
    jenkins_info_id = IntegerField(verbose_name='jenkins_info_id')

    class Meta:
        db_table = 'jenkins_mgt_jenkins_job_info'
        verbose_name = 'jenkins_job信息表'


class JenkinsMgtJobCompositionDetail(Base):
    __tablename__ = 'jenkins_mgt_job_composition_detail'

    id = Column(BIGINT(11), primary_key=True)
    batch_no = Column(String(255), nullable=False, comment='批次号')
    suite_code = Column(String(50), nullable=False, comment='环境')
    job_business_id = Column(String(20), comment='job业务ID，用于观察状态')
    job_url = Column(String(255), comment='job url')
    job_name = Column(String(255), nullable=False, comment='任务名称（全局唯一）')
    job_business_param = Column(JSON, comment='接口请求参数')
    step_num = Column(INTEGER(2), comment='组内任务顺序')
    status = Column(String(20), nullable=False, comment='状态')
    create_time = Column(TIMESTAMP)
    update_time = Column(TIMESTAMP)
    create_user = Column(String(50))
    update_user = Column(String(50))


class JenkinsMgtJobComposition(Base):
    __tablename__ = 'jenkins_mgt_job_composition'

    id = Column(BIGINT(11), primary_key=True)
    batch_no = Column(String(255), nullable=False, comment='批次号')
    job_build_id = Column(String(20), nullable=False, comment='任务ID')
    job_url = Column(String(255), comment='组合任务url')
    suite_code = Column(String(50), nullable=False, comment='环境')
    status = Column(String(20), nullable=False, comment='状态')
    create_time = Column(TIMESTAMP)
    update_time = Column(TIMESTAMP)
    create_user = Column(String(50))
    update_user = Column(String(50))


class JenkinsMgtBizJobInfoLog(Base):
    __tablename__ = 'jenkins_mgt_biz_job_info_log'
    __table_args__ = {'comment': '业务执行流水线日志表'}

    id = Column(BIGINT(11), primary_key=True)
    job_name = Column(String(255), comment='job名字')
    job_build_id = Column(BIGINT(11), comment='jenkins构建编号')
    job_param = Column(JSON, comment='http请求参数dict')
    job_url = Column(String(512), comment='job请求地址')
    pre_build_id = Column(BIGINT(11), comment='前一次buildID')
    job_queue_item = Column(INTEGER(11), comment='JOB队列值')
    batch_no = Column(String(255), comment='批次号')
    testset_order = Column(JSON, comment='测试集执行顺序')
    status = Column(String(20), comment='流水线状态')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class JenkinsMgtBizJobRunTestset(Base):
    __tablename__ = 'jenkins_mgt_biz_job_run_testset'
    __table_args__ = {'comment': '业务执行流水线执行测试集信息表'}

    id = Column(BIGINT(11), primary_key=True)
    batch_no = Column(String(255), comment='批次号')
    testset_id = Column(INTEGER(11), comment='测试集id')
    testset_detail = Column(JSON, comment='测试集应用脚本分支详情')
    testset_run_status = Column(String(20), comment='测试集执行状态')
    order_no = Column(INTEGER(11), comment='执行顺序')
    execute_id = Column(String(255), comment='测试集执行id')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizTestFlowScheduleConfig(Base):
    __tablename__ = 'biz_test_flow_schedule_config'
    __table_args__ = (
        {'comment': '自动化流水线配置表'}
    )

    id = Column(BIGINT(11), primary_key=True)
    biz_iter_branch = Column(String(50), nullable=False, comment='业务迭代分支')
    biz_code = Column(String(255), nullable=False, comment='业务编码')
    jenkins_info_id = Column(BIGINT(11), comment="jenkins_info表的id")
    jenkins_url = Column(String(1000), comment='定时流水线 url')
    pipeline_url = Column(String(1000), comment='定时流水线 最后运行的 pipeline url')
    biz_flow_name = Column(String(255), nullable=False, comment='业务流水线')
    suite_code = Column(String(50), nullable=False, comment='环境')
    enable_schedule = Column(TINYINT(2), nullable=False, comment='是否启用定时')
    cron = Column(String(50), nullable=False, comment='定时表达式')
    create_time = Column(TIMESTAMP, nullable=False, comment='创建时间')
    create_user = Column(String(20), nullable=False, comment='创建人')
    update_time = Column(TIMESTAMP, comment='更新时间')
    update_user = Column(String(20), comment='更新人')


class BizTestFlowGlobalParam(Base):
    __tablename__ = 'biz_test_flow_global_param'
    __table_args__ = (
        {'comment': '自动化流水线全局action参数表'}
    )

    id = Column(BIGINT(11), primary_key=True)
    schedule_config_id = Column(BIGINT(11), nullable=False, comment='定时配置ID')
    param = Column(JSON, nullable=False, comment='{"app":"acc-center-server","branch":"3.1.1"}')
    param_type = Column(String(50), nullable=False, comment='应用/等')
    create_time = Column(TIMESTAMP, nullable=False, comment='创建时间')
    create_user = Column(String(20), nullable=False, comment='创建人')
    update_time = Column(TIMESTAMP, comment='更新时间')
    update_user = Column(String(20), comment='更新人')
