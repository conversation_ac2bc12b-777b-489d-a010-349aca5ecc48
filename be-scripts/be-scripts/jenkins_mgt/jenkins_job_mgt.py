import sys
import os
from peewee import DoesNotExist
from spider_common_utils.jenkins.jenkins_server import JenkinsServerInfo, JenkinsServer

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from utils.jenkins import pipeline
from settings import logger
from jenkins_mgt.models import JenkinsInfo, JenkinsJobInfo
from jenkins_mgt.bo.create_pipeline_bo import CreatePipelineBo


class JenkinsJobMgt:
    __jenkins_info = JenkinsInfo
    __jenkins_job_info = JenkinsJobInfo
    __jenkins_server_info = JenkinsServerInfo
    __jenkins_server = JenkinsServer

    def _get_jenkins_info(self):
        jenkins_server = self.__jenkins_server(
            self.__jenkins_info.select().where(self.__jenkins_info.jenkins_state == 1))
        return jenkins_server

    def _record_jenkins_job_info(self, job_info: JenkinsJobInfo):
        self.__jenkins_job_info.insert(job_info.__dict__).execute()

    def _create_job(self, create_pipeline_bo: CreatePipelineBo) -> int:
        jenkins_info = self._get_jenkins_info()
        salve_server = jenkins_info.get_salve_server()
        master_server = jenkins_info.jenkins_master_server
        pipeline_creator = pipeline.PipelineCreator(master_server.server,
                                                    salve_server.server,
                                                    create_pipeline_bo.pipeline_name,
                                                    create_pipeline_bo.tapd_id,
                                                    template_name=create_pipeline_bo.template_name)
        pipeline_creator.create_pipeline(compile_args=create_pipeline_bo.pipeline_params,
                                         entrance=create_pipeline_bo.entrance)
        logger.info(create_pipeline_bo.iteration_id)
        return salve_server.jenkins_info_id

    def create_jenkins_pipeline(self, create_pipeline_bo: CreatePipelineBo):
        jenkins_info_id = self._create_job(create_pipeline_bo)
        self.__jenkins_job_info(job_name=create_pipeline_bo.pipeline_name,
                                app_name=create_pipeline_bo.app_name,
                                iteration_id=create_pipeline_bo.iteration_id,
                                jenkins_info_id=jenkins_info_id).save()

    def migrate_jenkins_pipeline(self, create_pipeline_bo: CreatePipelineBo):
        jenkins_info_id = self._create_job(create_pipeline_bo)
        self.__jenkins_job_info.update({self.__jenkins_job_info.jenkins_info_id: jenkins_info_id}) \
            .where(self.__jenkins_job_info.job_name == create_pipeline_bo.pipeline_name).execute()

    def get_job_info_by_iteration_id_app_name(self, iteration_id: str, app_name: str):
        try:
            job_info = self.__jenkins_job_info.get((self.__jenkins_job_info.iteration_id == iteration_id)
                                                   & (self.__jenkins_job_info.app_name == app_name))
            return job_info
        except DoesNotExist as e:
            logger.warning("未找{}迭代，{}应用的到jenkins_job 记录".format(iteration_id, app_name))
            return None

    def get_job_info_by_job_name(self, job_name: str):
        try:
            job_info = self.__jenkins_job_info.get(self.__jenkins_job_info.job_name == job_name)
            return job_info
        except DoesNotExist as e:
            logger.warning("未找job_name {]， 记录".format(job_name))
            return None

    def get_jenkins_server_info_by_id(self, id: int) -> JenkinsServerInfo:
        jenkins_info = self.__jenkins_info.get(self.__jenkins_info.id == id)
        logger.info(jenkins_info.jenkins_url)
        jenkins_server_info = self.__jenkins_server_info(jenkins_info.jenkins_url, jenkins_info.username,
                                                         jenkins_info.password)
        return jenkins_server_info

    def get_all_jenkins_server_info(self, ) -> dict:
        jenkins_server_info_dict = {}
        for jenkins_info in self.__jenkins_info.select():
            jenkins_server_info_dict[jenkins_info.id] = self.__jenkins_server_info(jenkins_info.jenkins_url,
                                                                                   jenkins_info.username,
                                                                                   jenkins_info.password)
        return jenkins_server_info_dict

    def get_jenkins_server_info_by_iteration_id_app_name(self, iteration_id: str, app_name: str):
        job_info = self.get_job_info_by_iteration_id_app_name(iteration_id, app_name)
        return self.get_jenkins_server_info_by_id(job_info.jenkins_info_id)

    def get_jenkins_server_info_by_job_name(self, job_name: str):
        job_info = self.get_job_info_by_job_name(job_name)
        return self.get_jenkins_server_info_by_id(job_info.jenkins_info_id)

    def delete_pipeline(self, iteration_id: str, app_name: str) -> str:
        jenkins_server_info = self.get_jenkins_server_info_by_iteration_id_app_name(iteration_id, app_name)
        logger.info(jenkins_server_info.__dict__)
        job_name = "{}_{}".format(iteration_id, app_name)
        self.__jenkins_job_info.delete().where((self.__jenkins_job_info.iteration_id == iteration_id)
                                               & (self.__jenkins_job_info.app_name == app_name)).execute()
        jenkins_server_info.server.delete_job(job_name)
        return job_name


if __name__ == "__main__":
    # create_pipeline_bo = CreatePipelineBo.Builder().set_iteration_id("scm_2.6.0").set_app_name("spider").set_template_name("pipeline_模板")\
    #     .set_tapd_id(0).build_bo()
    # logger.info(create_pipeline_bo.__dict__)
    # jenkins_job_mgt = JenkinsJobMgt()
    # jenkins_job_mgt.create_jenkins_pipeline(create_pipeline_bo)
    # jenkins_job_mgt.delete_pipeline("scm_2.6.0", "spider3")

    __jenkins_job_mgt = JenkinsJobMgt()
    jenkins_info = __jenkins_job_mgt._get_jenkins_info()
    master_server = jenkins_info.jenkins_master_server
    logger.info(master_server.server.get_job_config("TRADE-BATCH_迭代测试"))

    # master_server.server.create_job("TRADE-BATCH_迭代测试111", config_xml)
