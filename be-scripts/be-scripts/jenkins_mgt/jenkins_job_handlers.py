import datetime
import time

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from jenkins_mgt.models import JenkinsMgtJobCompositionDetail, JenkinsMgtBizJobInfoLog, JenkinsInfo
from settings import logger, INTERFACE_URL
from test_publish_aio.test_env_mgt_test_suite_init_log import TestEnvMgtTestSuiteInitLog
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import http_request_post


class JenkinsCompositionParent:
    handler_name = "JenkinsCompositionParent"

    def __init__(self, batch_no, stage_name):
        self.batch_no = batch_no
        self.stage_name = stage_name
        self.job_url = None
        self.job_business_id = None

    """
          启动jenkins
    """

    def start_jenkins(self):
        logger.info("正在启动:{}".format(self.stage_name))
        request_param = self.get_job_params()
        if request_param:
            self.execute_start_job(request_param)

    def execute_start_job(self, request_param):
        pass

    """
        更新jenkins 详细信息
    """

    def update_jenkins_info(self, job_url=None, job_business_id=None):
        logger.info("job_business_id:{}".format(self.job_business_id))
        if self.job_business_id:
            with DBConnectionManagerForSqlalchemy() as db:
                db.session.query(JenkinsMgtJobCompositionDetail).filter(
                    JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                    JenkinsMgtJobCompositionDetail.job_name == self.stage_name).update(
                    {"job_business_id": self.job_business_id})
        if self.job_url:
            with DBConnectionManagerForSqlalchemy() as db:
                db.session.query(JenkinsMgtJobCompositionDetail).filter(
                    JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                    JenkinsMgtJobCompositionDetail.job_name == self.stage_name).update(
                    {"job_url": self.job_url})

    """
       监测jenkins状态
    """

    def watch_jenkins_status(self):
        logger.info("watch_jenkins_status:{}".format(self.stage_name))

    def get_job_params(self):
        request_param = None
        with DBConnectionManagerForSqlalchemy() as db:
            job_composition_detail = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                JenkinsMgtJobCompositionDetail.job_name == self.stage_name).one()
            request_param = job_composition_detail.job_business_param
            logger.info("stage_name:{}， param:{}".format(self.stage_name, request_param))
        return request_param


class InitJenkinsHandler(JenkinsCompositionParent):
    handler_name = "mult_init"
    """
          启动jenkins
    """

    def watch_jenkins_status(self):
        if self.job_business_id:
            start_time = datetime.datetime.now()
            while True:
                # 检查字段状态
                job_info_log = None
                with DBConnectionManagerForSqlalchemy() as db:
                    job_info_log = db.session.query(TestEnvMgtTestSuiteInitLog).filter(
                        TestEnvMgtTestSuiteInitLog.id == self.job_business_id).one()
                logger.info("self.job_url:{}".format(self.job_url))
                logger.info("job_info_log.job_build_id:{}".format(job_info_log.job_build_id))
                if not self.job_url and job_info_log and job_info_log.job_build_id:
                    self.job_url = JenkinsInfo.get(
                        JenkinsInfo.jenkins_identity == "master").jenkins_url + "blue/organizations/jenkins/test_suite_init/detail/test_suite_init/{}/pipeline/".format(
                        job_info_log.job_build_id)
                    logger.info("job_url:{}".format(self.job_url))
                    with DBConnectionManagerForSqlalchemy() as db:
                        update_obj = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                            JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                            JenkinsMgtJobCompositionDetail.job_name == self.stage_name).update(
                            {"job_url": self.job_url})
                        logger.info("job_url更新:{}".format("完成" if update_obj > 0 else "失败"))
                        db.session.commit()
                # 检查时间是否已经超过 10 分钟
                elapsed_time = datetime.datetime.now() - start_time
                # 等待 3 秒钟后再继续检查
                if job_info_log:
                    logger.info("任务状态:{}".format(job_info_log.job_result))
                    if job_info_log.job_result:
                        if job_info_log.job_result.upper() == "SUCCESS":
                            break
                        elif job_info_log.job_result.upper() in ["ABORTED", "FAILURE"]:
                            raise Exception("执行失败")
                if elapsed_time.seconds >= 7200:
                    logger.info("已经超过2小时，校验失败")
                    raise Exception("校验失败")
                logger.info("等待 20 秒钟后再继续检查")
                time.sleep(20)
        else:
            raise Exception("执行失败")

    def execute_start_job(self, request_param):
        logger.info("{} 启动中。。。".format(request_param))
        url = INTERFACE_URL['spider'] + INTERFACE_URL['spider_context'] + INTERFACE_URL[
            'start_mult_job_url']
        try:
            res = http_request_post(url, request_param, headers={'Content-Type': 'application/json',
                                                                 "Authorization": INTERFACE_URL['spider_api_Authorization']})
            result = res.json()
            logger.info(result)
            if "status" in result and result["status"] == "success":
                logger.info(result["data"])
                self.job_business_id = result["data"]["jenkins_url"]
                return True
            else:
                return "启动发生错误"
        except Exception as ex:
            logger.error(ex)
            return "启动发生错误"


class BizTestExecJenkinsHandler(JenkinsCompositionParent):
    handler_name = "biz_job"

    def execute_start_job(self, request_param):
        logger.info("{} 启动中。。。".format(request_param))
        url = INTERFACE_URL['spider'] + INTERFACE_URL['spider_context'] + INTERFACE_URL[
            'start_biz_job_url']
        try:
            res = http_request_post(url, request_param, headers={'Content-Type': 'application/json',
                                                                 "Authorization": INTERFACE_URL['spider_api_Authorization']})
            result = res.json()
            logger.info(result)
            if "status" in result and result["status"] == "success":
                logger.info(result["data"])
                self.job_business_id = result["data"]["id"]
                return True
            else:
                return "启动发生错误"
        except Exception as ex:
            logger.error(ex)
            return "启动发生错误"

    def watch_jenkins_status(self):
        if self.job_business_id:
            start_time = datetime.datetime.now()
            while True:
                # 检查字段状态
                job_info_log = None
                with DBConnectionManagerForSqlalchemy() as db:
                    job_info_log = db.session.query(JenkinsMgtBizJobInfoLog).filter(
                        JenkinsMgtBizJobInfoLog.id == self.job_business_id).one()
                if not self.job_url and job_info_log and job_info_log.job_url:
                    self.job_url = job_info_log.job_url
                    logger.info("job_url:{}".format(self.job_url))
                    with DBConnectionManagerForSqlalchemy() as db:
                        update_obj = db.session.query(JenkinsMgtJobCompositionDetail).filter(
                            JenkinsMgtJobCompositionDetail.batch_no == self.batch_no,
                            JenkinsMgtJobCompositionDetail.job_name == self.stage_name).update(
                            {"job_url": self.job_url})
                        logger.info("job_url更新:{}".format("完成" if update_obj > 0 else "失败"))
                        db.session.commit()
                # 检查时间是否已经超过 10 分钟
                elapsed_time = datetime.datetime.now() - start_time
                # 等待 3 秒钟后再继续检查
                if job_info_log:
                    logger.info("任务状态:{}".format(job_info_log.status))
                    if job_info_log.status:
                        if job_info_log.status:
                            if job_info_log.status.upper() == "SUCCESS":
                                break
                            elif job_info_log.status.upper() in ["ABORTED", "FAILURE"]:
                                raise Exception("执行失败")
                if elapsed_time.seconds >= 10800:
                    logger.info("已经超过3小时，校验失败")
                    raise Exception("校验失败")

                logger.info("等待 20 秒钟后再继续检查")
                time.sleep(20)
        else:
            raise Exception("执行失败")
