from dao.connect.mysql import DBConnectionManager
from ci_pipeline.ci_pipeline_utils.lib_repo_info_record import LibRepoInfoRecorder



def get_online_br():
    """获取最新迭代的分支名称

    :param project_group: string.迭代版本号
    :return: list. br_list list
    """
    with DBConnectionManager() as db:
        db.cur.execute('''   SELECT
        m.pipeline_id,
        m.br_name,
        m.br_end_date,
        m.br_status,
        i.appName,
        i.git_repo_version,
        mm.lib_repo,
    IF
        ( v.max_br_end_date IS NOT NULL, m.br_name, NULL ) AS online_br_name 
    FROM
        iter_mgt_iter_info m
        INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
        AND i.git_repo_version
        IS NOT NULL LEFT JOIN (
    SELECT
        i.appName,
        MAX( m.br_end_date ) AS max_br_end_date 
    FROM
        iter_mgt_iter_info m
        INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
        AND i.git_repo_version IS NOT NULL 
    WHERE
        m.br_status = 'close' 
    GROUP BY
        i.appName 
        ) v ON v.appName = i.appName 
        AND v.max_br_end_date = m.br_end_date 
     LEFT JOIN app_mgt_app_module mm ON mm.module_name = i.appName
    WHERE v.max_br_end_date IS NOT NULL AND m.pipeline_id NOT LIKE "h5_%"''')
        br_list = []
        for row in db.cur.fetchall():
            br_list.append(row)
    return br_list


def insert_lib_repo():
    for row in get_online_br():

        # lib_repo_dto = LibRepoDto()
        # lib_repo_dto.set_iteration_id(row["pipeline_id"])
        # lib_repo_dto.set_lib_repo_branch(row["br_name"])
        # lib_repo_dto.set_module_name(row["appName"])
        # lib_repo_dto.set_lib_repo_version(row["git_repo_version"])
        # lib_repo_dto.set_lib_repo_version_log("")
        # lib_repo_dto.set_lib_repo_size("")
        # logger.info(row["appName"])
        # lib_repo_dto.set_lib_repo_url(os.path.join(PRODUCT_STORE_URL, row["lib_repo"] + '.git'))
        # logger.info("最后的制品信息 {}".format(lib_repo_dto.__dict__))
        # lib_repo_table = LibRepoTable()
        # lib_repo_table.insert_lib_repo_info(lib_repo_dto)
        lib_repo_info_recorder = LibRepoInfoRecorder(row["appName"], row["br_name"], "prod")
        lib_repo_info_recorder.record_info()
        lib_repo_info_recorder = LibRepoInfoRecorder(row["appName"], row["br_name"], "bs-zb")
        lib_repo_info_recorder.record_info()
        lib_repo_info_recorder = LibRepoInfoRecorder(row["appName"], row["br_name"], "hk-prod")
        lib_repo_info_recorder.record_info()

insert_lib_repo()