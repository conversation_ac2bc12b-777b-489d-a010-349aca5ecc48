#!/usr/bin/env python3
# coding=utf-8
# ================
# 从「vcs」中生成ES索引制品 

import asyncio
# ==== 1、环境变量 ====
import os
import sys
import traceback
import re
import concurrent.futures
import subprocess

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from abc import ABC, abstractmethod
from utils.form.models import EsMgtAppBind
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import get_all_files_by_tree
from es_mgt.es_business.models import EsMgtIndexInfo
from peewee import IntegrityError
from datetime import datetime
from threading import Lock
from settings import ES, logger, GITLAB_URL, GITLAB_LIB_SSH
from enum import unique, Enum
from ci_pipeline.pipeline_record.pipeline_record import Pipeline<PERSON>tatus, PipelineRecorder
from job.pipeline_log.pipeline_log_decorator_mgt import ShiftLeftTestPiplineLog

print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")

CREATE_USER = "howbuyscm"
UPDATE_USER = "howbuyscm"
CREATE_STAMP = 0


@unique
class StepEnum(Enum):
    STEP_PRINT_INFO = ('print_info', "信息打印")
    STEP_PULL_ES = ('pull_es', "拉取ES")
    STEP_EXTRACT_ES = ('extract_es', "提取ES")
    STEP_PUSH_ES = ('push_es', "推送ES")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class VcsTypeEnum(Enum):
    VCS_SVN = ('svn', "svn")
    VCS_GIT = ('git', "git")
    VCS_GITLAB = ('gitlab', "gitlab")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


def check_es_index_name(es_file_str):
    if " " in es_file_str:
        raise ValueError("文件名「{}」中不能包含空格。".format(es_file_str))


def ck_gitlab_code(workspace, git_group, br_name):
    es_group_name = git_group
    es_local_dir = os.path.join(workspace, es_group_name)
    if not os.path.isdir(es_local_dir):
        os.makedirs(es_local_dir)
    os.chdir(es_local_dir)
    es_repo_name = ES.get("git_repo_name")
    if not os.path.isdir(es_repo_name):
        git_code_url = "{}:{}/{}.git".format(GITLAB_URL, es_group_name, es_repo_name)
        cmd = 'pwd && git clone {}'.format(git_code_url)
        logger.info(cmd)
        os.system(cmd)

        os.chdir(es_repo_name)
        cmd = 'pwd && git checkout {}'.format(br_name)
        logger.info(cmd)
        os.system(cmd)
    else:
        os.chdir(es_repo_name)
        cmd = 'pwd && git pull'
        logger.info(cmd)
        os.system(cmd)
        cmd = 'pwd && git checkout {}'.format(br_name)
        logger.info(cmd)
        os.system(cmd)


def ck_gitlab_lib(workspace, git_repo_name, br_name):
    es_lib_group_name = ES.get("gitlib_group_name")
    es_local_dir = os.path.join(workspace, es_lib_group_name)
    if not os.path.isdir(es_local_dir):
        os.makedirs(es_local_dir)
    os.chdir(es_local_dir)
    es_lib_repo_name = git_repo_name
    if not os.path.exists(es_lib_repo_name):
        es_lib_repo_url = '{}:{}/{}.git'.format(GITLAB_LIB_SSH, es_lib_group_name, es_lib_repo_name)
        cmd = 'pwd && git clone {}'.format(es_lib_repo_url)
        logger.info(cmd)
        os.system(cmd)
    es_lib_repo_dir = os.path.join(es_local_dir, es_lib_repo_name)

    # 进入仓库目录
    os.chdir(es_lib_repo_dir)

    # 获取远程分支列表
    logger.info("获取远程分支列表")
    os.system("git fetch")
    result = subprocess.run("git branch -r", shell=True, capture_output=True, text=True)
    remote_branches = result.stdout

    # 检查远程分支是否存在
    branch_exists = f"origin/{br_name}" in remote_branches
    logger.info(f"检查远程分支 {br_name} 是否存在: {branch_exists}")

    if branch_exists:
        # 如果远程分支存在，直接checkout
        logger.info(f"远程分支 {br_name} 已存在，直接checkout")
        cmd = f"git checkout {br_name} 2>/dev/null || git checkout -b {br_name} origin/{br_name}"
        logger.info(cmd)
        os.system(cmd)
    else:
        # 如果远程分支不存在，创建新分支
        logger.info(f"远程分支 {br_name} 不存在，创建新分支")
        cmd = f"git checkout -b {br_name}"
        logger.info(cmd)
        os.system(cmd)

    # 显示当前分支信息
    os.system("git branch -a")

    # 尝试从远程拉取更新（如果远程分支存在）
    if branch_exists:
        cmd = f"git pull origin {br_name}"
        logger.info(cmd)
        os.system(cmd)


# 策略参数类
class EsStrategyParams:
    """ES索引策略参数类"""

    def __init__(self, job_build_id, workspace, git_group, br_name, app_name_list, run_flag):
        """初始化参数
        
        Args:
            job_build_id: 任务ID
            workspace: 工作目录
            git_group: 迭代分组
            br_name: 分支名称
            app_name_list: 应用名列表
            run_flag: 执行标识
        """
        self.job_build_id = job_build_id
        self.workspace = workspace
        self.git_group = git_group
        self.br_name = br_name
        self.app_name_list = app_name_list
        self.run_flag = run_flag

    def to_dict(self):
        """转换为字典，用于JSON序列化
        
        Returns:
            dict: 参数字典
        """
        return {
            'job_build_id': self.job_build_id,
            'workspace': self.workspace,
            'git_group': self.git_group,
            'br_name': self.br_name,
            'app_name_list': self.app_name_list,
            'run_flag': self.run_flag
        }


# 策略接口
class EsIndexStrategy(ABC):
    """ES索引处理策略接口"""

    @abstractmethod
    def execute(self, params: EsStrategyParams) -> tuple[str, str]:
        """执行策略
        
        Args:
            params: 策略参数对象
            
        Returns:
            tuple: (状态, 结果信息)
        """
        pass


# 具体策略类 - 拉取ES索引
class PullEsStrategy(EsIndexStrategy):
    """拉取ES索引策略"""

    @ShiftLeftTestPiplineLog(step_name="pull_es")
    def execute(self, params: EsStrategyParams) -> tuple[str, str]:
        """拉取ES索引文件
        
        Args:
            params: 策略参数对象
            
        Returns:
            tuple: (状态, 结果信息)
        """
        logger.info(">>>> 开始拉取ES索引，迭代ID：{}，执行标记为： {}".format(
            params.git_group + '_' + params.br_name, params.run_flag))

        result = "ES索引拉取成功"

        if params.run_flag:
            ck_gitlab_code(params.workspace, params.git_group, params.br_name)

        else:
            result = "无需ES索引拉取"
        return PipelineStatus.success, result


# 具体策略类 - 提取ES索引
class ExtractEsStrategy(EsIndexStrategy):
    """提取ES索引策略"""

    def _get_es_local_dir(self, workspace, git_group):
        """获取ES本地目录
        
        Args:
            workspace: 工作目录
            git_group: es分组目录
            
        Returns:
            str: ES本地目录路径
        """
        es_repo_name = ES.get("git_repo_name")
        return os.path.join(workspace, git_group, es_repo_name)

    def _extract_index_definitions(self, content, file, extracted_indices):
        """提取索引定义
        
        Args:
            content: 文件内容
            file: 文件路径
            extracted_indices: 提取的索引信息字典
        """
        # 提取索引名称部分 (PUT index_name {...})
        index_pattern = re.compile(r'PUT\s+([^\s{/]+)\s*\n?\s*\{([^}]+)\}', re.DOTALL)
        index_matches = index_pattern.findall(content)

        # 处理索引定义
        for index_name, settings in index_matches:
            index_name = index_name.strip()
            # 检查索引名称是否合法
            check_es_index_name(index_name)

            if index_name not in extracted_indices:
                extracted_indices[index_name] = {
                    'settings': settings.strip(),
                    'mappings': None,
                    'source_file': file
                }
            else:
                extracted_indices[index_name]['settings'] = settings.strip()
                extracted_indices[index_name]['source_file'] = file

    def _extract_mapping_definitions(self, content, file, extracted_indices):
        """提取映射定义
        
        Args:
            content: 文件内容
            file: 文件路径
            extracted_indices: 提取的索引信息字典
        """
        # 提取索引映射部分
        mapping_pattern = re.compile(r'PUT\s+([^\s{]+)/_mapping/_doc', re.DOTALL)
        mapping_matches = []

        for match in mapping_pattern.finditer(content):
            mapping_path = match.group(1).strip()
            start_pos = match.end()
            # 查找映射定义的开始位置（左花括号）
            left_brace_pos = content.find('{', start_pos)
            if left_brace_pos != -1:
                # 从左花括号位置开始
                pos = left_brace_pos + 1
                # 花括号计数器
                brace_count = 1
                # 查找匹配的右花括号
                while pos < len(content) and brace_count > 0:
                    if content[pos] == '{':
                        brace_count += 1
                    elif content[pos] == '}':
                        brace_count -= 1
                    pos += 1

                if brace_count == 0:  # 找到了匹配的右花括号
                    properties = content[left_brace_pos + 1:pos - 1]
                    mapping_matches.append((mapping_path, properties))

        # 处理索引映射
        for mapping_path, properties in mapping_matches:
            # 从映射路径中提取索引名称
            index_name = mapping_path.split('/')[0].strip()

            if index_name in extracted_indices:
                extracted_indices[index_name]['mappings'] = properties.strip()
            else:
                # 如果先遇到映射再遇到索引定义，也创建索引记录
                extracted_indices[index_name] = {
                    'settings': None,
                    'mappings': properties.strip(),
                    'source_file': file
                }

    def _process_es_file(self, file, extracted_indices, lock=None):
        """处理ES索引文件
        
        Args:
            file: 文件路径
            extracted_indices: 提取的索引信息字典
            lock: 线程锁，用于多线程环境下的同步
        
        Returns:
            dict: 提取的索引信息字典（单线程模式）或None（多线程模式）
        """
        if not file.endswith(".txt"):
            return None

        logger.info("处理ES索引文件：{}".format(file))
        # 创建本地索引字典，避免线程冲突
        local_indices = {}

        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                self._extract_index_definitions(content, file, local_indices)
                self._extract_mapping_definitions(content, file, local_indices)

            # 如果是多线程模式，使用锁同步更新主字典
            if lock:
                with lock:
                    for index_name, info in local_indices.items():
                        if index_name not in extracted_indices:
                            extracted_indices[index_name] = info
                        else:
                            # 合并已存在的索引信息
                            if info['settings'] and not extracted_indices[index_name]['settings']:
                                extracted_indices[index_name]['settings'] = info['settings']
                            if info['mappings'] and not extracted_indices[index_name]['mappings']:
                                extracted_indices[index_name]['mappings'] = info['mappings']
                            extracted_indices[index_name]['source_file'] = info['source_file']
                return None
            else:
                # 单线程模式，直接返回本地索引
                return local_indices
        except Exception as e:
            logger.error(f"处理文件 {file} 时发生错误: {str(e)}")
            return None

    def _collect_index_records(self, extracted_indices, git_group, br_name):
        """收集索引记录
        
        Args:
            extracted_indices: 提取的索引信息字典
            git_group: git分组
            br_name: 分支名称
            
        Returns:
            tuple: (所有记录列表, 唯一键列表)
        """
        all_records = []
        unique_keys = []
        create_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        for index_name, info in extracted_indices.items():
            source_file = info['source_file']
            logger.info("索引名称: {}, 来源文件: {}".format(index_name, source_file))
            es_file_name = os.path.basename(source_file)
            # 从文件路径中提取业务代码
            es_file_path = os.path.dirname(source_file).split(os.sep)[-1]
            es_biz_code = es_file_path
            logger.info("es文件名称: {}, 属于es的业务: {}".format(es_file_name, es_biz_code))

            # 收集记录信息
            record = {
                'es_biz_code': es_biz_code,
                'es_index_name': index_name,
                'es_file_name': es_file_name,
                'es_file_path': es_file_path,
                'es_gitlib_repo_name': git_group,
                'es_branch_name': br_name,
                'create_user': CREATE_USER,
                'create_time': create_time,
                'upload_status': 0,
                'stamp': 0
            }
            all_records.append(record)
            unique_keys.append((es_biz_code, index_name))

        return all_records, unique_keys

    def _batch_query_existing_records(self, unique_keys):
        """批量查询已存在的记录
        
        Args:
            unique_keys: 唯一键列表
            
        Returns:
            list: 已存在的记录列表
        """
        existing_records = []
        if not unique_keys:
            return existing_records

        # 构建查询条件
        query_conditions = []
        for es_biz_code, es_index_name in unique_keys:
            condition = ((EsMgtIndexInfo.es_biz_code == es_biz_code) &
                         (EsMgtIndexInfo.es_index_name == es_index_name))
            query_conditions.append(condition)

        # 如果有多个条件，使用OR连接它们
        if query_conditions:
            final_condition = query_conditions[0]
            for condition in query_conditions[1:]:
                final_condition = final_condition | condition

            # 执行批量查询
            existing_db_records = EsMgtIndexInfo.select().where(final_condition)

            # 将查询结果转换为字典，方便后续处理
            for record in existing_db_records:
                existing_records.append((record.es_biz_code, record.es_index_name))

        return existing_records

    def _insert_new_records(self, all_records, existing_records):
        """插入新记录
        
        Args:
            all_records: 所有记录列表
            existing_records: 已存在的记录列表
            
        Returns:
            tuple: (已存在记录数, 新插入记录数)
        """
        records_to_insert = []
        existing_count = 0

        for record in all_records:
            es_biz_code = record['es_biz_code']
            es_index_name = record['es_index_name']

            # 检查记录是否已存在
            if (es_biz_code, es_index_name) in existing_records:
                existing_count += 1
                logger.info("记录已存在：es_biz_code={}, es_index_name={}".format(es_biz_code, es_index_name))
            else:
                records_to_insert.append(record)
                logger.info("新记录待插入：es_biz_code={}, es_index_name={}".format(es_biz_code, es_index_name))

        # 批量插入新记录
        if records_to_insert:
            try:
                with EsMgtIndexInfo._meta.database.atomic():
                    EsMgtIndexInfo.insert_many(records_to_insert).execute()
                logger.info("成功批量插入{}条新记录到es_mgt_index_info表".format(len(records_to_insert)))
            except IntegrityError as e:
                logger.error("批量插入记录时发生完整性错误：{}".format(str(e)))
            except Exception as e:
                logger.error("批量插入记录时发生异常：{}".format(str(e)))

        return existing_count, len(records_to_insert)

    def _get_es_biz_file_list(self, file_list, app_name_list):
        es_biz_code_list = [biz.es_biz_code for biz in EsMgtAppBind.select(EsMgtAppBind.es_biz_code).where(
            EsMgtAppBind.module_name.in_(app_name_list)).distinct()]

        # 过滤出文件路径中包含es_biz_code的文件
        filtered_file_list = [file for file in file_list if any(biz_code in file for biz_code in es_biz_code_list)]

        return filtered_file_list

    @ShiftLeftTestPiplineLog(step_name="extract_es")
    def execute(self, params: EsStrategyParams) -> tuple[str, str]:
        """提取ES索引文件
        
        Args:
            params: 策略参数对象
            
        Returns:
            tuple: (状态, 结果信息)
        """
        logger.info(">>>> 开始提取S索引，迭代ID：{}，执行标记为： {}".format(
            params.git_group + '_' + params.br_name, params.run_flag))

        result = "ES索引提取成功"

        if params.run_flag:
            # 获取ES本地目录
            es_local_dir = self._get_es_local_dir(params.workspace, params.git_group)

            # 获取所有文件
            file_list = get_all_files_by_tree(es_local_dir)
            logger.info(file_list)

            file_list = self._get_es_biz_file_list(file_list, params.app_name_list)
            # 存储提取的索引信息的字典
            extracted_indices = {}

            # 使用线程池并行处理文件
            # 创建线程锁用于同步对extracted_indices的访问
            thread_lock = Lock()

            # 设置最大工作线程数，可根据系统性能调整
            max_workers = min(10, (os.cpu_count() or 4) * 4)
            logger.info(f"使用 {max_workers} 个线程并行处理ES索引文件")

            # 使用线程池执行文件处理
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有文件处理任务
                futures = []
                for file in file_list:
                    if file.endswith(".txt"):
                        future = executor.submit(self._process_es_file, file, extracted_indices, thread_lock)
                        futures.append(future)

                # 等待所有任务完成
                completed = 0
                total = len(futures)
                for future in concurrent.futures.as_completed(futures):
                    completed += 1
                    if completed % 10 == 0 or completed == total:
                        logger.info(f"文件处理进度: {completed}/{total}")
                    try:
                        # 获取任务结果（这里不需要使用返回值，因为结果已经通过线程安全方式合并到extracted_indices）
                        future.result()
                    except Exception as e:
                        logger.error(f"处理文件时发生异常: {str(e)}")

            logger.info(f"所有文件处理完成，共处理 {len(file_list)} 个文件")

            # 输出提取结果
            if extracted_indices:
                logger.info("成功提取到以下ES索引信息：")

                # 收集索引记录
                all_records, unique_keys = self._collect_index_records(extracted_indices, params.git_group,
                                                                       params.br_name)

                # 批量查询已存在的记录
                existing_records = self._batch_query_existing_records(unique_keys)

                # 插入新记录
                existing_count, inserted_count = self._insert_new_records(all_records, existing_records)

                logger.info("处理完成：共发现{}条记录，其中{}条已存在，{}条新插入".format(
                    len(extracted_indices),
                    existing_count,
                    inserted_count
                ))
            else:
                logger.warning("未从文件中提取到任何ES索引信息")
                result = "未找到ES索引信息"
        else:
            result = "无需ES索引提取"
        return PipelineStatus.success, result


# 具体策略类 - 推送ES索引
class PushEsStrategy(EsIndexStrategy):
    """推送ES索引策略"""

    @ShiftLeftTestPiplineLog(step_name="push_es")
    def execute(self, params: EsStrategyParams) -> tuple[str, str]:
        """推送ES索引
        
        Args:
            params: 策略参数对象
            
        Returns:
            tuple: (状态, 结果信息)
        """
        logger.info(">>>> 开始推送S索引，迭代ID：{}，执行标记为： {}".format(
            params.git_group + '_' + params.br_name, params.run_flag))

        result = "ES索引推送成功"

        if params.run_flag:
            ck_gitlab_lib(params.workspace, params.git_group, params.br_name)

            es_source_dir = os.path.join(params.workspace, params.git_group, ES.get("git_repo_name"))
            es_target_dir = os.path.join(params.workspace, ES.get("gitlib_group_name"), params.git_group)

            # 用rsync方式同步文件, 排除.git目录
            rsync_cmd = "rsync -avz --delete --exclude='.git' {}/ {}".format(es_source_dir, es_target_dir)
            logger.info(rsync_cmd)
            os.system(rsync_cmd)

            os.chdir(es_target_dir)

            logger.info("git pull")
            os.system("git pull")
            logger.info("git add -A")
            os.system("git add -A")
            logger.info("git commit -m 'push {} es index'".format(params.br_name))
            os.system("git commit -m 'push {} es index'".format(params.br_name))
            logger.info("git push --set-upstream origin {}".format(params.br_name))
            os.system("git push --set-upstream origin {}".format(params.br_name))

            # 更新数据库中的记录状态
            try:
                # 更新es_mgt_index_info表中符合条件的记录
                update_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                update_query = EsMgtIndexInfo.update(
                    upload_status=1,
                    upload_time=update_time,
                    update_user=UPDATE_USER
                ).where(
                    (EsMgtIndexInfo.es_branch_name == params.br_name) &
                    (EsMgtIndexInfo.upload_status == 0)
                )
                updated_count = update_query.execute()
                logger.info("成功更新{}条记录的上传状态为已上传".format(updated_count))
            except Exception as e:
                logger.error("更新记录状态时发生异常：{}".format(str(e)))
                logger.error(traceback.format_exc())

        else:
            result = "无需ES索引推送"
        return PipelineStatus.success, result


# 上下文类
class EsIndexContext:
    """ES索引处理上下文"""

    def __init__(self):
        self._strategies = {
            StepEnum.STEP_PULL_ES.type_name: PullEsStrategy(),
            StepEnum.STEP_EXTRACT_ES.type_name: ExtractEsStrategy(),
            StepEnum.STEP_PUSH_ES.type_name: PushEsStrategy()
        }

    def execute_strategy(self, step_name, job_build_id, workspace, git_group, br_name, app_name_list, run_flag) -> \
            tuple[str, str]:
        """执行指定的策略
        
        Args:
            step_name: 步骤名称
            job_build_id: 任务ID
            workspace: 工作目录
            git_group: 迭代分组
            br_name: 分支名称
            app_name_list: 应用名列表
            run_flag: 执行标识
            
        Returns:
            tuple: (状态, 结果信息)
        """
        if step_name not in self._strategies:
            raise ValueError("未知的步骤名称：{}".format(step_name))

        strategy = self._strategies[step_name]
        params = EsStrategyParams(job_build_id, workspace, git_group, br_name, app_name_list, run_flag)
        return strategy.execute(params)


def main(step_name, param_list):
    """主函数
    
    Args:
        step_name: 步骤名称
        param_list: 参数列表
        
    Returns:
        int: 执行状态码
    """
    try:
        # 解析作业名称获取迭代ID和应用名
        job_build_id = param_list[1]
        workspace = param_list[2]
        iteration_id = param_list[5]
        app_name_str = param_list[6]
        app_name_list = app_name_str.split(',')
        git_group = iteration_id.split("_")[0]
        br_name = iteration_id.split("_")[1]
        # 获取迭代关联的应用列表
        if EsMgtAppBind.select().where(EsMgtAppBind.module_name.in_(app_name_list)).exists():
            run_flag = True
        else:
            run_flag = False
        logger.info("run_flag: {}".format(run_flag))
        logger.info("step_name：{}".format(step_name))
        # 使用策略模式处理不同步骤
        if step_name in [StepEnum.STEP_PULL_ES.type_name, StepEnum.STEP_EXTRACT_ES.type_name,
                         StepEnum.STEP_PUSH_ES.type_name]:
            context = EsIndexContext()
            status, result = context.execute_strategy(step_name, job_build_id, workspace, git_group, br_name,
                                                      app_name_list, run_flag)
            logger.info("执行结果：{}".format(result))
            return 0 if status == PipelineStatus.success else 1
        else:
            logger.error("未知的步骤名称：{}".format(step_name))
            return 1

    except Exception as e:
        logger.error("执行过程中发生异常：{}".format(str(e)))
        logger.error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    # 参数检查
    if len(sys.argv) < 7:
        print("参数不足，使用方式：python create_es_index_from_vcs.py <步骤名> <标志文件目录> <作业名称> <工作空间>")
        sys.exit(1)

    step_name = sys.argv[1]
    # 执行主函数
    exit_code = main(step_name, sys.argv[1:])
    sys.exit(exit_code)
