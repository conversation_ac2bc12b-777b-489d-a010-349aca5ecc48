from peewee import *
from dao.base_model import SpiderBaseModels


class EsMgtIndexInfo(SpiderBaseModels):
    """
    es索引信息表
    """
    es_biz_code = CharField(verbose_name='es业务', max_length=50)
    es_index_name = Char<PERSON><PERSON>(verbose_name='es索引名称', max_length=100)
    es_file_name = CharField(verbose_name='es文件名称', max_length=50)
    es_file_path = Char<PERSON>ield(verbose_name='es文件路径', max_length=50)
    es_gitlib_repo_name = CharField(verbose_name='es制品仓库名', max_length=20)
    es_branch_name = CharField(verbose_name='es分支名称', max_length=500)
    upload_status = IntegerField(verbose_name='上传状态', default=0)
    upload_time = DateTimeField(verbose_name='上传时间', null=True)
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'es_mgt_index_info'
        verbose_name = 'es索引信息表'
        unique_together = ('es_biz_code', 'es_index_name')
