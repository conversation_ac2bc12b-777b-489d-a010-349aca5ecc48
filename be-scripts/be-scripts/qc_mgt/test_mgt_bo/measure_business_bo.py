class MeasureBusinessBo:
    def __init__(self, builder):
        self.__flag_file_dir = builder.flag_file_dir
        self.__iteration_id = builder.iteration_id
        self.__app_name = builder.app_name
        self.__workspace = builder.workspace
        self.__sid = builder.sid

    @property
    def flag_file_dir(self):
        return self.__flag_file_dir

    @property
    def iteration_id(self):
        return self.__iteration_id

    @property
    def module_name(self):
        return self.__app_name

    @property
    def app_name(self):
        return self.__app_name

    @property
    def workspace(self):
        return self.__workspace

    @property
    def sid(self):
        return self.__sid

    class Builder:
        flag_file_dir: str = ""
        iteration_id: str = ""
        app_name: str = ""
        workspace: str = ""
        sid: int = None

        def set_flag_file_dir(self, flag_file_dir: str):
            self.flag_file_dir = flag_file_dir
            return self

        def set_iteration_id(self, iteration_id: str):
            self.iteration_id = iteration_id
            return self

        def set_app_name(self, app_name: str):
            self.app_name = app_name
            return self

        def set_workspace(self, workspace: str):
            self.workspace = workspace
            return self

        def set_sid(self, sid: int):
            self.sid = sid
            return self

        def verify_basic_property(self):
            if self.app_name == "":
                raise Exception("app_name 不可为空")
            if self.iteration_id == "":
                raise Exception("iteration_id 不可为空")
            if self.sid is None:
                raise Exception("sid 不可为空")
            if self.flag_file_dir == "":
                raise Exception("flag_file_dir 不可为空")
            if self.workspace == "":
                raise Exception("workspace 不可为空")

        def build_bo(self):
            self.verify_basic_property()
            return MeasureBusinessBo(self)
