<?xml version="1.0"?>

<ruleset name="AlibabaJavaOthers" xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
    <description>AlibabaJavaOthers</description>

    <rule name="AvoidPatternCompileInMethodRule" language="java"
        message="java.other.AvoidPatternCompileInMethodRule.rule.msg"
        class="com.alibaba.p3c.pmd.lang.java.rule.other.AvoidPatternCompileInMethodRule">
        <description>java.other.AvoidPatternCompileInMethodRule.rule.desc</description>
        <priority>1</priority>
        <example>
<![CDATA[
    public class XxxClass {
        // Use precompile
        private static Pattern NUMBER_PATTERN = Pattern.compile("[0-9]+");
        public Pattern getNumberPattern() {
            // Avoid use Pattern.compile in method body.
            Pattern localPattern = Pattern.compile("[0-9]+");
            return localPattern;
        }
    }
]]>
      </example>
    </rule>

    <rule name="AvoidApacheBeanUtilsCopyRule" language="java"
        message="java.other.AvoidApacheBeanUtilsCopyRule.rule.msg"
        class="com.alibaba.p3c.pmd.lang.java.rule.other.AvoidApacheBeanUtilsCopyRule">
        <description>java.other.AvoidApacheBeanUtilsCopyRule.rule.desc</description>
        <priority>1</priority>
        <example>
<![CDATA[
    TestObject a = new TestObject();
    TestObject b = new TestObject();
    a.setX(b.getX());
    a.setY(b.getY());
]]>
      </example>
    </rule>

    <rule name="AvoidNewDateGetTimeRule" language="java"
        message="java.other.AvoidNewDateGetTimeRule.rule.msg"
        class="com.alibaba.p3c.pmd.lang.java.rule.other.AvoidNewDateGetTimeRule">
        <description>java.other.AvoidNewDateGetTimeRule.rule.desc</description>
        <priority>1</priority>
        <example>
<![CDATA[
    public class TimeMillisDemo {
        public static void main(String args[]) {
            // Positive example:
            long a = System.currentTimeMillis();
            // Negative example:
            long b = new Date().getTime();

            System.out.println(a);
            System.out.println(b);
        }
    }
]]>
      </example>
    </rule>

    <rule name="AvoidMissUseOfMathRandomRule" language="java"
          message="java.other.AvoidMissUseOfMathRandomRule.rule.msg"
          class="com.alibaba.p3c.pmd.lang.java.rule.other.AvoidMissUseOfMathRandomRule">
        <priority>3</priority>
        <example>
<![CDATA[
Negative example:
    Long randomLong =(long) (Math.random() * 10);
]]>
        </example>
        <example>
<![CDATA[
Positive example:
    Long randomLong = new Random().nextLong();
]]>
        </example>
    </rule>

    <rule name="MethodTooLongRule" language="java"
          message="java.other.MethodTooLongRule.rule.msg"
          class="com.alibaba.p3c.pmd.lang.java.rule.other.MethodTooLongRule">
        <description>java.other.MethodTooLongRule.rule.desc</description>
        <priority>3</priority>
    </rule>

    <rule name="UseRightCaseForDateFormatRule" language="java"
          message="java.other.UseRightCaseForDateFormatRule.rule.msg"
          class="com.alibaba.p3c.pmd.lang.java.rule.other.UseRightCaseForDateFormatRule">
        <description>java.other.UseRightCaseForDateFormatRule.rule.desc</description>
        <priority>2</priority>
        <example>
            <![CDATA[
Negative example:
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
 ]]>
        </example>
        <example>
            <![CDATA[
Positive example:
        SimpleDateFormat format = new SimpleDateFormat("YYYY-mm-dd HH:mm:ss");
]]>
        </example>
    </rule>

    <rule name="AvoidDoubleOrFloatEqualCompareRule" language="java"
          message="java.other.AvoidDoubleOrFloatEqualCompareRule.rule.msg"
          class="com.alibaba.p3c.pmd.lang.java.rule.other.AvoidDoubleOrFloatEqualCompareRule">
        <description>java.other.AvoidDoubleOrFloatEqualCompareRule.rule.desc</description>
        <priority>2</priority>
        <example>
            <![CDATA[
Negative example:
        float g = 0.7f-0.6f;
        float h = 0.8f-0.7f;
        if (g == h) {
            System.out.println("true");
        }
 ]]>
        </example>
        <example>
            <![CDATA[
Positive example:
        double dis = 1e-6;
        double d1 = 0.0000001d;
        double d2 = 0d;
        System.out.println(Math.abs(d1 - d2) < dis);
]]>
        </example>
    </rule>
</ruleset>
