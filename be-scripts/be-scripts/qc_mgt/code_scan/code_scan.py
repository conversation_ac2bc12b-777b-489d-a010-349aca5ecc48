import os
import lizard

from settings import logger, SONAR_SCANNER
from iter_mgt.models import IterAppInfo


class CodeScanner:
    p3c_tool_name = "p3c-pmd-2.0.1-jar-with-dependencies.jar"
    p3c_tool_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources")
    p3c_tool_path = os.path.join(p3c_tool_dir, p3c_tool_name)
    p3c_rules_dir = os.path.join(p3c_tool_dir, "p3c_rules", "java")

    def ccn_scan(self, code_dir, file_name):
        file_path = os.path.join(code_dir, file_name)
        res = lizard.main(["", "-C", "20", "-s", "cyclomatic_complexity", "-l", "java", code_dir, "-o", file_path, "--csv"])
        # res = lizard.main(["", code_dir, "-o", file_path, "--csv"])
        logger.info(res)
        return file_path

    def _get_classes_path(self, iteration_id: str, git_path: str):
        """ 根据pom文件的目录 决定target目录有几层
         ftx-console/ftx-console-common/pom.xml
 ftx-console/ftx-console-dao/pom.xml
ftx-console/ftx-console-facade/pom.xml
 ftx-console/ftx-console-service/pom.xml
 ftx-console/ftx-console-web/pom.xml
 ftx-console/pom.xml
 ['./**/target/classes', './target/classes']
        :param iteration_id:
        :return:
        """
        classes_dir_list = []
        sql = """                   
                 SELECT pom_path FROM iter_mgt_iter_app_info i LEFT JOIN app_mgt_app_build
            a ON i.appName = a.module_name LEFT JOIN app_mgt_app_info f ON a.app_id = f.id
             WHERE a.package_type !=  'pom' and i.pipeline_id ='{}' and f.git_path = '/{}'""".format(iteration_id, git_path)
        logger.info(sql)
        for row in IterAppInfo.raw(sql):
            logger.info(row.pom_path)
            dir_level = len(row.pom_path.split("/")) - 2
            classes_dir = "./{}target/classes".format(dir_level*"**/")
            if classes_dir in classes_dir_list:
                continue
            else:
                classes_dir_list.append(classes_dir)
        logger.info(classes_dir_list)
        return classes_dir_list

    def get_sonar_scan_cmd(self, iteration_id, code_dir, app_name, br_name, git_path):
        classes_dir_list=self._get_classes_path(iteration_id, git_path)
        sonar_cmd_list = ["cd", code_dir, "&&", "{}/bin/sonar-scanner".format(SONAR_SCANNER["base_dir"]),
                          "-Dsonar.projectKey=com.howbuy:{}".format(app_name),
                          "-Dsonar.projectName={}".format(app_name),
                          "-Dsonar.sourceEncoding=UTF-8",
                          "-Dsonar.language=java",
                          "-Dsonar.projectVersion={}".format(br_name),
                          "-Dsonar.branch.name={}".format(br_name),
                          "-Dsonar.java.binaries={}".format(",".join(classes_dir_list)),
                          "-Dsonar.sources=."]

        return " ".join(sonar_cmd_list)
        # os.system(" ".join(sonar_cmd_list))

    @staticmethod
    def _get_p3c_rules(p3c_rules_dir):
        dir_list = []
        for file_name in os.listdir(p3c_rules_dir):
            dir_list.append(os.path.join(p3c_rules_dir, file_name))
        return ",".join(dir_list)

    def p3c_scan(self, code_dir, file_name, p3c_tool_path="", p3c_rules_dir=""):
        if p3c_tool_path == "":
            p3c_tool_path = self.p3c_tool_path
        if p3c_rules_dir == "":
            p3c_rules_dir = self.p3c_rules_dir
        file_path = os.path.join(code_dir, file_name)
        cmd = "java -cp {} net.sourceforge.pmd.PMD -d {} -R {} -f csv -r {}".format(p3c_tool_path,
                                                                                      code_dir,
                                                                                      self._get_p3c_rules(p3c_rules_dir),
                                                                                      file_path)
        logger.info(cmd)
        os.system(cmd)
        return file_path


if __name__ == "__main__":
    code_dir = "D:\\qap\\howbuy-qa-info"
    file_name = "cnn.csv"
    code_scanner = CodeScanner()
    #code_scanner.p3c_scan(code_dir, file_name)
    code_scanner._get_classes_path("ftx_patest-0105")
