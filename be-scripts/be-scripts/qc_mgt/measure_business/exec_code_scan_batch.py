import json
import os
import sys
import datetime
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger
from common.call_api.gitlab.gitlab_api import GitLabApi
from iter_mgt.iter_mgt_ser import get_open_iter_app_repo_info
from product_mgt.models import LibInfo
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
import time


class CodeScanBatch:
    __lib_info = LibInfo
    __jenkins_job_mgt = JenkinsJobMgt

    def _get_open_repo_br_dict(self, open_iter_app_repo):
        repo_br_dict = {}
        for row in open_iter_app_repo:
            if row["code_path"] in repo_br_dict:
                repo_br_dict[row["code_path"]].append(row["br_name"])
            else:
                repo_br_dict[row["code_path"]] = [row["br_name"]]
        return repo_br_dict

    def _get_repo_br_last_version(self, repo_br_dict: dict)->dict:
        repos_path_id_dict = {}
        gitlab_api = GitLabApi()
        repo_br_last_version_dict = {}
        for p in gitlab_api.gl.projects.list(all=True, all_list=False):
            repos_path_id_dict[p.path_with_namespace] = p.id

        for reps_path, br_list in repo_br_dict.items():
            app_repo = gitlab_api.gl.projects.get(repos_path_id_dict[reps_path])
            for b in app_repo.branches.list(all=True, all_list=False):
                if b.name in br_list:
                    repo_br_last_version_dict["{}_{}".format(reps_path, b.name)] = b.commit['id']
        logger.info(repo_br_last_version_dict)
        return repo_br_last_version_dict

    def get_not_code_scan_jenkins_job(self):
        open_iter_app_repo = []
        job_info_dict = {}
        for row in get_open_iter_app_repo_info():
            open_iter_app_repo.append(row)
        repo_br_dict = self._get_open_repo_br_dict(open_iter_app_repo)
        repo_br_last_version_dict = self._get_repo_br_last_version(repo_br_dict)
        for row in open_iter_app_repo:
            is_product = False
            for product_info in self.__lib_info.select().where((self.__lib_info.iteration_id == row["iteration_id"]),
                   (self.__lib_info.app_name == row["app_name"]),
                   (self.__lib_info.lib_type << ["p3c", "ccn"]),
                   (self.__lib_info.suite_code == "")):
                is_product = True
                repo_br_last_version = repo_br_last_version_dict.get("{}_{}".format(row["code_path"], row["br_name"]))
                if not repo_br_last_version:
                    logger.info("gitlab上找不到仓库{}分支{}的最后提交记录!".format(row["code_path"], row["br_name"]))
                else:
                    if repo_br_last_version != product_info.code_last_version:
                        job_info_dict[row["job_name"]] = row["jenkins_info_id"]
                        break
            if is_product is False:
                job_info_dict[row["job_name"]] = row["jenkins_info_id"]
        logger.info(job_info_dict)
        logger.info("本次执行个数{}".format(len(job_info_dict)))
        return job_info_dict

    def _exec_code_scan_batch(self, job_info_dict):
        jenkins_job_mgt = self.__jenkins_job_mgt()
        jenkins_server_info_dict = jenkins_job_mgt.get_all_jenkins_server_info()
        for job_name, jenkins_info_id in job_info_dict.items():
            jenkins_server = jenkins_server_info_dict[jenkins_info_id]
            logger.info(jenkins_server.jenkins_url)
            logger.info("取消 {} 的环境绑定".format(job_name))
            self._remove_test_env_publish(jenkins_server.server, job_name)
            logger.info("开始执行 {}".format(job_name))
            jenkins_server.server.build_job(job_name, parameters={"is_code_scan": "true"})
            time.sleep(1)
        # self._remove_test_env_publish(jenkins_server.server, "ftx_0.0.0000_ftx-console-web")
        # jenkins_server.server.build_job("ftx_0.0.0000_ftx-console-web", parameters={"is_code_scan": "true"})

    @staticmethod
    def _remove_test_env_publish(jenkins_server, job_name):
        job_xml = jenkins_server.get_job_config(job_name)
        new_pipeline_xml_list = job_xml.split("//bind_suite")
        new_suite_code = "无"
        new_xml = "//bind_suite".join([new_pipeline_xml_list[0],
                                       '\n suite_code = "{}"\n'.format(new_suite_code),
                                       new_pipeline_xml_list[2]])
        jenkins_server.reconfig_job(job_name, new_xml)

    def main(self):
        job_info_dict = self.get_not_code_scan_jenkins_job()
        logger.info(len(job_info_dict))
        self._exec_code_scan_batch(job_info_dict)


if __name__ == "__main__":

    csb = CodeScanBatch()
    csb.main()
