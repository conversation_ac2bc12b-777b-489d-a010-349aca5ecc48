import json
import os
import sys
import datetime
import traceback

import gitlab

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql.iter_info import get_app_git_path, get_latest_archive_iteration_version, get_module_name_ops_info
from product_mgt.bo.product_info_bo import ProductInfoBo
from product_mgt.product_repos import ProductRepos
from common.ext_cmd.nginx.nginx_repos import NginxRepos
from settings import logger, ES, GITLAB_HTTP, GITLAB_TOKEN
from qc_mgt.code_scan.code_scan import CodeScanner
from qc_mgt.test_mgt_bo.measure_business_bo import MeasureBusinessBo
from ci_pipeline.pipeline_record.pipeline_record import PipelineRecorder, PipelineStatus
from settings import NGINX_LIB_REPO
from product_mgt.product_record import ProductRecorder
from utils.call_external_interface.call_http_api import MantisHttpCaller, SpiderHttpCaller
from qc_mgt.guard_switch.guard_switch import GuardSwitch
from iter_mgt.models import IterInfo
from qc_mgt.qc_mgt_ser import get_measure_info, get_measure_info_by_group_name
from time import sleep
from ci_pipeline.ci_pipeline_models.iter_models import BranchesModel
from utils.form.models import EsMgtAppBind


class MeasureBusiness:
    __code_scanner = CodeScanner
    __check_switch = GuardSwitch
    __nginx_repos = NginxRepos
    __nginx_info = NGINX_LIB_REPO
    _file_rule = "{}_{}_{}_{}.csv"

    @staticmethod
    def __p3c_scan(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        file_name = MeasureBusiness._file_rule.format(ProductRepos.LibType.P3C.value,
                                                      measure_business_bo.iteration_id,
                                                      measure_business_bo.app_name,
                                                      datetime.datetime.now().strftime("%Y%m%d%H%M%S"))
        git_path = get_app_git_path(measure_business_bo.app_name)
        git_path = git_path.replace("/", "")
        code_dir = os.path.join(measure_business_bo.workspace, git_path)

        code_scanner = MeasureBusiness.__code_scanner()
        code_scanner.p3c_scan(code_dir, file_name)
        with open(measure_business_bo.flag_file_dir, "r+") as f:
            json_dict = json.loads(f.read())
            json_dict["{}_file_name".format(ProductRepos.LibType.P3C.value)] = file_name
            f.seek(0, 0)
            f.truncate()
            f.write(json.dumps(json_dict))
        return PipelineStatus.success, "p3c 扫描成功"

    @staticmethod
    def __ccn_scan(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        file_name = MeasureBusiness._file_rule.format(ProductRepos.LibType.CCN.value,
                                                      measure_business_bo.iteration_id,
                                                      measure_business_bo.app_name,
                                                      datetime.datetime.now().strftime("%Y%m%d%H%M%S"))
        git_path = get_app_git_path(measure_business_bo.app_name)
        git_path = git_path.replace("/", "")
        code_dir = os.path.join(measure_business_bo.workspace, git_path)
        code_scanner = MeasureBusiness.__code_scanner()
        code_scanner.ccn_scan(code_dir, file_name)
        with open(measure_business_bo.flag_file_dir, "r+") as f:
            json_dict = json.loads(f.read())
            json_dict["{}_file_name".format(ProductRepos.LibType.CCN.value)] = file_name
            f.seek(0, 0)
            f.truncate()
            f.write(json.dumps(json_dict))
        return PipelineStatus.success, "ccn 扫描成功"

    @staticmethod
    def __push_p3c_report(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        with open(measure_business_bo.flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
            file_name = json_dict["{}_file_name".format(ProductRepos.LibType.P3C.value)]
        product_info_bo = ProductInfoBo.Builder().set_iteration_id(measure_business_bo.iteration_id). \
            set_app_name(measure_business_bo.app_name).set_lib_type(ProductRepos.LibType.P3C.value).set_lib_repo_tool(
            ProductRepos.LibRepoTool.NGINX.value) \
            .set_lib_name(file_name).build_bo()
        pr = ProductRepos()
        pr.upload_file(measure_business_bo.workspace, product_info_bo)
        return PipelineStatus.success, "p3c 推送制品成功"

    @staticmethod
    def __push_ccn_report(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        with open(measure_business_bo.flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
            file_name = json_dict["{}_file_name".format(ProductRepos.LibType.CCN.value)]
        product_info_bo = ProductInfoBo.Builder().set_iteration_id(measure_business_bo.iteration_id). \
            set_app_name(measure_business_bo.app_name).set_lib_type(ProductRepos.LibType.CCN.value).set_lib_repo_tool(
            ProductRepos.LibRepoTool.NGINX.value) \
            .set_lib_name(file_name).build_bo()
        pr = ProductRepos()
        pr.upload_file(measure_business_bo.workspace, product_info_bo)
        return PipelineStatus.success, "lizard 推送制品成功"

    @staticmethod
    def __analyze_p3c_report(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        data = ProductRecorder.get_request_product_info(measure_business_bo.app_name,
                                                        measure_business_bo.iteration_id,
                                                        ProductRepos.LibType.P3C.value)
        last_app_archive_info = get_latest_archive_iteration_version(measure_business_bo.app_name)
        data.update(last_app_archive_info)
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("analysis_p3c_report", json=data)
            logger.info(res)
            if res["status"] == "success":
                return PipelineStatus.success, res["msg"]
            else:
                # 如果门禁开启 返回失败
                if MeasureBusiness.__check_switch().get_guard_switch(GuardSwitch.GuardName.P3C.value):
                    return PipelineStatus.failure, res["msg"]
                else:
                    return PipelineStatus.unstable, res["msg"]
        except Exception as e:
            logger.error(e)
            return PipelineStatus.failure, "分析失败"

    @staticmethod
    def __analyze_ccn_report(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        data = ProductRecorder.get_request_product_info(measure_business_bo.app_name,
                                                        measure_business_bo.iteration_id,
                                                        ProductRepos.LibType.CCN.value)
        last_app_archive_info = get_latest_archive_iteration_version(measure_business_bo.app_name)
        data.update(last_app_archive_info)
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("analysis_ccn_report", json=data)
            logger.info(res)
            if res["status"] == "success":
                return PipelineStatus.success, res["msg"]
            else:
                # 如果门禁开启 返回失败
                if MeasureBusiness.__check_switch().get_guard_switch(GuardSwitch.GuardName.CCN.value):
                    return PipelineStatus.failure, res["msg"]
                else:
                    return PipelineStatus.unstable, res["msg"]
        except Exception as e:
            logger.error(e)
            return PipelineStatus.failure, "分析失败"

    @staticmethod
    def __get_p3c_result(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        # measure_info = get_measure_info(measure_business_bo.iteration_id, measure_business_bo.app_name)
        measure_info = get_measure_info_by_group_name(measure_business_bo.iteration_id, measure_business_bo.app_name)
        if measure_info.get('p3c'):
            p3c_value = measure_info.get('p3c')
            logger.info("{}已设p3c置白名单".format(measure_business_bo.app_name))
        else:
            p3c_value = 0
        params = ProductRecorder.get_request_product_info(measure_business_bo.app_name,
                                                          measure_business_bo.iteration_id,
                                                          ProductRepos.LibType.P3C.value)
        last_app_archive_info = get_latest_archive_iteration_version(measure_business_bo.app_name)
        ops_info = get_module_name_ops_info(measure_business_bo.app_name)
        params.update(last_app_archive_info)
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("p3c_entrance_guard", params=params)
            logger.info(res)
            if res["status"] == "success":
                return PipelineStatus.success, res["msg"]
            else:
                # 如果门禁开启 返回失败
                if ops_info['need_online'] == 0:
                    return PipelineStatus.success, res["msg"]
                if MeasureBusiness.__check_switch().get_guard_switch(GuardSwitch.GuardName.P3C.value) and \
                        not p3c_value:
                    return PipelineStatus.failure, res["msg"]
                else:
                    return PipelineStatus.unstable, res["msg"]
        except Exception as e:
            logger.error(e)
            return PipelineStatus.failure, "分析失败"

    @staticmethod
    def __get_ccn_result(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        measure_info = get_measure_info_by_group_name(measure_business_bo.iteration_id, measure_business_bo.app_name)
        if measure_info.get('ccn'):
            ccn_value = measure_info.get('ccn')
            logger.info("{}已设ccn置白名单".format(measure_business_bo.app_name))
        else:
            ccn_value = 0
        params = ProductRecorder.get_request_product_info(measure_business_bo.app_name,
                                                          measure_business_bo.iteration_id,
                                                          ProductRepos.LibType.CCN.value)
        last_app_archive_info = get_latest_archive_iteration_version(measure_business_bo.app_name)
        params.update(last_app_archive_info)
        ops_info = get_module_name_ops_info(measure_business_bo.app_name)
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("ccn_entrance_guard", params=params)
            logger.info(res)
            if res["status"] == "success":
                return PipelineStatus.success, res["msg"]
            else:
                # 如果门禁开启 返回失败
                if ops_info['need_online'] == 0:
                    return PipelineStatus.success, res["msg"]
                if MeasureBusiness.__check_switch().get_guard_switch(GuardSwitch.GuardName.CCN.value) and \
                        not ccn_value:
                    return PipelineStatus.failure, res["msg"]
                else:
                    return PipelineStatus.unstable, res["msg"]
        except Exception as e:
            logger.error(e)
            return PipelineStatus.failure, "分析失败"

    @staticmethod
    def __get_sonar_scan_cmd(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        git_path = get_app_git_path(measure_business_bo.app_name)
        git_path = git_path.replace("/", "")
        code_dir = os.path.join(measure_business_bo.workspace, git_path)
        code_scanner = MeasureBusiness.__code_scanner()
        br_name = IterInfo.get(IterInfo.pipeline_id == measure_business_bo.iteration_id).br_name
        sonar_cmd = code_scanner.get_sonar_scan_cmd(measure_business_bo.iteration_id,
                                                    code_dir, measure_business_bo.app_name, br_name, git_path)
        logger.info(sonar_cmd)
        with open(measure_business_bo.flag_file_dir, "r+") as f:
            json_dict = json.loads(f.read())
            json_dict["sonar_cmd"] = sonar_cmd
            f.seek(0, 0)
            f.truncate()
            f.write(json.dumps(json_dict))
        return PipelineStatus.success, sonar_cmd

    @staticmethod
    def __analysis_sonar_report(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        git_path = get_app_git_path(measure_business_bo.app_name)
        git_path = git_path.replace("/", "")
        report_task_path = os.path.join(measure_business_bo.workspace, git_path, '.scannerwork/report-task.txt')
        report_url_start = "http://sonar.howbuy.pa/sonarqube/project/issues?"
        report_url_end = "&resolved=false"
        logger.info("report_task_path========={}".format(report_task_path))
        logger.info("job_url========={}".format(os.environ["BUILD_URL"]))
        job_url = os.environ["BUILD_URL"]

        with open(report_task_path, "r") as f:
            res = f.readlines()
            for text in res:
                text = text.strip()
                if text.startswith("branch="):
                    br_name = text.split("=")[-1]
                elif text.startswith("dashboardUrl="):
                    report_url = "=".join(text.split("=")[1:])
                elif text.startswith("ceTaskId="):
                    task_id = text.split("=")[-1]
        params = {"app_name": measure_business_bo.app_name, "br_name": br_name,
                  "iteration_id": measure_business_bo.iteration_id,
                  "report_url": report_url_start + report_url.split("?")[1] + report_url_end,
                  "task_id": task_id}
        logger.info("mantis接口请求参数为：{}".format(params))
        code_last_version = ProductRepos.get_code_last_version(os.path.join(measure_business_bo.workspace, git_path))
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("analysis_sonar_report", json=params)
            logger.info(res)
            if res["status"] == "success":
                product_info_bo = ProductInfoBo.Builder().set_iteration_id(measure_business_bo.iteration_id). \
                    set_app_name(measure_business_bo.app_name).set_lib_type(
                    ProductRepos.LibType.SONAR.value).set_lib_repo_tool(
                    ProductRepos.LibRepoTool.SONAR.value).set_lib_version(br_name). \
                    set_code_last_version(code_last_version) \
                    .set_lib_md5(task_id).set_lib_name("report-task.txt").set_lib_url(
                    params.get("report_url")).build_bo()
                product_recorder = ProductRecorder()
                product_recorder.record_product_info(product_info_bo)
                return PipelineStatus.success, res["msg"] + ":" + job_url
            else:
                return PipelineStatus.failure, res["msg"] + ":" + job_url
        except Exception as e:
            logger.error(e)
            return PipelineStatus.failure, "分析失败" + ":" + job_url

    @staticmethod
    def __check_sonar_result(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        measure_info = get_measure_info_by_group_name(measure_business_bo.iteration_id, measure_business_bo.app_name)
        if measure_info.get("sonar"):
            sonar_value = measure_info.get("sonar")
            logger.info("{}已设sonar置白名单".format(measure_business_bo.app_name))
        else:
            sonar_value = 0
        git_path = get_app_git_path(measure_business_bo.app_name)
        report_task_path = measure_business_bo.workspace + os.path.join(git_path, '.scannerwork/report-task.txt')
        ops_info = get_module_name_ops_info(measure_business_bo.app_name)
        with open(report_task_path, "r") as f:
            res = f.readlines()
            for text in res:
                text = text.strip()
                if text.startswith("ceTaskId="):
                    task_id = text.split("=")[-1]

        params = {"app_name": measure_business_bo.app_name, "iteration_id": measure_business_bo.iteration_id,
                  "task_id": task_id}
        http_caller = MantisHttpCaller()
        try:
            http_caller.login()
            res = http_caller.request("check_sonar_result", params=params)
            logger.info(res)
            if res["status"] == "success":
                return PipelineStatus.success, res["msg"]
            else:
                if ops_info['need_online'] == 0:
                    return PipelineStatus.success, res["msg"]
                if MeasureBusiness.__check_switch().get_guard_switch(GuardSwitch.GuardName.SONAR.value) and \
                        not sonar_value:
                    return PipelineStatus.failure, res["msg"]
                else:
                    return PipelineStatus.unstable, res["msg"]
        except Exception as e:
            logger.error(traceback.format_exc())
            return PipelineStatus.failure, "调用mantis检查接口失败"

    @staticmethod
    def __spider_sonar_api(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        workspace = measure_business_bo.workspace
        job_name = "_".join([measure_business_bo.iteration_id, measure_business_bo.app_name])
        node_name = os.environ["NODE_NAME"]
        params = {"workspace": workspace, "job_name": job_name, "vm": node_name}
        business_name = "call_spider_sonar_api"
        max_retries = 1
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                shc = SpiderHttpCaller()
                result = shc.spider_request_post(business_name, params)
                logger.info(f"第{retry_count + 1}次调用结果: {result}")

                if "success" == result.get("status"):
                    return PipelineStatus.success, "调用spider接口成功"

                last_error = result.get("msg", "未知错误")
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"第{retry_count}次调用失败，准备重试，错误信息: {last_error}")
                    sleep(2)  # 重试前等待2秒

            except Exception as e:
                last_error = str(e)
                logger.error(f"第{retry_count + 1}次调用发生异常: {last_error}")
                logger.error(traceback.format_exc())
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"第{retry_count}次调用异常，准备重试")
                    sleep(2)  # 重试前等待2秒

        error_msg = f"Spider接口调用失败，已重试{max_retries}次。最后一次错误: {last_error}"
        logger.error(error_msg)
        raise Exception(error_msg)  # 抛出异常以中断流水线

    @staticmethod
    def _check_es_branch_exists(es_repo_path, br_name):
        """检查ES仓库中是否存在指定分支"""
        try:
            # 尝试连接主GitLab实例
            gl = gitlab.Gitlab(GITLAB_HTTP, private_token=GITLAB_TOKEN)

            # 查找ES项目
            projects = gl.projects.list(search=es_repo_path.split('/')[-1], all=True)
            es_project = None

            for project in projects:
                if project.path_with_namespace == es_repo_path:
                    es_project = project
                    break

            if not es_project:
                logger.warning(f"未找到ES仓库: {es_repo_path}")
                return False

            # 检查分支是否存在
            try:
                es_project.branches.get(br_name)
                return True
            except gitlab.exceptions.GitlabGetError:
                return False

        except Exception as e:
            logger.error(f"检查ES仓库分支时发生错误: {str(e)}")
            return False

    @staticmethod
    def __shift_left_test_api(measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        app_name_str = measure_business_bo.app_name

        if not EsMgtAppBind.select().where(EsMgtAppBind.module_name.in_(app_name_str.split(","))).exists():
            return PipelineStatus.success, "应用{}无需es业务".format(app_name_str)

        result = BranchesModel.select(BranchesModel.project_group, BranchesModel.br_name).where(
            BranchesModel.pipeline_id == measure_business_bo.iteration_id).get()
        project_group = result.project_group
        br_name = result.br_name
        es_git_code_repos = "{}/{}".format(project_group, ES.get("git_repo_name"))

        if not MeasureBusiness._check_es_branch_exists(es_git_code_repos, br_name):
            logger.info(f"ES仓库{es_git_code_repos}中不存在分支{br_name}，跳过执行流水线")
            return PipelineStatus.success, "应用{}无需es业务".format(app_name_str)

        job_name = "shift_left_test"
        params = {
            "job_name": job_name,
            "iteration_id": measure_business_bo.iteration_id,
            "app_name_str": measure_business_bo.app_name,
            "es_type": 1,
            "sql_type": 0,
            "apidoc_type": 0
        }
        business_name = "call_shift_left_test_job_api"
        max_retries = 1
        retry_count = 0
        last_error = None

        while retry_count < max_retries:
            try:
                shc = SpiderHttpCaller()
                result = shc.spider_request_post(business_name, params)
                logger.info(f"第{retry_count + 1}次调用结果: {result}")

                if "success" == result.get("status"):
                    return PipelineStatus.success, "调用spider接口成功"

                last_error = result.get("msg", "未知错误")
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"第{retry_count}次调用失败，准备重试，错误信息: {last_error}")
                    sleep(2)  # 重试前等待2秒

            except Exception as e:
                last_error = str(e)
                logger.error(f"第{retry_count + 1}次调用发生异常: {last_error}")
                logger.error(traceback.format_exc())
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"第{retry_count}次调用异常，准备重试")
                    sleep(2)  # 重试前等待2秒

        error_msg = f"Spider接口调用失败，已重试{max_retries}次。最后一次错误: {last_error}"
        logger.error(error_msg)
        raise Exception(error_msg)  # 抛出异常以中断流水线

    strategy_pattern = {
        "p3c_scan": __p3c_scan.__func__,
        "ccn_scan": __ccn_scan.__func__,
        "get_sonar_scan_cmd": __get_sonar_scan_cmd.__func__,
        "push_p3c_report": __push_p3c_report.__func__,
        "push_ccn_report": __push_ccn_report.__func__,
        "analyze_p3c_report": __analyze_p3c_report.__func__,
        "analyze_ccn_report": __analyze_ccn_report.__func__,
        "get_p3c_result": __get_p3c_result.__func__,
        "get_ccn_result": __get_ccn_result.__func__,
        "analysis_sonar_report": __analysis_sonar_report.__func__,
        "check_sonar_result": __check_sonar_result.__func__,
        "spider_sonar_api": __spider_sonar_api.__func__,
        "shift_left_test_api": __shift_left_test_api.__func__
    }

    @classmethod
    @PipelineRecorder()
    def _run_step(cls, step_name, measure_business_bo: MeasureBusinessBo) -> tuple[str, str]:
        exec_status, exec_msg = cls.strategy_pattern[step_name](measure_business_bo)
        return exec_status, exec_msg

    @classmethod
    def call(cls, params: list):
        # 修改为传入值
        step_name = params[0]
        flag_file_dir = params[1]
        if not flag_file_dir.endswith(".cp"):
            flag_file_cp_dir = flag_file_dir + ".cp"
        else:
            flag_file_cp_dir = flag_file_dir
        job_name = params[2]
        workspace = params[3]
        iteration_id = "_".join(job_name.split("_")[:-1])
        app_name = job_name.split("_")[-1]
        if "{" in app_name:
            app_name = app_name.split("{")[0]
        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
        logger.info("缓存文件内容是 {}".format(json_dict))
        sid = json_dict["sid"]
        with open(flag_file_cp_dir, "r") as f:
            json_dict = json.loads(f.read())
            logger.info("缓存文件{}内容是 {}".format(flag_file_cp_dir, json_dict))
        measure_business_bo = MeasureBusinessBo.Builder().set_iteration_id(iteration_id).set_app_name(app_name) \
            .set_sid(sid).set_flag_file_dir(flag_file_cp_dir).set_workspace(workspace).build_bo()
        cls._run_step(step_name, measure_business_bo)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    MeasureBusiness.call(sys.argv[1:])
