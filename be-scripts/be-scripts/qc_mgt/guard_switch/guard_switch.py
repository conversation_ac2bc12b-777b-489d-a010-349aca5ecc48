from enum import Enum
from qc_mgt.modles import GuardSwitchInfo
from settings import logger


class GuardSwitch:
    __guard_switch_info = GuardSwitchInfo

    def __init__(self):
        pass

    class GuardName(Enum):
        CCN = "ccn"
        SONAR = "sonar"
        P3C = "p3c"
        JUNIT = "unit_test"

    def get_guard_switch(self, guard_name: str)->bool:
        guard_switch = self.__guard_switch_info.get(guard_name=guard_name).guard_switch
        logger.info("{}的开关状态为{}".format(guard_name, guard_switch))
        return guard_switch


if __name__ == "__main__":
    gs = GuardSwitch()
    if gs.get_guard_switch("ccn"):
        logger.info("开启")
    else:
        logger.info("关闭")
