from dao.connect.mysql import DBConnectionManager
from settings import logger
def get_measure_info(pipeline_id, app_name):
    sql = '''
    SELECT v.group_name,
           v.iter_name,
           v.br_name,
           v.module_name,
           JSON_OBJECTAGG(v.switch_name, v.wl_val) AS switch_json
    FROM (
        SELECT
               iter_i.project_group AS group_name,
               iter_i.pipeline_id AS iter_name,
               iter_i.br_name,
               iter_a.appName AS module_name,
               switch.guard_name AS switch_name,
               CASE
                   WHEN switch.id = 14 THEN
                       IF(switch.guard_switch, IFNULL(group_v.wl_val, 5), 999)
                   ELSE
                       IF(switch.guard_switch, IFNULL(IFNULL(app_v.wl_val, group_v.wl_val), 0), 1)
                   END AS wl_val
        FROM iter_mgt_iter_info iter_i
            INNER JOIN iter_mgt_iter_app_info iter_a ON iter_a.pipeline_id = iter_i.pipeline_id
            INNER JOIN app_mgt_app_module app_m ON app_m.module_name = iter_a.appName
            INNER JOIN app_mgt_app_info app_i ON app_i.id = app_m.app_id
            INNER JOIN qc_mgt_guard_switch_info switch ON switch.id IN(14,13,5,9,1,2,3,4)
            LEFT JOIN(
                SELECT 'group_val' AS union_type,
                       s.id AS switch_id,
                       s.guard_name AS switch_name,
                       s.guard_switch AS switch_value,
                       g.wl_group_name AS wl_name,
                       g.wl_group_value AS wl_val
                FROM iter_whitelist_group g
                    INNER JOIN qc_mgt_guard_switch_info s ON s.id = g.wl_switch_id AND s.id IN(14)
                UNION ALL
                SELECT 'group_pass' AS union_type,
                       s.id AS switch_id,
                       s.guard_name AS switch_name,
                       s.guard_switch AS switch_value,
                       g.wl_group_name AS wl_name,
                       g.wl_group_pass AS wl_val
                FROM iter_whitelist_group g
                    INNER JOIN qc_mgt_guard_switch_info s ON s.id = g.wl_switch_id AND s.id IN(13)
                )group_v ON group_v.switch_id = switch.id AND group_v.wl_name = iter_i.project_group
            LEFT JOIN(
                SELECT 'iter' AS union_type,
                       s.id AS switch_id,
                       s.guard_name AS switch_name,
                       s.guard_switch AS switch_value,
                       a.wl_name AS wl_name,
                       a.wl_pass AS wl_val
                FROM iter_whitelist_group g
                    INNER JOIN iter_whitelist_app a ON a.wl_group_id = g.id AND a.wl_type = 2
                    INNER JOIN qc_mgt_guard_switch_info s ON s.id = g.wl_switch_id AND s.id IN(13)
                UNION ALL
                SELECT 'app' AS union_type,
                       s.id AS switch_id,
                       s.guard_name AS switch_name,
                       s.guard_switch AS switch_value,
                       a.wl_name AS wl_name,
                       a.wl_pass AS wl_val
                FROM iter_whitelist_group g
                    INNER JOIN iter_whitelist_app a ON a.wl_group_id = g.id AND a.wl_type = 1
                    INNER JOIN qc_mgt_guard_switch_info s ON s.id = g.wl_switch_id AND s.id IN(5,9,1,2,3,4)
                )app_v ON app_v.switch_id = switch.id AND (
                    (app_v.union_type = 'iter' AND app_v.wl_name = iter_i.pipeline_id)
                        OR (app_v.union_type = 'app' AND app_v.wl_name = iter_a.appName))
        WHERE 1=1
          AND iter_i.br_status = 'open'
          AND app_m.need_online = 1
          AND app_m.need_check = 1
          AND app_m.lib_repo IS NOT NULL AND app_m.lib_repo <> ''
          AND app_i.third_party_middleware = 0
          AND app_i.platform_type = 1
          AND iter_i.pipeline_id = '{}'
          AND iter_a.appName = '{}'
        ORDER BY iter_i.project_group, iter_i.pipeline_id, iter_a.appName
        )v
GROUP BY v.group_name, v.iter_name, v.br_name, v.module_name

    '''.format(pipeline_id, app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchone()

def get_measure_info_by_group_name(iteration_id, app_name):
    group_name = iteration_id.split('_')[0]
    measure_info = {}
    sql = '''
     SELECT s.guard_name,g.wl_group_name, g.wl_group_pass, g.wl_group_value,
        a.wl_name, a.wl_pass, a.wl_value,
        IFNULL(a.wl_pass, g.wl_group_pass) AS pass
        FROM qc_mgt_guard_switch_info s
        INNER JOIN iter_whitelist_group g ON g.wl_switch_id = s.id 
        LEFT JOIN iter_whitelist_app a ON a.wl_group_id = g.id AND a.wl_name = '{}'
        LEFT JOIN `iter_mgt_iter_app_info` ai ON ai.appName = a.wl_name
        LEFT JOIN `iter_mgt_iter_info` i ON i.pipeline_id = ai.pipeline_id AND i.project_group = g.wl_group_name
        WHERE 1=1 AND i.pipeline_id = '{}'
    '''.format(app_name, iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result = db.cur.fetchall()
        logger.info(sql)
        for item in result:
            measure_info[item['guard_name']] = item['pass']
    logger.info(measure_info)
    return measure_info

if __name__ == '__main__':
    # info = get_measure_info('AMS_1.12.6-2', 'howbuy-ams-server')
    # print(info['switch_json'])
    measure_info = get_measure_info_by_group_name('mtx_test-guarn-check','zeus-service')
    print(measure_info)