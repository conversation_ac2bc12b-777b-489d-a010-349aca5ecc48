from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_iterative_pipeline_detail(br_name, appName):
    """获取某个应用的分支对应迭代的详细信息"""
    sql = '''
        SELECT f.pom_path, t.br_status, t.pipeline_id
        FROM iter_mgt_iter_info t
        LEFT JOIN iter_mgt_iter_app_info f ON t.pipeline_id = f.pipeline_id
        WHERE t.br_name = "{}" AND f.appName = "{}"
    '''.format(br_name, appName)
    logger.info(sql)

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()
