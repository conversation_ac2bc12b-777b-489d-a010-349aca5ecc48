import functools
from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import base


@functools.lru_cache(None)
def get(appName):
    """查询common_service_artifactinfo表(spider_app_view视图)"""
    meta = dict(
        appName=appName,
    )
    sql = '''
        SELECT jdkVersion, package_type, packageName, need_online, gitRepo
        FROM spider_app_view
        WHERE {}
    '''.format(base.query_condition_by_and(meta))

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


@functools.lru_cache(None)
def get_common_app():
    """获取common类型的应用列表"""
    sql = '''
        SELECT appName, gitCodePath, package_type
        FROM spider_app_view
        WHERE gitCodePath like 'common/%' or gitCodePath like 'tms-common/%'
    '''

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


@functools.lru_cache(None)
def get_scm_app():
    """获取scm应用列表"""
    sql = '''
        SELECT appName, gitCodePath, package_type
        FROM spider_app_view
    '''

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_by_in(appName_list):
    sql = '''
        SELECT appName, gitCodePath, package_type
        FROM spider_app_view
        WHERE appName in (%s)
    '''
    in_p = ','.join(list(map(lambda x: '%s', appName_list)))
    sql = sql % in_p

    with DBConnectionManager() as db:
        db.cur.execute(sql, appName_list)
    return db.cur.fetchall()
