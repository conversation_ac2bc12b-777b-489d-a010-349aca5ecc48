import datetime
import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql import db_mgt_bind_view
from settings import logger
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.connect.mysql import DBConnectionManager
from sqlalchemy import text


def get_member_list(project_group):
    """获取上线应用

    :param project_group: string.迭代版本号
    :return: list. app_list list
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT appName FROM spider_app_view WHERE gitCodePath LIKE "{}/%" '
                       'AND need_online=1'.format(project_group))
        app_list = []
        for row in db.cur.fetchall():
            app_list.append(row["appName"])
    return app_list


def get_all_projects_without_project_id():
    with DBConnectionManager() as db:
        db.cur.execute(
            "SELECT * FROM app_mgt_app_info WHERE project_id is null and git_url is not null and git_url <> ''")
        app_list = []
        for row in db.cur.fetchall():
            app_list.append(row)
    return app_list


def get_project_ids(interval_minute):
    with DBConnectionManager() as db:
        sql = """
            SELECT
                distinct iai.id, am.module_name, ai.project_id, ii.br_name, last.archived_commit
            FROM
                iter_mgt_iter_app_info iai
            inner join
                iter_mgt_iter_info ii
            on iai.pipeline_id = ii.pipeline_id
            inner join
                app_mgt_app_module am
            on iai.appName = am.module_name
            inner join
                app_mgt_app_info ai
            on ai.id =am.app_id
            left outer join
            (
            select iai.appName, iai.git_last_version as archived_commit, ii.br_name, last.dt
            from
                iter_mgt_iter_app_info iai
            inner join iter_mgt_iter_info ii
            on iai.pipeline_id = ii.pipeline_id
            inner join
            (
            SELECT
                iai.appName, max(ii.br_end_date) as dt
            FROM
                iter_mgt_iter_app_info iai, iter_mgt_iter_info ii
            where iai.pipeline_id = ii.pipeline_id
            and iai.sys_status = '已归档'
            group by appName
            ) last
            on iai.appName = last.appName and ii.br_end_date = last.dt
            ) last
            on last.appName = am.module_name
            
            where
            iai.git_last_update > date_add(now(), interval - {} minute)
        """.format(interval_minute)

        db.cur.execute(sql)
        module_list = []
        for row in db.cur.fetchall():
            module_list.append(row)
    return module_list


def get_app_type(git_path_repos):
    """
     获取存在的 应用类型
    :param git_path_repos:
    :return:
    """

    with DBConnectionManager() as db:
        sql = """SELECT m.package_type, m.module_name ,CONCAT(t.git_url,t.git_path) AS git_repos_path 
                    FROM app_mgt_app_info t  LEFT JOIN app_mgt_app_build m ON
                     t.id=m.app_id WHERE  
                     CONCAT(t.git_url,t.git_path) IN ('{}')
                     """.format("','".join(git_path_repos))
        logger.info(sql)
        db.cur.execute(sql)
    #     type_dict = {}
    #     app_info = {}
    #     for row in db.cur.fetchall():
    #         type_dict[row["git_repos_path"]] = row["package_type"]
    #         app_info[row["module_name"]] = row["git_repos_path"]
    #
    # return type_dict, app_info
    return db.cur.fetchall()


def diff_mvn_app(app_list):
    """
    区分mvn应用
    :param app_list:
    :return:
    """
    with DBConnectionManager() as db:
        sql = '''
            SELECT module_name,package_type 
            FROM app_mgt_app_build 
            WHERE module_name in ("{}")
        '''.format('","'.join(app_list))
        logger.info(sql)
        db.cur.execute(sql)
        mvn_app = []
        non_mvn_app = []
        for row in db.cur.fetchall():
            if row["package_type"] in ["jar", "war", "tar", "pom"]:
                mvn_app.append(row["module_name"])
            else:
                non_mvn_app.append(row["module_name"])

    return mvn_app, non_mvn_app


def get_dependencies_version_ranges(mappings):
    placeholders = ', '.join(['(%s, %s)'] * len(mappings))
    sql = """
            SELECT DISTINCT dependency_name, min_version, max_version, whitelist
            FROM iter_mgt_sdk_dependency_rule 
            WHERE (sdk_name, sdk_branch) IN ({placeholders})
            AND is_active = 1
        """.format(placeholders=placeholders)

    params = [item for sublist in mappings for item in sublist]
    with DBConnectionManager() as db:
        db.cur.execute(sql, params)
        dependency_version_ranges = {}
        for item in db.cur.fetchall():
            whitelist = []
            if item['whitelist']:
                whitelist = item['whitelist'].split(",")
            dependency_version_ranges[item['dependency_name']] = {
                "min": item['min_version'],
                "max": item['max_version'],
                "whitelist": whitelist
            }

    return dependency_version_ranges


def app_package_type(app_list):
    """
    区分mvn应用
    :param app_list:
    :return:
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT module_name,package_type FROM app_mgt_app_build WHERE module_name in ("{}")'
                       .format('","'.join(app_list)))

    return db.cur.fetchall()


def get_join_zeus_app(app_list, pipeline_id):
    """
    获取接入zeus应用
    :param app_list:
    :param pipeline_id:
    :return:
    """
    with DBConnectionManager() as db:
        # 查找应用被标记为宙斯的或者分支中被标记为宙斯的应用列表 20230615 by fwm
        db.cur.execute(
            '''SELECT module_name FROM app_mgt_app_module 
              WHERE module_name IN ("{module_name}") AND zeus_type=1
              UNION 
              SELECT appName AS module_name FROM iter_mgt_iter_app_info ai
              INNER JOIN iter_mgt_iter_info i ON ai.pipeline_id = i.pipeline_id
              WHERE i.pipeline_id = "{pipeline_id}" AND ai.zeus_type = 1 
              AND ai.appName IN ("{module_name}")
                '''.format(module_name='","'.join(app_list), pipeline_id=pipeline_id)
        )
        join_zeus_app = []

        for row in db.cur.fetchall():
            join_zeus_app.append(row["module_name"])

    return join_zeus_app


def get_sharding_app(app_list):
    sharding_app = []
    for module_name in app_list:
        with DBConnectionManager() as db:
            db.cur.execute('''
                SELECT DISTINCT dm.domain_db_type FROM db_mgt_domain dm
                LEFT JOIN db_mgt_app_bind ab ON dm.id = ab.db_domain_id
                WHERE ab.app_module_name = '{}';
               '''.format(module_name))
            for row in db.cur.fetchall():
                if row["domain_db_type"] > 1:
                    sharding_app.append(module_name)
    return sharding_app


def get_old_git_jar():
    """获取老git应用"""
    with DBConnectionManager() as db:
        db.cur.execute("""SELECT f.module_name FROM app_mgt_app_info t LEFT JOIN app_mgt_app_module f ON f.app_id = t.id 
                       LEFT JOIN app_mgt_app_build m ON m.app_id=t.id LEFT JOIN old_git_online_version h
                       ON h.module_name = f.module_name WHERE t.git_url IS NOT NULL AND 
                       f.need_online = 0 AND m.package_type="jar" AND t.platform_type IS NULL AND h.module_name IS NOT NULL""")
        jar_list = []
        for row in db.cur.fetchall():
            jar_list.append(row["module_name"])
    return jar_list


def get_group_module_name(project_group):

    with DBConnectionManager() as db:
        sql = """
            SELECT DISTINCT am.module_name FROM app_mgt_app_module am
            INNER JOIN app_mgt_app_info ai on am.app_id = ai.id
            WHERE ai.git_url = '{project_group}'
            AND am.module_status = 1
        """.format(project_group=project_group)
        db.cur.execute(sql)
        module_list = []
        for row in db.cur.fetchall():
            module_list.append(row["module_name"])
    return module_list


def get_online_jar_version():
    """获取最近归档的线上jar包版本"""
    sql = '''
      SELECT
	i.appName,         
        m.br_name       
    FROM iter_mgt_iter_info m
    LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
    LEFT JOIN(
                 SELECT 
        i.appName, MAX(m.br_end_date) AS max_br_end_date
        FROM iter_mgt_iter_info m
        INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
        INNER JOIN app_mgt_app_build b ON b.module_name =  i.appName
        WHERE m.br_status = 'close'  AND  b.package_type = "jar" AND  i.git_last_version IS NOT NULL
        GROUP BY i.appName        
        UNION 
         SELECT 
        i.appName, MAX(m.br_end_date) AS max_br_end_date
        FROM iter_mgt_iter_info m
        INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
        INNER JOIN app_mgt_app_build b ON b.module_name =  i.appName
        WHERE m.br_status = 'close'  AND  b.package_type = "pom" 
        GROUP BY i.appName
    )v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
    WHERE m.br_end_date IS NOT NULL AND    
    m.br_status = 'close' AND v.max_br_end_date IS NOT NULL '''
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        jar_dict = {}
        for row in db.cur.fetchall():
            jar_dict[row["appName"]] = row["br_name"] + "-RELEASE"
    return jar_dict


def get_mock_app():
    """获取mock 的应用"""
    mock_list = []
    with DBConnectionManager() as db:
        db.cur.execute(
            'SELECT module_name FROM app_mgt_app_build WHERE need_mock=1')

        for row in db.cur.fetchall():
            mock_list.append(row["module_name"])
    return mock_list


def get_need_online_server(app_names):
    """获取需要上线的后端的应用"""
    app_names = '","'.join(app_names)
    new_app_list = []
    with DBConnectionManager() as db:
        sql = '''select module.module_name 
                 from app_mgt_app_module module 
                 join  app_mgt_app_build build on module.module_name=build.module_name 
                 where build.package_type in ('jar','tar','war') 
                 and module.need_online=1 
                 and module.module_name in ("{}")'''.format(
            app_names)
        print(sql)
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            new_app_list.append(row["module_name"])
    return new_app_list


def get_need_mock_app(app_name):
    """
    获取应用是否需要mock以及mock命令
    :param app_name:
    :return:
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT need_mock,mock_build_cmd FROM app_mgt_app_build WHERE module_name = "{}"'
                       .format(app_name))
        need_mock = ''
        mock_build_cmd = ''
        for row in db.cur.fetchall():
            need_mock = row["need_mock"]
            mock_build_cmd = row["mock_build_cmd"]

    return need_mock, mock_build_cmd


def get_latest_archive_version(app_name):
    sql = '''
        SELECT
            m.br_name,
            i.appName 
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
            GROUP BY
                i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            and i.appName = '{}';
        '''.format(app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row["br_name"]


# 查询指定日期范围内归档的应用和版本
def query_archived_applications_and_versions(start_date, end_date):
    sql = '''
           SELECT
            i.appName as app_name,         
            m.br_name   ,
            m.br_end_date   
        FROM iter_mgt_iter_info m
        LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
        LEFT JOIN(
            SELECT
            i.appName, MAX(m.br_end_date) AS max_br_end_date
            FROM iter_mgt_iter_info m
            INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            INNER JOIN app_mgt_app_module mo ON i.appName = mo.module_name and mo.need_online=1
            INNER JOIN app_mgt_app_build mb ON i.appName = mb.module_name and mb.package_type in ('jar','tar','war')
            WHERE m.br_status = 'close'  and m.br_end_date < '{end_date}' and m.br_end_date > '{start_date}'
            GROUP BY i.appName
        )v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
        WHERE m.br_end_date IS NOT NULL AND    
        m.br_status = 'close' AND v.max_br_end_date IS NOT NULL order by m.br_end_date asc
        '''.format(end_date=end_date, start_date=start_date)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        datas = []
        for row in db.cur.fetchall():
            datas.append((row["app_name"], row["br_name"], row["br_end_date"]))
        return datas


def get_db_info_by_app_name(app_str, suite_code=None, module_suite_tuple=None):
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
    """
    env_db_info_list = db_mgt_bind_view.get(module_name_str=app_str)
    if suite_code:
        env_db_info_list = db_mgt_bind_view.get(module_name_str=app_str, suite_code=suite_code)
    if module_suite_tuple:
        env_db_info_list = db_mgt_bind_view.get(module_suite_tuple=module_suite_tuple)

    datas = []

    for env_db_info in env_db_info_list:
        # 暂时处理mysql的库
        if env_db_info.get("db_srv_type") == 'mysql':
            datas.append({'db_srv_type': env_db_info.get("db_srv_type"), 'db_name': env_db_info.get("db_name"),
                          'suite_db_name': env_db_info.get("suite_db_name"),
                          'db_group_name': env_db_info.get("db_group_name"),
                          'module_name': env_db_info.get("app_module_name")})
        else:
            datas.append({'db_srv_type': env_db_info.get("db_srv_type"), 'db_name': env_db_info.get("db_name"),
                          'suite_db_name': env_db_info.get("suite_db_name"),
                          'db_group_name': env_db_info.get("db_group_name"),
                          'module_name': env_db_info.get("app_module_name")})

    unique_list = list(set(tuple(sorted(d.items())) for d in datas))
    result = [dict(item) for item in unique_list]
    # with DBConnectionManager() as db:
    #     db.cur.execute(sql)
    #     datas = []
    #     for row in db.cur.fetchall():
    #         datas.append(
    #             {'db_srv_type': row["db_srv_type"], 'db_name': row["db_info_suffix_name"],
    #              'db_group_name': row["db_group_name"], 'module_name': row["app_module_name"]})
    return result


def get_db_in_transit_br_names(db_name):
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
    """
    sql = '''
            SELECT
            DISTINCT
            m.br_name  AS br_name
            FROM
            iter_mgt_iter_info m
            INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            INNER JOIN db_mgt_app_bind db_bind ON   i.appName=db_bind.app_module_name
            INNER JOIN db_mgt_logic_info li ON db_bind.db_domain_id = li.db_domain_id  AND  li.logic_db_name='{db_name}'
            WHERE
            m.br_status = 'open'  
            '''.format(db_name=db_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        datas = []
        for row in db.cur.fetchall():
            datas.append(row["br_name"])
        return datas


def get_db_in_transit_br_names_group_name(db_group_name):
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
    """
    sql = '''
            SELECT
            DISTINCT
            m.br_name  AS br_name
            FROM
            iter_mgt_iter_info m
            INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            INNER JOIN db_mgt_app_bind db_bind ON   i.appName=db_bind.app_module_name
            INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
            INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id AND g.db_group_name = '{db_group_name}'
            WHERE
            m.br_status = 'open'  
            '''.format(db_group_name=db_group_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        datas = []
        for row in db.cur.fetchall():
            datas.append(row["br_name"])
        return datas


def get_latest_and_has_api_archive_version(app_name):
    sql = '''
        SELECT
            m.br_name,
            i.appName 
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN  app_mgt_apidoc_info api on m.br_name=api.iter_branch and api.module_name='{}'
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
            GROUP BY
                i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            and i.appName = '{}';
        '''.format(app_name, app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row["br_name"]


def get_latest_and_has_interface_archive_version(app_name):
    sql = '''
        SELECT
            m.br_name,
            i.appName 
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN  app_mgt_interface_info api on m.br_name=api.branch_name and api.module_name='{}'
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
				and i.appName='{}'
            GROUP BY   i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            and i.appName = '{}';
        '''.format(app_name, app_name, app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row["br_name"]


def get_latest_and_has_sharding_archive_version(app_name):
    sql = '''
        SELECT
            m.br_name,
            i.appName 
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN  db_mgt_app_sharding_rule sharding on m.br_name=sharding.branch and sharding.module_name='{}'
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
            GROUP BY
                i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            and i.appName = '{}';
        '''.format(app_name, app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row["br_name"]

def get_iteration_id(app_name, br_name):
    sql = '''
        SELECT DISTINCT
            ii.pipeline_id
        FROM
            iter_mgt_iter_info ii
            left join iter_mgt_iter_app_info ai ON ai.pipeline_id = ii.pipeline_id
        WHERE
            ai.appName = '{app_name}'
            AND ii.br_name = '{br_name}'
        '''.format(app_name=app_name, br_name=br_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row["pipeline_id"]


# def log_warn_log(app, branch, iteration_id, agent_type):
#     agent_mgt_publish_warn_log = AgentMgtPublishWarnLog(module_name=app,
#                                                         branch=branch,
#                                                         iteration_id=iteration_id,
#                                                         agent_type=agent_type,
#                                                         exec_batch="",
#                                                         status="waiting",
#                                                         create_user="scm",
#                                                         create_time=datetime.datetime.now(),
#                                                         )
#     with DBConnectionManagerForSqlalchemy() as db:
#         db.session.add(agent_mgt_publish_warn_log)
#         db.session.commit()


# def update_warn_log_info(module_name, branch, status, msg):
#     with DBConnectionManagerForSqlalchemy() as db:
#         db.session.query(AgentMgtPublishWarnLog).filter(
#             AgentMgtPublishWarnLog.module_name == module_name,
#             AgentMgtPublishWarnLog.branch == branch).update({"status": status, "err_msg": msg})
#         db.session.commit()


def get_log_warn_log_info(iteration_id=None):
    # with DBConnectionManagerForSqlalchemy() as db:
        # condition = ""
        # if iteration_id:
        #     condition = "AND  iteration_id={}".format(iteration_id)
        # sql = '''SELECT
        #     	wr.*,
        #     	max( main.suite_name ) success_publish_suite_name
        #     FROM
        #     	(
        #     	SELECT
        #     		t1.module_name,
        #     		t1.branch,
        #     		t1.iteration_id,
        #     		t1.agents,
        #     		t1.create_time
        #     	FROM
        #     		(
        #     		SELECT
        #     			log.module_name,
        #     			log.branch,
        #     			log.iteration_id,
        #     			GROUP_CONCAT( DISTINCT log.agent_type ) agents,
        #     			MIN( log.create_time ) create_time
        #     		FROM
        #     			agent_mgt_publish_warn_log log
        #     		WHERE
        #     			log.`status` = 'waiting'
        #     			AND (
        #     				log.branch NOT LIKE '%pa-sql%'
        #     				AND log.branch NOT LIKE '%basic-features%'
        #     			)
        #     			{condition}
        #     		GROUP BY
        #     			module_name,
        #     			branch,
        #     			iteration_id
        #     		) t1
        #     		JOIN (
        #     		SELECT
        #     			module_name,
        #     			MIN( create_time ) create_time
        #     		FROM
        #     			agent_mgt_publish_warn_log
        #     		WHERE
        #     			`status` = 'waiting'
        #     			AND ( branch NOT LIKE '%pa-sql%' AND branch NOT LIKE '%basic-features%' )
        #     		GROUP BY
        #     			module_name
        #     		) t2 ON t1.module_name = t2.module_name
        #     		AND t1.create_time = t2.create_time
        #     		LIMIT 20
        #     	) wr
        #     	JOIN pipeline_log_main main ON main.app_name = wr.module_name
        #     	AND main.iteration_id = wr.iteration_id
        #     	AND main.`status` = "success"
        #     GROUP BY
        #     	wr.module_name,
        #     	wr.branch,
        #     	wr.iteration_id'''.format(condition=condition)
        # all_logs = db.session.execute(text(sql)).fetchall()
    results = []
        # for logs in all_logs:
        #     results.append(dict(zip(["module_name", "branch", "iteration_id",
        #                              "agents", "create_time", "success_publish_suite_name"], logs)))
    return results


def get_app_branch_last_agent_scan_status_is_success(app, branch, agent_type):
    with DBConnectionManagerForSqlalchemy() as db:
        sharding_app = db.session.execute(text(
            'SELECT status FROM agent_mgt_exec_log WHERE module_name = "{}" and branch="{}" and agent_type="{}" order by id desc limit 1'
            .format(app, branch, agent_type))).fetchall()
        logger.info(sharding_app)
        return sharding_app and sharding_app[0] and sharding_app[0][0] == "success"


if __name__ == '__main__':
    dependencies = {"howbuy-dfile-service":"patest-1.9.0-RELEASE"}
    mappings = []
    # 遍历字典并转换键值对
    for key, value in dependencies.items():
        mappings.append((key, value.capitalize()))
    print(mappings)
    dependency_version_ranges = get_dependencies_version_ranges(mappings)
    print(dependency_version_ranges)
    # print(get_db_info_by_app_name(['acc-center-server']))
