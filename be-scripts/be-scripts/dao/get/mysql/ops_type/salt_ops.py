from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_salt_ops(operating_type, suite_code, app_name, salt_func):

    # 启用新表，不再使用视图 20211124 by fwm
    # sql = '''
    # SELECT exec_cmd, minion_id FROM spider.publish_salt_cmd
    # where operate_type = "{}" and suite_code = "{}" and app_name = "{}" and salt_func = "{}"
    # '''.format(operating_type, suite_code, app_name, salt_func)

    sql = '''
        SELECT exec_cmd, minion_id FROM publish_exec_salt_cmd 
        where operate_type = "{}" and suite_code = "{}" and app_name = "{}" and salt_func = "{}"
        '''.format(operating_type, suite_code, app_name, salt_func)

    logger.info(sql)

    with DBConnectionManager() as db:
        db.cur.execute(sql)

    rst = db.cur.fetchone()
    return rst
