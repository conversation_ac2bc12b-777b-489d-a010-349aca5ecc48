from ci_pipeline.ci_pipeline_models.iter_models import IterAppInfoModel
from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_iter_by_sys_status(sys_status, app_name_list):
    sql = ' SELECT DISTINCT pipeline_id FROM iter_mgt_iter_app_info WHERE sys_status = "{}" AND appName IN ("{}")' \
        .format(sys_status, '","'.join(app_name_list))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    iter_list = []
    for row in db.cur.fetchall():
        iter_list.append(row["pipeline_id"])
    return iter_list


def get_rollback_app_info(app_name_list: list) -> list:
    """
    获取处于回滚状态的应用信息

    Args:
        app_name_list: 应用名称列表

    Returns:
        list: 包含回滚应用信息的列表，每个元素是一个字典

    Raises:
        ValueError: 如果输入参数无效
        DatabaseError: 如果数据库查询失败
    """
    # 1. 参数校验
    if not app_name_list or not isinstance(app_name_list, list):
        raise ValueError("app_name_list must be a non-empty list")

    try:
        # 2. 执行查询
        query = (
            IterAppInfoModel
            .select(
                IterAppInfoModel.pipeline_id,
                IterAppInfoModel.appName
            )
            .where(
                IterAppInfoModel.appName.in_(app_name_list),
                IterAppInfoModel.sys_status.contains("回滚中")  # 更清晰的语法
            )
        )

        # 3. 处理结果
        rollback_apps = []
        for app in query:
            logger.debug(f"Found rollback app: {app.appName}, pipeline: {app.pipeline_id}")
            rollback_apps.append({
                "pipeline_id": app.pipeline_id,
                "app_name": app.appName
            })

        logger.info(f"Found {len(rollback_apps)} apps in rollback status")
        return rollback_apps

    except Exception as e:
        logger.error(f"Failed to query rollback apps: {str(e)}")
        raise Exception(f"Database query failed: {str(e)}")


def get_component_pipeline_info(pipeline_id, app_name_list):
    sql = '''
            SELECT r.* FROM iterative_app_component_relations r
            LEFT JOIN iter_mgt_iter_info i ON r.dep_pipeline_id = i.pipeline_id
            WHERE r.pipeline_id = '{}' AND i.br_status = 'open' AND r.app_name in ("{}")
          '''.format(pipeline_id, '","'.join(app_name_list))
    component_pipeline_info = {}
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            if row.get("dep_pipeline_id") not in component_pipeline_info:
                component_pipeline_info[row.get("dep_pipeline_id")] = [row.get("component_app_name")]
            else:
                component_pipeline_info[row.get("dep_pipeline_id")].append(row.get("component_app_name"))
    return component_pipeline_info


def get_product_version(iteration_id, app_name_list):
    sql = 'SELECT appName,git_repo_version FROM iter_mgt_iter_app_info where pipeline_id = "{}" and ' \
          'appName in ("{}")'.format(iteration_id, '","'.join(app_name_list))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        return db.cur.fetchall()


def get_app_list(iteration_id):
    sql = 'SELECT appName FROM iter_mgt_iter_app_info where pipeline_id = "{}" '.format(iteration_id)
    logger.info(sql)
    app_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        app_list.append(row["appName"])
    return app_list


def get_iter_jdk_version(iteration_id, app_name):
    sql = 'select t.jdkVersion from iter_mgt_iter_app_info t where t.pipeline_id = "{}" and t.appName = "{}";'.format(
        iteration_id, app_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchall()
    # 如果本迭代找不到该应用名，说明该应用是个外部应用，则需要去依赖应用所在迭代查找应用的迭代jdk信息
    if not result:
        sql = 'select t.jdkVersion from iter_mgt_iter_app_info t where t.pipeline_id in (select r.dep_pipeline_id ' \
              'from iterative_pipeline_relations r where r.cur_pipeline_id = "{cur_pipeline_id}" and r.dep_app = ' \
              '"{app_name}") and t.appName = "{app_name}"; '.format(cur_pipeline_id=iteration_id, app_name=app_name)
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        result = db.cur.fetchall()
    return result


def get_iter_app_and_jdk_version(iteration_id):
    sql = 'select t.appName, t.jdkVersion from iter_mgt_iter_app_info t where t.pipeline_id = "{}";'.format(
        iteration_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    app_jdk_dict = {}
    for row in db.cur.fetchall():
        app_jdk_dict.update({row['appName']: row['jdkVersion']})
    return app_jdk_dict


def get_iter_app_info_for_archive(iteration_id):
    sql = '''
            select i.br_name, group_concat(t.appName) as app_name_list from iter_mgt_iter_app_info t 
            left join iter_mgt_iter_info i on t.pipeline_id = i.pipeline_id
            left join app_mgt_app_module m on t.appName = m.module_name
            where t.pipeline_id = '{}' and m.need_online = '1'
            GROUP BY i.br_name
        '''.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    iter_app_info_dict = {}
    for row in db.cur.fetchall():
        iter_app_info_dict.update({"branch_name": row['br_name'], "app_list": row['app_name_list'].split(",")})
    return iter_app_info_dict


def get_iter_app_flyway_info(app_tuple, iteration_id):
    logger.info(app_tuple)
    if len(app_tuple) == 1:  # 元组元素个数为一个的时候会有一个逗号，临时处理
        sql = """SELECT * FROM `db_mgt_sql` WHERE module_name = '{}' AND iteration_id = '{}'""".format(app_tuple[0],
                                                                                                       iteration_id)
    else:
        sql = """SELECT * FROM `db_mgt_sql` WHERE module_name in {} AND iteration_id = '{}'""".format(app_tuple,
                                                                                                      iteration_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_test_data_sql_info(bis_pipeline_id):
    sql = " SELECT * FROM `db_mgt_bis_test_sql` WHERE bis_pipeline_id = '{}' and sql_ver_name is not null ".format(
        bis_pipeline_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_committed_data_sql_info(bis_pipeline_id):
    sql = " SELECT * FROM `db_mgt_bis_test_sql` WHERE bis_pipeline_id = '{}' and sql_ver_name is not null and sql_ver_upload_status =1".format(
        bis_pipeline_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def update_test_data_sql_for_archive_version(id, arc_sql_ver_name):
    sql = '''update db_mgt_bis_test_sql set arc_sql_ver_name="{}" WHERE id = {}'''.format(arc_sql_ver_name, id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def get_component_iter_and_online_app(iteration_id, app_name_list):
    # 不存在于本次上线迭代中的依赖组件，其所在的迭代是否含有上线应用，如果有会影响后续的归档，应该在上线前予以删除
    sql = '''
            SELECT ai.pipeline_id, ai.appName FROM iterative_app_component_relations r
                LEFT JOIN iter_mgt_iter_app_info ai ON r.dep_pipeline_id = ai.pipeline_id
                LEFT JOIN app_mgt_app_module m ON ai.appName = m.module_name
                WHERE r.pipeline_id = "{iteration_id}" AND r.app_name IN ("{app_list}") 
                AND r.dep_pipeline_id != "{iteration_id}"
                AND m.need_online = 1 AND ai.sys_status != "已归档"
          '''.format(iteration_id=iteration_id, app_list='", "'.join(app_name_list))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    component_pipeline_info = {}
    for row in db.cur.fetchall():
        if row.get("pipeline_id") not in component_pipeline_info:
            component_pipeline_info[row.get("pipeline_id")] = [row.get("appName")]
        else:
            component_pipeline_info[row.get("pipeline_id")].append(row.get("appName"))
    return component_pipeline_info


if __name__ == "__main__":
    # app_jdk_dict = get_iter_app_info_for_archive('mojie_5.2.1')
    # print(app_jdk_dict)
    # nf_iteration_id,sys_status = get_nf_iteration_id('h5_0.0.00001','fund')
    # print(sys_status)
    # print(nf_iteration_id)
    print(get_rollback_app_info(['howbuy-wechat-message-console']))