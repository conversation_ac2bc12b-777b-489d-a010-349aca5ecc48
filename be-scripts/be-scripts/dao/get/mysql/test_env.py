#
from dao.connect.mysql import DBConnectionManager


def get_env_info_by_tms(env):
    """获取测试环境ip对应关系
    :param env: string.环境名称
    :return:
    """

    with DBConnectionManager(host="**************", port=3306, user="scm", password="howbuyscm", db="django_scm") as db:
        db.cur.execute("""SELECT name, tp, tms FROM env_env WHERE name="{}" """.format(env))

    return db.cur.fetchone()


def get_env_cache_info(env):
    """获取测试环境cache对应关系
    :param env: string.环境名称
    :return:
    """
    with DBConnectionManager(host="***************", port=3306, user="act", password="act", db="testcenter") as db:
        db.cur.execute("""SELECT ENV_NAME, BACKSTAGE_IP, CACHE_NODE FROM envinfo WHERE env_name="{}" """.format(env))

    return db.cur.fetchone()


def get_tp_rule(vm_ip):
    with DBConnectionManager(host="**************", port=3306, user="scm", password="howbuyscm", db="django_scm") as db:
        condition = 'where back_ip="{}" and front_ip like "192.168.%"'.format(vm_ip)
        db.cur.execute(
            'SELECT front_ip,back_ip,dba_name_per,baoxian,simu,backdb_ip,frontdb_ip,tp_dba_name,tp_cache_name '
            'FROM env_serverip ' + condition)

    return db.cur.fetchone()


def get_product_store(app_name):
    with DBConnectionManager(host="**************", port=3306, user="scm", password="howbuyscm", db="django_scm") as db:
        result = db.cur.execute(
            '''
            SELECT appName,gitRepo,containerName,appType,packageName
            FROM common_service_artifactinfo
            WHERE appName="{}" '''.format(app_name))
    if result == 0:
        return {}
    else:
        return db.cur.fetchall()[0]


def get_tms_info_list(env_name, tms_name_list):
    """
    获取tms信息列表的信息 zt@2020-07-30
    :param env_name: 环境套名称
    :param tms_name_list: tms名称列表
    :return:
    """
    if env_name and len(tms_name_list) > 0:
        tms_name_str = ','.join('\'' + x + '\'' for x in tms_name_list)
        with DBConnectionManager(host="**************", port=3306, user="scm", password="howbuyscm", db="spider") as db:
            db.cur.execute(
                """
                SELECT DISTINCT
                    m.module_name,
                    b.package_type
                FROM app_mgt_app_module m
                INNER JOIN app_mgt_app_build b on b.module_name = m.module_name
                INNER JOIN env_mgt_node_bind nb ON nb.module_name = m.module_name
                INNER JOIN env_mgt_container c ON nb.node_docker = c.container_code
                INNER JOIN env_mgt_suite s ON nb.suite_id = s.id
                WHERE s.suite_code = '{}' 
                AND nb.deploy_type = 2
                AND m.module_name in ({})
                ORDER BY m.module_name ASC;
                """.format(env_name, tms_name_str)
            )
        return db.cur.fetchall()

    return None
