from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_git_code_path(feature_iteration_id):
    """获取子分支的代码源

    :param feature_iteration_id: string.迭代版本号
    :return: list. repos list
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT release_iteration_id FROM iterative_pipeline_feature_relations '
                       'WHERE feature_iteration_id = "{}" ;'.format(feature_iteration_id))
        for row in db.cur.fetchall():
            return row["release_iteration_id"]
    # 找不到父分支，返回master
    return None
