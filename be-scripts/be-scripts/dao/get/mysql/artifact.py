#
from dao.connect.mysql import DBConnectionManager


def get_artifact_repo(app_name):
    """获取应用基础信息
    :param app_name: 构建应用的名称
    :return: 应用基础信息
    """
    with DBConnectionManager() as db:
        db.cur.execute("select gitRepo from spider_app_view where appName='{}'".format(app_name))
        app_info = db.cur.fetchone()

    repo_path = ''
    if app_info and app_info['gitRepo']:
        repo_path = app_info['gitRepo']
    return repo_path


def get_repos_list(app_name_list):
    """获取仓库列表
    :param app_name_list: 应用列表
    :return: git仓库地址列表
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT gitCodePath FROM spider_app_view WHERE appName IN ("{}")'
                       .format('","'.join(app_name_list)))
        git_repos_path_list = []
        for row in db.cur.fetchall():
            git_repos_path_list.append(row["gitCodePath"])
    return git_repos_path_list


def get_branch_version(iteration_id):
    """
    通过迭代版本，获取应用分支名
    :param iteration_id: 迭代id
    :return: 应用基础信息
    """
    with DBConnectionManager() as db:
        db.cur.execute("select br_name from iter_mgt_iter_info where pipeline_id='{}'".format(iteration_id))
        iteration_info = db.cur.fetchone()

    br_name = ''
    if iteration_info and iteration_info['br_name']:
        br_name = iteration_info['br_name']

    return br_name


def get_repos_list_dep_without_onlineapp(pipeline_id, app_name_list, git_code_path_list):
    with DBConnectionManager() as db:
        query_str = 'SELECT distinct av.gitCodePath FROM iterative_pipeline_relations pr, spider_app_view av WHERE ' \
                    'pr.dep_app = av.appName and pr.dep_pipeline_id = pr.cur_pipeline_id and pr.cur_pipeline_id = "{' \
                    '}" and pr.appName IN ("{}") and av.gitCodePath not in ("{}") '.format(pipeline_id,
                                                                                           '","'.join(app_name_list),
                                                                                           '","'.join(
                                                                                               git_code_path_list))
        db.cur.execute(query_str)
        git_repos_path_list = []
        for row in db.cur.fetchall():
            git_repos_path_list.append(row["gitCodePath"])
    return git_repos_path_list
