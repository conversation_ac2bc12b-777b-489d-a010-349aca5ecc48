from dao.connect.mysql import DBConnectionManager
from dao.get.model.pipeline_log_main import PipelineLogMain


def get_last_running_pipelines():
    with DBConnectionManager() as db:
        sql = """
        select lm.*
        FROM
            pipeline_log_main lm
        inner join
        (
        SELECT
            lm.exec_parameter, max(lm.sid) sid
        FROM
            pipeline_log_main lm
        group by exec_parameter
        ) last
        on lm.sid = last.sid
        where (status = 'running' or status is null) and job_number is null and start_at > date_add(now(), interval -10 day)
        """
        db.cur.execute(sql)
        list = []
        for row in db.cur.fetchall():
            m = PipelineLogMain(row["sid"], row["exec_jenkins"], row["exec_parameter"])
            list.append(m)
    return list


def get_last_running_pipelines_with_sid():
    with DBConnectionManager() as db:
        sql = """
        select lm.*
        FROM
            pipeline_log_main lm
        inner join
        (
        SELECT
            lm.exec_parameter, max(lm.sid) sid
        FROM
            pipeline_log_main lm
        group by exec_parameter
        ) last
        on lm.sid = last.sid
        where (status = 'running' or status is null) and job_number is not null
        """
        db.cur.execute(sql)
        list = []
        for row in db.cur.fetchall():
            m = PipelineLogMain(row["sid"], row["exec_jenkins"], row["exec_parameter"], row["job_number"])
            list.append(m)
    return list
