import functools
from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import base


@functools.lru_cache(None)
def get(pipeline_id, appName=None):
    """查询iter_mgt_iter_app_info表"""
    meta = dict(
        pipeline_id=pipeline_id,
        appName=appName,
    )

    sql = '''
        SELECT git_repo_version, git_last_version, appName
        FROM iter_mgt_iter_app_info
        WHERE {}
    '''.format(base.query_condition_by_and(meta))

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()
