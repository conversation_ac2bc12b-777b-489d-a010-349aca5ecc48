from dao.connect.mysql import DBConnectionManager
from settings import logger


def app_product_info(iteration_id, suite_code, app_name_list):
    """
    :param iteration_id:
    :param suite_code:
    :return:
    """
    sql = '''
    SELECT t.module_name,t.product_version AS transit_version,p.lib_repo_version AS last_repos_version
FROM publish_transit_repos_info t RIGHT JOIN iter_mgt_iter_app_info f ON t.module_name = f.appName
RIGHT JOIN product_mgt_product_info p ON p.module_name = f.appName
WHERE t.suite_name = "{suite_code}" AND f.pipeline_id = "{iteration_id}" AND p.id IN (
SELECT MAX(id) FROM product_mgt_product_info WHERE iteration_id = 
"{iteration_id}" AND suite_code = "{suite_code}" GROUP BY module_name)
AND t.module_name IN ("{app_name_list}")
    '''.format(iteration_id=iteration_id,suite_code=suite_code, app_name_list='","'.join(app_name_list))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()
