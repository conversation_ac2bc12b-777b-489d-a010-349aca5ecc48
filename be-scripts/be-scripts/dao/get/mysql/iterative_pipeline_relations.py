from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import base


def get(cur_pipeline_id, appName=None, dep_app=None):
    meta = dict(
        cur_pipeline_id=cur_pipeline_id,
        appName=appName,
        dep_app=dep_app
    )
    sql = '''
        SELECT cur_pipeline_id, appName, app_br_name, app_pipeline_id, app_group, dep_app, dep_br_name, dep_pipeline_id,
               dep_app_group 
        FROM iterative_pipeline_relations
        WHERE {}
    '''.format(base.query_condition_by_and(meta))

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()
