import functools
from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import base


@functools.lru_cache(None)
def get(pipeline_id):
    """查询iter_mgt_iter_info表数据"""
    meta = dict(
        pipeline_id=pipeline_id,
    )
    sql = '''
        SELECT br_name, br_status, description
        FROM iter_mgt_iter_info
        WHERE {}
    '''.format(base.query_condition_by_and(meta))

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_br_name(iteration_id):
    sql = 'SELECT br_name  FROM iter_mgt_iter_info WHERE pipeline_id="{}";'.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["br_name"]
    return None


def get_tapd_id(iteration_id):
    """
     获取tapd_id
    :param iteration_id: 迭代版本号
    :return:
    """
    sql = 'SELECT tapd_id  FROM iter_mgt_iter_info WHERE pipeline_id="{}";'.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["tapd_id"]
    return None
