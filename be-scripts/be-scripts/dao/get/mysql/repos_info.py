from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_compile_code_version(iteration_id):
    sql = 'SELECT DISTINCT t.gitCodePath,f.git_last_version FROM spider_app_view t LEFT JOIN ' \
          'iter_mgt_iter_app_info f ON t.appName = f.appName WHERE f.pipeline_id = "{}" AND ' \
          'git_last_version IS NOT NULL'.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    compile_last_version = {}
    for row in db.cur.fetchall():
        compile_last_version[row["gitCodePath"]] = row["git_last_version"]
    return compile_last_version


def get_compile_code_version(iteration_id):
    sql = 'SELECT DISTINCT t.gitCodePath,f.git_last_version FROM spider_app_view t LEFT JOIN ' \
          'iter_mgt_iter_app_info f ON t.appName = f.appName WHERE f.pipeline_id = "{}" AND ' \
          'git_last_version IS NOT NULL'.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    compile_last_version = {}
    for row in db.cur.fetchall():
        compile_last_version[row["gitCodePath"]] = row["git_last_version"]
    return compile_last_version


def get_exist_transit_app(app_list, suite_name):
    exist_app = []
    sql = 'SELECT module_name FROM publish_transit_repos_info WHERE module_name IN ("{}") AND suite_name = "{}"'\
        .format('","'.join(app_list), suite_name)
    logger.info(sql)

    with DBConnectionManager() as db:
        db.cur.execute(sql)

    for row in db.cur.fetchall():
        exist_app.append(row["module_name"])
    return exist_app


def get_exist_transit_app_version(app_name, short_env_name):

    sql = '''SELECT h.product_version FROM env_mgt_node_bind f  LEFT JOIN env_mgt_suite t ON f.suite_id = t.id 
          LEFT JOIN env_mgt_region m ON t.region_id=m.id LEFT JOIN publish_transit_repos_info h ON 
          h.module_name = f.module_name  WHERE f.module_name = "{}" AND m.type_short_name="{}" 
          ORDER BY h.update_time DESC'''.format(app_name, short_env_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)

    for row in db.cur.fetchall():
        return row["product_version"]

    return None


def get_latest_compile_code_version(iteration_id):
    sql = """SELECT DISTINCT t.gitCodePath,f.git_last_version 
            FROM spider_app_view t LEFT JOIN iter_mgt_iter_app_info f ON t.appName = f.appName 
            LEFT JOIN product_mgt_product_info p on f.git_repo_version = p.lib_repo_version 
            AND p.suite_code in ("vph","vps","pre") 
            WHERE f.pipeline_id = "{}" AND 
            git_last_version IS NOT NULL order by f.create_date desc limit 1 """.format(iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    compile_last_version = {}
    for row in db.cur.fetchall():
        compile_last_version[row["gitCodePath"]] = row["git_last_version"]
    return compile_last_version

