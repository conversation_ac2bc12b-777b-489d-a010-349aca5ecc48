#
import os
import sys
import pymysql
from pymysql.cursors import DictCursor

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES


class MysqlConn(object):
    def __init__(self):
        self.conn = None

    def connect_mysql(self):
        self.conn = pymysql.connect(
            host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
            password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"])

    def close_mysql(self):
        self.conn.close()
        self.conn = None

    def __enter__(self):
        self.connect_mysql()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.conn.close()


class ValidatorMysqlWorker(MysqlConn):
    def __init__(self):
        if not hasattr(self, 'conn'):
            self.connect_mysql()

    def get_br_name(self, pipeline_id):
        if not self.conn:
            self.connect_mysql()
        cmd = """select br_name
                 from iter_mgt_iter_info
                 where pipeline_id = '%s' """ % pipeline_id
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        if result:
            return result['br_name']
        else:
            return ''

    def get_git_code_path(self, app_name):
        if not self.conn:
            self.connect_mysql()
        cmd = """select gitCodePath
                 from spider_app_view
                 where appName = '%s'""" % app_name

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        if result:
            return result['gitCodePath']
        else:
            return ''

    def get_artifact_repo_path(self, app_name):
        if not self.conn:
            self.connect_mysql()
        cmd = """select gitRepo from spider_app_view
                 where appName='{}'""".format(app_name)
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        if result and result['gitRepo']:
            return result['gitRepo']
        return ''

    def get_record_app_version(self, pipeline_id, app_name):
        cmd = """select git_last_version
                 from iter_mgt_iter_app_info
                 where pipeline_id = '%s' and appName = '%s' """ % (pipeline_id, app_name)

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        if result and result['git_last_version']:
            return result['git_last_version']
        return ''

    def get_all_repo_app(self, pipeline_id):
        cmd = """select appName, git_repo_version from iter_mgt_iter_app_info
                 where pipeline_id = '{}' and git_repo_version is not null """.format(pipeline_id)

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchall()

        return result

    def get_apply_app_repo_version(self, pipeline_id, app_name):
        cmd = """select br_name, git_repo_version
                 from iter_mgt_publish_application
                 where pipeline_id = '{}' and appName = '{}' order by id desc""".format(pipeline_id, app_name)

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        return result

    def get_all_pipeline_app(self, pipeline_id):
        cmd = """SELECT t.appName,t.sys_status,f.package_type FROM iter_mgt_iter_app_info t LEFT JOIN 
                app_mgt_app_build f ON t.appName = f.module_name
                 where t.pipeline_id = '%s'""" % pipeline_id

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchall()

        return result

    def get_all_pipeline_relations(self, pipeline_id):
        cmd = """select appName, app_pipeline_id, dep_app, dep_pipeline_id
                 from iterative_pipeline_relations
                 where cur_pipeline_id = '%s' """ % pipeline_id

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchall()

        return result

    def get_all_pipeline_changeable_relations(self, pipeline_id):
        all_relations = self.get_all_pipeline_relations(pipeline_id)
        available_result = []
        for item in all_relations:
            if item['app_pipeline_id'] == pipeline_id or \
                    item['app_pipeline_id'].startswith('tms-common_') or \
                    item['app_pipeline_id'].startswith('common_'):
                if item['dep_pipeline_id'] == pipeline_id or \
                        item['dep_pipeline_id'].startswith('tms-common_') or \
                        item['dep_pipeline_id'].startswith('common_'):
                    pass
                else:
                    item['dep_app'] = ''
                    item['dep_pipeline_id'] = ''
                if item not in available_result:
                    available_result.append(item)

        return available_result

    def get_open_pipeline_info(self, pipeline_id):
        cmd = """select pipeline_id from iter_mgt_iter_info
                 where pipeline_id = '{}' and br_status = 'open'""".format(pipeline_id)

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        return result

    def get_pipeline_app_info(self, pipeline_id, app_name):
        cmd = """select id from iter_mgt_iter_app_info
                 where pipeline_id = '{}' and appName='{}' and sys_status= '上线中' """.format(
            pipeline_id, app_name)
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchone()

        return result

    def get_app_publish_pipeline(self, app_name):
        cmd = """select pipeline_id from iter_mgt_iter_app_info
                 where appName='{}' and sys_status= '上线中' """.format(app_name)
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            result = cursor.fetchall()

        return result


if __name__ == '__main__':
    mysql = ValidatorMysqlWorker()
    mysql.close_mysql()
