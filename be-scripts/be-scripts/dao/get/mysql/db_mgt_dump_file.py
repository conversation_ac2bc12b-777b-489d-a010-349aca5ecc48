from dao.connect.mysql import DBConnectionManager
from settings import logger


# def get_db_dump_info_by_module_name(module_name, dump_bis_code, opt_type):
#     """
#         因增加domain，需优化 已完成优化 20231113
#         因重构，二次优化 20240606
#     """
#     """获取vcs的相关信息"""
#     data_list = []
#     if module_name:
#         with DBConnectionManager() as db:
#             condition = 1
#             if opt_type and opt_type == 3:
#                 condition = 0
#             inner_sql = """
#                         SELECT MAX( dump_file_batch ) AS max_batch_num, biz_base_db_bind_id, dmsb.db_logic_id
#                         FROM db_mgt_dump_file df
#                         INNER JOIN db_mgt_suite_bind dmsb ON df.db_info_id = dmsb.db_info_id
#                         WHERE dump_file_is_active = {}
#                         GROUP BY biz_base_db_bind_id, dmsb.db_logic_id
#                 """.format(condition)
#             sql = """
#                     SELECT DISTINCT
#                         f.id,
#                         f.dump_file_name,
#                         IFNULL(f.dump_source, 0) as dump_source,
#                         f.db_name as source_db_name,
#                         f.dump_file_path,
#                         li.logic_db_name AS db_name
#                     FROM
#                     db_mgt_dump_file f
#                             INNER JOIN (
#                                 {inner_sql}
#                                 ) v ON f.dump_file_batch = v.max_batch_num AND f.biz_base_db_bind_id = v.biz_base_db_bind_id
#                             INNER JOIN biz_base_db_bind db_bind ON f.biz_base_db_bind_id = db_bind.id
#                             INNER JOIN db_mgt_suite_bind sb ON f.db_info_id = sb.db_info_id
#                             INNER JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id  AND v.db_logic_id = li.id
#                             INNER JOIN db_mgt_app_bind ab ON ab.db_domain_id = li.db_domain_id
#                             WHERE
#                             ab.app_module_name = '{module_name}'
#                             AND db_bind.biz_base_db_code = '{dump_bis_code}'
#                 """.format(inner_sql=inner_sql, module_name=module_name, dump_bis_code=dump_bis_code)
#             logger.info(sql)
#             db.cur.execute(sql)
#             data_list = db.cur.fetchall()
#
#     return data_list


def get_db_dump_info_by_biz_iter_id(module_name, biz_iter_id):
    with DBConnectionManager() as db:
        sql = '''
                SELECT DISTINCT
                            f.id,
                            f.dump_file_name,
                            IFNULL(f.dump_source, 0) AS dump_source,
                            i.suite_db_name AS source_db_name,
                            li.logic_db_name AS db_name 
                FROM
                        db_mgt_dump_file f
                        INNER JOIN db_mgt_info i ON i.id = f.db_info_id
                        INNER JOIN db_mgt_suite_bind sb ON f.db_info_id = sb.db_info_id
                        INNER JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id 
                        INNER JOIN db_mgt_app_bind ab ON ab.db_domain_id = li.db_domain_id
                        WHERE ab.app_module_name = '{}' AND f.biz_test_iter_id = '{}';
              '''.format(module_name, biz_iter_id)

        logger.info(sql)
        db.cur.execute(sql)
        data_list = db.cur.fetchall()

    return data_list


def get_db_dump_info_by_dump_file_id(dump_file_id):
    with DBConnectionManager() as db:
        sql = '''
                SELECT DISTINCT
                            f.id,
                            f.dump_file_name,
                            IFNULL(f.dump_source, 0) AS dump_source,
                            i.suite_db_name AS source_db_name,
                            li.logic_db_name AS db_name 
                FROM
                        db_mgt_dump_file f
                        INNER JOIN db_mgt_info i ON i.id = f.db_info_id
                        INNER JOIN db_mgt_suite_bind sb ON f.db_info_id = sb.db_info_id
                        INNER JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id 
                        INNER JOIN db_mgt_app_bind ab ON ab.db_domain_id = li.db_domain_id
                        WHERE f.id = {} ;
              '''.format(dump_file_id)

        logger.info(sql)
        db.cur.execute(sql)
        data_list = db.cur.fetchall()

    return data_list


def get_empty_dump_with_module_name_for_oracle_clear(module_name, biz_iter_id):
    """根据伟敏设定的特殊迭代，获取到此应用的空dump文件。zt@2024-09-20"""
    with DBConnectionManager() as db:
        sql = '''
            SELECT DISTINCT 
                db_dump.id,
                db_dump.dump_file_name,
                IFNULL(db_dump.dump_source, 0) AS dump_source,
                db_info.suite_db_name          AS source_db_name,
                db_logic.logic_db_name         AS db_name
            FROM db_mgt_dump_file db_dump
                INNER JOIN db_mgt_info db_info ON db_info.id = db_dump.db_info_id
                INNER JOIN db_mgt_suite_bind db_suite_bind ON db_dump.db_info_id = db_suite_bind.db_info_id
                INNER JOIN db_mgt_logic_info db_logic ON db_suite_bind.db_logic_id = db_logic.id
                INNER JOIN db_mgt_app_bind db_app_bind ON db_app_bind.db_domain_id = db_logic.db_domain_id
            WHERE db_app_bind.app_module_name = '{}'
              AND db_dump.biz_test_iter_id = '{}';
        '''.format(module_name, biz_iter_id)

        logger.info(sql)
        db.cur.execute(sql)
        data_list = db.cur.fetchall()

    return data_list


def update_status_by_bis_pipeline_id(bis_pipeline_id):
    if bis_pipeline_id:
        with DBConnectionManager() as db:
            sql = """
                  update db_mgt_dump_file set dump_file_is_active = 1 where biz_iter_id = '{biz_iter_id}'
                 """.format(biz_iter_id=bis_pipeline_id)
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()


if __name__ == "__main__":
    print()
    # get_db_dump_info_by_module_name('fbs-online-service', 'it100', 1)
