#
from dao.connect.mysql import DBConnectionManager


def get_sync_config():
    """ 获取zeus sync_config api信息
    :return: zeus sync_config api信息
    """
    with DBConnectionManager() as db:
        db.cur.execute(
            '''SELECT interface_name, request_type, request_address
               FROM task_mgt_external_interface WHERE interface_name = "sync_config" '''
        )
    return db.cur.fetchone()


def get_env_config():
    """ 获取zeus env_config api信息
    :return: zeus env_config api信息
    """
    with DBConnectionManager() as db:
        db.cur.execute(
            '''SELECT interface_name, request_type, request_address
               FROM task_mgt_external_interface WHERE interface_name = "env_config" '''
        )
    return db.cur.fetchone()


def get_c_config_br():
    """ 获取zeus c_config_br api信息
    :return: zeus c_config_br api信息
    """
    with DBConnectionManager() as db:
        db.cur.execute(
            '''SELECT interface_name, request_type, request_address
               FROM task_mgt_external_interface WHERE interface_name = "c_config_br" '''
        )
    return db.cur.fetchone()


def get_archive_config():
    """ 获取zeus archive_config api信息
    :return: zeus archive_config api信息
    """
    with DBConnectionManager() as db:
        db.cur.execute(
            '''SELECT interface_name, request_type, request_address, request_params
               FROM task_mgt_external_interface WHERE interface_name = "archive_config" '''
        )
    return db.cur.fetchone()


def get_app_code_path(app_name):
    with DBConnectionManager() as db:
        db.cur.execute(
            '''SELECT a.git_url, a.git_path, m.module_name
            FROM app_mgt_app_module m
            LEFT JOIN app_mgt_app_info a ON m.app_id=a.id
            WHERE m.module_name="{}" '''.format(app_name)
        )
    return db.cur.fetchone()
