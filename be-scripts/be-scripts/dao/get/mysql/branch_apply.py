from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_git_code_path(iteration_id):
    """获取迭代存在的仓库

    :param iteration_id: string.迭代版本号
    :return: list. repos list
    """
    with DBConnectionManager() as db:
        sql = """SELECT m.package_type, m.module_name ,CONCAT(t.git_url,t.git_path) as git_repos_path 
                    FROM app_mgt_app_info t  LEFT JOIN app_mgt_app_build m ON
                     t.id=m.app_id left join iter_mgt_iter_app_info h on h.appName = m.module_name WHERE  
                      h.pipeline_id = '{}'""".format(iteration_id)
        logger.info(sql)
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_have_share_module_app_list(share_module_name):
    """
    获取有共用module的 应用列表
    """
    app_list = []
    with DBConnectionManager() as db:
        sql = """select module_name from app_mgt_app_module t where t.share_module_name = '{}'""".format(share_module_name)

        db.cur.execute(sql)
        for row in db.cur.fetchall():
            app_list.append(row["module_name"])
    return app_list


def get_need_online_app(git_path_repos, iteration_id=None):
    """获取迭代内 仓库列表需要上线的应用
     :param git_path_repos: list
    :param iteration_id: string.迭代版本号
    :return: list. app list
    """
    app_list = []

    if iteration_id is None:
        sql = """SELECT m.module_name
                           FROM app_mgt_app_info t  LEFT JOIN app_mgt_app_build m ON
                            t.id=m.app_id LEFT JOIN app_mgt_app_module z ON z.module_name = m.module_name
                             WHERE   CONCAT(t.git_url,t.git_path) IN ('{}') AND z.need_online = 1 
                            """.format("','".join(git_path_repos))
    else:
        sql = """SELECT m.module_name
                                  FROM app_mgt_app_info t  LEFT JOIN app_mgt_app_build m ON
                                   t.id=m.app_id LEFT JOIN iter_mgt_iter_app_info h ON h.appName = m.module_name  
                                   LEFT JOIN app_mgt_app_module z ON z.module_name = m.module_name
                                    WHERE   CONCAT(t.git_url,t.git_path) IN ('{}') AND
                                    h.pipeline_id = '{}'  AND z.need_online = 1 
                                   """.format("','".join(git_path_repos), iteration_id)

    with DBConnectionManager() as db:
        logger.info(sql)
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            app_list.append(row["module_name"])

    return app_list


def select_need_online_app(module_name_list):
    """通过应用列表获取 需要上线的应用
    :param module_name_list: list.迭代版本号
    :return: list. app list
    """
    app_list = []
    with DBConnectionManager() as db:
        sql = """
           SELECT module_name FROM app_mgt_app_module WHERE need_online = 1 AND module_name IN ('{}')
           """.format("','".join(module_name_list))
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            app_list.append(row["module_name"])
    return app_list


def get_compile_info(app_list):
    with DBConnectionManager() as db:
        sql = """
             SELECT m.module_name,b.build_cmd,b.package_type,m.lib_repo,CONCAT(i.git_url,i.git_path) as git_repo_path 
             FROM app_mgt_app_build b 
             LEFT JOIN app_mgt_app_module m ON b.module_name=m.module_name 
             LEFT JOIN app_mgt_app_info i ON i.id= m.app_id
        WHERE m.module_name  IN ('{}')
             """.format("','".join(app_list))
        db.cur.execute(sql)

    compile_info = {}
    for row in db.cur.fetchall():
        compile_info[row["module_name"]] = {"git_repo_path": row["git_repo_path"],
                                            "lib_repo": row["lib_repo"],
                                            "build_cmd": row["build_cmd"],
                                            "package_type": row["package_type"]}
    return compile_info


def get_release_app_name(repos_list):
    """获取发布的应用

    :param repos_list: string.迭代版本号
    :return: list. repos list
    """

    with DBConnectionManager() as db:
        db.cur.execute('SELECT appName FROM spider_app_view WHERE gitCodePath IN '
                       '("{}") AND need_online=1;'
                       .format('","'.join(repos_list)))
        app_name = []
        for row in db.cur.fetchall():
            app_name.append(row["appName"])
    return app_name


def get_repos_app(repos_path):
    """
    获取仓库下的应用
    :param repos_path:
    :return:list. 应用列表
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT appName FROM spider_app_view WHERE gitCodePath = "{}";'.format(repos_path))
        app_name = []
        for row in db.cur.fetchall():
            app_name.append(row["appName"])
    return app_name


def get_release_apps(pipeline_id):
    """获取发布的应用
    :return: list. repos list
    """

    with DBConnectionManager() as db:
        db.cur.execute('''
            SELECT appName FROM `iter_mgt_iter_app_info`
            WHERE pipeline_id='{}' AND git_repo_version IS NOT null AND git_repo_version <> ''
        '''.format(pipeline_id))
        app_name = []
        for row in db.cur.fetchall():
            app_name.append(row["appName"])
    return app_name


def get_release_version(git_code_path):
    """
      获取应用的线上版本
    :param git_code_path:
    :return: str. 版本号
    """
    with DBConnectionManager() as db:
        db.cur.execute('''
             SELECT f.br_name FROM iter_mgt_iter_app_info t
                 LEFT JOIN spider_app_view m ON t.appName=m.appName
                 LEFT JOIN iter_mgt_iter_info f ON t.pipeline_id=f.pipeline_id
             WHERE m.gitCodePath = "{}" AND f.br_end_date IS NOT NULL ORDER BY f.br_end_date DESC
             LIMIT 1
        '''.format(git_code_path))
        for row in db.cur.fetchall():
            return row["br_name"]
    return "0.0.0"


def get_code_path(pipeline_id):
    """获取迭代包含的仓库
    :return: list. git code path list
    """

    with DBConnectionManager() as db:
        db.cur.execute('''
                   SELECT DISTINCT m.gitCodePath FROM iter_mgt_iter_app_info t  
                   LEFT JOIN spider_app_view m ON t.appName=m.appName
                   WHERE t.pipeline_id= "{}" and m.gitCodePath IS NOT NULL
        '''.format(pipeline_id))
        code_path_list = []
        for row in db.cur.fetchall():
            code_path_list.append(row["gitCodePath"])
    return code_path_list


def get_feature_num(release_iteration_id):
    """
     获取最新的featrue 号
     :param release_br_name:
    """

    with DBConnectionManager() as db:
        db.cur.execute('SELECT feature_num FROM iterative_pipeline_feature_relations '
                       'WHERE release_iteration_id="{}" ORDER BY feature_num DESC LIMIT 1'.format(release_iteration_id))
        feature_num = 0
        for row in db.cur.fetchall():
            return row["feature_num"]
    return feature_num

def get_iter_status(pipeline_id):
    """获取迭代状态

    :param pipeline_id: string.迭代版本号
    :return: str colse ，open
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT br_status FROM iter_mgt_iter_info WHERE pipeline_id = "{}";'.format(pipeline_id))

        for row in db.cur.fetchall():
            return row["br_status"]
    return None


def get_share_module_name(module_name):
    """
    获取应用的共用share_module_name的属性
    """
    with DBConnectionManager() as db:
        db.cur.execute('select amam.share_module_name  from app_mgt_app_module amam where amam.module_name = "{}"'.format(module_name))

        for row in db.cur.fetchall():
            return row["share_module_name"]


if __name__ == '__main__':
    aaa = get_share_module_name('cgi-ehowbuy-container')
    list = [{'repos_path': 'cgi/cgi', 'module_name': 'cgi-ehowbuy-container'}]
    if 0:
        print(list[0]['module_name'])
    print(aaa)