from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_branch_list(project_group):
    """获取最新迭代的分支名称

    :param project_group: string.迭代版本号
    :return: list. br_list list
    """
    with DBConnectionManager() as db:
        db.cur.execute('''SELECT br_name from iter_mgt_iter_info
                          WHERE project_group = "{}"
                                AND br_status = "open"
                                AND br_style in ("release", "bugfix");'''.format(project_group))
        br_list = []
        for row in db.cur.fetchall():
            br_list.append(row["br_name"])
    return br_list


def get_close_branch_between_date(app_name, start_date=None, end_date=None):
    """获取指定日期之间的关闭的分支名称

    :param start_date: string.开始日期
    :param end_date: string.结束日期
    :return: list. br_list list
    """
    br_list = []
    with DBConnectionManager() as db:
        sql = '''
            SELECT  
                m.br_name
            FROM iter_mgt_iter_info m
            INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id AND i.git_repo_version IS NOT NULL
            LEFT JOIN(
                SELECT 
                i.appName, MAX(m.br_end_date) AS max_br_end_date
                FROM iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id AND i.git_repo_version IS NOT NULL
                WHERE m.br_status = 'close'
                GROUP BY i.appName
            )v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
            WHERE m.br_status = 'close' AND i.appName = '{}'
        '''.format(app_name)
        if start_date:
            sql += 'and m.br_end_date > {}'.format(start_date)
        if end_date:
            sql += 'and m.br_end_date < {}'.format(end_date)
        sql += ' order by m.br_end_date'
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            br_list.append(row["br_name"])
    return br_list


def get_open_pipeline_list():
    """获取新流水线正在开发的迭代名称
    :return: list. pipeline_id list
    """
    with DBConnectionManager() as db:
        db.cur.execute('''SELECT pipeline_id from iter_mgt_iter_info
                          WHERE br_status = "open" and br_style in ("bugfix", "release");''')
        p_list = []
        for row in db.cur.fetchall():
            p_list.append(row["pipeline_id"])
    return p_list


def get_feature_info(release_iteration_id):
    """
     获取所有feature分支
     :param release_iteration_id: release 迭代号
    """

    with DBConnectionManager() as db:
        db.cur.execute('SELECT t.feature_iteration_id,f.br_name FROM iterative_pipeline_feature_relations t '
                       'LEFT JOIN iter_mgt_iter_info f ON t.feature_iteration_id = f.pipeline_id '
                       'WHERE t.release_iteration_id="{}" AND f.br_status="open"'.format(release_iteration_id))
        feature_iteration_dict = {}
        for row in db.cur.fetchall():
            feature_iteration_dict[row["feature_iteration_id"]] = row["br_name"]
    return feature_iteration_dict


def get_owner(iteration_id,git_repos_path):
    """
    获取仓库申请者
    :param iteration_id:
    :param git_repos_path:
    :return: .str
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT DISTINCT t.proposer FROM iter_mgt_iter_app_info t '
                       'LEFT JOIN spider_app_view f ON t.appName=f.appName WHERE '
                       't.pipeline_id="{}" AND f.gitCodePath="{}"'.format(iteration_id,git_repos_path))
        for row in db.cur.fetchall():
            return row["proposer"]


def get_iteration_id(app_name, br_name):
    """
    获取仓库申请者
    :param app_name:
    :param br_name:
    :return: .str
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT t.pipeline_id FROM iter_mgt_iter_app_info t LEFT JOIN iter_mgt_iter_info f ON '
                       't.pipeline_id=f.pipeline_id WHERE t.appName="{}" AND f.br_name="{}"'
                       .format(app_name, br_name))
        for row in db.cur.fetchall():
            return row["pipeline_id"]

