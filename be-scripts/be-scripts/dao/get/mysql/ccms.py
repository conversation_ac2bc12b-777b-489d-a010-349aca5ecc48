#
from dao.connect.mysql import DBConnectionManager


def get_backdb_ip_by_target(target_front_ip, back_ip):
    """
    #根据目标前端IP，获得目标后台IP和数据库名字
    :param front_ip:
    :return:
    """
    result = {}
    with DBConnectionManager(host="**************", port=3306, user="scm", password="howbuyscm", db="django_scm") as db:
        db.cur.execute("select back_ip,dba_name_per,backdb_ip,frontdb_ip from env_serverip where front_ip='{}'".format(
            target_front_ip))
        for tmp_result in db.cur.fetchall():
            result['name'] = tmp_result['dba_name_per']
            result['ip'] = back_ip
            result['backdb_ip'] = tmp_result['backdb_ip']
            result['frontdb_ip'] = tmp_result['frontdb_ip']
    return result


def get_backdb_ip_by_src(src_front_ip):
    """
    #根据源前端IP，获得目标后台IP和数据库名字
    :param src_front_ip:
    :return:
    """
    result = {}
    with DBConnectionManager(host="**************", port=3306, user="scm", password="howbuyscm", db="django_scm") as db:
        db.cur.execute("select back_ip,dba_name_per,backdb_ip,frontdb_ip from env_serverip where front_ip='{}'".format(
            src_front_ip))
        for tmp_result in db.cur.fetchall():
            result['name'] = tmp_result['dba_name_per']
            result['ip'] = tmp_result['back_ip']
            result['backdb_ip'] = tmp_result['backdb_ip']
            result['frontdb_ip'] = tmp_result['frontdb_ip']
    return result
