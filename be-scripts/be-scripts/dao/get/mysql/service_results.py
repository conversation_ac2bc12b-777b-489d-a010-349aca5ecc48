from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_exec_params(id):
    """获取执行参数

    :param id: string.迭代版本号
    :return: list. repos list
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT script_params FROM task_mgt_service_results WHERE id = {}'.format(id))
        for row in db.cur.fetchall():
            return row["script_params"]
        return None
