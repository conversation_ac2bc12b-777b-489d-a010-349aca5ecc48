import json
import re
import datetime

from dao.get.mysql import db_mgt_bind_view
from settings import logger
from dao.connect.mysql import DBConnectionManager


def get_vcs_info_by_module_name(module_name):
    """获取vcs的相关信息"""
    if module_name:
        with DBConnectionManager() as db:
            sql = """select distinct 
                b.app_module_name,
                p.vcs_path_name, p.vcs_root_path, p.vcs_proj_name,
                i.db_vcs_type, i.db_vcs_ip, i.db_vcs_port,
                i.db_vcs_domain, i.db_vcs_context, i.db_vcs_protocol,
                i.db_vcs_username, i.db_vcs_password
                from db_mgt_app_bind b
                INNER join db_mgt_vcs_path p on b.vcs_path_id = p.id
                INNER join db_mgt_vcs_info i on p.vcs_info_id = i.id
                where b.db_app_bind_is_active = 1
                and app_module_name = '{}';
            """.format(module_name)
            # logger.info(">>>>get_max_br_number():sql = {}".format(sql))
            db.cur.execute(sql)
            data_list = db.cur.fetchall()

    return data_list


def get_db_group_by_iteration_id(iteration_id, module_name=None):
    """因增加domain，需优化 已完成优化 20231113"""
    """获取db分组的相关信息"""
    """因重构，二次优化 20240606"""
    with DBConnectionManager() as db:
        sql = """
                SELECT b.app_module_name,
                       li.logic_db_name AS db_info_suffix_name,
                       g.db_group_name
                FROM iter_mgt_iter_app_info app_info 
                INNER JOIN db_mgt_app_bind b ON app_info.appName = b.app_module_name
                LEFT JOIN db_mgt_logic_info li ON b.db_domain_id = li.db_domain_id
                LEFT JOIN db_mgt_domain dmd ON li.db_domain_id = dmd.id
                LEFT JOIN db_mgt_group g ON dmd.db_group_id = g.id
                WHERE b.db_app_bind_is_active = 1
                and b.read_or_write = 1 -- 添加写库限定。zt@2024-09-14
                AND app_info.pipeline_id = '{}'
        """.format(iteration_id)
        if module_name:
            sql = sql + " and b.app_module_name = '{}'".format(module_name)
        # logger.info(">>>>get_max_br_number():sql = {}".format(sql))
        db.cur.execute(sql)
        data_list = db.cur.fetchall()

    return data_list


def get_db_mgt_sql(iteration_id, module_name=None):
    sql = """select s.id,
                    s.module_name,
                    s.iteration_id,
                    s.git_group,
                    s.br_name,
                    s.sql_file_path,
                    s.sql_file_name,
                    s.sql_file_hash,
                    s.sql_src_type,
                    s.sql_src_path,
                    s.sql_ver_name,
                    s.sql_ver_db,
                    s.sql_ver_group,
                    s.sql_ver_tgt,
                    s.sql_ver_upload_status,
                    s.sql_ver_upload_time,
                    s.sql_file_desc
                from db_mgt_sql s
                where s.iteration_id = '{}'
                AND s.sql_ver_upload_status = 0
                """.format(iteration_id)
    if module_name:
        sql += """ and s.module_name = '{}' """.format(module_name)
    with DBConnectionManager() as db:
        # logger.info(">>>>get_max_br_number():sql = {}".format(sql))
        db.cur.execute(sql)
        data_list = db.cur.fetchall()
        logger.info(">>>>get_db_mgt_sql():data_list = {}".format(data_list))
    for item in data_list:
        if '/' in item.get('sql_file_path'):
            if item.get('sql_file_path').split('/')[-2] in ["DDL", "DML"]:
                item["path_type"] = item.get('sql_file_path').split('/')[-2]
            else:
                item["path_type"] = ""
        else:
            item["path_type"] = ""

    return data_list


def get_all_db_mgt_sql(module_name, iteration_id):
    """根据「应用」和「迭代」，获取所有SQL文件记录，忽略状态。zt@2024-09-10"""
    if module_name:
        with DBConnectionManager() as db:
            search_sql = '''
                select m.id,
                       m.module_name,
                       m.iteration_id,
                       m.git_group,
                       m.br_name,
                       m.sql_file_path,
                       m.sql_file_name,
                       m.sql_file_hash,
                       m.sql_src_type,
                       m.sql_src_path,
                       m.sql_ver_name,
                       m.sql_ver_db,
                       m.sql_ver_group,
                       m.sql_ver_tgt,
                       m.sql_ver_upload_status,
                       m.sql_ver_upload_time,
                       m.sql_file_desc,
                       m.arc_sql_ver_name
                from db_mgt_sql m
                where m.module_name = '{}'
                and m.iteration_id = '{}'
                order by m.sql_file_name
                ;'''.format(module_name, iteration_id)
            logger.info(">>>> search_sql = {}".format(search_sql))
            # cursor = db.cursor()
            # cursor.execute(search_sql)
            db.cur.execute(search_sql)
        return db.cur


def get_all_db_mgt_sql_only_iter(iteration_id):
    """只根据「迭代」，获取所有SQL文件记录，忽略状态。zt@2024-09-23"""
    item_dict_list = []
    if iteration_id:
        with DBConnectionManager() as db:
            search_sql = '''
                select m.id,
                       m.module_name,
                       m.iteration_id,
                       m.git_group,
                       m.br_name,
                       m.sql_file_path,
                       m.sql_file_name,
                       m.sql_file_hash,
                       m.sql_src_type,
                       m.sql_src_path,
                       m.sql_ver_name,
                       m.sql_ver_db,
                       m.sql_ver_group,
                       m.sql_ver_tgt,
                       m.sql_ver_upload_status,
                       m.sql_ver_upload_time,
                       m.sql_file_desc,
                       m.arc_sql_ver_name
                from db_mgt_sql m
                where m.iteration_id = '{}'
                order by m.sql_file_name
                ;'''.format(iteration_id)
            logger.info(">>>> search_sql = {}".format(search_sql))
            # cursor = db.cursor()
            # cursor.execute(search_sql)
            db.cur.execute(search_sql)
            for obj in db.cur.fetchall():
                item_dict_list.append(
                    {
                        "id": obj.get("id"),
                        "module_name": obj.get("module_name"),
                        "iteration_id": obj.get("iteration_id"),
                        "git_group": obj.get("git_group"),
                        "br_name": obj.get("br_name"),
                        "sql_file_path": obj.get("sql_file_path"),
                        "sql_file_name": obj.get("sql_file_name"),
                        "sql_file_hash": obj.get("sql_file_hash"),
                        "sql_src_type": obj.get("sql_src_type"),
                        "sql_src_path": obj.get("sql_src_path"),
                        "sql_ver_name": obj.get("sql_ver_name"),
                        "sql_ver_db": obj.get("sql_ver_db"),
                        "sql_ver_group": obj.get("sql_ver_group"),
                        "sql_ver_tgt": obj.get("sql_ver_tgt"),
                        "sql_ver_upload_status": obj.get("sql_ver_upload_status"),
                        "sql_ver_upload_time": obj.get("sql_ver_upload_time"),
                        "sql_file_desc": obj.get("sql_file_desc"),
                        "arc_sql_ver_name": obj.get("arc_sql_ver_name"),
                    }
                )

    return item_dict_list


def get_sql_file_by_module_and_iter(iteration_id, module_name=None):
    """再次优化，基于应用对应库的SQL。zt@2024-09-24"""
    item_dict_list = []
    if iteration_id:
        with DBConnectionManager() as db:
            search_sql = '''
                select db_sql_file.id,
                       db_sql_file.module_name,
                       db_sql_file.iteration_id,
                       db_sql_file.git_group,
                       db_sql_file.br_name,
                       db_sql_file.sql_file_path,
                       db_sql_file.sql_file_name,
                       db_sql_file.sql_file_hash,
                       db_sql_file.sql_src_type,
                       db_sql_file.sql_src_path,
                       db_sql_file.sql_ver_name,
                       db_sql_file.sql_ver_db,
                       db_sql_file.sql_ver_group,
                       db_sql_file.sql_ver_tgt,
                       db_sql_file.sql_ver_upload_status,
                       db_sql_file.sql_ver_upload_time,
                       db_sql_file.sql_file_desc,
                       db_sql_file.arc_sql_ver_name
                from db_mgt_app_bind db_app_bind
                inner join db_mgt_domain db_domain on db_domain.id = db_app_bind.db_domain_id
                inner join db_mgt_logic_info db_logic_info on db_logic_info.db_domain_id = db_domain.id
                inner join db_mgt_sql db_sql_file on db_sql_file.sql_ver_db = db_logic_info.logic_db_name
                where db_sql_file.iteration_id = '{}'
                '''.format(iteration_id)
            if module_name:
                search_sql += " and db_app_bind.app_module_name = '{}' ".format(module_name)
            search_sql += " order by db_sql_file.sql_file_path,db_sql_file.sql_file_name"
            logger.info(">>>> search_sql = {}".format(search_sql))
            # cursor = db.cursor()
            # cursor.execute(search_sql)
            db.cur.execute(search_sql)
            for obj in db.cur.fetchall():
                item_dict_list.append(
                    {
                        "id": obj.get("id"),
                        "module_name": obj.get("module_name"),
                        "iteration_id": obj.get("iteration_id"),
                        "git_group": obj.get("git_group"),
                        "br_name": obj.get("br_name"),
                        "sql_file_path": obj.get("sql_file_path"),
                        "sql_file_name": obj.get("sql_file_name"),
                        "sql_file_hash": obj.get("sql_file_hash"),
                        "sql_src_type": obj.get("sql_src_type"),
                        "sql_src_path": obj.get("sql_src_path"),
                        "sql_ver_name": obj.get("sql_ver_name"),
                        "sql_ver_db": obj.get("sql_ver_db"),
                        "sql_ver_group": obj.get("sql_ver_group"),
                        "sql_ver_tgt": obj.get("sql_ver_tgt"),
                        "sql_ver_upload_status": obj.get("sql_ver_upload_status"),
                        "sql_ver_upload_time": obj.get("sql_ver_upload_time"),
                        "sql_file_desc": obj.get("sql_file_desc"),
                        "arc_sql_ver_name": obj.get("arc_sql_ver_name"),
                    }
                )

    return item_dict_list


def del_db_mgt_sql_with_batch(del_id_str):
    if del_id_str:
        with DBConnectionManager() as db:
            delete_sql = 'delete from db_mgt_sql where id in ({})'.format(del_id_str)
            logger.info(">>>> delete_sql = {}".format(delete_sql))
            del_count = db.cur.execute(delete_sql)
            db.connection.commit()
            return del_count

def del_db_mgt_sql(module_name, iteration_id):
    if module_name and iteration_id:
        with DBConnectionManager() as db:
            sql = """delete from db_mgt_sql  where module_name = '{}'  and iteration_id = '{}';""".format(module_name, iteration_id)
            print(sql)
            db.cur.execute(sql)
            db.connection.commit()

def del_db_mgt_sql_by_file_name_iteration_id( iteration_id,sql_file_name_list):
    if sql_file_name_list and iteration_id:
        sql_file_name_list_str = ', '.join("'" + item + "'" for item in sql_file_name_list)
        with DBConnectionManager() as db:
            sql = """delete from db_mgt_sql  where sql_file_name in ({})  and iteration_id = '{}';""".format(sql_file_name_list_str, iteration_id)
            db.cur.execute(sql)
            db.connection.commit()


def ins_db_mgt_sql(db_ins_tuple_list):
    ins_count = 0
    if db_ins_tuple_list:
        with DBConnectionManager() as db:
            sql = """INSERT INTO db_mgt_sql(
                create_user,
                create_time,
                update_user,
                update_time,
                stamp,
                module_name,
                iteration_id,
                git_group,
                br_name,
                sql_file_path,
                sql_file_name,
                sql_file_hash,
                sql_src_type,
                sql_src_path,
                sql_ver_name,
                sql_ver_db,
                sql_ver_group,
                sql_ver_tgt,
                sql_ver_upload_status,
                sql_ver_upload_time,
                sql_file_desc
            )VALUES(%s,%s,%s,%s,%s,
            %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,
            %s,%s,%s,%s,%s,%s);
            """
            # logger.info(">>>>sql = {}".format(sql))
            # logger.info(">>>>db_ins_tuple_list = {}".format(db_ins_tuple_list))
            ins_count = db.cur.executemany(sql, db_ins_tuple_list)

            db.connection.commit()

    return ins_count


def upd_db_mgt_sql_for_make(db_upd_tuple_list):
    upd_count = 0
    if db_upd_tuple_list:
        with DBConnectionManager() as db:
            sql = """update db_mgt_sql
                set
                    update_user = %s,
                    update_time = %s,
                    sql_ver_name = %s,
                    sql_ver_upload_status = %s
                where
                    id = %s;
            """
            upd_count = db.cur.executemany(sql, db_upd_tuple_list)

            db.connection.commit()

    return upd_count


def upd_db_mgt_sql_for_upload(db_upd_tuple_list):
    upd_count = 0
    if db_upd_tuple_list:
        with DBConnectionManager() as db:
            sql = """update db_mgt_sql
                set
                    update_user = %s,
                    update_time = %s,
                    sql_ver_upload_status = %s,
                    sql_ver_upload_time = %s
                where
                    id = %s;
            """
            upd_count = db.cur.executemany(sql, db_upd_tuple_list)

            db.connection.commit()

    return upd_count


def get_db_suffix_name_by_module_name(module_name):
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
        只对写库创建devops-std下创建目录接口：/BATCH/batch/DDL,/BATCH/batch/DML
    """
    sql = '''
            SELECT li.logic_db_name AS db_info_suffix_name
            FROM db_mgt_app_bind t 
            INNER JOIN db_mgt_logic_info li ON t.db_domain_id = li.db_domain_id
            WHERE t.app_module_name = '{}' AND t.db_app_bind_is_active = 1 AND t.read_or_write = 1;
        '''.format(module_name)
    data_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        data_list = db.cur.fetchall()
    return data_list



def get_archery_info_by_db_info(db_info_result):
    db_srv_id_list = []
    for db_info in db_info_result:
        db_srv_id_list.append(db_info.get("db_srv_id"))

    if not db_srv_id_list:
        return []

    sql = '''
            SELECT DISTINCT ai.id as archery_id, ab.id as archery_bind_id, ab.db_srv_id, 
                   ai.archery_srv_hosts, ai.archery_srv_port, ab.group_id, ab.instance_id 
            FROM db_mgt_archery_info ai
            LEFT JOIN db_mgt_archery_bind ab ON ai.id = ab.archery_id
            WHERE ab.db_srv_id in ({});
          '''.format(', '.join(str(x) for x in db_srv_id_list))

    srv_archery_info_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for item in db.cur.fetchall():
        srv_archery_info_list.append(
            {"db_srv_id": item.get("db_srv_id"), "archery_bind_id": item.get("archery_bind_id"),
             "archery_srv_hosts": item.get("archery_srv_hosts"),
             "archery_srv_port": item.get("archery_srv_port"),
             "group_id": item.get("group_id"), "instance_id": item.get("instance_id"),
             "archery_id": item.get("archery_id")})
    # 没有配置db_mgt_archery_bind的绑定关系时，返回空列表
    if not srv_archery_info_list:
        return []

    archery_info_list = []
    for db_info in db_info_result:
        for srv_archery_info in srv_archery_info_list:
            if db_info.get("db_srv_id") == srv_archery_info.get("db_srv_id"):
                db_info.update({"archery_srv_hosts": srv_archery_info.get("archery_srv_hosts"),
                                "archery_srv_port": srv_archery_info.get("archery_srv_port"),
                                "group_id": srv_archery_info.get("group_id"),
                                "instance_id": srv_archery_info.get("instance_id"),
                                "archery_bind_id": srv_archery_info.get("archery_bind_id"),
                                "archery_id": srv_archery_info.get("archery_id")})
                archery_info_list.append(db_info)

    # 去掉archery列表中的应用信息并去重
    new_list = [{key: value for key, value in item.items() if key != 'app_module_name' and key != 'db_alias'} for item
                in archery_info_list]
    new_list = list({tuple(sorted(d.items())): d for d in new_list}.values())
    return new_list


def get_publish_sql_info(iteration_id, app_name):
    sql_group_list = []
    sql_file_info = []
    sql = '''
    SELECT module_name,sql_ver_name,sql_ver_group,sql_file_path FROM `db_mgt_sql`
     WHERE module_name = '{}' AND iteration_id = "{}"
    '''.format(app_name, iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        print(sql)
        for item in db.cur.fetchall():
            if item['sql_ver_group'] not in sql_group_list:
                sql_group_list.append(item['sql_ver_group'])
            sql_file_info.append({'module_name': item['module_name'], 'sql_name': item['sql_ver_name'],
                                  'sql_file_path': item['sql_file_path']})
    return sql_group_list, sql_file_info


def get_db_type_by_module_name(module_name, suite_code=None):
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
    """
    env_db_info_list = db_mgt_bind_view.get(module_name=module_name)
    if suite_code:
        env_db_info_list = db_mgt_bind_view.get(module_name=module_name, suite_code=suite_code)
    for env_db_info in env_db_info_list:
        if env_db_info.get("db_srv_type"):
            # 这里默认返回第一个（it01）db_srv_type
            # 如果应用存在不同环境不同类型的情况，需要传带suite_code查询
            return env_db_info.get("db_srv_type")
    return None


def get_min_sql_time_for_clear(iteration_id, module_name):
    """根据「迭代」和「应用」，获取sql最早创建时间（检查归档，清理）。zt@2025-05-26"""
    rst = None
    with DBConnectionManager() as db:
        search_sql = '''
                select db_sql.id as sql_id,
                       db_sql.create_user,
                       db_sql.create_time,
                       db_sql.module_name,
                       db_sql.iteration_id,
                       db_sql.git_group,
                       db_sql.br_name,
                       db_sql.sql_file_path,
                       db_sql.sql_file_name,
                       db_sql.sql_file_hash,
                       db_sql.sql_src_type,
                       db_sql.sql_src_path,
                       db_sql.sql_ver_name,
                       db_sql.sql_ver_db,
                       db_sql.sql_ver_group,
                       iter_i.id as iter_id,
                       iter_i.br_style,
                       iter_i.br_start_date,
                       iter_i.br_end_date,
                       iter_i.br_status,
                       iter_a.appName,
                       iter_a.sys_status,
                       iter_a.proposer
                from db_mgt_sql db_sql
                         inner join iter_mgt_iter_info iter_i on iter_i.pipeline_id = db_sql.iteration_id -- 1、找到此迭代的所有应用
                         inner join iter_mgt_iter_app_info iter_a on iter_a.pipeline_id = iter_i.pipeline_id
                         inner join db_mgt_app_bind db_bind on db_bind.app_module_name = iter_a.appName
                    and db_bind.read_or_write = 1 -- 2、找到关联库的应用
                where db_sql.iteration_id = '{}'
                  and iter_a.appName = '{}'
                ;'''.format(iteration_id, module_name)
        logger.info(">>>> search_sql = {}".format(search_sql))
        # cursor = db.cursor()
        # cursor.execute(search_sql)
        db.cur.execute(search_sql)
        rst = db.cur.fetchall()
    return rst


def get_archive_time_for_group(iteration_id):
    """根据「迭代」获取此「分组」的最后迭代时间。zt@2025-05-26"""
    rst = None
    with DBConnectionManager() as db:
        search_sql = '''
                select
                    MAX(iter_i.br_end_date) AS archive_time
                from iter_mgt_iter_info sql_iter_i
                inner join iter_mgt_iter_info iter_i on iter_i.project_group = sql_iter_i.project_group
                inner join iter_mgt_iter_app_info iter_a on iter_a.pipeline_id = iter_i.pipeline_id
                inner join db_mgt_sql db_sql on db_sql.iteration_id = iter_i.pipeline_id
                where sql_iter_i.pipeline_id = '{}'
                  and iter_i.br_status = 'close'
                  and iter_a.sys_status = '已归档'
                ;'''.format(iteration_id)
        logger.info(">>>> search_sql = {}".format(search_sql))
        # cursor = db.cursor()
        # cursor.execute(search_sql)
        db.cur.execute(search_sql)
        rst = db.cur.fetchone()
    return rst


if __name__ == '__main__':
    # result = get_archery_info_by_module_name_suite('product-center-remote','it29')
    # sql_group_list, sql_file_info = get_publish_sql_info('product-center_test-archery-2023080302', 'product-center-remote')
    # print(sql_group_list)
    # print(sql_file_info)
    # get_default_suite('product-center-remote')
    # result = get_prod_archery_info('product-center-remote')
    # db_info_list = [{'app_module_name': 'product-center-remote', 'suite_code': 'it10', 'db_group_name': 'PRODUCT', 'db_group_cn_name': '中台产品组', 'db_srv_id': 22, 'db_srv_type': 'mysql', 'db_srv_hosts': '**************', 'db_srv_port': 3306, 'db_srv_name': None, 'db_alias': 'product-center-remote', 'db_name': 'product', 'suite_db_name': 'docker_it10_product', 'username': 'tms', 'password': 'tms', 'conn_url': '***************************************************************************************************************************************************************************************', 'db_vcs_type': 'gitlab', 'vcs_url': '*************************:devops-sql', 'db_srv_username': 'mysql', 'db_srv_password': '123456', 'db_srv_bash_profile': '~/.bash_profilemysql', 'db_srv_socket_path': '/data/mysql57/data/mysql.sock', 'data_dump_dir': '/data/dmp/mysqlbak/', 'db_info_id': 3501}]
    # l = get_archery_info_by_db_info(db_info_list)
    # print(l)
    print(get_db_type_by_module_name('product-center-remote'))
