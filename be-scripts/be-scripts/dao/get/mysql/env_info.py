#
import json
import re
import datetime

from job.jenkins.test_env.pipeline_test_publish_constants import PtpStepEnum
from settings import logger
from dao.connect.mysql import DBConnectionManager
from po.env_info.env_node_info import NodeAppNodePaths


def get_machine_room_list(app_name_list, env):
    """获取应用机房信息

    :param app_name_list: list.应用列表
    :param env: string.环境名称
    :return:
    """

    with DBConnectionManager() as db:
        db.cur.execute('''
                            SELECT DISTINCT t.suite_code,f.module_name FROM env_mgt_node_bind f 
                                LEFT JOIN env_mgt_suite t ON f.suite_id = t.id 
                                LEFT JOIN env_mgt_region m ON t.region_id=m.id 
                                LEFT JOIN env_mgt_node n ON f.node_id = n.id
                            WHERE f.module_name IN ("{}") AND f.enable_bind = "1" 
                                AND m.type_short_name="{}" AND n.node_status = 0;
                        '''.format('","'.join(app_name_list), env))

    return db.cur.fetchall()


def get_suite_git_repos(app_name_list, env, iteration_id):
    """
     获取 环境套和制品库地址
    :param app_name_list:
    :param env:
    :return:
    """
    sql = '''SELECT DISTINCT t.suite_code,f.module_name,h.lib_repo,z.git_repo_version,j.br_name 
             FROM env_mgt_node_bind f 
             LEFT JOIN env_mgt_suite t ON f.suite_id = t.id 
             LEFT JOIN env_mgt_region m ON t.region_id=m.id 
             LEFT JOIN app_mgt_app_module h ON h.module_name = f.module_name 
             LEFT JOIN iter_mgt_iter_app_info z ON z.appName =  h.module_name 
             LEFT JOIN iter_mgt_iter_info j on j.pipeline_id = z.pipeline_id 
             WHERE f.enable_bind = '1' 
               AND f.module_name IN ("{}")
               AND m.region_group="{}" 
               AND z.pipeline_id = "{}" ''' \
        .format('","'.join(app_name_list), env, iteration_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_suite_code_by_region_group(region_group_list):
    """
    获取可用域下可用的环境
    :param region_group_list:
    :return:
    """
    sql = '''
        SELECT distinct su.suite_code 
        FROM env_mgt_suite su
        INNER JOIN env_mgt_region  re ON re.id =su.region_id 
        WHERE re.region_group in ("{}") 
        AND su.suite_is_active = 1
        AND re.region_is_active = 1;
        '''.format('","'.join(region_group_list))
    logger.info(sql)
    suite_code_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            suite_code_list.append(row.get("suite_code"))
    return suite_code_list


def get_env_cn_name(env_name):
    with DBConnectionManager() as db:
        db.cur.execute('SELECT type_name FROM env_mgt_region WHERE type_short_name="{}"'.format(env_name))

    return db.cur.fetchone()


def get_whether_app_suite_bind(app_name, env_name):
    with DBConnectionManager() as db:
        if re.match(r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$", env_name):
            db.cur.execute('SELECT id FROM env_mgt_node WHERE node_ip="{}"'.format(env_name))
            node_id = db.cur.fetchone()
            if node_id:
                db.cur.execute('SELECT id FROM env_mgt_node_bind WHERE module_name="{}" and node_id="{}"'.format(
                    app_name, node_id['id']))
                bind_id = db.cur.fetchone()
                if bind_id:
                    return True
        else:
            db.cur.execute('SELECT id FROM env_mgt_suite WHERE suite_code="{}" and suite_is_active=1'.format(env_name))
            suite_id = db.cur.fetchone()
            if suite_id:
                db.cur.execute('SELECT id FROM env_mgt_node_bind WHERE module_name="{}" and suite_id="{}"'.format(
                    app_name, suite_id['id']))
                bind_id = db.cur.fetchone()
                if bind_id:
                    return True
    return False


def get_whether_app_use_zeus(app_name):
    with DBConnectionManager() as db:
        db.cur.execute('SELECT id FROM app_mgt_app_module WHERE module_name="{}" and zeus_type=1'.format(app_name))
        module_id = db.cur.fetchone()
        if module_id:
            return True
    return False


def get_app_type(app_name):
    with DBConnectionManager() as db:
        db.cur.execute('SELECT package_type FROM app_mgt_app_build WHERE module_name="{}"'.format(app_name))
        result = db.cur.fetchone()
        if result:
            return result['package_type']
        else:
            return ''


def get_app_container_name(app_name):
    with DBConnectionManager() as db:
        db.cur.execute('SELECT container_name FROM app_mgt_app_module WHERE module_name="{}"'.format(app_name))
        result = db.cur.fetchone()
        if result:
            return result['container_name']
        else:
            return ''


def get_br_name_list(app_name_list):
    """
    根据应用名列表，获取「prod」最新发布的分支列表 zt@2020-07-30
    :param app_name_list:
    :return:
    """
    if app_name_list and len(app_name_list) > 0:
        union_sql = None
        for idx in range(len(app_name_list)):
            app_name = app_name_list[idx]
            if idx == 0:
                union_sql = "SELECT '{}' AS appName".format(app_name)
            else:
                union_sql += " UNION SELECT '{}'".format(app_name)

        with DBConnectionManager() as db:
            sql = """
            SELECT 
            m.br_name, v.appName
            FROM iter_mgt_publish_application m
            INNER JOIN(
                SELECT v.appName, MAX(a.id) AS max_pub_id
                FROM iter_mgt_publish_application a
                INNER JOIN({}) v ON a.appName LIKE CONCAT('%',v.appName,'%')
                WHERE a.env = 'prod'
                GROUP BY v.appName
            )v ON v.max_pub_id = m.id
            """.format(union_sql)

            logger.info(">>>>get_br_name_list():sql = {}".format(sql))

            db.cur.execute(sql)

            return db.cur.fetchall()
    else:
        return None


def get_suite_code_by_node(app_name, node_ip):
    """
    根据「应用名」和「虚拟机IP」，反向获取对应的环境套 zt@2020-08-21
    :param app_name:
    :param node_ip:
    :return:
    """
    suite_code = None
    if app_name and node_ip:

        with DBConnectionManager() as db:
            sql = """
                SELECT 
                    s.suite_code
                FROM env_mgt_node_bind m
                INNER JOIN env_mgt_node n ON m.node_id = n.id AND n.node_ip = '{}'
                INNER JOIN env_mgt_suite s ON m.suite_id = s.id
                WHERE m.module_name = '{}'
            """.format(node_ip, app_name)

            logger.info(">>>>get_suite_code_by_node():sql = {}".format(sql))

            db.cur.execute(sql)

            rst = db.cur.fetchone()

            if rst:
                suite_code = rst['suite_code']
    return suite_code


def get_paths_by_app_node(app_name, node_ip, default_deploy_path, default_script_path):
    """
    根据「应用名」和「虚拟机IP」，获取要部署的路径
    :param app_name:
    :param node_ip:
    :param default_deploy_path:
    :param default_script_path:
    :return:
    """

    if app_name and node_ip:

        with DBConnectionManager() as db:
            sql = """
                SELECT 
                    m.module_name, concat(m.deploy_path,'/') as deploy_path, m.script_path
                FROM env_mgt_node_bind m
                INNER JOIN env_mgt_node n ON m.node_id = n.id AND n.node_ip = '{}'
                WHERE (m.deploy_type = '1' or m.deploy_type = '3') and m.module_name = '{}'
            """.format(node_ip, app_name)

            logger.info(">>>>get_paths_by_app_node():sql = {}".format(sql))

            db.cur.execute(sql)

            rst = db.cur.fetchone()

            if rst:
                deploy_path = rst["deploy_path"] if rst["deploy_path"] else default_deploy_path
                script_path = rst["script_path"] if rst["script_path"] else default_script_path

                return NodeAppNodePaths(True, app_name, deploy_path, script_path)
            else:
                return NodeAppNodePaths(False, app_name, default_deploy_path, default_script_path)

#get_paths_by_app_node('ftx-online-search-web','***************','abc','xyz')


def get_pipeline_id_by_branch(app_name, br_name):
    """
    根据「应用名」和「分支名」，获取迭代号 zt@2020-08-24
    :param app_name:
    :param br_name:
    :return:
    """
    data_list = None
    if app_name and br_name:

        with DBConnectionManager() as db:
            sql = """
                SELECT 
                    m.pipeline_id,
                    m.br_name,
                    m.project_group,
                    i.appName
                FROM iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                WHERE i.appName = '{}'
                AND m.br_name = '{}'
            """.format(app_name, br_name)

            logger.info(">>>>get_pipeline_id_by_branch():sql = {}".format(sql))

            db.cur.execute(sql)

            data_list = db.cur.fetchall()

    return data_list


def get_max_br_number(app_name):
    """
    根据「应用名」，获取最新的产线必布分支号 zt@2020-08-24
    :param app_name:
    :return:
    """
    if app_name:

        with DBConnectionManager() as db:
            sql = """
                SELECT 
                    m.br_name, 
                    m.appName,
                    m.pipeline_id
                from iter_mgt_publish_application m
                inner join (
                    select appName, max(id) as max_pub_id
                    from iter_mgt_publish_application
                    where env = 'prod'
                    and appName = '{}'
                    group by appName
                )v on v.max_pub_id = m.id
            """.format(app_name)

            # logger.info(">>>>get_max_br_number():sql = {}".format(sql))

            db.cur.execute(sql)

            data_list = db.cur.fetchall()

    return data_list


def get_node_info_for_test(app_name, suite_code):
    """
    根据「应用名」和「环境套」，获取绑定的节点列表 zt@2020-08-24
    :param suite_code:
    :param app_name:
    :return:
    """
    data_list = None
    if app_name:

        with DBConnectionManager() as db:
            sql = """
                SELECT 
                    m.module_name,
                    s.suite_code,
                    IF(m.node_docker IS NOT NULL AND TRIM(m.node_docker) != '','Docker','VM') AS node_type,
                    IF(m.node_docker IS NOT NULL AND TRIM(m.node_docker) != '',c.container_name,n.node_ip) AS node_code,
                    IF(m.node_docker IS NOT NULL AND TRIM(m.node_docker) != '',
                        c.container_name,
                        IF(m.node_port IS NULL OR TRIM(m.node_port) = '',n.node_ip,CONCAT(n.node_ip,':',m.node_port))) AS node_name,
                    m.deploy_path,
                    m.tomcat_path,
                    m.script_path,
                    m.script_name,
                    m.config_path,
                    m.log_path
                FROM env_mgt_node_bind m
                INNER JOIN env_mgt_suite s ON m.suite_id = s.id AND s.region_id = 32
                LEFT JOIN env_mgt_node n ON m.node_id = n.id AND (m.node_docker IS NULL or TRIM(m.node_docker) = '') AND m.deploy_type = 1
                LEFT JOIN env_mgt_container c ON m.node_docker = c.container_code AND m.deploy_type = 2
                WHERE m.module_name = '{}'
                AND s.suite_code = '{}'
                ORDER BY m.module_name, node_type, suite_code, node_code
            """.format(app_name, suite_code)

            # logger.info(">>>>get_node_info_for_test():sql = {}".format(sql))

            db.cur.execute(sql)

            data_list = db.cur.fetchall()

    return data_list


def get_app_info_by_name(app_name):
    """
    根据「应用名」，获取应用信息 zt@2020-08-26
    :param app_name:
    :return:
    """
    data_list = None
    if app_name:

        with DBConnectionManager() as db:
            sql = """
                SELECT 
                    m.container_name,
                    m.lib_repo,
                    m.zeus_type,
                    b.package_type,
                    b.package_full,
                    b.package_name
                FROM app_mgt_app_module m
                INNER JOIN app_mgt_app_info i ON m.app_id = i.id
                INNER JOIN app_mgt_app_build b ON b.module_name = m.module_name
                WHERE m.module_name = '{}'
            """.format(app_name)

            # logger.info(">>>>get_app_info_by_name():sql = {}".format(sql))

            db.cur.execute(sql)

            data_list = db.cur.fetchall()

    return data_list


def ins_ptp_log_for_create(ptp_batch_num, ptp_type, app_name, suite_code, br_name, err_msg):
    """
    生成「主日志ID」 zt@2020-08-31
    :param ptp_batch_num:
    :param ptp_type:
    :param app_name:
    :param suite_code:
    :param br_name:
    :param err_msg:
    :return:
    """
    ptp_id = None

    cur_time = datetime.datetime.now()
    with DBConnectionManager() as db:
        node_name = None
        ptp_key = "{}_{}_{}_all".format(app_name, suite_code, br_name) if app_name and suite_code and br_name else None
        ptp_step = PtpStepEnum.CREATE.value
        ptp_param = None
        ptp_result = None
        ptp_desc = err_msg if err_msg else "「ptp_id」未指定，由脚本直接生成。"
        create_user = 'howbuyscm'
        create_time = cur_time
        update_user = 'howbuyscm'
        update_time = cur_time
        stamp = 0

        sql = """
        INSERT INTO env_mgt_ptp_log(
            ptp_batch_num,
            ptp_type,
            ptp_app_name,
            ptp_suite_code,
            ptp_br_name,
            ptp_node_name,
            ptp_key,
            ptp_step,
            ptp_param,
            ptp_result,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp
        )VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);
        """.format()

        param_tuple = (
            ptp_batch_num,
            ptp_type,
            app_name,
            suite_code,
            br_name,
            node_name,
            ptp_key,
            ptp_step,
            ptp_param,
            ptp_result,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp,
        )

        ins_count = db.cur.execute(sql, param_tuple)
        if ins_count != 1:
            err_msg = "create流水线测试环境发布日志失败({}){}_{}_{}".format(ptp_type, app_name, suite_code, br_name)
            raise ValueError(err_msg)
        ptp_id = db.cur.lastrowid

        # detail
        detail_sql = """
        INSERT INTO env_mgt_ptp_log_detail(
            ptp_id,
            ptp_step,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp
        )VALUES(%s,%s,%s,%s,%s,%s,%s,%s);
        """.format()

        detail_param_tuple = (
            ptp_id,
            ptp_step,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp
        )

        db.cur.execute(detail_sql, detail_param_tuple)

        db.connection.commit()

    return ptp_id


def upd_ptp_log_for_init(ptp_id):
    """
    更新init日志 zt@2020-08-31
    :param ptp_id:
    :return:
    """
    upd_count = None
    if ptp_id:
        ptp_step_value = PtpStepEnum.INIT.value
        ptp_desc = "开始初始化"

        upd_count = _upd_ptp_log(ptp_id, ptp_step_value, None, ptp_desc)

    return upd_count


def upd_ptp_log_for_start(ptp_id, po, node):
    """
    更新start日志 zt@2020-08-31
    :param ptp_id:
    :param po:
    :param node:
    :return:
    """
    upd_count = None
    cur_time = datetime.datetime.now()
    if ptp_id:
        ptp_app_name = po.app_name
        ptp_suite_code = po.suite_code
        ptp_br_name = po.br_name
        ptp_node_name = node['node_name']

        ptp_key = "{}_{}_{}_{}".format(ptp_app_name, ptp_suite_code, ptp_br_name, ptp_node_name)
        ptp_step_value = PtpStepEnum.START.value
        ptp_desc = "准备开始运行"
        ptp_param_dict = {
            'po': po.__dict__,
            'node': node
        }
        ptp_param = json.dumps(ptp_param_dict)
        create_user = 'howbuyscm'
        create_time = cur_time
        update_user = 'howbuyscm'
        update_time = cur_time
        stamp = 0
        with DBConnectionManager() as db:

            sql = """
            UPDATE
                env_mgt_ptp_log
            SET
                ptp_br_name = %s,
                ptp_node_name = %s,
                ptp_key = %s,
                ptp_step = %s,
                ptp_param = %s,
                ptp_desc = %s,
                update_user = %s,
                update_time = %s
            WHERE id = %s;
            """.format()

            param_tuple = (
                ptp_br_name,
                ptp_node_name,
                ptp_key,
                ptp_step_value,
                ptp_param,
                ptp_desc,
                update_user,
                update_time,
                ptp_id,
            )

            upd_count = db.cur.execute(sql, param_tuple)

            # detail
            detail_sql = """
            INSERT INTO env_mgt_ptp_log_detail(
                ptp_id,
                ptp_step,
                ptp_desc,
                create_user,
                create_time,
                update_user,
                update_time,
                stamp
            )VALUES(%s,%s,%s,%s,%s,%s,%s,%s);
            """.format()

            detail_param_tuple = (
                ptp_id,
                ptp_step_value,
                ptp_desc,
                create_user,
                create_time,
                update_user,
                update_time,
                stamp,
            )

            db.cur.execute(detail_sql, detail_param_tuple)

            db.connection.commit()

    return upd_count


def ins_ptp_log_for_start(ptp_id, ptp_batch_num, ptp_type, po, node):
    """
    日志分裂 zt@2020-08-31
    :param ptp_id:
    :param ptp_batch_num:
    :param ptp_type:
    :param po:
    :param node:
    :return:
    """
    new_ptp_id = None
    cur_time = datetime.datetime.now()
    with DBConnectionManager() as db:
        ptp_type_value = ptp_type.value
        ptp_app_name = po.app_name
        ptp_suite_code = po.suite_code
        ptp_br_name = po.br_name
        ptp_node_name = node['node_name']
        ptp_key = "{}_{}_{}_{}".format(ptp_app_name, ptp_suite_code, ptp_br_name, ptp_node_name)
        ptp_step = PtpStepEnum.START.value
        ptp_param_dict = {
            'po': po.__dict__,
            'node': node
        }
        ptp_param = json.dumps(ptp_param_dict)
        ptp_result = None
        ptp_desc = "由「ptp_id = {}」分裂产生子日志。".format(ptp_id)
        create_user = 'howbuyscm'
        create_time = cur_time
        update_user = 'howbuyscm'
        update_time = cur_time
        stamp = 0

        sql = """
        INSERT INTO env_mgt_ptp_log(
            ptp_batch_num,
            ptp_type,
            ptp_app_name,
            ptp_suite_code,
            ptp_br_name,
            ptp_node_name,
            ptp_key,
            ptp_step,
            ptp_param,
            ptp_result,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp
            )VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);
        """.format()

        param_tuple = (
            ptp_batch_num,
            ptp_type_value,
            ptp_app_name,
            ptp_suite_code,
            ptp_br_name,
            ptp_node_name,
            ptp_key,
            ptp_step,
            ptp_param,
            ptp_result,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp,
        )
        ins_count = db.cur.execute(sql, param_tuple)
        if ins_count != 1:
            err_msg = "多节点部署时，日志分裂失败({}){}_{}_{}_{}".format(
                ptp_type.name,
                ptp_app_name,
                ptp_suite_code,
                ptp_br_name,
                ptp_node_name,
            )
            raise ValueError(err_msg)
        new_ptp_id = db.cur.lastrowid

        # detail
        detail_sql = """
        INSERT INTO env_mgt_ptp_log_detail(
            ptp_id,
            ptp_step,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp
        )VALUES(%s,%s,%s,%s,%s,%s,%s,%s);
        """.format()

        detail_param_tuple = (
            new_ptp_id,
            ptp_step,
            ptp_desc,
            create_user,
            create_time,
            update_user,
            update_time,
            stamp,
        )

        db.cur.execute(detail_sql, detail_param_tuple)

        db.connection.commit()

    return new_ptp_id


def upd_ptp_log_for_rs(ptp_id, po, node):
    """
    开始运行更新日志 zt@2020-09-01
    :return:
    """
    is_ok = False

    cur_time = datetime.datetime.now()
    if ptp_id:
        ptp_app_name = po.app_name
        ptp_suite_code = po.suite_code
        ptp_br_name = po.br_name
        ptp_node_name = node['node_name']

        ptp_step_value = PtpStepEnum.RUN.value
        ptp_desc = "开始运行"
        create_user = 'howbuyscm'
        create_time = cur_time
        update_user = 'howbuyscm'
        update_time = cur_time
        stamp = 0
        with DBConnectionManager() as db:
            sql = """
            UPDATE
                env_mgt_ptp_log
            SET
                ptp_step = %s,
                ptp_desc = %s,
                update_user = %s,
                update_time = %s
            WHERE (ptp_step LIKE 'R%%' OR (id = %s AND ptp_step != 'Success' AND ptp_step LIKE 'S%%'))
                AND ptp_app_name = %s
                AND ptp_suite_code = %s
                AND ptp_br_name = %s
                AND ptp_node_name = %s;
            """.format()

            param_tuple = (
                ptp_step_value,
                ptp_desc,
                update_user,
                update_time,
                ptp_id,
                ptp_app_name,
                ptp_suite_code,
                ptp_br_name,
                ptp_node_name,
            )

            upd_count = db.cur.execute(sql, param_tuple)
            if upd_count == 1:
                # detail
                detail_sql = """
                INSERT INTO env_mgt_ptp_log_detail(
                    ptp_id,
                    ptp_step,
                    ptp_desc,
                    create_user,
                    create_time,
                    update_user,
                    update_time,
                    stamp
                )VALUES(%s,%s,%s,%s,%s,%s,%s,%s);
                """.format()

                detail_param_tuple = (
                    ptp_id,
                    ptp_step_value,
                    ptp_desc,
                    create_user,
                    create_time,
                    update_user,
                    update_time,
                    stamp,
                )

                db.cur.execute(detail_sql, detail_param_tuple)

                db.connection.commit()
                is_ok = True
            else:
                db.connection.rollback()

    return is_ok


def upd_ptp_log_for_sleep(ptp_id, sleep_idx):
    """
    休眠时记录日志 zt@2020-09-01
    :return:
    """
    upd_count = None

    if ptp_id:
        ptp_step_value = 'S{}'.format(sleep_idx)
        ptp_desc = "S{}：第『{}』次休眠".format(sleep_idx, sleep_idx)

        upd_count = _upd_ptp_log(ptp_id, ptp_step_value, None, ptp_desc)

    return upd_count


def upd_ptp_log_for_timeout(ptp_id):
    """
    超时时记录日志 zt@2020-09-01
    :return:
    """
    upd_count = None
    if ptp_id:
        ptp_step_value = PtpStepEnum.TIMEOUT.value
        ptp_result = False
        ptp_desc = "执行超时"

        upd_count = _upd_ptp_log(ptp_id, ptp_step_value, ptp_result, ptp_desc)

    return upd_count


def upd_ptp_log_for_fail(ptp_id, err_msg):
    """
    失败时更新日志 zt@2020-09-01
    :return:
    """
    upd_count = None
    if ptp_id:
        ptp_step_value = PtpStepEnum.Fail.value
        ptp_result = False
        ptp_desc = err_msg

        upd_count = _upd_ptp_log(ptp_id, ptp_step_value, ptp_result, ptp_desc)

    return upd_count


def upd_ptp_log_for_success(ptp_id):
    """
    成功时更新日志 zt@2020-09-01
    :return:
    """
    upd_count = None
    if ptp_id:
        ptp_step_value = PtpStepEnum.SUCCESS.value
        ptp_result = True
        ptp_desc = "执行成功"

        upd_count = _upd_ptp_log(ptp_id, ptp_step_value, ptp_result, ptp_desc)

    return upd_count


def upd_ptp_log_for_run(ptp_id, run_step, func_name, func_doc):
    """
    运行时更新日志 zt@2020-09-01
    :return:
    """
    upd_count = None
    if ptp_id:
        ptp_step_value = 'R{}'.format(run_step)
        ptp_desc = "运行至第{}步：{}({})".format(run_step, func_name, func_doc)

        upd_count = _upd_ptp_log(ptp_id, ptp_step_value, None, ptp_desc)

    return upd_count


def _upd_ptp_log(ptp_id, ptp_step, ptp_result, ptp_desc):
    """
    更新日志 zt@2020-09-01
    :return:
    """
    upd_count = None
    cur_time = datetime.datetime.now()
    if ptp_id:
        create_user = 'howbuyscm'
        create_time = cur_time
        update_user = 'howbuyscm'
        update_time = cur_time
        stamp = 0
        with DBConnectionManager() as db:
            sql = """
            UPDATE
                env_mgt_ptp_log
            SET
                ptp_step = %s,
                ptp_result = %s,
                ptp_desc = %s,
                update_user = %s,
                update_time = %s
            WHERE id = %s;
            """.format()

            param_tuple = (
                ptp_step,
                ptp_result,
                ptp_desc,
                update_user,
                update_time,
                ptp_id,
            )

            upd_count = db.cur.execute(sql, param_tuple)

            # detail
            detail_sql = """
            INSERT INTO env_mgt_ptp_log_detail(
                ptp_id,
                ptp_step,
                ptp_desc,
                create_user,
                create_time,
                update_user,
                update_time,
                stamp
            )VALUES(%s,%s,%s,%s,%s,%s,%s,%s);
            """.format()

            detail_param_tuple = (
                ptp_id,
                ptp_step,
                ptp_desc,
                create_user,
                create_time,
                update_user,
                update_time,
                stamp,
            )

            db.cur.execute(detail_sql, detail_param_tuple)

            db.connection.commit()

    return upd_count


def _ins_ptp_log_detail(ptp_id, ptp_step, ptp_desc):
    """
    更新日志 zt@2020-09-01
    :return:
    """
    ins_count = None
    cur_time = datetime.datetime.now()
    if ptp_id:
        create_user = 'howbuyscm'
        create_time = cur_time
        update_user = 'howbuyscm'
        update_time = cur_time
        stamp = 0

        # detail
        with DBConnectionManager() as db:
            detail_sql = """
            INSERT INTO env_mgt_ptp_log_detail(
                ptp_id,
                ptp_step,
                ptp_desc,
                create_user,
                create_time,
                update_user,
                update_time,
                stamp
            )VALUES(%s,%s,%s,%s,%s,%s,%s,%s);
            """.format()

            detail_param_tuple = (
                ptp_id,
                ptp_step,
                ptp_desc,
                create_user,
                create_time,
                update_user,
                update_time,
                stamp,
            )

            ins_count = db.cur.execute(detail_sql, detail_param_tuple)

            db.connection.commit()

    return ins_count


def get_tomcat_password_by_node_ip(node_ip):
    """根据IP获取tomcat密码 zt@2020-09-09"""
    tomcat_password = None
    if node_ip:
        with DBConnectionManager() as db:
            sql = """
            SELECT m.tomcat_password
            FROM env_mgt_node m
            WHERE m.node_ip = '{}'
            ORDER BY m.node_status;
            """.format(node_ip)

            db.cur.execute(sql)

            rst = db.cur.fetchone()

            if rst:
                tomcat_password = rst['tomcat_password']
    return tomcat_password
