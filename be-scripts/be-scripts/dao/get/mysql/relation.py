#
import logging
from dao.connect.mysql import DBConnectionManager


def get_iterative_pipeline_relations(cur_pipeline_id, app_name):
    """根据cur_pipeline_id 获取所有依赖关系
    :param cur_pipeline_id: string.迭代号
    :param app_name: string.构建应用的名称
    :return: list.构建应有的所有依赖关系
    """
    with DBConnectionManager() as db:
        db.cur.execute(
            '''SELECT DISTINCT cur_pipeline_id, appName, app_br_name, app_pipeline_id, app_group, dep_app, dep_br_name,
                      dep_pipeline_id, dep_app_group 
               FROM iterative_pipeline_relations
               WHERE cur_pipeline_id = "{}" AND appName = "{}";'''.format(cur_pipeline_id, app_name)
        )
    return db.cur.fetchall()

def get_iter_module_name(cur_pipeline_id, app_name):
    """根据cur_pipeline_id 获取所有依赖关系
    :param cur_pipeline_id: string.迭代号
    :param app_name: string.构建应用的名称
    :return: list.构建应有的所有依赖关系
    """
    sql = '''SELECT DISTINCT cur_pipeline_id, appName, app_br_name, app_pipeline_id, app_group
               FROM iterative_pipeline_relations
               WHERE cur_pipeline_id = "{cur_pipeline_id}" AND iter_app_name = "{iter_app_name}";
               '''.format(cur_pipeline_id=cur_pipeline_id, iter_app_name=app_name)
    logging.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


if __name__ == '__main__':
    data = {('otc-center-remote', 'otc-center-server'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                         'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                         'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-common-service-new'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                              'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                              'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-service-new', 'otc-common-pojo'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                            'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                            'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-pojo', ''): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15', 'app_group': 'otc',
                                      'dep_br_name': '', 'dep_pipeline_id': '', 'dep_app_group': ''},
            ('otc-common-service-new', 'otc-common-constant'): {'app_br_name': '4.0.15',
                                                                'app_pipeline_id': 'otc_4.0.15', 'app_group': 'otc',
                                                                'dep_br_name': '4.0.15',
                                                                'dep_pipeline_id': 'otc_4.0.15',
                                                                'dep_app_group': 'otc'},
            ('otc-common-constant', ''): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15', 'app_group': 'otc',
                                          'dep_br_name': '', 'dep_pipeline_id': '', 'dep_app_group': ''},
            ('otc-common-service-new', 'otc-common-utils-new'): {'app_br_name': '4.0.15',
                                                                 'app_pipeline_id': 'otc_4.0.15', 'app_group': 'otc',
                                                                 'dep_br_name': '4.0.15',
                                                                 'dep_pipeline_id': 'otc_4.0.15',
                                                                 'dep_app_group': 'otc'},
            ('otc-common-utils-new', 'otc-common-constant'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                              'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                              'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-commons-utils'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                         'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                         'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-commons-utils', 'otc-common-utils-new'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                            'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                            'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-center-client'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                         'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                         'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-client', 'otc-commons-client'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                          'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                          'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-commons-client', ''): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15', 'app_group': 'otc',
                                         'dep_br_name': '', 'dep_pipeline_id': '', 'dep_app_group': ''},
            ('otc-center-server', 'otc-commons-server'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                          'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                          'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-commons-server', 'otc-commons-client'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                           'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                           'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-commons-server', 'otc-common-utils-new'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                             'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                             'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-commons-server', 'otc-commons-utils'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                          'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                          'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-component'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                     'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                     'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-component', 'otc-commons-client'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                      'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                      'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-component', 'otc-commons-server'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                      'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                      'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-common-dao'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                      'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                      'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-dao', 'otc-common-pojo'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                    'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                    'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-dao', 'otc-commons-utils'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                      'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                      'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-outservice'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                      'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                      'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-outservice', 'otc-common-utils-new'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                         'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                         'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-outservice', 'otc-commons-utils'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                      'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                      'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-outservice', 'otc-commons-server'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                       'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                       'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-center-server', 'otc-common-service'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                          'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                          'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-service', 'otc-commons-client'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                           'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                           'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-service', 'otc-commons-utils'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                          'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                          'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-service', 'otc-common-dao'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                       'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                       'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-service', 'otc-commons-server'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                           'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                           'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'},
            ('otc-common-service', 'otc-outservice'): {'app_br_name': '4.0.15', 'app_pipeline_id': 'otc_4.0.15',
                                                       'app_group': 'otc', 'dep_br_name': '4.0.15',
                                                       'dep_pipeline_id': 'otc_4.0.15', 'dep_app_group': 'otc'}}

    unique_data = list(set(tuple(item.items()) for item in data))
    result = [dict(item) for item in unique_data]

    print(result)
