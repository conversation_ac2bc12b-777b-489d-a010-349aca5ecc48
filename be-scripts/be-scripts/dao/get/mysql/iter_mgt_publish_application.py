#
from dao.connect.mysql import DBConnectionManager
from dao.get.mysql import db_mgt_bind_view, db_mgt_info
from settings import logger


def get_publish_application_count(iteration_id, app_name, env):
    """获取迭代中应用的产线申请记录次数

    :param iteration_id: string.迭代版本号
    :return:
    """
    with DBConnectionManager() as db:
        # need_online = 0 或者 need_ops = 0 时，都不需要校验是否有上线申请记录
        db.cur.execute(
            "select t.need_online, t.need_ops from app_mgt_app_module t where t.module_name = '{}'".format(app_name))
        execute_result = db.cur.fetchall()[0]
        if execute_result.get("need_online") == 0 or execute_result.get("need_ops") == 0:
            return {'apply_count': 1}
        db.cur.execute(
            "select count(1) as apply_count from iter_mgt_publish_application where pipeline_id ='{}' and appName like '%{}%' and env='{}'".format(
                iteration_id, app_name, env))
    return db.cur.fetchall()[0]


def get_publish_application_id(iteration_id, env):
    """获取迭代中应用的产线申请id
    :param iteration_id: string.迭代版本号
    :return:
    """
    sql = '''
    select id from iter_mgt_publish_application where pipeline_id ='{}' and env='{}' order by id desc 
    '''.format(iteration_id, env)
    with DBConnectionManager() as db:
        logger.info(sql)
        db.cur.execute(sql)
    return db.cur.fetchall()[0]


def get_publish_check_result(application_id):
    """获取迭代中应用的产线申请id
    :param application_id: long.申请记录id
    :return:
    """
    sql = '''
    SELECT check_result FROM iter_mgt_publish_application_extra_info WHERE application_id = '{}' '''.format(application_id)
    with DBConnectionManager() as db:
        logger.info(sql)
        db.cur.execute(sql)
    return db.cur.fetchone()


def get_publish_sql_info(iteration_id, app_names):
    sql_group_list = []
    sql_file_info = []
    sql = '''
    SELECT module_name,sql_ver_name,sql_ver_group,sql_file_path FROM `db_mgt_sql`
     WHERE module_name in ({}) AND iteration_id = "{}"
    '''.format(app_names, iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for item in db.cur.fetchall():
            if item['sql_ver_group'] not in sql_group_list:
                sql_group_list.append(item['sql_ver_group'])
            sql_file_info.append({'module_name': item['module_name'], 'sql_name': item['sql_ver_name'],
                                  'sql_file_path': item['sql_file_path']})
    return sql_group_list, sql_file_info


def get_publish_sql_info_by_sql_name(sql_names, iteration_id):
    sql_group_list = []
    sql_file_info = []
    sql = '''
    SELECT module_name,sql_ver_name,sql_ver_group,sql_file_path, sql_ver_db FROM `db_mgt_sql`
     WHERE sql_ver_name in ({}) and iteration_id = '{}'
     ORDER BY sql_file_path
    '''.format(sql_names, iteration_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for item in db.cur.fetchall():
            if item['sql_ver_group'] not in sql_group_list:
                sql_group_list.append(item['sql_ver_group'])
            sql_file_info.append({'module_name': item.get('module_name'), 'sql_name': item.get('sql_ver_name'),
                                  'sql_file_path': item.get('sql_file_path'), 'db_name': item.get("sql_ver_db"),
                                  'sql_ver_group': item.get("sql_ver_group")})
    return sql_group_list, sql_file_info


def get_app_publish_suite_code_list(sql_names, iteration_id):
    sql = '''
            SELECT DISTINCT ms.module_name, s.suite_code FROM `db_mgt_sql` ms
            LEFT JOIN env_mgt_node_bind b ON ms.module_name = b.module_name
            LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
            LEFT JOIN env_mgt_region r ON s.region_id = r.id
            WHERE sql_ver_name IN ({}) AND iteration_id = '{}'
            AND r.region_group = 'prod';
        '''.format(sql_names, iteration_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_archery_info_list(module_suite_list):
    logger.info("module_suite_list={}".format(module_suite_list))
    if len(module_suite_list) == 1:
        module_suite_tuple = '(("' + module_suite_list[0]['module_name'] + '", "' + module_suite_list[0][
            'suite_code'] + '"))'
    else:
        module_suite_tuple = tuple((d['module_name'], d['suite_code']) for d in module_suite_list)
    logger.info(module_suite_tuple)
    logger.info(''' and (vv.app_module_name, vv.suite_code) IN {}'''.format(module_suite_tuple))
    db_info_result = db_mgt_bind_view.get(module_suite_tuple=module_suite_tuple)
    archery_info_list = db_mgt_info.get_archery_info_by_db_info(db_info_result)
    return archery_info_list


def get_iteration_app_db_bind_info(iteration_id):
    sql = '''
            SELECT distinct b.vcs_path_id FROM iter_mgt_iter_info t
            LEFT JOIN iter_mgt_iter_app_info ai ON t.pipeline_id = ai.pipeline_id
            LEFT JOIN app_mgt_app_module m ON ai.appName = m.module_name
            LEFT JOIN db_mgt_app_bind b ON m.module_name = b.app_module_name
            WHERE t.pipeline_id = '{}';
          '''.format(iteration_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    vcs_path_id_list = []
    for item in db.cur.fetchall():
        if item.get("vcs_path_id"):
            vcs_path_id_list.append(item.get("vcs_path_id"))
    return vcs_path_id_list


def get_logic_db_name_by_iteration_id(iteration_id, app_name_list):
    sql = '''
            SELECT DISTINCT li.logic_db_name FROM iter_mgt_iter_app_info i 
            INNER JOIN app_mgt_app_module m ON i.appName = m.module_name
            INNER JOIN db_mgt_app_bind b ON m.module_name = b.app_module_name AND b.read_or_write = 1
            INNER JOIN db_mgt_logic_info li ON b.db_domain_id = li.db_domain_id
            WHERE m.need_online = 1 AND m.need_check = 1
            AND i.pipeline_id = '{}' AND m.module_name IN ('{}');
          '''.format(iteration_id, "','".join(app_name_list))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    logic_db_name_list = []
    for item in db.cur.fetchall():
        logic_db_name_list.append(item.get("logic_db_name"))
    return logic_db_name_list


if __name__ == '__main__':
    # module_suite_list = [{'module_name': 'product-center-remote', 'suite_code': 'prod'}, {'module_name': 'product-center-remote', 'suite_code': 'bs-zb'}]
    # # aaa = get_publish_application_count('member-client_5.8.1', 'center-client', 'prod')
    # get_archery_info_list(module_suite_list)
    aa = get_iteration_app_db_bind_info('prod-info_1.0.0')
    print("==={}".format(aa))
    if aa:
        print("111")
    else:
        print("222")
