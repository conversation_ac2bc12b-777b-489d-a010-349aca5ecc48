from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_member_list(project_group):
    """获取gitlab人员

    :param project_group: string.迭代版本号
    :return: list. user_list list
    """
    with DBConnectionManager() as db:
        db.cur.execute('SELECT username FROM user_gitlab_members WHERE git_group_name="{}" '
                       'AND permission>=30;'.format(project_group))
        user_list = []
        for row in db.cur.fetchall():
            user_list.append(row["username"])
    return user_list

