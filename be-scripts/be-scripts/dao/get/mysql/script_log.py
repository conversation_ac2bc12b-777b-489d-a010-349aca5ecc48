import sys
import datetime
from dao.connect.mysql import DBConnectionManager


def get_args(sid):
    """
    通过sid 获得脚本执行的参数
    :param sid: 执行任务的主键
    :return: 执行的参数
    """
    tmp_args = {}
    if sid:
        with DBConnectionManager() as db:
            db.cur.execute("select exec_parameter from public_be_script_main where sid='{}'".format(sid))
            tmp_args = db.cur.fetchone()
    return tmp_args["exec_parameter"]


if __name__ == '__main__':
    # get_args('1') # 等待表字段完善，有测试数据后，可测
    pass
