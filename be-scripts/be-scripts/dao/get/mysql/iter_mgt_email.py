#
from dao.connect.mysql import DBConnectionManager


def get_mail_list(business_id):
    """获取迭代邮件列表

    :param business_id: string.迭代版本号
    :return: mail_list
    """
    with DBConnectionManager() as db:
        db.cur.execute('''
                          SELECT email FROM iter_mgt_publish_application_cc WHERE businessID="{}"
                       '''.format(business_id))
        mail_list = []
        for row in db.cur.fetchall():
            mail_list.append(row["email"])
    return mail_list
