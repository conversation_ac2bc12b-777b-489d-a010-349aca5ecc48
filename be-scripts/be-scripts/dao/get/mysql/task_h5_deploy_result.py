from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_publish_status(iteration_id, app_name, op_type):
    """查询对应环境最后一次产物是否发布

    :param iteration_id: string.迭代版本号
    :param app_name: string.应用名
    :param op_type: string.操作类型
    :return: list. repos list
    """
    with DBConnectionManager() as db:
        str_sql = "SELECT app_name,`status` FROM task_mgt_deploy_result WHERE iteration_id = '{}' " \
                  "AND app_name = '{}' AND op_type = '{}' AND action_id = " \
                  "(SELECT action_id FROM task_mgt_deploy_result WHERE iteration_id = '{}' " \
                  "AND app_name = '{}' AND op_type = '{}' GROUP BY action_id desc LIMIT 1)" \
                  "ORDER BY op_time desc".format(iteration_id, app_name, op_type, iteration_id, app_name, op_type)
        logger.info(str_sql)
        db.cur.execute(str_sql)
    return db.cur.fetchall()


# if __name__ == "__main__":
#     result = get_publish_status('tms_9.9.8', 'simuhelp1', 'pro_publish')
#     if not result:
#         print(111)
#     print(result)
