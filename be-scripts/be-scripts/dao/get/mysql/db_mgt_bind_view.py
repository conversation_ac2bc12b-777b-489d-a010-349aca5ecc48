from sqlalchemy import text

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from settings import logger


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]

def get(module_name=None, suite_code=None, db_group_name=None, db_srv_type=None, db_name=None, db_info_id=None,
        module_name_list=None, module_suite_tuple=None, module_name_str=None, suite_db_name=None):
    """
        因增加domain，需优化 已完成优化 2020-12-15
        因重构，二次优化 20240606
    """
    """
    :param module_name:
    :param suite_code:
    :param db_group_name:
    :param db_srv_type:
    :param db_name:
    :param db_info_id:
    :param module_name_list:
    :param module_suite_tuple:
    :return:
    """
    sql = '''
            SELECT DISTINCT vv.app_module_name,
                   vv.suite_code,
                   vv.db_group_name,
                   vv.db_group_cn_name,
                   vv.db_srv_id,
                   vv.db_srv_type,
                   vv.db_srv_hosts,
                   vv.db_srv_port,
                   vv.db_srv_name,
                   vv.db_alias,
                   vv.db_name,
                   vv.suite_db_name,
                   vv.username,
                   vv.password,
                   vv.conn_url,
                   vv.db_vcs_type,
                   vv.vcs_url,
                   vv.db_srv_username,
                   vv.db_srv_password,
                   vv.db_srv_bash_profile,
                   vv.db_srv_socket_path,
                   vv.data_dump_dir,
                   vv.db_domain_id,
                   vv.db_info_id,
                   vv.db_table_space,
                   vv.db_logic_info_id
            FROM (SELECT app_bind.app_module_name,
                       suite_bind.db_bind_suite_code AS suite_code,
                       db_group.db_group_name,
                       db_group.db_group_cn_name,
                       db_srv.id AS db_srv_id,
                       db_srv.db_srv_type,
                       db_srv.db_srv_hosts,
                       db_srv.db_srv_port,
                       db_srv.db_srv_name,
                       CASE
                           WHEN domain.alias_display = 'APP_NAME' THEN app_bind.app_module_name
                           WHEN domain.alias_display = 'ALIAS' THEN domain.db_alias
                           ELSE
                               CONCAT('数据库别名显示配置类型错误：', domain.alias_display)
                           END AS db_alias,
                       logic_info.logic_db_name AS db_name,
                       db_info.suite_db_name,
                       db_info.db_info_username AS username,
                       db_info.db_info_password AS PASSWORD,
                       CASE
                           WHEN db_srv.db_srv_type = 'oracle' THEN
                               IFNULL(db_srv.db_srv_url, CONCAT('jdbc:oracle:thin:@', db_srv.db_srv_hosts, ':', db_srv.db_srv_port, ':', db_srv.db_srv_name))
                           WHEN db_srv.db_srv_type = 'mysql' THEN
                               CONCAT('jdbc:mysql://', db_srv.db_srv_hosts, ':', db_srv.db_srv_port, '/', db_info.suite_db_name, '?', db_info.db_info_conn_param)
                           WHEN db_srv.db_srv_type = 'postgresql' THEN
                               CONCAT('jdbc:postgresql://', db_srv.db_srv_hosts, ':', db_srv.db_srv_port, '/', db_info.suite_db_name, '?', db_info.db_info_conn_param)
                           ELSE
                               CONCAT('不支持的数据库类型「连接串」：', db_srv.db_srv_type)
                           END AS conn_url,
                       vcs_info.db_vcs_type,
                       CASE
                           WHEN vcs_info.db_vcs_type = 'svn' THEN
                               CONCAT(vcs_info.db_vcs_protocol, '://', vcs_info.db_vcs_domain, '/',vcs_info.db_vcs_context, vcs_path.vcs_root_path, '/', vcs_path.vcs_proj_name)
                           WHEN vcs_info.db_vcs_type = 'git' THEN
                               CONCAT(IFNULL(vcs_path.vcs_path_username, vcs_info.db_vcs_username), '@', vcs_info.db_vcs_domain, ':', vcs_path.vcs_root_path, '/', vcs_path.vcs_proj_name, '.git')
                           WHEN vcs_info.db_vcs_type = 'gitlab' THEN
                               CONCAT(vcs_info.db_vcs_protocol, '@', vcs_info.db_vcs_domain, ':', vcs_path.vcs_proj_name)
                           ELSE
                               CONCAT('不支持的vcs类型：', vcs_info.db_vcs_type)
                           END AS vcs_url,
                       db_srv.db_srv_username,
                       db_srv.db_srv_password,
                       db_srv.db_srv_bash_profile,
                       db_srv.db_srv_socket_path,
                       db_srv.data_dump_dir,
                       domain.id AS db_domain_id,
                       db_info.id AS db_info_id,
                       db_info.db_table_space,
                       logic_info.id AS db_logic_info_id
                FROM db_mgt_app_bind app_bind
                LEFT JOIN db_mgt_domain domain ON domain.id = app_bind.db_domain_id         -- 1、库信息（主要关联）：领域->逻辑库->环境->物理库->服务器
                LEFT JOIN db_mgt_logic_info logic_info ON logic_info.db_domain_id = app_bind.db_domain_id
                LEFT JOIN db_mgt_suite_bind suite_bind ON suite_bind.db_logic_id = logic_info.id    -- 新建「逻辑表」
                LEFT JOIN db_mgt_info db_info ON db_info.id = suite_bind.db_info_id
                LEFT JOIN db_mgt_srv db_srv ON db_srv.id = db_info.db_srv_id
                LEFT JOIN db_mgt_group db_group ON db_group.id = domain.db_group_id             -- 2、组信息
                LEFT JOIN db_mgt_vcs_path vcs_path ON vcs_path.id = app_bind.vcs_path_id        -- 3、vcs仓库信息
                LEFT JOIN db_mgt_vcs_info vcs_info ON vcs_info.id = vcs_path.vcs_info_id
                where app_bind.db_app_bind_is_active = 1
                ORDER BY app_bind.app_module_name, suite_bind.db_bind_suite_code, logic_info.logic_db_name) vv
                WHERE 1=1 
    '''
    if module_name:
        sql += ''' and vv.app_module_name = '{}' '''.format(module_name)
    if suite_code:
        sql += ''' and vv.suite_code = '{}' '''.format(suite_code)
    if db_group_name:
        sql += ''' and vv.db_group_name = '{}' '''.format(db_group_name)
    if db_srv_type:
        sql += ''' and vv.db_srv_type = '{}' '''.format(db_srv_type)
    if db_name:
        sql += ''' and vv.db_name = '{}' '''.format(db_name)
    if db_info_id:
        sql += ''' and vv.db_info_id = '{}' '''.format(db_info_id)
    if suite_db_name:
        sql += ''' and vv.suite_db_name = '{}' '''.format(suite_db_name)
    if module_name_list:
        sql += ''' and vv.app_module_name in ('{}')'''.format("', '".join(module_name_list))
    if module_suite_tuple:
        sql += ''' and (vv.app_module_name, vv.suite_code) IN {}'''.format(module_suite_tuple)
    if module_name_str:
        sql += ''' and vv.app_module_name in ({})'''.format(module_name_str)
    logger.info(sql)
    results = list()
    columns = ["app_module_name", "suite_code", "db_group_name", "db_group_cn_name", "db_srv_id",
               "db_srv_type", "db_srv_hosts", "db_srv_port", "db_srv_name", "db_alias",
               "db_name", "suite_db_name", "username", "password", "conn_url",
               "db_vcs_type", "vcs_url", "db_srv_username", "db_srv_password",
               "db_srv_bash_profile", "db_srv_socket_path", "data_dump_dir", "db_domain_id",
               "db_info_id", "db_table_space", "db_logic_info_id"]
    with DBConnectionManagerForSqlalchemy() as db:
        db_result_list = db.session.execute(text(sql)).fetchall()
        for result_item in db_result_list:
            results.append(dict(zip(columns, result_item)))

    return results


if __name__ == "__main__":

    result = get(module_name_list=['acc-center-server', 'ftx-online-web'], suite_code='it10')
    for row in result:
        print(row)
