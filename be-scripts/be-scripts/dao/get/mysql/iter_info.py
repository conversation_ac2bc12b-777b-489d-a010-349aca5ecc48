import datetime
import logging

from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_iter_relations(iteration_id):
    """获取迭代依赖的 公共仓库

    :param iteration_id: string.迭代版本号
    :param common_group_list: list. 公共的gitlab组   已经去除，落入数据库中 iterative_check_group_message
    :return:
    """
    with DBConnectionManager() as db:
        str_sql = 'SELECT DISTINCT t.gitCodePath,m.dep_pipeline_id,m.dep_br_name ' \
                  'FROM iterative_pipeline_relations m, spider_app_view t ' \
                  'WHERE ' \
                  't.appName = m.dep_app ' \
                  'AND m.cur_pipeline_id = "{}" ' \
                  'AND m.app_pipeline_id = "{}" ' \
                  'AND m.dep_app_group IN ( SELECT git_group FROM iterative_check_group_message) ' \
            .format(iteration_id, iteration_id)
        logger.info('查询公共组sql:' + str_sql)
        # db.cur.execute('SELECT DISTINCT t.gitCodePath,m.dep_pipeline_id,m.dep_br_name FROM spider_app_view t '
        #                'RIGHT JOIN  (SELECT dep_pipeline_id,dep_app,dep_br_name FROM iterative_pipeline_relations '
        #                'WHERE cur_pipeline_id = "{}" AND app_pipeline_id="{}" AND dep_app_group '
        #                'IN (SELECT git_group FROM iterative_check_group_message)) m ON t.appName = m.dep_app'
        #                .format(iteration_id, iteration_id))
        db.cur.execute(str_sql)
    return db.cur.fetchall()


def get_mobile_iter_relations(iteration_id, app_name_list=None):
    """获取移动端迭代依赖的 公共仓库

    :param iteration_id: string.迭代版本号
    :param app_name_list: list. 应用列表
    :return:
    """
    with DBConnectionManager() as db:
        str_sql = '''
                    SELECT CONCAT(ai.git_url, ai.git_path) AS gitCodePath,
                           cr.dep_pipeline_id, i.br_name AS dep_br_name,
                           cr.component_app_name 
                    FROM `iterative_app_component_relations` cr 
                    LEFT JOIN  app_mgt_app_module m ON cr.component_app_name = m.module_name
                    LEFT JOIN app_mgt_app_info ai ON m.app_id = ai.id 
                    LEFT JOIN  iter_mgt_iter_info i ON cr.dep_pipeline_id = i.pipeline_id
                    WHERE cr.pipeline_id = "{}"'''.format(iteration_id)
        if app_name_list:
            str_sql += ' AND cr.app_name in ("{}")'.format('","'.join(app_name_list))
        logger.info('查询依赖sql:' + str_sql)
        db.cur.execute(str_sql)
    return db.cur.fetchall()


def get_iter_repos_app(iteration_id, repos_path):
    """获取迭代内 仓库的 上线应用

    :param iteration_id: string.迭代版本号
    :param repos_path: list
    :return:
    """
    sql = '''
     SELECT  t.appName  FROM iter_mgt_iter_app_info t
  left JOIN app_mgt_app_module f ON f.module_name=t.appName left join app_mgt_app_build z on f.module_name=z.module_name
  left join app_mgt_app_info s on f.app_id =s.id 
  left JOIN auth_user h ON t.proposer=h.username WHERE t.pipeline_id="{}" and CONCAT(s.git_url,s.git_path) ="{}"
   and f.need_online = 1
'''.format(iteration_id, repos_path)
    logger.info(sql)
    app_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        app_list.append(row["appName"])
    return app_list


def get_iter_dep_repos_without_online_app(job_name):
    """
    获取迭代内依赖的外部没有任何上线应用的仓库
    测试时可将job_name改成'tms-gongmu_3.4.30.1_robot-order-center-remote'，对应代码库*************************:tms-public/tms-common.git
    @param job_name:
    @return:
    """

    sql = '''
select distinct t.module_name, t.pom_path, t.app_id, t.jdk_version, t.pipeline_id from
(
-- =========计算出要deploy的内容和路径=========
select t.*, ifnull(t.build_jdk_version, t.app_jdk_version) as jdk_version,
@n := t.module_name, 
case when @n = t.module_name then @s := @s + 1 else @s := 0 end as ser
from
(
select distinct
am.module_name, am.app_id, iai.pipeline_id,
iai.pom_path, length(ifnull(iai.pom_path, '')) as lens, 
ab.build_jdk_version, 
ai.app_jdk_version
from
app_mgt_app_module am, 
(
-- *********计算出这些仓库中有上线应用的*********
select sum(need_online) as need_online, dep_repos_app_id, dep_pipeline_id from
(
-- =========找到全部依赖的外部仓库的module=========
SELECT
	distinct am_dep.module_name as dep_module, 
	am_dep_repos.module_name as dep_repos_module_name, am_dep_repos.need_online, am_dep_repos.app_id as dep_repos_app_id, 
	pr.dep_pipeline_id, concat(pr.cur_pipeline_id, '_', pr.appName)
FROM
	iterative_pipeline_relations pr_main, 
	app_mgt_app_module am_dep, 
	iterative_pipeline_relations pr, 
	app_mgt_app_module am_cur,
	app_mgt_app_module am_dep_repos
where am_dep.module_name = pr.dep_app
and am_cur.module_name = pr.appName
and am_dep.app_id <> am_cur.app_id
and am_dep.app_id = am_dep_repos.app_id
and concat(pr_main.cur_pipeline_id, '_', pr_main.appName) = '{}'
and pr.cur_pipeline_id = pr_main.cur_pipeline_id
-- *********找到全部依赖外部仓库module所在的仓库*********
) a
group by dep_repos_app_id, dep_pipeline_id
-- *********计算出这些仓库中有上线应用的*********
) b,
iter_mgt_iter_app_info iai,
 (select @s := 0, @n := null) t, 
app_mgt_app_build ab,
app_mgt_app_info ai
where am.app_id = b.dep_repos_app_id and b.need_online = 0 
and iai.pipeline_id = b.dep_pipeline_id  and iai.appName = am.module_name
and am.module_name = ab.module_name
and am.app_id = ai.id
order by am.app_id, length(ifnull(iai.pom_path, ''))
) t
-- *********计算出要deploy的内容和路径*********
) t
where ser = 1
order by app_id
'''.format(job_name)
    logger.info(sql)
    app_compile_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_need_jacoco_test(app_name):
    sql = '''
        select need_jacoco_test from test_mgt_app_test where module_name = "{}"
        '''.format(app_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["need_jacoco_test"]


def get_app_git_path(app_name):
    sql = '''
            select ai.git_path from app_mgt_app_module am 
            left join app_mgt_app_info ai on am.app_id = ai.id
            where am.module_name = "{}";
        '''.format(app_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["git_path"]


def get_app_branch_info(days=0):
    sql1 = ''' select v.team_alias, v.new_group, v.app_name, GROUP_CONCAT(v.br_name) AS br_name from
                (
                SELECT DISTINCT
                    m.module_name AS app_name,
                    IFNULL( p.team_alias, 'no_team' ) AS team_alias,
                    CONCAT( IFNULL( p.team_alias, 'no_team' ), '-', app.git_url ) AS new_group,
                     imii.br_name as br_name
                FROM
                    app_mgt_app_module m
                    INNER JOIN app_mgt_app_info app ON m.app_id = app.id
                    INNER JOIN tool_mgt_git_url gu ON app.git_url = gu.git_url 
                    LEFT JOIN team_mgt_app_bind t ON m.app_id = t.app_id
                    LEFT JOIN tool_mgt_team_info ti ON t.team_id = ti.id
                    LEFT JOIN tool_mgt_team_info p ON p.id = ti.parent_id 
                    INNER JOIN iter_mgt_iter_app_info imiai on imiai.appName = m.module_name
                    INNER JOIN iter_mgt_iter_info imii on imiai.pipeline_id = imii.pipeline_id
                    where m.need_online = 1 and m.need_check = 1 and app.third_party_middleware <> 1 
            '''
    sql3 = ''' ) v
            GROUP BY v.team_alias, v.new_group, v.app_name                     
            ORDER BY
                team_alias,
                new_group,
                app_name
         '''
    sql = sql1 + sql3
    if days > 0:
        timeline = datetime.datetime.now() - datetime.timedelta(days=days)
        sql2 = ''' and ( imii.br_start_date > "{}" or imiai.create_date > "{}" ) '''.format(
            timeline.strftime("%Y-%m-%d %H:%M:%S"), timeline.strftime("%Y-%m-%d %H:%M:%S"))
        sql = sql1 + sql2 + sql3

    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_env_info():
    sql = '''
            select s.id, r.region_group as envCode, s.suite_name as envName, s.suite_code as tenantId,  r.addr_short_name as roomCode from  env_mgt_suite s
            left join env_mgt_region r on s.region_id = r.id
            where s.suite_is_active = 1;
        '''
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchall()


def get_latest_archive_iteration_version(app_name):
    sql = '''
        SELECT
            m.pipeline_id as last_arc_iteration_id,
            i.appName 
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
            GROUP BY
                i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            and i.appName = '{}';
        '''.format(app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
        if result_list:
            return result_list[0]
        else:
            return {'last_arc_iteration_id': '', 'appName': ''}


def get_module_name_ops_info(app_name):
    sql = "select t.need_online from app_mgt_app_module t where t.module_name = '{}'".format(app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    if result_list:
        return result_list[0]
    else:
        return {'need_online': ''}


def get_bis_app_name_and_archive_br(bis_code):
    sql = '''
            SELECT b.app_module_name, c.archive_br as archive_br_name FROM app_br_cache c
            INNER JOIN biz_app_bind b ON c.app_module_name = b.app_module_name
            INNER JOIN (
                select time_batch,
                   max(batch_number) as max_batch_num
                from app_br_cache
                where time_batch = (select max(time_batch) from app_br_cache)
                )v_max ON (v_max.time_batch, v_max.max_batch_num) = (c.time_batch, c.batch_number)
            WHERE b.biz_app_bind_type IN (1, 2) AND b.biz_code = '{}' AND b.biz_app_bind_is_active = 1;
        '''.format(bis_code)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    return result_list


def get_bis_latest_archive_version(bis_code):
    sql = '''
            SELECT * FROM biz_test_iter WHERE biz_code = "{bis_code}" 
            AND br_end_time IN (SELECT MAX(br_end_time) FROM biz_test_iter  
            WHERE br_status = "close" AND biz_code = "{bis_code}")
          '''.format(bis_code=bis_code)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    if result_list:
        return result_list[0]['biz_test_iter_br']
    else:
        return ''


def get_before_current_latest_archive_version(bis_code, bis_pipeline_id):
    sql = '''
             select biz_test_iter_id from biz_test_iter 
             where biz_code = '{}' and br_status = 'close'
             and br_end_time < (
             select br_end_time from biz_test_iter where biz_test_iter_id = '{}'
             ) 
             order by br_end_time DESC LIMIT 1
          '''.format(bis_code, bis_pipeline_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    if result_list:
        return result_list[0]['biz_test_iter_id']
    else:
        return None


def get_bis_latest_archive_iter(bis_code, br_end_time=None):
    sql = '''
         SELECT * FROM biz_test_iter WHERE biz_code = "{bis_code}" 
            AND br_status = "close"
    '''.format(bis_code=bis_code)
    if br_end_time:
        sql = sql + " AND br_end_time = '{br_end_time}'".format(br_end_time=br_end_time)
    sql = sql + " ORDER BY br_end_time DESC LIMIT 1"
    logging.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    return result_list


def get_biz_test_iter(biz_test_iter_id):
    sql = '''
         SELECT * FROM biz_test_iter WHERE biz_test_iter_id = "{biz_test_iter_id}"
    '''.format(biz_test_iter_id=biz_test_iter_id)
    logging.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    return result_list


def get_dev_pipeline_id_info(pipeline_id):
    sql = '''
             SELECT pipeline_id, br_end_date, br_status FROM iter_mgt_iter_info WHERE pipeline_id = "{pipeline_id}"
        '''.format(pipeline_id=pipeline_id)
    logging.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    return result_list


def get_db_group_name_and_br_name(pipeline_id_list, module_name_list):
    if not pipeline_id_list or not module_name_list:
        return None
    sql = '''
            SELECT distinct i.br_name, g.db_group_name FROM iter_mgt_iter_info i
            LEFT JOIN app_mgt_app_info ai ON i.project_group = ai.git_url
            LEFT JOIN app_mgt_app_module m ON ai.id = m.app_id
            LEFT JOIN db_mgt_app_bind ab ON m.module_name = ab.app_module_name
            LEFT JOIN db_mgt_domain dm ON ab.db_domain_id = dm.id
            LEFT JOIN db_mgt_group g ON dm.db_group_id = g.id
            WHERE i.pipeline_id IN ('{}') AND m.module_name IN ('{}');
          '''.format("','".join(pipeline_id_list), "','".join(module_name_list))
    logging.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result_list = db.cur.fetchall()
    return result_list


def get_latest_archive_br_name(app_module_name, br_end_time):
    sql = '''
            SELECT b.app_module_name,b.archive_br_name
            FROM biz_test_iter a
            JOIN biz_test_iter_app b ON a.biz_test_iter_id = b.biz_test_iter_id
            WHERE a.br_end_time < '{br_end_time}' 
            AND a.br_status = 'close' 
            AND b.app_module_name = '{app_module_name}'
            ORDER BY a.br_end_time DESC
            LIMIT 1
    '''.format(br_end_time=br_end_time, app_module_name=app_module_name)
    logging.info(sql)
    branch_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            branch_list.append(row['archive_br_name'])
    return branch_list


if __name__ == "__main__":
    # dir_name = datetime.datetime.now().strftime('%Y%m%d')
    # logger.info(dir_name)
    # l = get_latest_archive_iteration_version('ftx-console-web')
    # print(l)
    # module_list = get_latest_archive_br_name('acc-center-server', '2024-03-14 10:01:46')
    # app_info_list = get_bis_app_name_and_archive_br('TRADE-BATCH-T0')
    # for app_info in app_info_list:
    #     app_info.update({'biz_test_iter_id': '123',
    #                      'create_user': '456', 'create_time': datetime.datetime.now()})

    pipeline_id_list = ['acc_20240511', 'hk-acc_3.3.3', 'pay_2.0.16']
    module_name_list = ['acc-center-server', 'hk-acc-online-service', 'pay-online-server']
    module_list = get_db_group_name_and_br_name(pipeline_id_list, module_name_list)
    logger.info(module_list)
