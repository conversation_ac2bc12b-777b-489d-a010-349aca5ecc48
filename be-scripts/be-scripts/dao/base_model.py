from settings import DATABASES
from peewee import *

mysql_db = MySQLDatabase(DATABASES['DB'], user=DATABASES['USER'], password=DATABASES['PASSWORD'], host=DATABASES['IP'],
                         port=DATABASES['PORT'], charset= DATABASES["CHARSET"])


class BaseModel(Model):
    class Meta:
        database = mysql_db


class SpiderBaseModels(BaseModel):
    create_user = Char<PERSON><PERSON>(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = Cha<PERSON><PERSON><PERSON>(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')
