import pymysql
from settings import DATABASES, logger
import datetime


class DBConnectionManager:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
                 password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"]):
        """
        :param host: 数据库地址
        :param port: 端口
        :param user: 用户名
        :param password: 密码
        :param db: 数据库名称
        :param charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset

    def __enter__(self):
        self.connection = pymysql.connect(host=self.host, port=self.port, user=self.user,
                                          passwd=self.password, db=self.db,
                                          charset=self.charset)
        self.cur = self.connection.cursor(cursor=pymysql.cursors.DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()


class DBTransactionConnectionManager:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
                 password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"]):
        """
        :param host: 数据库地址
        :param port: 端口
        :param user: 用户名
        :param password: 密码
        :param db: 数据库名称
        :param charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset

    def __enter__(self):
        self.connection = pymysql.connect(host=self.host, port=self.port, user=self.user,
                                          passwd=self.password, db=self.db,
                                          charset=self.charset)
        self.connection.begin()
        self.cur = self.connection.cursor(cursor=pymysql.cursors.DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.commit()
        self.connection.close()


class BaseModel:

    def insert(self):
        key_str = ""
        value_str = ""
        for key, value in self.__dict__.items():
            if key_str == "":
                key_str = key
            else:
                key_str = "{},{}".format(key_str, key)

            if isinstance(value, (str, datetime.date)):
                value = '"{}"'.format(value)
            if value is None:
                value = "NULL"
            if value_str == "":
                value_str = value
            else:
                value_str = "{},{}".format(value_str, value)

        sql = 'INSERT INTO {} ({}) VALUES ({})'.format(self.Meta.db_table, key_str, value_str)
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            db.connection.commit()

    def select(self):
        pass

    class Meta:
        db_table = ""


if __name__ == "__main__":
    with DBConnectionManager() as db:
        db.cur.execute("select * from spider_app_view")
        for i in db.cur.fetchall():
            print(i)
