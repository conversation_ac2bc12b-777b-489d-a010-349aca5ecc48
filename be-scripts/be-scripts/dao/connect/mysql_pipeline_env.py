#
import os
import sys
import pymysql
from pymysql.cursors import DictCursor

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES


class PipelineEnv(object):
    def __init__(self):
        self.conn = pymysql.connect(
            host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
            password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"])

    def close_mysql(self):
        self.conn.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.conn.close()

    def delete_pipeline_env_bind(self, pipeline_id):
        cmd = """delete from pipeline_env_bind
                 where pipeline_id = '{}'""".format(pipeline_id)
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            self.conn.commit()

    def create_pipeline_env_bind(self, pipeline_id, env, operator=''):
        cmd = """insert into pipeline_env_bind
                 (`pipeline_id`, `env`, `operator`)
                 values ('{}', '{}', '{}')""".format(pipeline_id, env, operator)
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
        self.conn.commit()

    def get_env_bind_info(self, pipeline_id):
        cmd = """select env
                 from pipeline_env_bind
                 where pipeline_id = '%s' """ % pipeline_id

        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            return cursor.fetchall()

    def get_env_vm(self, env):
        cmd = """select tp
                 from env_env
                 where name = '%s' """ % env
        with self.conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(cmd)
            return cursor.fetchone()
