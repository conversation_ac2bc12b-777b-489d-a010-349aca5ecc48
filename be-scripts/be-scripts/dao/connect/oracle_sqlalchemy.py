import logging

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from settings import TEST_DATA_INIT



class DBConnectionManagerForSqlalchemyOracle:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, host, port, user,
                 password, sid):
        """
        :param host: 数据库地址
        :param port: 端口
        :param user: 用户名
        :param password: 密码
        :param db: 数据库名称
        :param charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.sid = sid

    def __enter__(self):
        engine = create_engine(
            'oracle+oracledb://{}:{}@{}:{}/{}'.format(self.user, self.password, self.host, self.port, self.sid),
            thick_mode={
                "lib_dir": TEST_DATA_INIT['oracle_driven_root']
            },
            echo=False)
        db_session = sessionmaker(bind=engine)
        # 创建session对象
        self.session = db_session()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()
        logging.info("session close")


if __name__ == '__main__':

    sql = '''select ID,FILE_TYPE,STRATEGY_SCRIPT,REC_STAT from BP_FILE_STRATEGY_CFG'''
    with DBConnectionManagerForSqlalchemyOracle("***************", 1521,
                                                "DOCKER_IT300_LCTZT", "howbuy2015", "trade") as db:

        for db_info in db.session.execute(text(sql)).fetchall():
            template = """DECLARE
REALLYBIGTEXTSTRING{ID} CLOB :=
'{STRATEGY_SCRIPT}';
BEGIN
delete from bp_file_strategy_cfg x where x.id = {ID};
insert into bp_file_strategy_cfg x values ({ID}, '{FILE_TYPE}','1', REALLYBIGTEXTSTRING{ID}, '','{REC_STAT}',sysdate,sysdate);
end;""".format(ID=db_info[0], STRATEGY_SCRIPT=db_info[2], FILE_TYPE=db_info[1], REC_STAT=db_info[3])
            lines = [template]
            with open(
                    "/Users/<USER>/devops-sql/tenpay/basic-features-sql-lctzt-oracle-ds/lctzt/DML/BP_FILE_STRATEGY_CFG_{}.sql".format(
                        db_info[0]),
                    'w',
                    encoding='utf-8', ) as f:
                f.writelines(lines)
