import cx_Oracle


class OracleConnectionManager:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, user, password, ip, sid, encoding="UTF-8"):
        """
        :param db_user: 用户名
        :param password: 密码
        :param ip: 数据库ip
        :param sid: 数据库服务名
        """
        self.user = user
        self.password = password
        self.ip = ip
        self.sid = sid
        self.encoding = encoding

    def __enter__(self):
        server = '/'.join([self.ip, self.sid])
        self.connection = cx_Oracle.connect(self.user, self.password, server)
        self.cur = self.connection.cursor()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cur.close()
        self.connection.close()


if __name__ == "__main__":
    with OracleConnectionManager("OTC", "howbuy2015", "***************","ptrade") as db:
        result = db.cur.execute('''select 1 from dual''')
        for i in result:
            print(i)
