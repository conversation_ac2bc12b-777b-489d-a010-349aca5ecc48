import logging

from sqlalchemy import create_engine
from sqlalchemy.engine import URL
from sqlalchemy.orm import sessionmaker, scoped_session

from dao.sqlarchemy.database_pool import Base
from settings import DATABASES


class SustainablePool:
    """
    通过工厂模式创建数据库连接
    """

    def __init__(self, host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
                 password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"], ehco=False,
                 pool_size=1):
        """
        :param host: 数据库地址
        :param port: 端口
        :param user: 用户名
        :param password: 密码
        :param db: 数据库名称
        :param charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset
        self.ehco = ehco
        url_object = URL.create(
            "mysql+mysqlconnector",
            username=self.user,
            password=self.password,
            host=self.host,
            database=self.db,
        )
        self.engine = create_engine(url_object, echo=self.ehco, pool_size=pool_size, pool_timeout=60, pool_recycle=60)

    def get_db_session(self):
        db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=self.engine))
        return db_session


class DBConnectionManagerForSqlalchemy:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
                 password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"], ehco=False):
        """
        :param host: 数据库地址
        :param port: 端口
        :param user: 用户名
        :param password: 密码
        :param db: 数据库名称
        :param charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset
        self.ehco = ehco

    def __enter__(self):
        url_object = URL.create(
            "mysql+mysqlconnector",
            username=self.user,
            password=self.password,
            host=self.host,
            database=self.db,
        )
        engine = create_engine(url_object, echo=self.ehco)
        db_session = sessionmaker(bind=engine)
        # 创建session对象
        self.session = db_session()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()
