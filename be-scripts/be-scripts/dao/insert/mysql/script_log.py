import sys
import datetime
from dao.connect.mysql import DBConnectionManager


def insert_minor_start_log(sid, step, start_at):
    """
    :param sid: 执行任务的主键
    :param step: 步骤名
    :param start_at: 开始时间
    :return:
    """
    if sid and step:
        with DBConnectionManager() as db:
            db.cur.execute(
                "INSERT INTO public_be_script_minor(sid, step, status, start_at, log, end_at) \
                 VALUES ({}, '{}', '{}', '{}', '', NULL);".format(sid, step, 'running', start_at))
            db.connection.commit()


if __name__ == '__main__':
    pass
