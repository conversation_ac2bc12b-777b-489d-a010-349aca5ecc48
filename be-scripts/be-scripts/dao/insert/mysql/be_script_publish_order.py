import sys
import datetime

from dao.common_model.iter_mgt_publish_application_model import IterMgtPublishApplication
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy


def insert(
        team,
        appName,
        br_name,
        applicant,
        pipeline_id,
        env,
        git_last_version,
        git_repo_version,
        status,
        pipeline_description=None,
        sql_address=None,
        description=None,
):
    """创建应用发布申请记录"""
    iterMgtPublishApplication = IterMgtPublishApplication(
        team=team,
        appName=appName,
        br_name=br_name,
        applicant=applicant,
        apply_at=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        pipeline_description=pipeline_description,
        sql_address=sql_address,
        description=description,
        pipeline_id=pipeline_id,
        env=env,
        git_last_version=git_last_version,
        git_repo_version=git_repo_version,
        status=status
    )

    with DBConnectionManagerForSqlalchemy() as db:
        db.session.add(iterMgtPublishApplication)
        db.session.commit()


if __name__ == '__main__':
    insert("test_team", "test_app", "test_br", "scm", "test_pipeline_id", "prod", "test_git_last_version",
           "test_git_repo_version", "上线中", None, "alter table sxy_simu_user_study_records add max_play_time int null comment '最大播放时间'", None)
