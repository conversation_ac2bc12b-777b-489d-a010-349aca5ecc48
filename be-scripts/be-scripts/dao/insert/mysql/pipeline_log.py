import sys
import datetime
from dao.connect.mysql import DBConnectionManager
from settings import logger


def insert_pipeline_log_main(*args, **kwargs):
    """

    :param args:
    :param kwargs:
    :return:
    """
    try:
        with DBConnectionManager() as db:
            db.cur.execute("INSERT INTO pipeline_log_main(exec_jenkins, exec_parameter, start_at, "
                           "iteration_id, app_name, status, suite_name)  "
                           "VALUES ('{}', '{}', '{}', '{}', '{}','{}','{}');".format(kwargs['exec_jenkins'],
                                                                                     kwargs['exec_parameter'],
                                                                                     kwargs['start_at'],
                                                                                     kwargs['iteration_id'],
                                                                                     kwargs['app_name'],
                                                                                     kwargs['status'],
                                                                                     kwargs['suite_name'],
                                                                                     ))
            row_id = db.cur.lastrowid
            db.connection.commit()
            return row_id

    except Exception as e:
        logger.error(str(e))


def insert_pipeline_log_minor(sid, step, start_at, module_name):
    """
    插入流水线子日志
    :param sid:
    :param step:
    :param start_at:
    :param module_name:
    :return:
    """
    if sid and step:
        with DBConnectionManager() as db:
            db.cur.execute(
                "INSERT INTO pipeline_log_minor(sid, step, status, start_at, module_name) \
                 VALUES ({}, '{}', '{}', '{}', '{}');".format(sid, step, 'running', start_at, module_name))
            db.connection.commit()


if __name__ == '__main__':

    insert_pipeline_log_main(exec_jenkins="", exec_parameter="", start_at=datetime.datetime.now(),
                             iteration_id="", app_name="", status="", suite_name="")