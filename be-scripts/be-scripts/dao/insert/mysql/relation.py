#
from dao.connect.mysql import DBConnectionManager


def insert_iterative_pipeline_relations(cur_pipleline_id, app_name, app_br_name, app_pipeline_id, app_group,
                                        dep_app, dep_br_name, dep_pipeline_id, dep_app_group, iter_app_name):
    """插入应用依赖关系"""
    with DBConnectionManager() as db:
        db.cur.execute(
            '''INSERT INTO iterative_pipeline_relations (cur_pipeline_id, appName, app_br_name,
                   app_pipeline_id, app_group, dep_app, dep_br_name, dep_pipeline_id, dep_app_group,iter_app_name)
               VALUES("{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}","{}");'''.format(
                cur_pipleline_id, app_name, app_br_name,
                app_pipeline_id, app_group, dep_app, dep_br_name, dep_pipeline_id, dep_app_group, iter_app_name)
        )
        db.connection.commit()
