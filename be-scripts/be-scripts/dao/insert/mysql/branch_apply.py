from dao.connect.mysql import DBConnectionManager
import time

from settings import logger


def insert_branches(pipeline_id, br_name, project_group, br_style, duedate, description, tapd_id):
    br_start_date = time.strftime('%Y-%m-%d_%H:%M:%S', time.localtime(time.time()))
    with DBConnectionManager() as db:
        if tapd_id:
            db.cur.execute(
                'INSERT INTO iter_mgt_iter_info (pipeline_id,br_name,project_group,br_style,br_start_date,duedate'
                ' ,description,br_status,tapd_id,is_new) '
                'VALUES( "{}", "{}", "{}","{}", "{}", "{}", "{}" ,"open", {}, 1);'.format
                (pipeline_id, br_name, project_group, br_style, br_start_date, duedate, description, tapd_id))
        else:
            db.cur.execute(
                'INSERT INTO iter_mgt_iter_info (pipeline_id,br_name,project_group,br_style,br_start_date,duedate'
                ' ,description,br_status, is_new) VALUES( "{}", "{}", "{}","{}", "{}", "{}", "{}" ,"open", 1);'.format
                (pipeline_id, br_name, project_group, br_style, br_start_date, duedate, description))
        db.connection.commit()


def insert_branch_include_sys(pipeline_id, app_name, iter_jdk_version, pom_path, proposer):
    with DBConnectionManager() as db:
        db.cur.execute(
            'INSERT INTO iter_mgt_iter_app_info (pipeline_id,appName,pom_path,jdkVersion,sys_status,proposer ) '
            'VALUES( "{}", "{}", "{}", "{}", "{}", "{}" );'.format(pipeline_id, app_name, pom_path, iter_jdk_version,
                                                                   "测试中", proposer))
        db.connection.commit()


def insert_feature_relations(release_iteration_id, feature_iteration_id, feature_num):
    with DBConnectionManager() as db:
        db.cur.execute(
            'INSERT INTO iterative_pipeline_feature_relations (release_iteration_id,feature_iteration_id,feature_num) '
            'VALUES( "{}", "{}", {});'.format(release_iteration_id, feature_iteration_id, feature_num))
        db.connection.commit()


def get_app_jdk_version(app_name):
    sql = """
        SELECT 
        IF(( isnull( v.jdkVersion ) OR ( trim( v.jdkVersion ) = '' )), i.app_jdk_version, v.jdkVersion ) AS iter_jdk_version
        from 
        (
        select 
        IF(( isnull( b.build_jdk_version ) OR ( trim( b.build_jdk_version ) = '' )), m.module_jdk_version, b.build_jdk_version ) AS jdkVersion, 
        b.app_id
        from app_mgt_app_build b
        INNER JOIN app_mgt_app_module m on b.module_name = m.module_name
        where b.module_name = '{app_name}') v
        INNER JOIN app_mgt_app_info i on v.app_id = i.id;
          """.format(app_name=app_name)

    with DBConnectionManager() as db:
        logger.info(sql)
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row["iter_jdk_version"]
        return ""


if __name__ == '__main__':
    jdk = get_app_jdk_version("cgi-container")
    print('jdk==={}'.format(jdk))
