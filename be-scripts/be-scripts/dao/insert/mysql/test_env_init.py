import datetime
from dao.connect.mysql import DBConnectionManager


class TestEnvInit:
    def __init__(self, suite_code, app_name_list):
        self.opt_type = "INIT"
        self.job_name = "test_suite_init"
        self.operator = "scm"
        self.suite_code = suite_code
        self.app_name_list = app_name_list

    def insert_test_env_mgt_test_suite_init_log(self):
        db_param_dict = {
            'OPT_TYPE': self.opt_type,
            'JACOCO_TYPE': 0,
            'SUITE_CODE': self.suite_code,
            'APP_NAME_LIST': self.app_name_list,
            'DB_ENV': None,
            'SYS_DATETIME': None,
            'IS_CLEAR_CACHE': 1,
            'TEST_SET': None,
            'PARAM1': None,
            'PARAM2': None,
            'PARAM3': None,
        }
        meta = dict(
            ob_http_suite_code=self.suite_code,
            job_http_opt_type=self.opt_type,
            job_http_job_name=self.job_name,
            job_http_dict=db_param_dict,
            job_app_count=len(self.app_name_list),
            job_desc="接口统计后恢复",
            create_user=self.operator,
            create_time=datetime.datetime.now(),
            update_user=self.operator,
            update_time=datetime.datetime.now(),
            stamp=0,
        )

    def insert_test_suite_init_log(self):
        meta = dict(
            parent_id=job_business_id,
            job_step='http_init',
            job_app_count=job_app_count,
            job_desc='「http初始化」步骤执行成功',
            create_user=self.operator,
            create_time=datetime.datetime.now(),
            update_user=self.operator,
            update_time=datetime.datetime.now(),
            stamp=0,
        )

        # 3、组装jenkins的http参数
        app_name_str = None
        if len(self.app_name_list) > 0:
            app_name_str = ','.join(x for x in self.app_name_list)

        req_param_dict = {
            'PID': job_business_id,
            'OPT_TYPE': type_enum.name,
            'SUITE_CODE': self.suite_code,
            'APP_NAME_LIST': app_name_str,
            'BR_NAME': self.br_name,
            'JACOCO_TYPE': self.jacoco_type,
        }

        job_desc = type_enum.type_desc
        # 4、请求jenkins
        jenkins_log_id, msg = send_to_jenkins(job_name,
                                              job_business_id=job_business_id,
                                              job_http_dict=req_param_dict,
                                              job_desc=job_desc,
                                              operator=self.operator)
    def insert_jenkins_job_log(self, req_param_dict):
        req_param_dict = {
            'PID': job_business_id,
            'OPT_TYPE': type_enum.name,
            'SUITE_CODE': self.suite_code,
            'APP_NAME_LIST': app_name_str,
            'BR_NAME': self.br_name,
            'JACOCO_TYPE': self.jacoco_type,
        }

        meta = dict(
            job_business_id=job_business_id,
            job_name=self.job_name,
            job_http_dict=req_param_dict,
            job_desc= "接口统计后恢复",
            create_user=self.operator,
            create_time=datetime.datetime.now(),
            update_user=self.operator,
            update_time=datetime.datetime.now(),
            stamp=0,
        )
        obj = JenkinsJobLog.objects.create(
            job_build_id=None,
            job_business_id=job_business_id,
            job_name=job_name,
            job_http_dict=job_http_dict,
            job_param_list=None,
            job_workspace=job_workspace,
            job_bo_id=job_bo_id,
            job_bo_key=job_bo_key,
            job_bo_dict=job_bo_dict,
            job_url=jenkins_url,
            job_username=jenkins_username,
            job_password=jenkins_password,
            job_info=job_info,
            last_build_job_info=last_build_job_info,
            last_build_number=last_build_number,
            last_build_info=last_build_info,
            job_queue_item=None,
            job_duration=None,
            job_result=None,
            job_desc=job_desc,
            create_user=operator,
            create_time=cur_time_str,
            update_user=operator,
            update_time=cur_time_str,
            stamp=0,
        )
