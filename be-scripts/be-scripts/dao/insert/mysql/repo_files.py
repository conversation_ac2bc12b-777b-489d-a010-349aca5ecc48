from dao.connect.mysql import DBConnectionManager


def insert_repo_files(git_pro_id, repo_namespace, repo_path, file_name, file_path, file_type, branch):
    with DBConnectionManager() as db:
        db.cur.execute(
            "INSERT INTO app_mgt_repo_files (git_pro_id, repo_namespace, repo_path, file_name, file_path, file_type, data_date, branch_git) "
            "VALUES( '{}','{}','{}','{}','{}','{}',DATE_FORMAT(now(), '%Y-%m-%d'), '{}' )".format(git_pro_id,
                                                                                                  repo_namespace,
                                                                                                  repo_path, file_name,
                                                                                                  file_path,
                                                                                                  file_type, branch))
        db.connection.commit()


def find_latest_prod_branch(repo_path):
    with DBConnectionManager() as db:
        str_sql = """select lib_repo_branch from
(
select mpi.module_name, mpi.lib_repo_branch, mpi.create_time from product_mgt_product_info mpi,
        (
            select max(nb.lib_repo_info_id) as lib_repo_info_id
            from app_mgt_app_module am, env_mgt_node_bind nb
            where am.lib_repo = '{}' and am.module_name = nb.module_name and nb.suite_id in ('99', '78', '96')
        ) a
        where
        mpi.id = a.lib_repo_info_id

union all

SELECT
	ai.appName, ii.br_name, ai.git_repos_time
FROM app_mgt_app_module am, iter_mgt_iter_app_info ai, iter_mgt_iter_info ii
where am.lib_repo = '{}' and am.module_name = ai.appName and ai.sys_status = '已归档' and ai.pipeline_id = ii.pipeline_id
) x
order by create_time desc
limit 1""".format(repo_path, repo_path)
        db.cur.execute(str_sql)
    return db.cur.fetchone()


def find_all_batch_dates():
    with DBConnectionManager() as db:
        str_sql = "SELECT distinct data_date FROM app_mgt_repo_files order by data_date desc"
        db.cur.execute(str_sql)
    return db.cur.fetchall()


def delete_data_before_date(date):
    with DBConnectionManager() as db:
        db.cur.execute("delete FROM app_mgt_repo_files where data_date < '{}'".format(date))
        db.connection.commit()
