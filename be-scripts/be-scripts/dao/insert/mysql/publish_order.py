import sys
from settings import logger
import datetime
from dao.connect.mysql import DBConnectionManager


def insert_publish_order(app_name, branch_name):
    """创建应用发布申请记录"""
    create_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with DBConnectionManager() as db:
        db.cur.execute(
            'INSERT INTO iter_mgt_publish_application ( app_name, branch_name, create_at ) '
            'VALUES( "{}", "{}", "{}" );'.format(app_name, branch_name, create_at)
        )
        db.connection.commit()


def insert_test_publish_order(team, app_name, br_name, git_last_version, git_repo_version):
    """创建测试环境发布记录"""
    apply_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    pipeline_id = team + '_' + br_name
    with DBConnectionManager() as db:
        db.cur.execute("""
            INSERT INTO iter_mgt_publish_application
            (team, br_name, appName, apply_at, pipeline_id, git_last_version, git_repo_version, env, status)
            VALUES("{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}"); """.format(
            team, br_name, app_name, apply_at, pipeline_id, git_last_version, git_repo_version, 'test', '已完成'))
        db.connection.commit()


def insert_publish_transit_repos(transit_info_dict_list):
    """记录中转库信息"""

    with DBConnectionManager() as db:
        for row in transit_info_dict_list:
            row["update_time"] = datetime.datetime.now()
            sql = """
                INSERT INTO publish_transit_repos_info
                (module_name, suite_name, transit_ip, product_path, br_name, product_version, update_time)
                VALUES("{module_name}", "{suite_name}", "{transit_ip}", "{product_path}", "{br_name}", 
                "{product_version}", "{update_time}"); """.format(**row)
            logger.info(sql)
            db.cur.execute(sql)
        db.connection.commit()


