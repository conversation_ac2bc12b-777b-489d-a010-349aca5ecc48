from dao.connect.mysql import DBConnectionManager
import datetime
from settings import logger


def update_transit_app(transit_info_dict_list):
    with DBConnectionManager() as db:
        for row in transit_info_dict_list:
            row["update_time"] = datetime.datetime.now()
            sql = '''UPDATE publish_transit_repos_info SET transit_ip="{transit_ip}",product_path="{product_path}"
,br_name="{br_name}" ,update_time="{update_time}",product_version="{product_version}" WHERE module_name="{module_name}"
and suite_name="{suite_name}"'''.format(**row)
            logger.info(sql)
            db.cur.execute(sql)
        db.connection.commit()
