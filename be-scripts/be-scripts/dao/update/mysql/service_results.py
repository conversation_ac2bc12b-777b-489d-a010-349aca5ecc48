import logging

from ci_pipeline.ci_pipeline_models.iter_models import TaskMgtServiceResults
from dao.connect.mysql import DBConnectionManager
import datetime

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from settings import logger


def update_exec_results(id, status, msg):
    """
    更新脚本执行结果
    :param id:
    :param status:
    :param msg:
    :return:
    """
    if isinstance(msg, list):
        sql = 'UPDATE task_mgt_service_results SET STATUS="{}",detail="{}",end_at="{}" WHERE id = {}'. \
            format(status, str(msg).replace('"', "'"), datetime.datetime.now(), id)
    else:
        sql = 'UPDATE task_mgt_service_results SET STATUS="{}",detail="{}",end_at="{}" WHERE id = {}'. \
            format(status, msg.replace('"', "'"), datetime.datetime.now(), id)
    # sql = 'UPDATE task_mgt_service_results SET STATUS="{}",detail="{}",end_at="{}" WHERE id = {}'.\
    #     format(status, msg.replace('"', "'"), datetime.datetime.now(), id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def update_exec_results_no_single(id, status, msg):
    """
    更新脚本执行结果
    :param id:
    :param status:
    :param msg:
    :return:
    """
    logging.info(msg)
    with DBConnectionManagerForSqlalchemy() as db:
        db.session.query(TaskMgtServiceResults).filter(
            TaskMgtServiceResults.id == id).update(
            {"status": status, "result_json": msg, "end_at": datetime.datetime.now()})
        db.session.commit()
