from dao.connect.mysql import DBConnectionManager
from settings import logger


def update_git_last_version(pipeline_id, app_name, git_last_version):
    """更新iter_mgt_iter_app_info表git_last_version字段"""
    with DBConnectionManager() as db:
        db.cur.execute(
            'UPDATE iter_mgt_iter_app_info '
            'SET git_last_version="{}", git_last_update = now() '
            'WHERE pipeline_id="{}" AND appName="{}"; '.format(
                git_last_version, pipeline_id, app_name)
        )
        db.connection.commit()


def update_status(pipeline_id, sys_status="上线中", app_name_list=""):
    sql = "UPDATE iter_mgt_iter_app_info SET sys_status='{}'  WHERE pipeline_id = '{}'".format(sys_status, pipeline_id)
    if app_name_list:
        sql = sql + "and appName in ('{}')".format("','".join(app_name_list))
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def update_user_name(pipeline_id):
    sql = '''
            UPDATE iter_mgt_iter_app_info a,
        auth_user u 
        SET a.user_name = CONCAT( u.last_name, u.first_name ) 
        WHERE
            a.pipeline_id = "{}"
            AND a.proposer = u.username'''.format(pipeline_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def update_duplicate_info(pipeline_id):
    sql = '''
            UPDATE iter_mgt_iter_app_info a,
            app_mgt_app_build b,
            app_mgt_app_module m,
            app_mgt_app_info i 
            SET a.package_type = b.package_type,
            a.need_online = m.need_online,
            a.build_cmd = b.build_cmd,
            a.git_path = CONCAT( i.git_url, i.git_path ) 
            WHERE
                a.pipeline_id = "{}" 
                AND a.appName = b.module_name 
                AND a.appName = m.module_name 
                AND m.app_id = i.id'''.format(pipeline_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def update_merged_master(id, merged_master):
    sql = "update iter_mgt_iter_app_info set merged_master = {} where id = {}".format(merged_master, id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()