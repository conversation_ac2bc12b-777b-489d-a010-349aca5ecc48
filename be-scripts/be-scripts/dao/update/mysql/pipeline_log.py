import logging

from dao.connect.mysql import DBConnectionManager
from settings import logger


def update_pipeline_main_log(sid, status, env_suite=None, end_at=None):
    """
    :param sid: 执行任务的主键
    :param end_at: 结束时间
    :param status: 执行结果
    :return:
    """
    logging.info(
        "update_pipeline_main_log param is : sid={},status={},env_suite={},end_at={}".format(sid, status, env_suite,
                                                                                             end_at))
    # 变更状态和结束时间
    if end_at and sid and status and env_suite and len(str(env_suite).strip()) > 0:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE pipeline_log_main \
                 SET end_at='{}', status='{}',suite_name='{}' \
                 WHERE sid={};".format(end_at, status, env_suite, sid))
            db.connection.commit()
    elif end_at and sid and status:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE pipeline_log_main \
                 SET end_at='{}', status='{}' \
                 WHERE sid={};".format(end_at, status, sid))
            db.connection.commit()
    # 变更状态
    elif sid and status:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE pipeline_log_main \
                 SET status='{}' \
                 WHERE sid={};".format(status, sid))
            db.connection.commit()
    elif sid and env_suite:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE pipeline_log_main \
                 SET env_suite='{}' \
                 WHERE sid={};".format(env_suite, sid))
            db.connection.commit()


def update_pipeline_minor_log(sid, step, log, end_at, status):
    """
    :param sid: 执行任务的主键
    :param step: 步骤名
    :param log: 日志内容
    :param end_at: 结束时间
    :param status: 执行结果
    :return:
    """
    if sid and step and log and end_at and status:
        with DBConnectionManager() as db:
            cmd = "UPDATE pipeline_log_minor \
                 SET log='{}', end_at='{}', status='{}' \
                 WHERE sid={} and step='{}';".format(log.replace('\'', '\\\''), end_at, status, sid, step)
            logger.warning(cmd)
            db.cur.execute(cmd)
            db.connection.commit()


if __name__ == '__main__':
    pass
