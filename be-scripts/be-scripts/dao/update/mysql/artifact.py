#
import datetime
from dao.connect.mysql import DBConnectionManager


def update_branch_include_sys_artifact(iteration_id, app_name, git_repo_version):
    """更新应用制品版本"""
    with DBConnectionManager() as db:
        db.cur.execute("""
          UPDATE iter_mgt_iter_app_info
          SET git_repo_version='{}',git_repos_time='{}'
          WHERE pipeline_id='{}' AND appName='{}'""".format(git_repo_version,
                                                            datetime.datetime.now(),
                                                            iteration_id,
                                                            app_name))
        db.connection.commit()
