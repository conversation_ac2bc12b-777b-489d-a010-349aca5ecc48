#
from dao.connect.mysql import DBConnectionManager


def update_iterative_pipeline_relations(cur_pipleline_id, app_name, app_br_name, app_pipeline_id, app_group,
                                        dep_app, dep_br_name, dep_pipeline_id, dep_app_group, iter_app_name):
    """更新应用关系"""
    with DBConnectionManager() as db:

        query_sql = '''
        select * from iterative_pipeline_relations  
          WHERE  app_br_name="{}" AND app_pipeline_id="{}" AND app_group="{}" AND dep_br_name="{}" AND dep_pipeline_id="{}" AND
                dep_app_group="{}" AND cur_pipeline_id="{}" AND appName="{}" AND dep_app="{}" AND iter_app_name="{}" 
        '''.format(app_br_name, app_pipeline_id, app_group, dep_br_name, dep_pipeline_id, dep_app_group,
                   cur_pipleline_id, app_name, dep_app, iter_app_name)
        db.cur.execute(query_sql)
        results = db.cur.fetchall()
        if results:
            db.cur.execute(
                '''UPDATE iterative_pipeline_relations
                   SET app_br_name="{}", app_pipeline_id="{}", app_group="{}", dep_br_name="{}", dep_pipeline_id="{}",
                    dep_app_group="{}"
                   WHERE cur_pipeline_id="{}" AND appName="{}" AND dep_app="{}" AND iter_app_name="{}"; 
                '''.format(app_br_name, app_pipeline_id, app_group, dep_br_name, dep_pipeline_id, dep_app_group,
                           cur_pipleline_id, app_name, dep_app, iter_app_name)
            )
            db.connection.commit()
        else:
            db.cur.execute(
                '''INSERT INTO iterative_pipeline_relations (cur_pipeline_id, appName, app_br_name,
                       app_pipeline_id, app_group, dep_app, dep_br_name, dep_pipeline_id, dep_app_group,iter_app_name)
                   VALUES("{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}","{}");'''.format(
                    cur_pipleline_id, app_name, app_br_name,
                    app_pipeline_id, app_group, dep_app, dep_br_name, dep_pipeline_id, dep_app_group, iter_app_name)
            )
            db.connection.commit()
