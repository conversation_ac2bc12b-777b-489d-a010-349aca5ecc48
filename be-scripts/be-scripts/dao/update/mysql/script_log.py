import sys
import datetime
from dao.connect.mysql import DBConnectionManager


def update_main_log(sid, status, end_at=None, start_at=None):
    """
    :param sid: 执行任务的主键
    :param end_at: 结束时间
    :param status: 执行结果
    :return:
    """
    # 变更状态和结束时间
    if end_at and sid and status:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE public_be_script_main \
                 SET end_at='{}', status='{}' \
                 WHERE sid={};".format(end_at, status, sid))
            db.connection.commit()
    elif start_at and sid and status:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE public_be_script_main \
                 SET start_at='{}', status='{}' \
                 WHERE sid={};".format(start_at, status, sid))
            db.connection.commit()
    # 变更状态
    elif sid and status:
        with DBConnectionManager() as db:
            db.cur.execute(
                "UPDATE public_be_script_main \
                 SET status='{}' \
                 WHERE sid={};".format(status, sid))
            db.connection.commit()


def update_minor_log(sid, step, log, end_at, status):
    """
    :param sid: 执行任务的主键
    :param step: 步骤名
    :param log: 日志内容
    :param end_at: 结束时间
    :param status: 执行结果
    :return:
    """
    if sid and step and log and end_at and status:
        with DBConnectionManager() as db:
            cmd = "UPDATE public_be_script_minor \
                 SET log='{}', end_at='{}', status='{}' \
                 WHERE sid={} and step='{}';".format(log.replace('\'', '\\\''), end_at, status, sid, step)
            db.cur.execute(cmd)
            db.connection.commit()


if __name__ == '__main__':
    pass
