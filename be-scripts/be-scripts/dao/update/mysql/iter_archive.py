from dao.connect.mysql import DBConnectionManager
from settings import logger
import time


def update_branch_status(iterative_id, end_time_str):
    """

    :param iterative_id:
    :param end_time_str:
    :return:
    """
    with DBConnectionManager() as db:
        db.cur.execute('UPDATE iter_mgt_iter_info SET br_status="close",br_end_date="{}" '
                       'WHERE pipeline_id = "{}";'.format(end_time_str, iterative_id))
        db.connection.commit()


def update_iter_message(iterative_id, archive_msg):
    """
    :param iterative_id:
    :param archive_msg:
    :return:
    """
    with DBConnectionManager() as db:
        str_sql = 'UPDATE iter_mgt_iter_info SET archive_msg=\'"{}"\' ' \
                  'WHERE pipeline_id = "{}"'.format(archive_msg, iterative_id)
        logger.info(str_sql)
        db.cur.execute(str_sql)
        db.connection.commit()


def update_branch_sys_status(sys_status, iterative_id):
    """

    :param sys_status:
    :param iterative_id:
    :return:
    """
    with DBConnectionManager() as db:
        db.cur.execute('UPDATE iter_mgt_iter_app_info SET sys_status="{}" WHERE pipeline_id = "{}";'
                       .format(sys_status, iterative_id))
        db.connection.commit()


def update_db_mgt_sql_for_archive_version(arc_sql_ver_name, sql_ver_name):
    with DBConnectionManager() as db:
        sql = """
        update db_mgt_sql set arc_sql_ver_name = '{}' where sql_ver_name = '{}'
        """.format(arc_sql_ver_name, sql_ver_name)
        logger.info(sql)
        db.cur.execute(sql)
        db.connection.commit()

def batch_update_db_mgt_sql_for_archive_version(upd_tuple_list):
    if upd_tuple_list:
        sql = "update db_mgt_sql set arc_sql_ver_name = %s where sql_ver_name = %s"

        with DBConnectionManager() as db:
            db.cur.executemany(sql, upd_tuple_list)
            db.connection.commit()


def update_bis_branch_status(bis_iter_id):
    """

    :param sys_status:
    :param iterative_id:
    :return:
    """
    br_end_date = time.strftime('%Y-%m-%d_%H:%M:%S', time.localtime(time.time()))
    with DBConnectionManager() as db:
        db.cur.execute('UPDATE biz_test_iter SET br_status="close", br_end_time="{}" WHERE biz_test_iter_id = "{}";'
                       .format(br_end_date, bis_iter_id))
        db.connection.commit()


if __name__ == '__main__':
    upd_tuple = ("aaa","bbb")
    dict = [upd_tuple]
    batch_update_db_mgt_sql_for_archive_version(dict)

