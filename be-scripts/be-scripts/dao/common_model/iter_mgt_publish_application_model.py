# coding: utf-8
from sqlalchemy import Column, String, text
from sqlalchemy.dialects.mysql import DATETIME, INTEGER
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class IterMgtPublishApplication(Base):
    __tablename__ = 'iter_mgt_publish_application'
    __table_args__ = {'comment': '上线发布申请表'}

    id = Column(INTEGER(11), primary_key=True)
    team = Column(String(100), comment='所属团队')
    br_name = Column(String(50), nullable=False, comment='分支名称')
    appName = Column(String(500), nullable=False, index=True, comment='应用名称')
    applicant = Column(String(20), comment='申请人')
    apply_at = Column(DATETIME(fsp=6), nullable=False, index=True)
    pipeline_description = Column(String(200), comment='需求/测试功能')
    sql_address = Column(String(500), comment='上线sql存放地址')
    description = Column(String(200), comment='上线注意事项')
    pipeline_id = Column(String(50), nullable=False, comment='迭代号')
    env = Column(String(50), nullable=False, comment='环境')
    git_last_version = Column(String(50), nullable=False, comment='源码仓库提交id')
    git_repo_version = Column(String(50), nullable=False, comment='制品库提交id')
    status = Column(String(8), server_default=text("'未完成'"), comment='发布申请状态')
