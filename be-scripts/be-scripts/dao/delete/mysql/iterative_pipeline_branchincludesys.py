from dao.connect.mysql import DBConnectionManager


def del_app(pipeline_id, appName_list):
    """删除应用"""
    with DBConnectionManager() as db:
        db.cur.execute('delete FROM iter_mgt_iter_app_info  WHERE '
                       'pipeline_id="{}" AND appName in ("{}"); '.format(pipeline_id, '","'.join(appName_list)))
        db.connection.commit()


def del_iterative(pipeline_id):
    """删除迭代"""
    with DBConnectionManager() as db:
        db.cur.execute('delete FROM iter_mgt_iter_app_info  WHERE pipeline_id="{}" ;'.format(pipeline_id))
        db.connection.commit()
