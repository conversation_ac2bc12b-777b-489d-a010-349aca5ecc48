#
from dao.connect.mysql import DBConnectionManager


def delete_iterative_pipeline_relations(cur_pipleline_id, app_name, dep_app, iter_app_name):
    """删除应用关系"""
    with DBConnectionManager() as db:
        db.cur.execute(
            '''DELETE FROM iterative_pipeline_relations
               WHERE cur_pipeline_id="{}" AND appName="{}" AND dep_app="{}" AND iter_app_name="{}" ; 
            '''.format(cur_pipleline_id, app_name, dep_app, iter_app_name)
        )
        db.connection.commit()
