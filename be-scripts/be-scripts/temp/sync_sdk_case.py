import datetime
import sys
import os
PROJECT_DIR = os.path.dirname((os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql import DBConnectionManager
from settings import logger


"""
同步sdk测试用例
"""
class SyncSdkCase:

    sync_table_list = ["project", "project_executor", "project_pivot_table_config", "scenario", "scenario_assertion",
                       "scenario_assertion_ignore", "scenario_assertion_possibility","scenario_case",
                       "scenario_condition","scenario_condition_possibility"]


    # sync_table_list = ["scenario_case",
    #                    "scenario_condition","scenario_condition_possibility"]
    src_db_info = {"host": "**************", "port": 3306, "user": "mring", "password": "howbuylpt", "db": "howbuy_sdk_tester", "charset": "utf8"}
    tgt_db_info = {"host": "**************", "port": 3306, "user": "howbuy_sdk_tester", "password": "oKJihRy91R11", "db": "howbuy_sdk_tester", "charset": "utf8"}

    # tgt_db_info = {"host": "**************", "port": 3306, "user": "mring", "password": "howbuylpt", "db": "shuaitest",
    #                "charset": "utf8"}
    def bakup_target_table(self, tgt_db, table_name):
        create_sql = "create table {}_bakup_{} like {}".format(table_name,
                                                               datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
                                                               table_name)
        insert_sql = "insert into {}_bakup_{} select * from {}".format(table_name,
                                                                       datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
                                                                       table_name)
        logger.info(create_sql)
        tgt_db.cur.execute(create_sql)
        logger.info(insert_sql)
        tgt_db.cur.execute(insert_sql)

    # def insert_target_table(self,tgt_db, table, data):
    #     insert_sql = r"INSERT INTO {}({}) VALUES ({});"
    #     for i in data:
    #         # 判断数据类型
    #         if isinstance(data[i], str):
    #             data[i] = "'{}'".format(data[i])
    #         if isinstance(data[i], datetime.datetime):
    #             data[i] = "'{}'".format(data[i].strftime("%Y-%m-%d %H:%M:%S"))
    #         if data[i] == None:
    #             data[i] = "NULL"
    #     values ='{' + '}, {'.join(data.keys()) + '}'
    #     query = insert_sql.format(table,','.join(data.keys()), values).format(**data)
    #     logger.info(query)
    #     logger.info(data)
    #
    #     tgt_db.cur.execute(query)
    def insert_target_table(self,tgt_db, table, data):
        insert_sql = r"INSERT INTO {}({}) VALUES ({});"
        values = ""
        data_list = []
        col = []
        for i in data:
            if i == "id" and table != "project":
                continue
            col.append(i)
            # 判断数据类型
            if isinstance(data[i], str):
                values = values + " %s,"
                data_list.append(data[i])
                continue
                #data[i] = "'{}'".format(data[i])
            if isinstance(data[i], datetime.datetime):
                # data[i] = "'{}'".format(data[i].strftime("%Y-%m-%d %H:%M:%S"))
                values = values + " %s,"
                data_list.append(data[i])
                continue
            if data[i] == None:
                # data[i] = "NULL"
                values = values + " %s,"
                data_list.append(data[i])
                continue
            if isinstance(data[i], int):
                # data[i] = str(data[i])
                values = values + " %s,"
                data_list.append(data[i])
                continue
        values = values.rstrip(",")
        # values ='{' + '}, {'.join(data.keys()) + '}'
        query = insert_sql.format(table,','.join(col), values)
        logger.info(query)
        logger.info(data_list)

        tgt_db.cur.execute(query, data_list)



    def sync_target_table(self, project_id):
        select_sql = "select * from {} where project_id = {}"
        project_select_sql = "select * from {} where id = {}"
        delete_sql = "delete from {} where project_id = {}"
        project_delete_sql = "delete from {} where id = {}"
        with DBConnectionManager(**self.src_db_info) as db:
            with DBConnectionManager(**self.tgt_db_info) as tgt_db:
                for table in self.sync_table_list:
                    # 备份目标表
                    self.bakup_target_table(tgt_db, table)
                    if table == "project":
                        # 删除目标表数据
                        logger.info(project_delete_sql.format(table, project_id))
                        tgt_db.cur.execute(project_delete_sql.format(table, project_id))
                        tgt_db.connection.commit()
                        # 查询源表数据
                        logger.info(project_select_sql.format(table, project_id))
                        db.cur.execute(project_select_sql.format(table, project_id))

                    else:
                        # 删除目标表数据
                        logger.info(delete_sql.format(table, project_id))
                        tgt_db.cur.execute(delete_sql.format(table, project_id))
                        tgt_db.connection.commit()
                        # 查询源表数据
                        logger.info(select_sql.format(table, project_id))
                        db.cur.execute(select_sql.format(table, project_id))
                    for row in db.cur.fetchall():
                        #print(row)
                        self.insert_target_table(tgt_db, table, row)
                    tgt_db.connection.commit()


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    project_id = sys.argv[1]
    sync_sdk_case = SyncSdkCase()
    sync_sdk_case.sync_target_table(project_id)