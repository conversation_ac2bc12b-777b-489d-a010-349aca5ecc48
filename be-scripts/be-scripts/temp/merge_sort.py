import copy


"""
归并排序
"""


def merge(list1, list2):
    """
    合并两个有序 数列
    :param list1:
    :param list2:
    :return:
    """
    if len(list1) == 0:
        new_list = copy.deepcopy(list2)
        return new_list
    if len(list2) == 0:
        new_list = copy.deepcopy(list1)
        return new_list

    if list1[0] <= list2[0]:
        new_list = [list1[0]]
        list1.pop(0)
        me_list = merge(list1, list2)
        new_list.extend(me_list)
        return new_list
    else:
        new_list = [list2[0]]
        list2.pop(0)
        me_list = merge(list1, list2)
        new_list.extend(me_list)
        return new_list


def merge_sort(data_list):
    """ 将无序数列 二分法分割，直到只有一个值
     然后通过合并方法 合并两个有序数列"""
    list_len = len(data_list)
    if list_len > 1:
        left = merge_sort(data_list[0:list_len//2])
        right = merge_sort(data_list[list_len//2:])
        my_list = merge(left, right)
        return my_list
    else:
        #print(data_list)
        return data_list


data_list = [7, 5, 99, 22, 55, 1, 467, 835, 145, 5623, 35, 34]
res = merge_sort(data_list)
print(res)
