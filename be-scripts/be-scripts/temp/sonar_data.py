from dao.connect.mysql import DBConnectionManager
from settings import logger


def get_dup_data():
    """获取上线应用

    :param project_group: string.迭代版本号
    :return: list. app_list list
    """
    with DBConnectionManager() as db:
        sql = ''' SELECT
                i.id,
                i.appName,
                m.br_name,
                i.pipeline_id,
                s.duplicated_files,s.duplicated_blocks,duplicated_lines,duplicated_lines_density,
                MAX( m.br_end_date ) AS max_br_end_date FROM iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                  RIGHT JOIN code_scanner.sonar_duplicated__report s ON s.app_name = i.appName
                 WHERE
                m.br_status = 'close' AND execute_id = "2022-12-01" GROUP BY i.appName '''
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            try:
                insert_dup_data(row)
            except Exception as e:
                logger.info(row)
                logger.error(str(e))



def insert_dup_data(data_info):
    with DBConnectionManager() as db:
        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_record_info(app_name, br_name, iteration_id,
                       report_url, task_id)  
                       VALUES ('{}', '{}', '{}','','');'''.format(data_info["appName"],
                                                                              data_info["br_name"],
                                                                              data_info["pipeline_id"]))
        row_id = db.cur.lastrowid
        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'DUPLICATED', 'duplicated_blocks','{}');'''.format(row_id,
                                                                   data_info["duplicated_blocks"]))


        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'DUPLICATED', 'duplicated_lines','{}');'''.format(row_id,
                                                                   data_info["duplicated_lines"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'DUPLICATED', 'duplicated_lines_density','{}');'''.format(row_id,
                                                                                                data_info["duplicated_lines_density"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                            value)  
                            VALUES ('{}', 'DUPLICATED', 'duplicated_files','{}');'''.format(row_id,
                                                                                            data_info["duplicated_files"]))


        db.connection.commit()
        return row_id


def get_bug_data():
    """获取上线应用

    :param project_group: string.迭代版本号
    :return: list. app_list list
    """
    with DBConnectionManager() as db:
        sql = ''' SELECT
                i.id,
                i.appName,
                m.br_name,
                i.pipeline_id,
                s.bug_blocker,s.bug_critical,s.bug_minor,s.bug_major,s.bug_info,s.vuln_blocker,s.vuln_critical,s.vuln_minor,s.vuln_major,s.vuln_info,
                s.smell_blocker,s.smell_critical,s.smell_minor,s.smell_major,s.smell_info,
                MAX( m.br_end_date ) AS max_br_end_date FROM iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                  RIGHT JOIN code_scanner.sonar_findbugs_report s ON s.app_name = i.appName
                 WHERE
                m.br_status = 'close' AND execute_id = "2022-12-01" GROUP BY i.appName  '''
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            try:
                insert_bug_data(row)
            except Exception as e:
                logger.error(row)
                logger.error(str(e))
            

def insert_bug_data(data_info):
    with DBConnectionManager() as db:
        try:
            db.cur.execute('''INSERT INTO mantis.code_quality_sonar_record_info(app_name, br_name, iteration_id,
                           report_url, task_id)  
                           VALUES ('{}', '{}', '{}','','');'''.format(data_info["appName"],
                                                                                  data_info["br_name"],
                                                                                  data_info["pipeline_id"]))
            row_id = db.cur.lastrowid
        except Exception as e:
            logger.info(str(e))
            db.cur.execute('''select id from mantis.code_quality_sonar_record_info where app_name 
= "{}" and iteration_id = "{}"'''.format(data_info["appName"], data_info["pipeline_id"]))
            row_id = db.cur.fetchone()["id"]

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'CODE_SMELL', 'INFO','{}');'''.format(row_id, data_info["smell_info"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'CODE_SMELL', 'MINOR','{}');'''.format(row_id, data_info["smell_minor"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'CODE_SMELL', 'MAJOR','{}');'''.format(row_id, data_info["smell_major"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                            value)  
                            VALUES ('{}', 'CODE_SMELL', 'CRITICAL','{}');'''.format(row_id,  data_info["smell_critical"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                                    value)  
                                    VALUES ('{}', 'CODE_SMELL', 'BLOCKER','{}');'''.format(row_id, data_info["smell_blocker"]))



        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'BUG', 'INFO','{}');'''.format(row_id, data_info["bug_info"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'BUG', 'MINOR','{}');'''.format(row_id, data_info["bug_minor"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'BUG', 'MAJOR','{}');'''.format(row_id, data_info["bug_major"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                            value)  
                            VALUES ('{}', 'BUG', 'CRITICAL','{}');'''.format(row_id,
                                                                                    data_info["bug_critical"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                                    value)  
                                    VALUES ('{}', 'BUG', 'BLOCKER','{}');'''.format(row_id,
                                                                                           data_info["bug_blocker"]))





        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'VULNERABILITY', 'INFO','{}');'''.format(row_id, data_info["vuln_info"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'VULNERABILITY', 'MINOR','{}');'''.format(row_id, data_info["vuln_minor"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                        value)  
                        VALUES ('{}', 'VULNERABILITY', 'MAJOR','{}');'''.format(row_id, data_info["vuln_major"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                            value)  
                            VALUES ('{}', 'VULNERABILITY', 'CRITICAL','{}');'''.format(row_id,
                                                                                    data_info["vuln_critical"]))

        db.cur.execute('''INSERT INTO mantis.code_quality_sonar_result_info(p_id, measure, segment,
                                    value)  
                                    VALUES ('{}', 'VULNERABILITY', 'BLOCKER','{}');'''.format(row_id,
                                                                                           data_info["vuln_blocker"]))


        db.connection.commit()
        return row_id

if __name__ == "__main__":
    get_bug_data()