from croniter import croniter
import csv
import datetime

# 读取csv文件
def read_csv(file_path):
    with open(file_path, 'r', encoding="utf-8") as file:
        reader = csv.reader(file)
        return list(reader)
def get_next_execution_time(cron_expression,current_time):
    try:
        cron = croniter(cron_expression,current_time)

    except Exception as e:
        print("无效的cron表达式:{}".format(cron_expression))
        return None
    return cron.get_next(datetime.datetime)

def get_more_then_month(cron_expression):
    # current_time_str = "2024-08-01 00:00:00"
    # current_time = datetime.datetime.strptime(current_time_str, "%Y-%m-%d %H:%M:%S")

    current_time = datetime.datetime.now()
    next_time = get_next_execution_time(cron_expression,current_time)
    next_time2 = get_next_execution_time(cron_expression,next_time)
    if next_time is None:
        return False
    timedelta = next_time2 - next_time
    if timedelta.days > 29:
        return True
    else:
        return False


def get_next_all_execution_time(cron_expression,current_time):
    try:
        cron = croniter(cron_expression,current_time)

    except Exception as e:
        print("无效的cron表达式:{}".format(cron_expression))
        return None
    return cron.all_next(datetime.datetime)
def get_more_next_two_hour(cron_expression):
    current_time_str = "2024-08-31 00:00:00"
    current_time = datetime.datetime.strptime(current_time_str, "%Y-%m-%d %H:%M:%S")

    next_time = get_next_execution_time(cron_expression,current_time)

    # timedelta = next_time - current_time
    # if timedelta.hours <2:
    #     return True;
    # else:
    #     return False

def get_only_exe_in_time_solt(cron_expression):
    current_time_str = "2024-08-31 00:00:00"
    current_time = datetime.datetime.strptime(current_time_str, "%Y-%m-%d %H:%M:%S")
    start_time_str = "2024-08-31 08:00:00"
    start_time = datetime.datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
    end_time_str = "2024-08-31 18:00:00"
    end_time = datetime.datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
    next_day = current_time + datetime.timedelta(days=1)
    current_next_time = get_next_execution_time(cron_expression, current_time)
    # print(current_next_time)
    end_time_next_time = get_next_execution_time(cron_expression, end_time)
    # print(end_time_next_time)
    # 找到当天只在开始时间和结束时间段内执行的
    if current_next_time is None:
        return False
    if current_next_time > start_time and current_next_time < end_time and end_time_next_time > next_day:
        print("当前时间:", current_time, "下一次执行时间:", current_next_time, "结束执行时间:", end_time_next_time,
              "下一天:", next_day)
        return True
    return False

def main():
    new_list = []
    for row in read_csv('D:\\logs\\test\\全量.csv'):
        if row[6] == "cron":
            new_list.append(row)
            continue
        #print(row)
        # if get_more_then_month(" ".join(row[6].split(" ")[1:])):
        #     new_list.append(row)
        #     print(row)
        if get_only_exe_in_time_solt(" ".join(row[6].split(" ")[1:])):
            new_list.append(row)
            print(row)
        # break
    # # 写入csv文件
    # with open('D:\\logs\\test\\new_cron.csv', 'w', newline='') as file:
    #     writer = csv.writer(file)
    #     writer.writerows(new_list)
    with open('D:\\logs\\test\\time_solt_cron.csv', 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerows(new_list)
# 定义cron表达式
cron_expression = "*/15 * * * *"




# 获取执行频率

if __name__ == "__main__":
    main()
    # print("下一次执行时间:", get_next_execution_time(cron_expression))
