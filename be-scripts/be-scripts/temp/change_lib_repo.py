import os
import sys
import subprocess
import logging


def shell_cmd(cmd):

    p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = p.communicate()
    rt_code = p.returncode
    stdout_info = stdout.decode()
    stderr_info = stderr.decode()
    print(stdout_info)
    return rt_code, stdout_info, stderr_info

def change_git_repo(dir_path):

    for row in os.listdir(dir_path):

        local_lib_repo_path = dir_path + "/" + row
        if os.path.isdir(local_lib_repo_path):
            rt_code, stdout_info, stderr_info = shell_cmd("cd "+local_lib_repo_path +" && git remote -v")
            if stdout_info:
                print(stdout_info.split("\n")[0].split("\t"))
                remote_path = stdout_info.split("\n")[0].split("\t")[1].split(" ")[0]
                print(remote_path)
            if "***************" in remote_path:
                new_url = remote_path.replace("***************", "gitlab-lib.howbuy.pa")
                cmd= "cd "+ local_lib_repo_path+" && git remote set-url origin "+new_url
                print(cmd)
                os.system(cmd)

dir_path = sys.argv[1]
change_git_repo(dir_path)