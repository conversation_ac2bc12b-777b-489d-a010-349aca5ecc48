import datetime
import sys
import os
PROJECT_DIR = os.path.dirname((os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql import DBConnectionManager
from settings import logger


class ModifyTestCaseAppDbName:
    # tgt_db_info = {"host": "**************", "port": 3306, "user": "mring", "password": "howbuylpt",
    #                "db": "qa_info", "charset": "utf8"}

    tgt_db_info = {"host": "**************", "port": 3306, "user": "qa_info", "password": "i363ln_MBB8d",
                   "db": "qa_info", "charset": "utf8"}
    def __init__(self,case_app,case_version,old_db_alias,old_app_name,new_db_alias,new_app_name):
        self.case_app = case_app
        self.case_version = case_version
        self.old_db_alias = old_db_alias
        self.old_app_name = old_app_name
        self.new_db_alias = new_db_alias
        self.new_app_name = new_app_name

    def bakup_target_table(self, tgt_db, table_name):
        create_sql = "create table {}_bakup_{} like {}".format(table_name,
                                                               datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
                                                               table_name)
        insert_sql = "insert into {}_bakup_{} select * from {}".format(table_name,
                                                                       datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
                                                                       table_name)
        logger.info(create_sql)
        tgt_db.cur.execute(create_sql)
        logger.info(insert_sql)
        tgt_db.cur.execute(insert_sql)
    def modify_db_assert(self, tgt_db):
        sql = '''UPDATE   testing_script_case_assert_db oo JOIN (SELECT t.id FROM testing_script_api a  JOIN testing_script_case c ON
    a.script_id = c.script_id  JOIN testing_script_case_assert s ON
    s.case_id=c.id  JOIN testing_script_case_assert_db t ON t.assert_id = s.id
    WHERE a.app_name = "{}" AND a.VERSION = "{}"
    AND t.app_name = "{}" AND t.db_alias = "{}") ii ON oo.id = ii.id
    SET oo.app_name = "{}"  , oo.db_alias = "{}"'''.format(self.case_app,self.case_version,self.old_app_name,self.old_db_alias,self.new_app_name,self.new_db_alias)
        logger.info(sql)
        tgt_db.cur.execute(sql)

    def modify_validation_setting(self, tgt_db):
        sql = '''UPDATE testing_data_validation_set oo JOIN (SELECT v.id FROM testing_script_api a  JOIN testing_script_case c ON
    a.script_id = c.script_id  JOIN testing_script_case_assert s ON
    s.case_id=c.id  JOIN testing_script_case_assert_validation_set t ON t.assert_id = s.id
    JOIN testing_data_validation_set v ON v.id =t.validation_set_id
    WHERE a.app_name = "{}" AND a.VERSION = "{}"   AND
    v.app_name = "{}" AND v.db_alias = "{}") ii ON oo.id = ii.id
    SET oo.app_name = "{}"  , oo.db_alias = "{}"'''.format(self.case_app,self.case_version,self.old_app_name,self.old_db_alias,self.new_app_name,self.new_db_alias)
        logger.info(sql)
        tgt_db.cur.execute(sql)

    def modify_per_script(self, tgt_db):

        sql = '''UPDATE testing_data_operation_db oo JOIN
    (SELECT t.id FROM testing_script_api a  JOIN testing_script_case c ON
    a.script_id = c.script_id  JOIN testing_data_operation s ON
    s.p_id=c.id  JOIN testing_data_operation_db t ON t.operation_id = s.id
    WHERE a.app_name = "{}" AND a.VERSION = "{}"
    AND
    t.app_name = "{}" AND t.db_alias = "{}" AND s.scope="case") ii ON oo.id = ii.id
    SET oo.app_name = "{}" , oo.db_alias = "{}"'''.format(self.case_app,self.case_version,self.old_app_name,self.old_db_alias,self.new_app_name,self.new_db_alias)
        logger.info(sql)
        tgt_db.cur.execute(sql)

    def main(self):
        logger.info("开始修改测试用例数据库别名")

        with DBConnectionManager(**self.tgt_db_info) as db:
            # self.bakup_target_table(db, "testing_script_case_assert_db")
            self.modify_db_assert(db)
            # self.bakup_target_table(db, "testing_data_validation_set")
            self.modify_validation_setting(db)
            # self.bakup_target_table(db, "testing_data_operation_db")
            self.modify_per_script(db)
            db.connection.commit()


if __name__ == "__main__":
    # logger.info("调用 {}".format(sys.argv[1:]))
    # params_list = sys.argv[1:]
    # params_list = ["cgi-simu-container", "4.3.11", "order-center-remote", "order-center-remote", "HIGH", "high-order-trade-remote"]
    # params_list = ["high-order-trade-remote", "4.5.11", "order-center-remote", "order-center-remote",
    #                "HIGH", "high-order-trade-remote"]
    # params_list = ["high-batch-center-remote", "4.5.11", "high-batch-center-remote", "high-batch-center-remote",
    #                "HIGH", "high-batch-center-remote"]
    params_list = ["high-batch-center-remote", "4.5.11", "batch-center-remote", "batch-center-remote",
                   "HIGH", "high-batch-center-remote"]
    md = ModifyTestCaseAppDbName(*params_list)
    md.main()