# 测试环境一键初始化：业务
# 第1版 zt@2020-10-21

import json
import os
import sys
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from settings import logger as log

from test_publish_aio.test_suite_init_constants import TST_CONTAINER_NAME, BO_BASE_KEY, TypeEnum
from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos
from test_publish_aio.test_suite_init_utils import filter_bo_list


def is_tpm(v):
    """"过滤是中间件 zt@2020-10-22"""
    return v.third_party_middleware


def is_not_tpm(v):
    """"过滤不是中间件 zt@2020-10-22"""
    return not is_tpm(v)


def is_agent(v):
    """"过滤是agent fwm@2022-06-15"""
    return v.is_agent


def is_not_agent(v):
    """"过滤不是agent fwm@2020-06-15"""
    return not is_agent(v)


def is_ccms(v):
    """"过滤CCMS应用 zhaowancheng@2021-03-03"""
    return v.zeus_type == 4


def is_tst(v):
    """"过滤是tst zt@2020-10-22"""
    return v.container_name == TST_CONTAINER_NAME


def is_not_tst(v):
    """"过滤不是tst zt@2020-10-22"""
    return not is_tst(v)


def is_remote(v):
    """"过滤是jar应用 zt@2020-11-19"""
    package_type = v.get('package_type')
    return package_type and package_type.lower() == 'jar'


def is_mobile_remote(v):
    """ 移动端的 remote 应用 zt@2021-04-25"""
    package_type = v.get('package_type')
    return package_type and package_type.lower() == 'remote'


def is_h5(v):
    """"过滤是h5 zt@2020-10-22"""
    return v.package_type == 'h5' or v.package_type == 'remote' or v.package_type == 'static' or v.package_type == 'static'


def is_not_h5(v):
    """"过滤不是h5 zt@2020-10-22"""
    return not is_h5(v)


def is_vm_deploy(v):
    """"过滤属于容器应用 zt@2020-10-21"""
    return v.deploy_type == 1


def is_docker_deploy(v):
    """"过滤属于容器应用 zt@2020-10-21"""
    return v.deploy_type == 2


def is_zeus(v):
    """"过滤属于宙斯配置 zt@2020-10-21"""
    return v.zeus_type == 1


def is_not_zeus(v):
    """"过滤不是宙斯配置 zt@2020-10-22"""
    return not is_zeus(v)


def is_conf(v):
    """"过滤属于配置管理 zt@2020-10-27"""
    return v.zeus_type != 0


def is_not_conf(v):
    """"过滤不是配置管理 zt@2020-10-27"""
    return not is_conf(v)


def is_svn(v):
    """"需要svn上传 zt@2020-11-18"""
    return bool(v.svn_container_path)


def get_bo_dict_by_list(bo_list):
    """简化1对1结构 zt@2020-10-29"""
    bo_dict = None
    if bo_list:
        bo_dict = {}
        for bo in bo_list:
            bo_dict_key = bo.get(BO_BASE_KEY)
            bo_dict[bo_dict_key] = bo

    return bo_dict


def get_bo_list_dict_by_list(bo_list):
    """完整1对多结构 zt@2020-10-29"""
    bo_dict = None
    if bo_list:
        bo_dict = {}
        for bo in bo_list:
            bo_dict_key = bo.get(BO_BASE_KEY)

            tmp_list = bo_dict.get(bo_dict_key)
            if not tmp_list:
                tmp_list = [bo]
                bo_dict[bo_dict_key] = tmp_list
            else:
                tmp_list.append(bo)

    return bo_dict


def get_bo_list_from_cache(suite_code, job_workspace, job_bo_key, app_dict_list, br_name=None, step_enum=None,
                           type_enum=None, filter_func_list=None, filter_key_list=None):
    """从帅的缓存中获取数据 zt@2020-10-19"""
    # log.info(">>>> 缓存新增参数：br_name = {}".format(br_name))
    # log.info(">>>> 缓存新增参数：step_enum = {}".format(step_enum))
    # log.info(">>>> 缓存新增参数：type_enum = {}".format(type_enum))

    # 获取帅的数据
    # business_name = 'env_init'
    # if type_enum and type_enum == TypeEnum.TEST_PUSH:
    #     business_name = 'test_publish'

    bo_iter = PublishTestRepos(suite_code, job_workspace, cache_data_code=job_bo_key, type_enum=type_enum,
                               br_name=br_name, app_dict_list=app_dict_list)
    log.info("bo_iter:{}".format(bo_iter.__dict__))
    app_module_name_list = [list(d.keys())[0] for d in app_dict_list]
    # 1、过滤
    bo_list = [bo for bo in bo_iter if bo.module_name in app_module_name_list]

    # 2、缓存
    if not job_bo_key:
        job_bo_key = bo_iter.cache_data_code
        log.info("======== 新生成缓存：job_bo_key = {} ========".format(job_bo_key))
    else:
        log.info("======== 直接用缓存：job_bo_key = {} ========".format(job_bo_key))

    # 3、过滤
    bo_list = filter_bo_list(bo_list, filter_func_list=filter_func_list, filter_key_list=filter_key_list)

    return job_bo_key, bo_list
