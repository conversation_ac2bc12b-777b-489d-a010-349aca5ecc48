# 测试环境一键初始化（拉制品）

import os
import sys
from settings import MIRROR_FACTORY, PIPELINE_TEST_PUBLISH
import time

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import ssh_path_whether_exist, ssh_path_mkdir
from settings import TEST_PUBLISH_AIO as TP_AIO

app_dict_str = """
"""


def pre_check(app_dict, jacoco_type):
    log.info("开始前置检查." + str(app_dict))
    log.info("是否带jacoco检查：{} (1：是；0：否)".format(jacoco_type))
    result_info = {}
    if app_dict:
        for key in app_dict.keys():
            deploy_type = app_dict[key]['deploy_type']
            remote_ip = ''
            container_name = app_dict[key]['container_name']
            package_type = app_dict[key]['package_type']
            result_info[time.strftime('%Y-%m-%d %H:%M:%S')] = "应用:{},容器{}".format(key, container_name)
            path_dict = {}

            if deploy_type == 1:
                remote_ip = app_dict[key]['active_node_ip']
                path_dict['deploy_config_path#' + remote_ip] = app_dict[key]['deploy_config_path']
                path_dict['deploy_path#' + remote_ip] = app_dict[key]['deploy_path']
                path_dict['start_script_path#' + remote_ip] = app_dict[key]['start_script_path']
                log.info("jacoco_type：{} (1：是；0：否)".format(jacoco_type))
                if jacoco_type is not None and int(jacoco_type) == 1:
                    path_dict['jacoco_path#' + remote_ip] = TP_AIO['jacoco_path']
                    jacoco_type = 0

            elif deploy_type == 2:
                remote_ip = MIRROR_FACTORY['IP']
                remote_ip2 = PIPELINE_TEST_PUBLISH['config_map_ip']
                path_dict['deploy_config_path#' + remote_ip2] = app_dict[key]['deploy_config_path']
                path_dict['deploy_path#' + remote_ip] = app_dict[key]['deploy_path']
                # 增加容器脚本路径检测 20220124 by fwm
                if package_type == 'war' or package_type == 'jar' or package_type == 'tar':
                    path_dict['start_script_path_dir#' + remote_ip] = app_dict[key]['start_script_path_dir']

            for temp_key in path_dict.keys():
                # 判断文件或文件夹
                is_file = False
                if temp_key.startswith('start_script_path#'):
                    is_file = True

                # 调用检测接口
                check_result = False
                chk_msg = ''
                try:
                    log.info("==================开始检测==================")
                    ket_str = temp_key.split("#")
                    check_result, chk_msg = ssh_path_whether_exist(ket_str[1], path_dict[temp_key], is_file)
                    if not check_result and '目录' in chk_msg:
                        ssh_path_mkdir(ket_str[1], path_dict[temp_key])
                        check_result, chk_msg = ssh_path_whether_exist(ket_str[1], path_dict[temp_key], is_file)
                except Exception:
                    pass
                log.info("检查返回结果:" + str(check_result))
                log.info("==================结束检测==================")
                # 写入返回字典
                if not check_result:
                    result_info[time.strftime('%Y-%m-%d %H:%M:%S') + "   项{}".format(temp_key)] = ",值{},检测结果:{}". \
                        format(path_dict[temp_key], str(check_result))

        print_love()
        result_flag = True
        for info_key in result_info.keys():
            log.info(info_key + " " + result_info[info_key])
            if result_info[info_key].endswith('False'):
                result_flag = False

        if not result_flag:
            raise ValueError("前置检查失败！")
        else:
            log.info("前置检查通过！")
    else:
        log.info("前置检查跳过！")


def print_love():
    print("　  ■■　　　　　■■　　　　　　　　　　　　■■　　　　　　　　　　　　　　　　■■　　　　　　　■■■■■■■■■■■　  ")
    print("　　■■　　　　　■■　　　　　　　　　　　　■■　　　　　　　　　■■　　　　　■■　　　　　　　■■■■■■■■■■■　　")
    print("　　■■　　　　■■■■　　　　　■■■■■■■■■■■■■　　　　■■　　■■■■■■■■■　　　■■　　　■■　　■■　　")
    print("■■■■■■　■■　■■■　　　　■■■■■■■■■■■■■　　　■■　　　■■■■■■■■■　　　■■■■■■■■■■■　　")
    print("■■■■■■■■　　　　■■　　　　　　■■　■■　■■　　　　■■　　■■　　　■■　　　　　　　■■■■■■■■■■■　　")
    print("　　■■　■■　■■■■　■■　　　■■■　　■■　　■■　　　■■■■■■　■■■■■■■　　　　■■　　　■■　　■■　　")
    print("　　■■　　　　■■■■　　　　■■■■■■■■■■■■■■■　　　■■　　　■■■■■■■　　　　■■■■■■■■■■■　　")
    print("　■■■　　　　　　　　■■　　　■　■■■■■■■■■■■　　　■■　　　　　　　　　　　　　　　■■■■■■■■■■■　　")
    print("　■■■■　■■　■■　■■　　　　　■■　　　　　　■■　　　■■■■■■　■■■■■■■　　　　　　　　　■■　　　　　　")
    print("■■■■■　■■　■■　■■　　　　　■■■■■■■■■■　　　■■■■■■　■■■■■■■　　■■■■■■■■■■■■■■■")
    print("■　■■　　　■■　　■■　　　　　　■■　　　　　　■■　　　　　　　　　　■■　　　■■　　■■■■■■■■■■■■■■■")
    print("　　■■　　　　■　　■■　　　　　　■■■■■■■■■■　　　　　　■■■　■■　　　■■　　　　　■■■　■■　■■　　　")
    print("　　■■　■■■■■■■■■■　　　　　　　　　　　　　　　　　■■■■■■　■■■■■■■　　　　■■■　　■■　　■■■　")
    print("　　■■　■■■■■■■■■■　　■■■■■■■■■■■■■■　■■■　　　　■■■■■■■　　■■■　　　　■■　　　■■■")
    print("　　■■　　　　　　　　　　　　　■■■■■■■■■■■■■■　　　　　　　　■■　　　■■　　　　　　　　　■■　　　　　　")
    print("========================================================================================================")
    print("========================================================================================================")
    print("检测内容说明：")
    print(" 1、虚机部署应用检测内容：")
    print("     a、目标机器的包路径")
    print("     b、目标机器的配置路径")
    print("     c、目标应用的启动脚本")
    print(" 2、容器部署应用的检测内容：")
    print("     a、镜像仓库的应用包路径")
    print("     b、configmap仓库的应用包外移文件路径")
    print("========================================================================================================")
    print("========================================================================================================")


if __name__ == '__main__':
    app_dict_in = {
        "stp-console-web": {
            "active_node_ip": "**************",
            "br_name": "5.12.0",
            "container_name": "mring-itest-service",
            "deploy_config_path": "",
            "deploy_path": "/data/app/mring-itest-service/lib/",
            "deploy_type": 1,
            "module_name": "mring-itest-service",
            "online_br_name": "5.11.0",
            "package_type": "jar",
            "start_script_path": "/data/app/mring-itest-service/start.sh"
        },
        "vendor": {
            "active_node_ip": '',
            "container_name": "tstatic",
            "deploy_config_path": "/home/<USER>/img-factory-online/tms18/tstatic/tstatic",
            "deploy_path": "/home/<USER>/img-factory-online/tms18/tstatic/tstatic",
            "deploy_type": 2,
            "node_docker": "tms18",
            "start_script_path": "/home/<USER>/img-factory-online/tms18/tstatic/tstatic/bin/startup.sh"
        }
    }
    pre_check(app_dict_in, 1)
