from dao.connect.mysql import DBConnectionManager
from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos

sql = '''SELECT f.module_name,f.node_docker,f.node_bind_desc,f.deploy_group,f.deploy_type,
f.deploy_path,f.health_check_url,f.stamp,f.tomcat_path,f.script_path,f.script_name,
f.config_path,f.log_path,f.enable_bind FROM env_mgt_suite l LEFT JOIN env_mgt_node_bind f 
ON l.id=f.suite_id WHERE l.suite_code = "tms18";'''
#test_publish_data_list = []


with DBConnectionManager() as db:
    db.cur.execute(sql)

sql2 = '''SELECT f.suite_id,f.node_id,f.module_name,f.node_docker,f.node_bind_desc,f.deploy_group,f.deploy_type,
f.deploy_path,f.health_check_url,f.stamp,f.tomcat_path,f.script_path,f.script_name,
f.config_path,f.log_path,f.enable_bind FROM env_mgt_suite l LEFT JOIN env_mgt_node_bind f 
ON l.id=f.suite_id WHERE l.suite_code = "tms01";'''
with DBConnectionManager() as db2:
    db2.cur.execute(sql2)

src_list = []
target_list = []
for row in db.cur.fetchall():
    src_list.append(row)
for row in db2.cur.fetchall():
    target_list.append(row)
    # app_suite_object = AppSuiteObject(row)

for row2 in target_list:
    if row2["node_id"]:
        node_id = row2["node_id"]
    if row2["node_docker"]:

        node_docker = row2["node_docker"]

for row in src_list:
    is_exist = 0
    #print(row["module_name"])
    for row2 in target_list:
        if row["module_name"] == row2["module_name"]:
            is_exist = 1
            # print('''update env_mgt_node_bind set node_bind_desc="{}",deploy_group="{}",deploy_type={},deploy_path="{}",health_check_url="{}",stamp={},tomcat_path="{}",script_path="{}",config_path="{}",log_path="{}",enable_bind={} where suite_id={} and module_name="{}";'''.format(
            #                                                                     row["node_bind_desc"],
            #                                                                     row["deploy_group"],
            #                                                                     row["deploy_type"],
            #                                                                     row["deploy_path"],
            #                                                                     row["health_check_url"],
            #                                                                     row["stamp"],
            #                                                                     row["tomcat_path"],
            #                                                                     row["script_path"],
            #                                                                     row["config_path"],
            #                                                                     row["log_path"],
            #                                                                     row["enable_bind"],
            #                                                                     row2["suite_id"],
            #                                                                     row2["module_name"]
            #                                                                     ))
    if is_exist == 0:
        if row["deploy_type"] == 1:

            print(
                '''insert into env_mgt_node_bind (suite_id,node_id,module_name,node_docker,node_bind_desc,deploy_group,deploy_type,deploy_path,health_check_url,stamp,tomcat_path,script_path,script_name,config_path,log_path,enable_bind) values ({},{},"{}","{}","{}","{}",{},"{}","{}",{},"{}","{}","{}","{}","{}","{}");'''.format(
                    row2["suite_id"],
                    node_id,
                    row["module_name"],
                    None,
                    row["node_bind_desc"],
                    row["deploy_group"],
                    row["deploy_type"],
                    row["deploy_path"],
                    row["health_check_url"],
                    row["stamp"],
                    row["tomcat_path"],
                    row["script_path"],
                    row["script_name"],
                    row["config_path"],
                    row["log_path"],
                    row["enable_bind"],
                ).replace('"None"','NULL').replace('None','NULL'))
        elif row["deploy_type"]==2:
            print ('''insert into env_mgt_node_bind (suite_id,node_id,module_name,node_docker,node_bind_desc,deploy_group,deploy_type,deploy_path,health_check_url,stamp,tomcat_path,script_path,script_name,config_path,log_path,enable_bind) values ({},{},"{}","{}","{}","{}",{},"{}","{}",{},"{}","{}","{}","{}","{}","{}");'''.format(
                row2["suite_id"],
                "NULL",
                row["module_name"],
                node_docker,
                row["node_bind_desc"],
                row["deploy_group"],
                row["deploy_type"],
                row["deploy_path"],
                row["health_check_url"],
                row["stamp"],
                row["tomcat_path"],
                row["script_path"],
                row["script_name"],
                row["config_path"],
                row["log_path"],
                row["enable_bind"],
            ).replace('"None"','NULL').replace('None','NULL'))


# pas = PublishTestRepos("tms18","E:\\test")
# #logger.info(pas.cache_data_code)
# for row in pas:
#     if row.module_name in test_publish_data_list:
#
#         if row.third_party_middleware==1:
#             print("update publish_deploy_info set start_level=0 where module_name = '{}';".format(row.module_name))
#         else:
#             if row.deploy_type == 1:
#                 print("update publish_deploy_info set start_level=1 where module_name = '{}';".format(row.module_name))
#             elif row.deploy_type == 2:
#                 print("update publish_deploy_info set start_level=2 where module_name = '{}';".format(row.module_name))
#     else:
#         print('INSERT publish_deploy_info (module_name) VALUES ("{}");'.format(row.module_name))


#print(test_publish_data_list)

