# 一键部署（底层方法实现）
import os
import sys
import time

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from settings import logger as log, TEST_PUBLISH_AIO
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import pull_info_git, push_info_git_for_static,\
    push_info_git_for_config, exec_local_cmd


def tstatic_config_opt(suite_code, app_dict):
    log.info('begin tstatic配置更新')
    if app_dict:
        # 更新tstatic所在配置仓库
        suite_root_path = os.path.join(TEST_PUBLISH_AIO['root_path'], suite_code)
        nfs_root_path = os.path.join(TEST_PUBLISH_AIO['nfs_root_path'], suite_code)
        test_git_url = TEST_PUBLISH_AIO['test_app_resource_gitlab_ssh']
        test_git_name = TEST_PUBLISH_AIO['test_app_resource_gitlab_name']
        test_git_branch = TEST_PUBLISH_AIO['test_app_resource_gitlab_branch']

        log.info('   1,开始pull static config.{}'.format(time.strftime('%Y-%m-%d %H:%M:%S')))
        log.info('   '+test_git_url+'|'+test_git_branch + '|' + suite_root_path + '|' + test_git_name)
        pull_info_git(test_git_url, test_git_branch, nfs_root_path, test_git_name)
        log.info('   1,结束pull static config.{}'.format(time.strftime('%Y-%m-%d %H:%M:%S')))

        for module_name in app_dict:
            if app_dict[module_name]["package_type"] == 'h5' or app_dict[module_name]["package_type"] == 'remote' \
                    or app_dict[module_name]["package_type"] == 'static':
                # 1、创建环境对应制品目录
                app_cache_path = os.path.join(TEST_PUBLISH_AIO['root_path'], suite_code, "app")
                if not os.path.exists(app_cache_path):
                    cmd = "mkdir -p {}".format(app_cache_path)
                    exec_local_cmd(cmd)
                # 2、切换至制品目录
                cmd = "cd {}".format(app_cache_path)
                exec_local_cmd(cmd)
                lib_cache_repo_path = app_cache_path
                module_name = app_dict[module_name]["module_name"]

                target_path = os.path.join(nfs_root_path, test_git_name, module_name)
                log.info('     2.1,开始提取配置覆盖' + target_path + '|' + lib_cache_repo_path)
                for root, dirs, files in os.walk(target_path):
                    for fl in files:
                        target_file = os.path.join(root, fl)
                        log.info('        target:' + target_file)
                        path_temp = os.path.join(root, fl).replace(target_path, '').strip('/')
                        source_file = os.path.join(lib_cache_repo_path + "/" + module_name, path_temp)
                        log.info('        source:' + source_file)
                        os.system("cp -r " + source_file + " " + target_file)
                log.info('     2.1,结束提取配置覆盖' + target_path + '|' + lib_cache_repo_path)

        log.info('   3,开始{}'.format(time.strftime('%Y-%m-%d %H:%M:%S')))
        commit_desc = "update by be-script"+time.strftime('%Y-%m-%d %H:%M:%S')
        log.info('   '+suite_root_path+'|'+test_git_branch + "|" + commit_desc)
        push_info_git_for_config(test_git_url, nfs_root_path, test_git_branch, commit_desc, test_git_name)
        log.info('   3,结束{}'.format(time.strftime('%Y-%m-%d %H:%M:%S')))


def tstatic_create_branch(suite_code, app_dict):
    log.info('begin tstatic分支-特殊处理')
    if app_dict:
        for module_name in app_dict:
            # +"_" + time.strftime('%Y_%m_%d_%H_%M_%S')
            if app_dict[module_name]["package_type"] == 'h5' or app_dict[module_name]["package_type"] == 'remote' \
                    or app_dict[module_name]["package_type"] == 'static':
                archive_br_name = app_dict[module_name]["archive_br_name"]
                lib_repo_path = app_dict[module_name]["lib_repo_path"]
                # 1、创建环境对应制品目录
                app_cache_path = os.path.join(TEST_PUBLISH_AIO['root_path'], suite_code, "app")
                if not os.path.exists(app_cache_path):
                    cmd = "mkdir -p {}".format(app_cache_path)
                    exec_local_cmd(cmd)
                # 2、切换至制品目录
                cmd = "cd {}".format(app_cache_path)
                exec_local_cmd(cmd)
                lib_cache_repo_path = app_cache_path
                module_name = app_dict[module_name]["module_name"]
                log.info('   1,开始{}'.format(time.strftime('%Y-%m-%d %H:%M:%S')))
                commit_desc = "create by be-script." + time.strftime('%Y-%m-%d %H:%M:%S')
                log.info('      ' + str(lib_cache_repo_path) + '|' + str(archive_br_name) + '|' + str(commit_desc))
                push_info_git_for_static(lib_repo_path, lib_cache_repo_path, archive_br_name, commit_desc, module_name)
                log.info('   1,结束{}'.format(time.strftime('%Y-%m-%d %H:%M:%S')))


if __name__ == '__main__':
    tstatic_config_opt()
