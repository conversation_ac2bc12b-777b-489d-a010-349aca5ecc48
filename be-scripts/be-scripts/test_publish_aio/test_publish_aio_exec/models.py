from peewee import *
from dao.base_model import SpiderBaseModels


class PublishMgtAppRestriction(SpiderBaseModels):
    """
    发布限制表
    """
    module_name = CharField(verbose_name='应用名', max_length=100)
    suite_code = CharField(verbose_name='环境名', max_length=50)
    stamp = BigIntegerField(verbose_name='版本')

    class Meta:
        db_table = 'publish_mgt_app_restriction'
        verbose_name = '发布限制表'
        unique_together = ('module_name', 'suite_code')
