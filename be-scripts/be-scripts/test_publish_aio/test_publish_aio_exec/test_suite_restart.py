import json
import time

from test_publish_aio.test_publish_aio_exec.models import PublishMgtAppRestriction
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_popen, exec_remote_cmd, \
    exec_local_cmd_by_sshpass_tomcat
from test_publish_aio.test_suite_init_constants import TypeEnum
from settings import logger, PUBLISH_TOOL
from settings import PIPELINE_TEST_PUBLISH, TEST_PUBLISH_AIO
from concurrent.futures import ThreadPoolExecutor, as_completed


class Starter:
    def __init__(self, type_enum=TypeEnum.INIT):
        self.type_enum = type_enum
        self.publish_node_ip = PUBLISH_TOOL['node_ip']

    def docker_pods_restart(self, node_docker, container_name_list):
        pods_build_cmd = 'python3.x {} {} {}'.format(PIPELINE_TEST_PUBLISH['docker_pods_build_cmd'],
                                                     node_docker,
                                                     ",".join(container_name_list))
        # logger.info(pods_build_cmd)
        exec_local_popen(pods_build_cmd)
        time.sleep(10)
        """pods重启"""
        pods_restart_cmd = 'python3.x {} {} {}'.format(PIPELINE_TEST_PUBLISH['docker_pods_restart_cmd'],
                                                       node_docker,
                                                       ",".join(container_name_list))
        # logger.info(pods_restart_cmd)
        exec_local_popen(pods_restart_cmd)

    def deploy_single_app(self, node_docker, container_name):
        param = '{{"suite_code": "{}", "app_name": "{}", "res_type":"pkg", "opt_type":"deploy"}}'.format(node_docker,
                                                                                                         container_name)
        publish_cmd = "python3.x {} '''{}'''".format(PUBLISH_TOOL['publish_cmd'], param)

        exec_remote_cmd(self.publish_node_ip, publish_cmd)

    def stop_single_app(self, node_docker, container_name):
        param = '{{"suite_code": "{}", "app_name": "{}", "res_type":"pkg", "opt_type":"stop"}}'.format(node_docker,
                                                                                                       container_name)
        publish_cmd = "python3.x {} '''{}'''".format(PUBLISH_TOOL['publish_cmd'], param)

        exec_remote_cmd(self.publish_node_ip, publish_cmd)

    def docker_pods_restart_by_paramiko(self, node_docker, container_name_list):
        query = (PublishMgtAppRestriction.select(PublishMgtAppRestriction.module_name).where(
            PublishMgtAppRestriction.suite_code == node_docker))
        restricted_modules = []
        if query.exists():
            # 获取查询结果中的module_name列表
            restricted_modules = [item.module_name for item in query]
            # 从container_name_list中排除restricted_modules中的模块
            container_name_list = [name for name in container_name_list if name not in restricted_modules]
            logger.info(f"排除受限制的模块后的应用列表: {container_name_list}")

        with ThreadPoolExecutor(
                max_workers=int(TEST_PUBLISH_AIO['thread_pool_executor_workers'])) as executor:
            logger.info("--------------多线程并行执行停止和部署应用-----------------并发线程数：{}".format(
                TEST_PUBLISH_AIO['thread_pool_executor_workers']))
            
            # 合并停止和部署任务
            future_to_app = {}
            # 添加停止任务
            for container_name in restricted_modules:
                future = executor.submit(self.stop_single_app, node_docker, container_name)
                future_to_app[future] = (node_docker, container_name, 'stop')
            # 添加部署任务
            for container_name in container_name_list:
                future = executor.submit(self.deploy_single_app, node_docker, container_name)
                future_to_app[future] = (node_docker, container_name, 'deploy')

            # 统一处理所有任务的完成情况
            for future in as_completed(future_to_app):
                app_info = future_to_app[future]
                operation = '停止' if app_info[2] == 'stop' else '部署'
                try:
                    future.result()
                    logger.info(f"{operation}应用 {app_info[1]} 成功")
                except Exception as exc:
                    logger.error(f"{operation}应用 {app_info[1]} 失败：{exc}")


    def vm_restart(self, vm_ip, app_info_list):
        # 杀进程
        kill_cmd_list = []
        start_cmd_list = []
        for row in app_info_list:
            # logger.info(row)
            # 虚机重启改用tomcat_name 20220506 by fwm
            if row["tomcat_name"] and not row["tomcat_name"].isspace():
                if row["tomcat_name"].endswith("/"):
                    kill_cmd_list.append("pkill -9 -f {}".format(row["tomcat_name"]))
                else:
                    kill_cmd_list.append("pkill -9 -f {}/".format(row["tomcat_name"]))
            start_cmd_list.append("sh {}".format(row["start_script_path"]))
        # logger.info("执行杀进程命令 {}".format(kill_cmd_list))
        exec_remote_cmd(vm_ip, " && ".join(kill_cmd_list))
        # logger.info("执行启动命令 {}".format(start_cmd_list))
        # 限制一次启动数量
        start_num = 2
        for i in range(0, len(start_cmd_list), start_num):
            exec_remote_cmd(vm_ip, " && ".join(start_cmd_list[i:i + start_num]))

    def start_check(self):
        """ 启动检查，暂时无法实现，先等60S"""
        time.sleep(60)

    def batch_app(self, app_dict):
        """
        分批处理
        :param app_dict:
        :return:
        """
        batch_dict = {}
        level = 1000
        for module_name in app_dict:
            for row in app_dict[module_name]:
                if row["start_level"] < level:
                    level = row["start_level"]
                if row["start_level"] in batch_dict:
                    batch_dict[row["start_level"]].append(row)
                else:
                    batch_dict[row["start_level"]] = [row]
        return level, batch_dict

    def start_type_class(self, app_info):
        """
        启动分类
        :param app_info:
        :return:
        """
        docker_dict = {}
        vm_dict = {}
        for row in app_info:
            if row["deploy_type"] == 1:
                if row["active_node_ip"] in vm_dict:
                    vm_dict[row["active_node_ip"]].append(row)
                else:
                    vm_dict[row["active_node_ip"]] = [row]
            elif row["deploy_type"] == 2:
                if row["node_docker"] in docker_dict:
                    if row["module_name"] in docker_dict[row["node_docker"]]:
                        continue
                    docker_dict[row["node_docker"]].append(row["module_name"])
                else:
                    docker_dict[row["node_docker"]] = [row["module_name"]]
        # logger.info(docker_dict)
        # logger.info(vm_dict)
        return docker_dict, vm_dict

    def restart(self, app_dict):
        """
        执行重启操作
        :param app_dict:
        :return:
        """

        level, batch_dict = self.batch_app(app_dict)

        # 彻底优化重启的逻辑（原来的太复杂了）zt@2021-01-08
        logger.info("========== 我是分割线 ==========")
        logger.info("重启总列表：{}".format(json.dumps(batch_dict, indent=4, sort_keys=True)))
        for k in sorted(batch_dict):
            logger.info("========== 重启等级『{}』开始处理 ==========".format(k))
            v = batch_dict[k]
            if k < 0 and self.type_enum == TypeEnum.INIT:
                msg = "「{}」跳过配置化重启，级别『{}』，应用：{}".format(self.type_enum.cn_name, k,
                                                                   json.dumps(v, indent=4, sort_keys=True))
                logger.info(msg)
                continue

            # 核心逻辑不变
            docker_dict, vm_dict = self.start_type_class(v)
            for node_ip in vm_dict:
                self.vm_restart(node_ip, vm_dict[node_ip])
            for node_docker in docker_dict:
                # self.docker_pods_restart(node_docker, docker_dict[node_docker])
                self.docker_pods_restart_by_paramiko(node_docker, docker_dict[node_docker])
            # self.start_check()


if __name__ == "__main__":
    param = '{{"suite_code": "{}", "app_name": "{}", "res_type":"pkg", "opt_type":"deploy"}}'.format('it29',
                                                                                                     'acc-center-web')
    publish_cmd = "python3.x {} '''{}'''".format(PUBLISH_TOOL['publish_cmd'], param)
    print(publish_cmd)
    # app_dict = {'Zookeeper': [{'module_name': 'Zookeeper', 'deploy_type': 2, 'active_node_ip': None, 'node_docker':
    # 'tms18', 'container_name': 'Zookeeper', 'start_script_path': '/usr/local/zookeeper-3.4.6/restart.sh',
    # 'start_level': 0}, {'module_name': 'Zookeeper', 'deploy_type': 1, 'active_node_ip': '**************',
    # 'node_docker': None, 'container_name': 'Zookeeper', 'start_script_path':
    # '/usr/local/zookeeper-3.4.6/restart.sh', 'start_level': 0}], 'ActiveMQ': [{'module_name': 'ActiveMQ',
    # 'deploy_type': 2, 'active_node_ip': None, 'node_docker': 'tms18', 'container_name': 'ActiveMQ',
    # 'start_script_path': '/home/<USER>/img-factory-online/tms18/ActiveMQ/ActiveMQ/bin/startup.sh', 'start_level':
    # 0}], 'Elasticsearch': [{'module_name': 'Elasticsearch', 'deploy_type': 2, 'active_node_ip': None,
    # 'node_docker': 'tms18', 'container_name': 'Elasticsearch', 'start_script_path':
    # '/home/<USER>/img-factory-online/tms18/Elasticsearch/Elasticsearch/bin/startup.sh', 'start_level': 0}],
    # 'Kafka': [{'module_name': 'Kafka', 'deploy_type': 2, 'active_node_ip': None, 'node_docker': 'tms18',
    # 'container_name': 'Kafka', 'start_script_path':
    # '/home/<USER>/img-factory-online/tms18/Kafka/Kafka/bin/startup.sh', 'start_level': 0}], 'Redis': [{
    # 'module_name': 'Redis', 'deploy_type': 2, 'active_node_ip': None, 'node_docker': 'tms18', 'container_name':
    # 'Redis', 'start_script_path': '/home/<USER>/img-factory-online/tms18/Redis/Redis/bin/startup.sh',
    # 'start_level': 0}], 'dubbo-admin': [{'module_name': 'dubbo-admin', 'deploy_type': 2, 'active_node_ip': None,
    # 'node_docker': 'tms18', 'container_name': 'dubbo-admin', 'start_script_path': '没有脚本路径', 'start_level': 0}]}
    # starter = Starter() # starter.restart(app_dict) # list = [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,17,20,
    # 21,22,23] # for i in range(0,len(list),10): #     print(i) #     print(list[i:i+10])
    #
    # aa = "/data/dd/ee/gg".split("/")
    # if aa[-1]:
    #     print("/".join(aa[:-1]))
    # else:
    #     print("/".join(aa[:-2]))
    # # print (list[0:2])
    # list = [
    #     {
    #         "active_node_ip": "",
    #         "container_name": "batch-center-remote",
    #         "deploy_type": 2,
    #         "module_name": "batch-center-remote",
    #         "node_docker": "tms15",
    #         "start_level": 2,
    #         "start_script_path": "/home/<USER>/img-factory-online/tms15/batch-center-remote/batch-center-remote/bin/startup.sh"
    #     },
    #     {
    #         "active_node_ip": "",
    #         "container_name": "elasticsearch-center-remote",
    #         "deploy_type": 2,
    #         "module_name": "elasticsearch-center-remote",
    #         "node_docker": "tms15",
    #         "start_level": 2,
    #         "start_script_path": "/home/<USER>/img-factory-online/tms15/elasticsearch-center-remote/elasticsearch-center-remote/bin/startup.sh"
    #     }]
    # starter.vm_restart('*******', list)
