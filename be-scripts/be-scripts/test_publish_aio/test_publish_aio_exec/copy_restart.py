import os
import sys
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from common.ext_cmd.ssh.sftp import SCP
from dao.connect.mysql import DBConnectionManager


def copy_restart_script(node_ip):
    pas = PublishTestRepos("tms18", "/data/env_init/restart_script")
    # logger.info(pas.cache_data_code)

    # for row in pas:
    #     if row.deploy_type == 1:
    #         node_ip = row.active_node_ip
    #         print("mkdir /data/env_init/restart_script/{}".format(row.module_name))
    #         os.system("mkdir /data/env_init/restart_script/{}".format(row.module_name))


    sql = '''select module_name from app_mgt_test_template where template_id= 9;'''
    #test_publish_data_list = []

    de_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        de_list.append(row["module_name"])
    #
    # with SSHConnectionManager("**************", "tomcat", "howbuy2015") as ssh:
    #     scp = SCP(ssh.SFTP)
    #     for row in pas:
    #         if row.deploy_type == 1 and row.module_name in de_list:
    #             print(row.start_script_path)
    #             print("/data/env_init/restart_script/{}/{}".format(row.module_name,row.script_name))
    #
    #             scp.get_file(row.start_script_path, "/data/env_init/restart_script/{}/{}".format(row.module_name,row.script_name))
        #     os.system("mkdir /data/env_init/restart_script/{}".format(row.module_name))
        # scp.push_dir("/data/env_init/docker/tomcat", "/home/<USER>/img-factory-online/test_tms/test")



    with SSHConnectionManager(node_ip, "tomcat", "howbuy2015") as ssh:
        scp = SCP(ssh.SFTP)
        for row in pas:
            if row.deploy_type == 1 and row.module_name in de_list:
                print(row.start_script_path)
                print("/data/env_init/restart_script/{}/{}".format(row.module_name,row.script_name))

                scp.push_file("/data/env_init/restart_script/{}/{}".format(row.module_name,row.script_name),row.start_script_path)

if __name__ == "__main__":
    copy_restart_script(sys.argv[1])