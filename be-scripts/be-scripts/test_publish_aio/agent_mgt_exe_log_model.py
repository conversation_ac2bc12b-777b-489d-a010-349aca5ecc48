# coding: utf-8
from sqlalchemy import Column, DateTime, String
from sqlalchemy.dialects.mysql import BIGIN<PERSON>
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class AgentMgtExecLog(Base):
    __tablename__ = 'agent_mgt_exec_log'

    id = Column(BIGINT(11), primary_key=True)
    module_name = Column(String(50), nullable=False, comment='应用名')
    branch = Column(String(100), nullable=False, comment='分支')
    agent_type = Column(String(100), nullable=False, comment='agent')
    exec_batch = Column(String(20), nullable=False, comment='批次号')
    status = Column(String(20), nullable=False, comment='状态')
    create_user = Column(String(20), nullable=False, comment='创建人')
    create_time = Column(DateTime, nullable=False, comment='创建时间')
    update_user = Column(String(20), comment='更新人')
    update_time = Column(DateTime, comment='更新时间')
