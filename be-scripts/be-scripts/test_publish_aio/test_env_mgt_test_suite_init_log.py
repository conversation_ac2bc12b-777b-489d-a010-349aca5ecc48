# coding: utf-8
from sqlalchemy import Column, DateTime, JSON, String
from sqlalchemy.dialects.mysql import BIGINT, INTEGER
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()
metadata = Base.metadata


class TestEnvMgtTestSuiteInitLog(Base):
    __tablename__ = 'test_env_mgt_test_suite_init_log'
    __table_args__ = {'comment': 'test_suite_init日志表'}

    id = Column(BIGINT(11), primary_key=True)
    job_build_id = Column(BIGINT(11), comment='jenkins构建编号')
    job_log_id = Column(BIGINT(11), comment='jenkins日志表ID')
    job_http_suite_code = Column(String(100), comment='参数：环境套')
    job_http_opt_type = Column(String(100), comment='参数：操作类型')
    job_http_job_name = Column(String(100), comment='对应的job名')
    job_http_dict = Column(JSON, comment='http请求参数dict')
    job_app_count = Column(INTEGER(4), comment='应用数量')
    job_param_list = Column(JSON, comment='jenkins脚本参数列表')
    job_workspace = Column(String(999), comment='工作空间')
    job_bo_id = Column(BIGINT(11), comment='参数实例ID')
    job_bo_key = Column(String(999), comment='参数实例key')
    job_bo_dict = Column(JSON, comment='参数实例dict')
    job_duration = Column(BIGINT(11), comment='构建耗时（毫秒）')
    job_result = Column(String(100), comment='构建结果')
    job_desc = Column(String(999), comment='构建说明')
    create_user = Column(String(20), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(20), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(20), comment='版本')
