# 测试环境一键初始化（临时测试）
# 第1版 zt@2020-10-16

import os
import sys
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from settings import logger as log

if __name__ == '__main__':

    print(__file__)
    print(os.path.abspath(__file__))
    print(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

    cur_time = datetime.datetime.now()
    cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    log.info(">>>>测试环境一键初始化（临时测试）".format(cur_time_str))
