
from settings import logger
import functools

db_error_code = {"shuai.liu": "报错了啊"}


class DbErrorCollector:
    '''
    1 、支持捕获返回类型为string的异常
    2、支持返回类型为对象，包含stdout属性的异常
    '''
    def __init__(self):
        self.db_error_code = db_error_code
    def __call__(self, func):
        @functools.wraps(func)
        def wrapped_func(*args, **kwargs):
            try:
                # 打印函数参数
                logger.info(f"调用函数 {func.__name__} 参数:")
                logger.info(f"位置参数: {args}")
                logger.info(f"关键字参数: {kwargs}")

                cmd_res = func(*args, **kwargs)
                if type(cmd_res) is str:
                    cmd_res = cmd_res.strip()
                else:
                    cmd_res = ""
                    try:
                        cmd_res = bytes.decode(cmd_res.stdout)
                    except Exception as err:
                        logger.warning("解析数据异常")
                        logger.warning(str(err))
                for catch_error in self.db_error_code:
                    if catch_error in cmd_res:
                        raise Exception("{}: \n{}".format(self.db_error_code[catch_error], cmd_res))
                return cmd_res
            except Exception as err:
                logger.error(str(err))

                raise Exception(str(err))

        return wrapped_func


@DbErrorCollector()
def db_error_catch(a):
    print(a)
    return "tttt test dfdfdff\nfadfafdsfzzz"


if __name__ == '__main__':
    db_error_catch(11)