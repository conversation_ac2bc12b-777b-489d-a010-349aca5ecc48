# 流水线的测试环境发布（常量定义，最底层）
# 第1版 zt@2020-10-19

import os
import sys
import enum

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)


JOB_DETAIL_DESC = "「{}」步骤执行成功"


@enum.unique
class AioStepEnum(enum.Enum):
    POD_STOP = 'pod_stop'
    HTTP_INIT = 'http_init'
    PARSE = 'parse'
    PRE = 'pre'
    APP_PULL = 'app_pull'
    APP_REPLACE = 'app_replace'
    APP_PUSH = 'app_push'
    APP_MAKE_IMG = 'app_make_img'
    CONF_PULL = 'conf_pull'
    CONF_REPLACE = 'conf_replace'
    CONF_ZEUS = 'conf_zeus'
    CONF_PUSH = 'conf_push'
    CONF_CM = 'conf_cm'
    CCMS_DOWNLOAD = 'ccms_download'
    CCMS_REPLACE = 'ccms_replace'
    CCMS_IMPORT = 'ccms_import'
    RECORD = 'record'
    OTHER = 'other'

