from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos
from settings import logger


class DataMover:
    def __init__(self, app_list, suite_code, workspace, cache_data_code=None):
        self.workspace = workspace
        self.app_list = app_list
        self.suite_code = suite_code
        self.cache_data_code = cache_data_code
        self.publish_test_repos = PublishTestRepos(suite_code, workspace, cache_data_code=cache_data_code)

    def pick_out_data(self):
        pass

    def carry_data(self):

        pick_data = self.pick_out_data()
        self.cache_data_code = self.publish_test_repos.cache_data_code
        return pick_data


class OutAppDataMover(DataMover):
    """第三方引用信息搬运"""

    def pick_out_data(self):
        out_app_list = []
        for row in self.publish_test_repos:
            if row.third_party_middleware == 1 and row.module_name in self.app_list:
                out_app_list.append(row.module_name)
        return out_app_list


class DockerAppDataMover(DataMover):
    """ 获取docker类型的应用"""

    def pick_out_data(self):
        docker_app_list = []
        for row in self.publish_test_repos:
            if row.third_party_middleware == 0 and row.deploy_type == 2 and row.module_name in self.app_list:
                docker_app_list.append(row.module_name)
        return docker_app_list


class AppInfoDataMover(DataMover):
    """ 应用信息获取"""

    def pick_out_data(self):
        pull_product_list = []
        for row in self.publish_test_repos:
            if row.third_party_middleware == 0 and row.module_name in self.app_list:
                pull_product_list.append(row)
        return pull_product_list


if __name__ == "__main__":
    #第三方应用列表
    out_app = OutAppDataMover(["batch-center-remote","robot-order-center-remote"], "tms07", "E:\\test")
    logger.info("第三方应用列表{}".format(out_app.carry_data()))
    logger.info(out_app.cache_data_code)
    #docker应用列表
    docker_app = DockerAppDataMover(["batch-center-remote", "robot-order-center-remote"], "tms07", "E:\\test")
    logger.info("第三方应用列表{}".format(docker_app.carry_data()))
    logger.info(out_app.cache_data_code)
    # 搬运应用的信息
    app_info_data = AppInfoDataMover(["asset","cxg","smasset","vendor","sxy","pcfund","pcsmfund"], "tms18", "E:\\test")
    for row in app_info_data.carry_data():
        logger.info("模块名： {}".format(row.module_name))
        logger.info("制品库路径： {}".format(row.lib_repo_path))
        logger.info("配置类型： {}".format(row.zeus_type))
        logger.info("容器名： {}".format(row.container_name))
        logger.info("是否第三方： {}".format(row.third_party_middleware))
        logger.info("包类型： {}".format(row.package_type))
        logger.info("是否整包： {}".format(row.package_full))
        logger.info("是否接入平台： {}".format(row.platform_type))
        #logger.info(row.gitlab_path)
        logger.info("线上版本： {}".format(row.online_br_name))
        logger.info("本地制品临时目录： {}".format(row.lib_cache_repo_path))
        logger.info("部署目录： {}".format(row.deploy_path))
        logger.info("部署IP： {}".format(row.active_node_ip))
        logger.info("部署方式： {}".format(row.deploy_type))
        logger.info("容器节点： {}".format(row.node_docker))
        logger.info("发布配置路径： {}".format(row.deploy_config_path))
        logger.info("启动脚本路径： {}".format(row.start_script_path))
        logger.info("本地配置目录： {}".format(row.lib_cache_config_path))
    logger.info(app_info_data.cache_data_code)
        #break
