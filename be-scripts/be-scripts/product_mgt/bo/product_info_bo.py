from iter_mgt.models import IterInfo
from settings import logger


class ProductInfoBo:
    def __init__(self, builder):
        self.__lib_md5 = builder.lib_md5
        self.__lib_url = builder.lib_url
        self.__lib_size = builder.lib_size
        self.__lib_name = builder.lib_name
        self.__app_name = builder.app_name
        self.__lib_version = builder.lib_version
        self.__iteration_id = builder.iteration_id
        self.__suite_code = builder.suite_code
        self.__lib_type = builder.lib_type
        self.__lib_repo_tool = builder.lib_repo_tool
        self.__code_last_version = builder.code_last_version

    def set_lib_md5(self, lib_md5):
        self.__lib_md5 = lib_md5
        return self

    def set_lib_url(self, lib_url):
        self.__lib_url = lib_url
        return self

    def set_lib_size(self, lib_size):
        self.__lib_size = lib_size
        return self

    def set_code_last_version(self, code_last_version):
        self.__code_last_version = code_last_version
        return self

    @property
    def code_last_version(self):
        return self.__code_last_version

    @property
    def lib_md5(self):
        return self.__lib_md5

    @property
    def lib_url(self):
        return self.__lib_url

    @property
    def lib_size(self):
        return self.__lib_size

    @property
    def lib_name(self):
        return self.__lib_name

    @property
    def app_name(self):
        return self.__app_name

    @property
    def lib_version(self):
        return self.__lib_version

    @property
    def iteration_id(self):
        return self.__iteration_id

    @property
    def suite_code(self):
        return self.__suite_code

    @property
    def lib_type(self):
        return self.__lib_type

    @property
    def lib_repo_tool(self):
        return self.__lib_repo_tool

    class Builder:
        lib_md5: str = ""
        lib_url: str = ""
        lib_size: str = ""
        code_last_version: str = ""
        lib_name: str
        app_name: str
        lib_version: str
        iteration_id: str
        suite_code: str = ""
        lib_type: str
        lib_repo_tool: str

        def set_code_last_version(self, code_last_version):
            self.code_last_version = code_last_version
            return self

        def set_lib_md5(self, lib_md5):
            self.lib_md5 = lib_md5
            return self

        def set_lib_url(self, lib_url):
            self.lib_url = lib_url
            return self

        def set_lib_size(self, lib_size):
            self.lib_size = lib_size
            return self

        def set_lib_name(self, lib_name):
            self.lib_name = lib_name
            return self

        def set_app_name(self, app_name):
            self.app_name = app_name
            return self

        def set_iteration_id(self, iteration_id):
            self.iteration_id = iteration_id
            return self

        def set_suite_code(self, suite_code):
            self.suite_code = suite_code
            return self

        def set_lib_repo_tool(self, lib_repo_tool):
            self.lib_repo_tool = lib_repo_tool
            return self

        def set_lib_type(self, lib_type):
            self.lib_type = lib_type
            return self

        def set_lib_version(self, lib_version):
            self.lib_version = lib_version
            return self

        @staticmethod
        def _get_lib_version(iteration_id: str)-> str:
            iter_info = IterInfo.get(IterInfo.pipeline_id == iteration_id)
            return iter_info.br_name

        def verify_basic_property(self):
            if self.lib_name == "":
                raise Exception("lib_name 不可为空")
            if self.app_name == "":
                raise Exception("app_name 不可为空")
            if self.iteration_id == "":
                raise Exception("iteration_id 不可为空")
            if self.lib_type == "":
                raise Exception("lib_type 不可为空")
            if self.lib_repo_tool == "":
                raise Exception("lib_repo_tool 不可为空")

        def build_bo(self):
            self.verify_basic_property()
            lib_version = self._get_lib_version(self.iteration_id)
            logger.info(lib_version)
            self.set_lib_version(lib_version)
            return ProductInfoBo(self)

