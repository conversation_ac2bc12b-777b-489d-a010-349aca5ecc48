

class LibInfoPo:
    app_name: str
    lib_version: str
    suite_code: str
    iteration_id: str
    lib_type: str
    lib_repo_tool: str

    def __init__(self, app_name: str, lib_version: str, iteration_id: str, lib_type: str,
                 lib_repo_tool: str, suite_code: str=""):
        self.app_name = app_name
        self.lib_version = lib_version
        self.iteration_id = iteration_id
        self.suite_code = suite_code
        self.lib_type = lib_type
        self.lib_repo_tool = lib_repo_tool
