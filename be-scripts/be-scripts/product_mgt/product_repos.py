import os

from enum import Enum
from common.ext_cmd import shell_cmd as common_shell_cmd

from common.ext_cmd.nginx.nginx_repos import NginxRepos
from settings import NGINX_LIB_REPO, logger
from dao.get.mysql.iter_info import get_app_git_path
from product_mgt.bo.product_info_bo import ProductInfoBo
from product_mgt.product_record import ProductRecorder


class ProductRepos:
    __product_recorder = ProductRecorder
    __nginx_repos = NginxRepos
    __nginx_info = NGINX_LIB_REPO

    class LibType(Enum):
        CCN = "ccn"
        SONAR = "sonar"
        P3C = "p3c"
        JUNIT = "junit"
        COMPILE = "compile"
        PACKAGE = "package"

    class LibRepoTool(Enum):
        NGINX = "nginx"
        NEXUS = "nexus"
        GIT = "git"
        SONAR = "sonar"

    def __init__(self):
        pass

    @staticmethod
    def get_code_last_version(code_path):
        get_commit_id_command = 'cd {}; git rev-parse HEAD'.format(code_path)
        stdcode, commit_id = common_shell_cmd.shell_cmd(get_commit_id_command)
        logger.info("获取最后一次提交记录结果为{}，最后一次提交记录为{}".format(stdcode, commit_id))
        if stdcode == 0:
            # 去除回车符
            commit_id = commit_id.replace('\n', '').replace('\r', '')
            return commit_id
        else:
            raise Exception("获取记录失败")

    def upload_file(self, workspace: str, product_info_bo: ProductInfoBo):

        git_path = get_app_git_path(product_info_bo.app_name).replace("/", "")
        logger.info(workspace)
        file_path = os.path.join(workspace, git_path, product_info_bo.lib_name)
        logger.info(file_path)
        remote_path = "{}/{}/{}/{}".format(self.__nginx_info["{}_report_dir".format(product_info_bo.lib_type)],
                                   product_info_bo.app_name,
                                   product_info_bo.iteration_id, product_info_bo.lib_name)
        nginx_repos = self.__nginx_repos()
        lib_info = nginx_repos.upload_file(file_path, remote_path)
        logger.info(lib_info.__dict__)
        code_last_version = self.get_code_last_version(os.path.join(workspace, git_path))
        product_info_bo.set_lib_url(lib_info.lib_rul).\
            set_lib_size(lib_info.lib_size).\
            set_lib_md5(lib_info.lib_md5).set_code_last_version(code_last_version)
        prd = self.__product_recorder()
        prd.record_product_info(product_info_bo)


if __name__ == "__main__":
    workspace = "D:\\qap\\howbuy-qa-info"
    product_info_bo = ProductInfoBo.Builder().set_iteration_id("ftx_0.0.0000"). \
        set_app_name("ftx-console").set_lib_type(ProductRepos.LibType.P3C.value).set_lib_repo_tool(ProductRepos.LibRepoTool.NGINX.value) \
       .set_lib_name("爱码仕-howbuy-wireless-cms-boot.csv").build_bo()
    pr = ProductRepos()
    pr.upload_file(workspace, product_info_bo)


