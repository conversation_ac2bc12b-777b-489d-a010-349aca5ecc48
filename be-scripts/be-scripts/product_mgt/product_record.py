import datetime
from settings import logger
from product_mgt.models import LibInfo, LibInfoDetail
from product_mgt.bo.product_info_bo import ProductInfoBo
from product_mgt.po.lib_info_detail_po import LibInfoDetailBo
from product_mgt.po.lib_info_po import LibInfoPo


class ProductRecorder:

    __lib_info = LibInfo
    __lib_info_detail = LibInfoDetail
    default_user = "howbuyscm"

    def __init__(self):
        pass

    def _insert_product_info(self, product_info_bo: ProductInfoBo):
        current_time = datetime.datetime.now()
        logger.info(product_info_bo.__dict__)
        lib_info_id = self.__lib_info.insert({self.__lib_info_detail.create_time: current_time,
                                              self.__lib_info_detail.create_user: self.default_user,
                                              self.__lib_info_detail.update_time: current_time,
                                              self.__lib_info_detail.update_user: self.default_user,
                                              self.__lib_info.app_name: product_info_bo.app_name,
                                              self.__lib_info.iteration_id: product_info_bo.iteration_id,
                                              self.__lib_info.lib_repo_tool: product_info_bo.lib_repo_tool,
                                              self.__lib_info.lib_type: product_info_bo.lib_type,
                                              self.__lib_info.lib_version: product_info_bo.lib_version,
                                              self.__lib_info.suite_code: product_info_bo.suite_code,
                                              self.__lib_info.code_last_version: product_info_bo.code_last_version}).execute()

        self.__lib_info_detail.insert({self.__lib_info_detail.create_time: current_time,
                                self.__lib_info_detail.create_user: self.default_user,
                                self.__lib_info_detail.update_time: current_time,
                                self.__lib_info_detail.update_user: self.default_user,
                                self.__lib_info_detail.lib_info_id: lib_info_id,
                                self.__lib_info_detail.lib_name: product_info_bo.lib_name,
                                self.__lib_info_detail.lib_size: product_info_bo.lib_size,
                                self.__lib_info_detail.lib_md5: product_info_bo.lib_md5,
                                self.__lib_info_detail.lib_url: product_info_bo.lib_url}).execute()

    def _update_product_info(self, lib_info_id: int, product_info_bo: ProductInfoBo):
        current_time = datetime.datetime.now()
        self.__lib_info.update({self.__lib_info.update_time: current_time,
                                self.__lib_info.update_user: self.default_user,
                                self.__lib_info.code_last_version: product_info_bo.code_last_version})\
            .where(self.__lib_info.id == lib_info_id).execute()
        logger.info(product_info_bo.__dict__)
        self.__lib_info_detail.delete().where(self.__lib_info_detail.lib_info_id == lib_info_id).execute()
        self.__lib_info_detail.insert({self.__lib_info_detail.create_time: current_time,
                                self.__lib_info_detail.create_user: self.default_user,
                                self.__lib_info_detail.update_time: current_time,
                                self.__lib_info_detail.update_user: self.default_user,
                                self.__lib_info_detail.lib_info_id: lib_info_id,
                                self.__lib_info_detail.lib_name: product_info_bo.lib_name,
                                self.__lib_info_detail.lib_size: product_info_bo.lib_size,
                                self.__lib_info_detail.lib_md5: product_info_bo.lib_md5,
                                self.__lib_info_detail.lib_url: product_info_bo.lib_url}).execute()

    def record_product_info(self, product_info_bo: ProductInfoBo):
        lib_info_id = self.__lib_info.select().where((self.__lib_info.iteration_id == product_info_bo.iteration_id),
                   (self.__lib_info.app_name == product_info_bo.app_name),
                   (self.__lib_info.lib_type == product_info_bo.lib_type),
                   (self.__lib_info.suite_code == product_info_bo.suite_code)).get_or_none()
        logger.info("获取到的制品主键id 为{}".format(lib_info_id))
        if lib_info_id is None:
            logger.info("开始插入制品数据")
            self._insert_product_info(product_info_bo)
        else:
            logger.info("开始更新制品数据")
            self._update_product_info(lib_info_id, product_info_bo)

    def get_product_info(self):
        pass

    @classmethod
    def get_request_product_info(cls, app_name, iteration_id, lib_type):
        lib_info_list = cls.__lib_info.select().where(cls.__lib_info.app_name == app_name,
                                               cls.__lib_info.iteration_id == iteration_id,
                                               cls.__lib_info.lib_type == lib_type)
        if lib_info_list:
            lib_info_id = lib_info_list[0].id
        else:
            logger.info("没有制品信息")
            raise Exception("没有制品信息")
        lib_info_list = cls.__lib_info_detail.select().where(cls.__lib_info_detail.lib_info_id == lib_info_id)
        request_dict = {'app_name': app_name, 'iteration_id': iteration_id, 'lib_type': lib_type,
                        'lib_md5': lib_info_list[0].lib_md5, 'lib_url': lib_info_list[0].lib_url}
        return request_dict


if __name__ == "__main__":
    product_info_bo = ProductInfoBo.Builder().set_iteration_id("ftx_0.0.0000"). \
        set_app_name("ftx-console").set_lib_type("p3c").set_lib_repo_tool("nginx") \
        .set_lib_md5("dfdffd").set_lib_name("p3c_ftx-console.csv").set_lib_size("4546456454").set_lib_url("urllruu"). \
        build_bo()
    pmg = ProductRecorder()
    pmg.record_product_info(product_info_bo)
