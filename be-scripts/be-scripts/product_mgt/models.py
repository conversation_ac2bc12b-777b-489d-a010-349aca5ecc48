from dao.base_model import SpiderBaseModels, BaseModel
from peewee import *


class LibInfo(SpiderBaseModels):
    (COMPILE, PACKAGE, CNN, P3C, SONAR, JUNIT) = ('compile', 'package', 'cnn', 'p3c', 'sonar', 'junit')
    LIB_TYPE_CHOICE = (
        (COMPILE, '编译制品'),
        (PACKAGE, '打包制品'),
        (CNN, '圈复杂度报告'),
        (P3C, 'p3c报告'),
        (SONAR, 'sonar报告'),
        (JUNIT, '单元测试报告'),
    )
    (GIT, NEXUS, NGINX, SONAR) = ('git', 'nexus', 'nginx', 'sonar')
    LIB_REPO_TOOL_CHOICE = (
        (GIT, 'git'),
        (NEXUS, 'nexus'),
        (NGINX, 'nginx'),
        (SONAR, 'sonar'),
    )
    app_name = Char<PERSON>ield(verbose_name='应用名', max_length=64)
    lib_version = CharField(verbose_name='制品版本', max_length=128)
    suite_code = CharField(verbose_name='环境', max_length=64, default="")
    iteration_id = CharField(verbose_name='迭代号', max_length=128)
    lib_type = CharField(choices=LIB_TYPE_CHOICE, max_length=8, verbose_name='制品类型')
    lib_repo_tool = CharField(choices=LIB_TYPE_CHOICE, max_length=8, verbose_name='制品库工具')
    code_last_version = CharField(verbose_name='code_last_version', max_length=64)

    class Meta:
        db_table = 'product_mgt_lib_info'
        verbose_name = '制品版本信息（新 为nexus 设计）'


class LibInfoDetail(SpiderBaseModels):
    lib_info_id = IntegerField(verbose_name='运行的lib_info_id')
    lib_md5 = CharField(verbose_name='lib_md5', max_length=64)
    lib_url = CharField(verbose_name='制品路径', max_length=256)
    lib_size = CharField(verbose_name='制品大小', max_length=64)
    lib_name = CharField(verbose_name='制品名', max_length=64)

    class Meta:
        db_table = 'product_mgt_lib_info_detail'
        verbose_name = '制品版本信息（新 为nexus 设计）'


class ProductMgtProductInfo(BaseModel):
    module_name = CharField(verbose_name='应用名', max_length=100)
    lib_repo_url = CharField(verbose_name='制品库地址', max_length=96)
    lib_repo_branch = CharField(verbose_name='制品分支', max_length=64)
    lib_repo_version = CharField(verbose_name='git提交md5信息', max_length=64)
    lib_repo_size = CharField(verbose_name='制品大小', max_length=96)
    create_time = DateTimeField(verbose_name='创建时间')
    iteration_id = CharField(verbose_name='迭代号', max_length=64)
    lib_repo_version_log = CharField(verbose_name='git提交日志', max_length=128)
    suite_code = CharField(verbose_name='环境套', max_length=24)
    package_stamp = CharField(verbose_name='制品的时间戳', max_length=50)

    class Meta:
        db_table = 'product_mgt_product_info'
        verbose_name = '制品信息表'
