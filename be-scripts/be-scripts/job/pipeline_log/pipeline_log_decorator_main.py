import sys
import datetime
import traceback
import enum

from job.pipeline_log.pipeline_log_decorator_constants import PipelineLogTypeEnum
from job.pipeline_log.pipeline_log_decorator_mgt import PipelineLog, BizAutoTestPipelineLog
from settings import logger as log
from pipeline_log_decorator_util import get_time_str


@enum.unique
class StepEnum(enum.Enum):
    PARSE = "parse"
    RUN = "run"
    STATUS = "status"


class TestPipelineLog:
    def __init__(self):
        self.step_dict = {
            StepEnum.PARSE: self.step_parse,
            StepEnum.RUN: self.step_run,
            StepEnum.STATUS: self.step_status
        }

    @BizAutoTestPipelineLog(step_name="step_parse")
    def step_parse(self, step_param):
        log.info(">>>> step: {}".format(step_param))

    @BizAutoTestPipelineLog(step_name="step_run")
    def step_run(self, step_param):
        log.info(">>>> step_param: {}".format(step_param))

    @BizAutoTestPipelineLog(step_name="step_status")
    def step_status(self, step_param):
        log.info(">>>> step: {}".format(step_param))


def pipeline_log_decorator_main(sys_argv):
    if len(sys_argv) > 1:
        step_str = sys_argv[1]
        step_enum = StepEnum(step_str)
        step_func = TestPipelineLog().step_dict[step_enum]
        step_func(sys_argv[1:])
    else:
        log.info(">>>> 无传参：pass。")


if __name__ == '__main__':
    cur_time = datetime.datetime.now()
    log.info(">>>> 流水线日志: {}".format(get_time_str(cur_time)))

    err_msg = None
    try:
        pipeline_log_decorator_main(sys.argv)
        # 正常返回
        sys.exit()
    except ValueError as e:
        # 打印错误信息
        err_msg = "流水线日志，ValueError：{}".format(e)
        log.error(err_msg)
        # 异常返回
        sys.exit(-1)
    except Exception as ex:
        # 记录错误信息
        err_msg = "流水线日志，系统级异常：{}".format(ex)
        log.error(err_msg)
        # 打印错误信息
        traceback.print_exc()
        # 异常返回
        sys.exit(-1)
