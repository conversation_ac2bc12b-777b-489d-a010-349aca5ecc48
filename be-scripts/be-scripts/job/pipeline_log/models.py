from peewee import *
from dao.base_model import BaseModel


class PipelineLogBizAutoTest(BaseModel):
    pipeline_type_name = CharField(max_length=100, verbose_name='pipeline_type_name')
    module_type_name = CharField(max_length=100, verbose_name='module_type_name')
    job_name = CharField(max_length=200, verbose_name='job_name')
    job_build_id = IntegerField(verbose_name="job构建id")
    step_name = CharField(max_length=100, verbose_name='step_name')
    step_order = CharField(max_length=20, verbose_name='step_order')
    params = CharField(max_length=999, verbose_name='请求params')
    s_time = DateTimeField(verbose_name='开始时间')
    e_time = DateTimeField(verbose_name='结束时间')
    cost_time = IntegerField(verbose_name='请求耗时')
    is_except = FloatField(verbose_name='执行是否异常')
    rst = TextField(verbose_name='请求结果')

    class Meta:
        db_table = 'pipeline_log_biz_auto_test'
        verbose_name = '编排线请求日志记录表'


class PipelineLogTestDataInit(BaseModel):
    pipeline_type_name = CharField(max_length=100, verbose_name='pipeline_type_name')
    module_type_name = CharField(max_length=100, verbose_name='module_type_name')
    job_build_id = IntegerField(verbose_name="job构建id")
    step_name = CharField(max_length=200, verbose_name='step_name')
    params = CharField(max_length=999, verbose_name='请求params')
    s_time = DateTimeField(verbose_name='开始时间')
    e_time = DateTimeField(verbose_name='结束时间')
    cost_time = IntegerField(verbose_name='请求耗时')
    is_except = FloatField(verbose_name='执行是否异常')
    rst = TextField(verbose_name='请求结果')

    class Meta:
        db_table = 'pipeline_log_test_data_init'
        verbose_name = '数据初始化线请求日志记录表'


class PipelineLogShiftLeftTest(BaseModel):
    pipeline_type_name = CharField(max_length=100, verbose_name='pipeline_type_name')
    module_type_name = CharField(max_length=100, verbose_name='module_type_name')
    job_build_id = IntegerField(verbose_name="job构建id")
    step_name = CharField(max_length=200, verbose_name='step_name')
    params = CharField(max_length=999, verbose_name='请求params')
    s_time = DateTimeField(verbose_name='开始时间')
    e_time = DateTimeField(verbose_name='结束时间')
    cost_time = IntegerField(verbose_name='请求耗时')
    is_except = FloatField(verbose_name='执行是否异常')
    rst = TextField(verbose_name='请求结果')

    class Meta:
        db_table = 'pipeline_log_shift_left_test'
        verbose_name = '左移流水线日志记录表'

