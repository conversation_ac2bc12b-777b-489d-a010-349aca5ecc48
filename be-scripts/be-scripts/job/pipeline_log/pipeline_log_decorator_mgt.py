import datetime
import functools
import json
import os
import sys
# PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# sys.path.append(PROJECT_DIR)
import time
from functools import partial
from job.pipeline_log.models import PipelineLogBizAutoTest, PipelineLogTestDataInit, PipelineLogShiftLeftTest
from settings import logger as log
from job.pipeline_log.pipeline_log_decorator_constants import PipelineLogTypeEnum


class PipelineLog:
    def __init__(self, step_enum=PipelineLogTypeEnum.PIPELINE_LOG_BASE, step_name=None):
        self.step_enum = step_enum
        self.pipeline_type_name = self.step_enum.pipeline_type_name
        self.module_type_name = self.step_enum.module_type_name
        self.type_desc = self.step_enum.type_desc
        self.step_name = step_name

    def __call__(self, func):
        # return self._decorator(func)
        @functools.wraps(func)
        def wrapped_func(*args, **kwargs):
            log_string = func.__name__ + " was called"
            log.info(log_string)
            s_time = datetime.datetime.now()
            is_except = False
            rst = None
            try:
                rst = func(*args, **kwargs)
            except Exception as e:
                is_except = True
                log.error("Exception: {}".format(e))
                raise e
            finally:
                e_time = datetime.datetime.now()
                self._write_log_to_db(s_time, e_time, rst, is_except, *args, **kwargs)
            return rst

        return wrapped_func

    def _write_log_to_db(self, s_time, e_time, rst, is_except, *args, **kwargs):
        log.info(">>>> PipelineLog >> _write_log_to_db(): 空实现")


class BizAutoTestPipelineLog(PipelineLog):
    def __init__(self, step_enum=PipelineLogTypeEnum.BIZ_AUTO_TEST_RUN, step_name=None):
        super(BizAutoTestPipelineLog, self).__init__(step_enum, step_name)

    def _write_log_to_db(self, s_time, e_time, rst, is_except, *args, **kwargs):
        log.debug(">>>> PipelineLog >> __write_log_to_db()")
        cost_time = round((e_time - s_time).total_seconds(), 2)
        params = {}
        try:
            if len(args[1:]) > 0:
                params = json.dumps(*args[1:])
            else:
                for key, value in vars(*args[0:]).items():
                    params[key] = value
            job_build_id = params.get('job_build_id')
            PipelineLogBizAutoTest.insert(
                {'pipeline_type_name': self.pipeline_type_name, 'module_type_name': self.module_type_name,
                 'job_name': params.get('job_name'),
                 'job_build_id': job_build_id,
                 'step_order': params.get('step_order'),
                 'step_name': self.step_name, 's_time': s_time, "params": params,
                 'e_time': e_time, 'rst': rst,
                 'is_except': is_except, 'cost_time': cost_time}).execute()
        except Exception as e:
            log.error('params---------->:{}'.format(params))
            log.error(e)


class TestDataInitPiplineLog(PipelineLog):
    def __init__(self, step_enum=PipelineLogTypeEnum.TEST_DATA_INIT_STATUS, step_name=None):
        super(TestDataInitPiplineLog, self).__init__(step_enum, step_name)

    def _write_log_to_db(self, s_time, e_time, rst, is_except, *args, **kwargs):
        log.debug(">>>> TestDataInitPiplineLog >> __write_log_to_db()")
        cost_time = round((e_time - s_time).total_seconds(), 2)
        params = {}
        try:
            if len(args[1:]) > 0:
                params = json.dumps(*args[1:])
                log.info(">>>> TestDataInitPiplineLog >> __write_log_to_db() params:{}".format(params))
            else:
                for key, value in vars(*args[0:]).items():
                    params[key] = value
            job_build_id = params.get('job_build_id')
            PipelineLogTestDataInit.insert(
                {'pipeline_type_name': self.pipeline_type_name, 'module_type_name': self.module_type_name,
                 'job_build_id': job_build_id,
                 'step_name': self.step_name, 's_time': s_time, "params": params,
                 'e_time': e_time, 'rst': rst,
                 'is_except': is_except, 'cost_time': cost_time}).execute()
        except Exception as e:
            log.error(e)


class ShiftLeftTestPiplineLog(PipelineLog):
    def __init__(self, step_enum=PipelineLogTypeEnum.SHIFT_LEFT_TEST_STATUS, step_name=None):
        super(ShiftLeftTestPiplineLog, self).__init__(step_enum, step_name)

    def _write_log_to_db(self, s_time, e_time, rst, is_except, *args, **kwargs):
        log.debug(">>>> ShiftLeftTestPiplineLog >> __write_log_to_db()")
        cost_time = round((e_time - s_time).total_seconds(), 2)
        params = {}
        try:
            if len(args[1:]) > 0:
                # 检查参数是否有to_dict方法（如EsStrategyParams对象）
                param_obj = args[1]
                if hasattr(param_obj, 'to_dict'):
                    params_dict = param_obj.to_dict()
                    params = json.dumps(params_dict)
                    log.info(">>>> ShiftLeftTestPiplineLog >> __write_log_to_db() params:{}".format(params))
                    job_build_id = params_dict.get('job_build_id')
                else:
                    params = json.dumps(*args[1:])
                    log.info(">>>> ShiftLeftTestPiplineLog >> __write_log_to_db() params:{}".format(params))
                    # 尝试解析JSON字符串获取job_build_id
                    try:
                        params_dict = json.loads(params)
                        job_build_id = params_dict.get('job_build_id') if isinstance(params_dict, dict) else None
                    except:
                        job_build_id = None
            else:
                for key, value in vars(*args[0:]).items():
                    params[key] = value
                job_build_id = params.get('job_build_id')
            
            PipelineLogShiftLeftTest.insert(
                {'pipeline_type_name': self.pipeline_type_name, 'module_type_name': self.module_type_name,
                 'job_build_id': job_build_id,
                 'step_name': self.step_name, 's_time': s_time, "params": params,
                 'e_time': e_time, 'rst': rst,
                 'is_except': is_except, 'cost_time': cost_time}).execute()
        except Exception as e:
            log.error(e)