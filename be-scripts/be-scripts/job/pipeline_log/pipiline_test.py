from datetime import time

from job.pipeline_log.pipeline_log_decorator_main import TestPipelineLog, StepEnum
import sys

def rate_limiter(max_requests, time_window):
    def decorator(func):
        counter = 0
        window_start = time.time()

        def limit(*args, **kwargs):
            nonlocal counter, window_start
            current_time = time.time()
            print(current_time)
            print(window_start)
            if current_time - window_start < time_window:
                counter += 1
            else:
                counter = 1
                window_start = current_time

            if counter > max_requests:
                raise Exception("接口访问次数超过限制！")

            return func(*args, **kwargs)

        return limit

    return decorator


@rate_limiter(max_requests=10, time_window=60)  # 每分钟最多访问10次
def api_function():
    print(1)

if __name__ == '__main__':
    # example_function(name='Alice', age=30, city='New York')
    # TestPipelineLog().step_status()
    TestPipelineLog().step_run({"aaa":"bbb"})
    # TestPipelineLog().step_dict[StepEnum.STATUS]('status')
    # example_function(name='Alice', age=30, city='New York')
    # TestPipelineLog().step_status()
    # TestPipelineLog().step_run({"aaa":"bbb"})
    # for _ in range(20):
    #     api_function()
