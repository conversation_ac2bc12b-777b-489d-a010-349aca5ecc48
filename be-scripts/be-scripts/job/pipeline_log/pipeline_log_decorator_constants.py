import enum


TIME_STR = "%Y-%m-%d %H:%M:%S"


@enum.unique
class PipelineLogTypeEnum(enum.Enum):
    # PIPELINE_LOG
    PIPELINE_LOG_BASE = ('PIPELINE_LOG', "BASE", "流水线-基础模块")
    # BIZ_AUTO_TEST
    BIZ_AUTO_TEST_PARSE = ('BIZ_AUTO_TEST', "PARSE", "测试集编排-解析模块")
    BIZ_AUTO_TEST_RUN = ('BIZ_AUTO_TEST', "RUN", "测试集编排-运行模块")
    BIZ_AUTO_TEST_STATUS = ('BIZ_AUTO_TEST', "STATUS", "测试集编排-状态记录模块")
    TEST_DATA_INIT_STATUS = ('TEST_DATA_INIT', "STATUS", "数据初始化线-状态记录模块")
    SHIFT_LEFT_TEST_STATUS = ('SHIFT_LEFT_TEST', "STATUS", "左移流水线-状态记录模块")

    def __init__(self, pipeline_type_name, module_type_name, type_desc):
        self.pipeline_type_name = pipeline_type_name
        self.module_type_name = module_type_name
        self.type_desc = type_desc
