import json
import os
import sys
import datetime
import traceback
import enum

from job.pipeline_log.pipeline_log_decorator_constants import PipelineLogTypeEnum
from job.pipeline_log.pipeline_log_decorator_mgt import PipelineLog, BizAutoTestPipelineLog
from settings import logger as log
from pipeline_log_decorator_util import get_time_str


if __name__ == '__main__':
    cur_time = datetime.datetime.now()
    log.info(">>>> 流水线日志: {}".format(get_time_str(cur_time)))
    sys_argv = sys.argv[1:]
    err_msg = None
    try:
        if len(sys_argv) > 0:
            file_abs_path = sys_argv[0]
            children_id_str = "||1157711941001183189|1157711941001183190|1157711941001183191"
            children_id_list = [int(x) for x in children_id_str.split("|") if x != ""]
            log.info(">>>> children_id_list: {}".format(children_id_list))

            children_path_str = "1157711941001183188::1157711941001183130::1157711941001183189:"
            try:
                children_path_list = [int(x) for x in children_path_str.split("::") if x != ""]
                log.info(">>>> children_path_list: {}".format(children_path_list))
            except ValueError as e:
                # 记录错误信息
                err_msg = "错误ID（非数字）：{}".format(e)
                log.error(err_msg)
                # 打印错误信息
                traceback.print_exc()

        else:
            err_msg = "缺少参数：文件绝对路径。"
            log.error(err_msg)
        # 正常返回
        sys.exit()
    except ValueError as e:
        # 打印错误信息
        err_msg = "流水线日志，ValueError：{}".format(e)
        log.error(err_msg)
        # 异常返回
        sys.exit(-1)
    except Exception as ex:
        # 记录错误信息
        err_msg = "流水线日志，系统级异常：{}".format(ex)
        log.error(err_msg)
        # 打印错误信息
        traceback.print_exc()
        # 异常返回
        sys.exit(-1)
