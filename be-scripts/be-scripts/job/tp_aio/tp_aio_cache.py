# 统一制品缓存 for 测试环境初始化 zt@2022-11-09
# 版本
#   2022-11-09  zt  First release
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/workspaces/PycharmProjects/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/tp_aio_cache/

# ==== 1、环境变量 ====
import os
import sys
print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "tp_aio_cache"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "tp_aio_cache.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
from datetime import datetime
from enum import unique, Enum
import pymysql, json
from pymysql.cursors import DictCursor
from settings import DATABASES, SYNC

CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None
ZONE_CACHE_SUITE_DICT = {}


@unique
class CacheTypeEnum(Enum):
    CACHE_TYPE_ALL = ('cache_all', "统一刷新所有应用")
    CACHE_TYPE_ONE = ('cache_one', "刷新指定应用")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_APP_NONE = ('err_app_none', "无应用的模块信息。")
    ERR_TYPE_LIB_NONE = ('err_lib_none', "制品库信息未配置")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgre的数据连接关闭"""
    conn.close()


def get_cost_time(st, et=None):
    """获取耗时，单位「秒」zt@2022-11-09"""
    ct = 0
    if st:
        if not et:
            et = datetime.now()
            delta = et - st
            ct = delta.seconds + round(delta.microseconds/1000000, 3)
    return ct


def add_err_to_dict(enum, err_group_dict, err_app_br_dict):
    """ 添加错误至「错误字典」zt@2022-11-09"""
    err_app_br_list = err_group_dict.get(enum)
    if not err_app_br_list or len(err_app_br_list) == 0:
        err_node_list = [err_app_br_dict]
        err_group_dict[enum] = err_node_list
    else:
        err_app_br_list.append(err_app_br_dict)


def get_br_name_from_db(conn, module_name):
    """获取应用的「产线 & 归档」版本"""
    # 应用子查询
    module_name_sql = "order by module_name"
    if module_name:
        module_name_sql = "and m.module_name = '{}'".format(module_name)
    # 主查询sql
    # 产线发布取外高桥的发布版本（对用户直接产生影响的版本） 20230313 by fwm
    # 优化使用「分支缓存」统一和加速 zt@2023-08-25
    query_sql = '''
        select m.module_name,
               m.lib_repo,
               c.archive_br AS archive_br_name,
               c.online_br AS online_br_name
        from app_mgt_app_module m
        inner join app_br_cache c on c.app_module_name = m.module_name
        inner join(
            select time_batch,
                   max(batch_number) as max_batch_num
            from app_br_cache
            where time_batch = (select max(time_batch) from app_br_cache)
        )v_max on (v_max.time_batch, v_max.max_batch_num) = (c.time_batch, c.batch_number)
        where c.archive_br is not null
        {}'''.format(module_name_sql)
    log.info(">>>> query_sql：{}".format(query_sql))

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(query_sql)
        result = cursor.fetchall()

    return result


def add_or_upd_lib_cache_by_gitlab(module_name, lib_repo, br_name, path_name, git_cmd_template):
    """新增或更新缓存 zt@2022-11-09"""
    if not os.path.exists(path_name):
        git_cmd = git_cmd_template.format(br_name, lib_repo, path_name)
        log.info(">>>> 新建「{}」分支「{}」的「{}」缓存：{}".format(module_name, br_name, path_name, git_cmd))
        add_st = datetime.now()
        os.system(git_cmd)

        add_cost_time = get_cost_time(add_st)
        log.info(">>>> 新建「{}」分支「{}」的「{}」缓存，耗时：{}秒".format(module_name, br_name, path_name, add_cost_time))
    else:
        os.chdir(path_name)
        log.info(">>>> 刷新「{}」分支「{}」的「{}」缓存：start!".format(module_name, br_name, path_name))
        upd_st = datetime.now()
        os.system("git remote set-branches origin '{}'".format(br_name))
        os.system("git fetch --depth 1 origin '{}'".format(br_name))
        os.system("git checkout '{}'".format(br_name))
        os.system("git pull")

        upd_cost_time = get_cost_time(upd_st)
        log.info(">>>> 刷新「{}」分支「{}」的「{}」缓存，耗时：{}秒".format(module_name, br_name, path_name, upd_cost_time))


def upd_lib_cache_for_archive_and_online(module_name, lib_repo, archive_br_name, online_br_name,
                                         cache_abs_path, git_cmd_template):
    """更新指定应用的「归档 & 线线」缓存"""
    archive_path_name = SYNC['sync_archive_path_name']
    online_path_name = SYNC['sync_online_path_name']
    app_st = datetime.now()

    # 应用缓存目录
    app_path = os.path.join(cache_abs_path, module_name)
    if not os.path.exists(app_path):
        os.system('mkdir -p {}'.format(app_path))

    # 切换应用目录
    os.chdir(app_path)
    # 日志强化（不变更目录）
    date_str = app_st.strftime("%Y-%m-%d")
    time_str = app_st.strftime("%Y-%m-%d %H:%M:%S")
    file_name = '{}.txt'.format(date_str)
    log_start_str = '{}: 「{} (lib_repo: {})」-->「br_name: {} -> {}」'.format(time_str,
                                                                       module_name,
                                                                       lib_repo,
                                                                       archive_br_name,
                                                                       online_br_name)
    os.system("echo '{}' >> {}".format(log_start_str, file_name))
    # 归档目录（可能改变目录）
    add_or_upd_lib_cache_by_gitlab(module_name, lib_repo, archive_br_name, archive_path_name, git_cmd_template)

    # 线上目录（可能改变目录）
    os.chdir(app_path)
    add_or_upd_lib_cache_by_gitlab(module_name, lib_repo, online_br_name, online_path_name, git_cmd_template)
    # 耗时统计（不变更目录，但需要切回应用目录）
    os.chdir(app_path)
    app_cost_time = get_cost_time(app_st)
    log.info("==== 处理应用『{}』总耗时：{}秒。\n\n".format(module_name, app_cost_time))
    log_end_str = '\t-->「{}」秒'.format(app_cost_time)
    os.system("echo '{}' >> {}".format(log_end_str, file_name))


def upd_lib_cache(app_br_list):
    """根据应用的「归档 & 产线」分支，更新缓存"""
    err_group_dict = {}
    if app_br_list:
        cache_path_name = '{}_{}'.format(SYNC['tp_aio_lib_cache_prefix'], SYNC['sync_env_flag'])
        cache_abs_path = os.path.join(SYNC['sync_root_path'], cache_path_name)
        gitlab_group_url = '{}@{}:{}'.format(SYNC['sync_gitlab_user'],
                                             SYNC['sync_gitlab_domain'],
                                             SYNC['sync_gitlab_group'])
        log.info(">>>> 缓存根目录：cache_abs_path = {}".format(cache_abs_path))
        log.info(">>>> gitlab地址：gitlab_group_url = {}".format(gitlab_group_url))

        git_cmd_template = 'git clone -b {{}} --depth 1 {}/{{}}.git {{}}'.format(gitlab_group_url)

        for app_br_dict in app_br_list:
            module_name = app_br_dict.get('module_name')
            lib_repo = app_br_dict.get('lib_repo')
            archive_br_name = app_br_dict.get('archive_br_name')
            online_br_name = app_br_dict.get('online_br_name')
            # 数据检查
            if not module_name:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_APP_NONE, err_group_dict, app_br_dict)
                continue
            if not lib_repo:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_LIB_NONE, err_group_dict, app_br_dict)
                continue

            # 默认分支号
            if not online_br_name:
                online_br_name = 'master'
            if not archive_br_name:
                archive_br_name = online_br_name

            # 处理应用缓存
            upd_lib_cache_for_archive_and_online(module_name, lib_repo, archive_br_name, online_br_name,
                                                 cache_abs_path, git_cmd_template)

    return err_group_dict


def main(param_cache_type_enum, module_name):
    """「节点对账」主方法 zt@2022-11-09"""
    log.info("======== 开始同步 time(start) {} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    if param_cache_type_enum == CacheTypeEnum.CACHE_TYPE_ALL:
        log.info("==== 『全量更新』：{}".format(param_cache_type_enum.type_desc))
        module_name = None
    else:
        log.info("==== 『单一更新』：{}".format(module_name))
    # 1、获取「归档 & 产线版本」
    scm_conn = connect_scm_mysql()

    try:
        query_st = datetime.now()
        app_br_list = get_br_name_from_db(scm_conn, module_name)

        query_cost_time = get_cost_time(query_st)
        app_br_count = len(app_br_list)
        log.info("==== 查询到『{}』笔数据，sql查询耗时：{}秒。".format(app_br_count, query_cost_time))

        if not app_br_list:
            raise ValueError(">>>> 没有查询到「应用」或对应的「版本」信息，应用名：{}".format(module_name))
        upd_st = datetime.now()
        err_group_dict = upd_lib_cache(app_br_list)

        upd_cost_time = get_cost_time(upd_st)
        log.info("==== 从gitlab获取『{}』笔数据，gitlab获取总耗时：{}秒。".format(app_br_count, upd_cost_time))

        for err_enum in err_group_dict:
            err_desc = err_enum.err_desc
            err_list = err_group_dict[err_enum]
            log.info(">>>> err_desc = {}：{}笔".format(err_desc, len(err_list)))
            log.info(">>>> err_list = {}".format(json.dumps(err_list, sort_keys=True, indent=4, ensure_ascii=False)))

    finally:
        conn_close(scm_conn)


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_cache_type_enum = CacheTypeEnum.CACHE_TYPE_ALL
    req_cache_module_name = None
    if len(sys.argv) > 1:
        req_cache_module_name = sys.argv[1]
        if req_cache_module_name.upper() == 'ALL':
            req_cache_type_enum = CacheTypeEnum.CACHE_TYPE_ALL
        else:
            req_cache_type_enum = CacheTypeEnum.CACHE_TYPE_ONE
    if len(sys.argv) > 2:
        log.error("==== ！！！！参数过多！！！！ ====")
        err_msg = ">>>> 一次只能传入一个「应用」！！！"
        log.error(err_msg)
        exit(1)
    try:
        main(req_cache_type_enum, req_cache_module_name)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        cost_time = get_cost_time(s_time)
        log.info("== 执行最终耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
