import sys
import os


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import URL_DEF, logger
from common.routers.router import Router
from utils.check.validator import check_link
from utils.publish import publish_apply, rollback_apply
from test_mgt import test_info_collect, db_info_collect
from job.iter_mgt.iter_archive import ArchiveApplyForm
from job.db_mgt.test_sql_iter_archive import TestSqlArchiveApplyForm
from job.db_mgt import quick_sql_iter_archive
from common.mysql import mysql_binlog


urls = {
        "publish_apply_check": check_link,
        "mobile_publish_apply_check": check_link,
        "publish_check": check_link,
        "archive_check": check_link,
        "compile_check": check_link,
        "package_check": check_link,
        "publish_apply": publish_apply.PublishApplyForm.as_view,
        "rollback_apply": rollback_apply.RollbackApplyForm.as_view,
        "archive": ArchiveApplyForm.as_view,
        "app_commit_check": check_link,
        "app_merge_check": check_link,
        "test_info_collect": test_info_collect.TestInfoCollectForm.as_view,
        "db_info_collect": db_info_collect.DbInfoCollectForm.as_view,
        "test_sql_archive": TestSqlArchiveApplyForm.as_view,
        "quick_sql_archive": quick_sql_iter_archive.QuickSqlArchiveApplyForm.as_view
}


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    rt = Router(urls, url_def=URL_DEF)
    rt.dispatch(sys.argv[1])
    #rt.dispatch(75)

