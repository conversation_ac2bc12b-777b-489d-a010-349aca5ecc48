# 基于Jenkins定时任务的磁盘清理 zt@2024-04-28
# 版本
#   2024-04-28  zt  First release
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/workspaces/PycharmProjects/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/disk_clean/
#   mkdir -p ~/data/ztst_logs/disk_clean/
# 清理策略：
#     schedule_cdc 保留7天
#     tp_aio_cache 保留1天
#     test_suite_init 保留30天
#     iter_publish_scheduler 保留7天
#     流帅线当前状态更新 保留1天
#     app_android_template 保留30天
#     clock_warning_robot_every10min 保留7天

# ==== 1、环境变量 ====
import os
import sys
import traceback
print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "disk_clean"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "jenkins_master_clean.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
import datetime
from enum import unique, Enum

JOBS_ROOT_DIR = "/data/jenkins_ws/jobs"
CLEAN_DICT = {
    "schedule_cdc": 7,
    "tp_aio_cache": 1,
    "test_suite_init": 30,
    "iter_publish_scheduler": 7,
    "流帅线当前状态更新": 1,
    "app_android_template": 30,
    "clock_warning_robot_every10min": 7,
}


@unique
class DiskCleanEnum(Enum):
    DISK_CLEAN_ALL = ('disk_clean_all', "清理Jenkins主节点的所有自定义目录")
    DISK_CLEAN_ONE = ('disk_clean_one', "清理Jenkins主节点的指定自定义目录")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_APP_NONE = ('err_app_none', "无应用的模块信息。")
    ERR_TYPE_LIB_NONE = ('err_lib_none', "制品库信息未配置")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def get_cost_time(st, et=None):
    """获取耗时，单位「秒」zt@2022-11-09"""
    ct = 0
    if st:
        if not et:
            et = datetime.datetime.now()
        ct = round((et - st).total_seconds(), 2)
    return ct


def main(param_disk_clean_enum, param_pipeline_name):
    """「磁盘清理」主方法 zt@2024-04-28"""
    log.info("======== 开始清理 time(start) {} ========".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    if param_disk_clean_enum == DiskCleanEnum.DISK_CLEAN_ALL:
        log.info("==== 『全量清理』：{}".format(param_disk_clean_enum.type_desc))
        module_name = None
    else:
        log.info("==== 『单一清理』：{}".format(param_pipeline_name))
    # 1、获取「归档 & 产线版本」
    s_time = datetime.datetime.now()
    try:
        for key, values in CLEAN_DICT.items():
            st = datetime.datetime.now()
            # log.info(">>>> key = {}".format(key))
            # log.info(">>>> value = {}".format(values))
            pipeline_dir = os.path.join(JOBS_ROOT_DIR, key, "builds")
            if os.path.exists(pipeline_dir):
                cmd = "find {} -maxdepth 1 -mtime +{} | xargs rm -rf".format(pipeline_dir, values)
                log.info(">>>> cmd = {}".format(cmd))
                if len(pipeline_dir) > 20 and pipeline_dir.endswith("builds"):
                    os.system(cmd)
                else:
                    log.warning(">>>> 需要清理的目录太短：{}".format(key))

                ct = get_cost_time(st)
                log.info(">>>> 清理「{}」目录，耗时{}秒".format(key, ct))
            else:
                log.warning(">>>> 目录不存在，跳过清理。：{}".format(pipeline_dir))
    finally:
        cost_time = get_cost_time(s_time)
        log.info(">>>> 本次目录清理「总」耗时：{}秒".format(cost_time))


if __name__ == '__main__':
    """主入口，先判断参数"""
    req_start_time = datetime.datetime.now()
    # 请求参数处理
    req_disk_clean_enum = DiskCleanEnum.DISK_CLEAN_ALL
    req_pipline_name = "ALL"
    if len(sys.argv) >= 1:
        if len(sys.argv) > 1:
            req_pipline_name = sys.argv[1]
        if req_pipline_name.upper() == 'ALL':
            req_disk_clean_enum = DiskCleanEnum.DISK_CLEAN_ALL
        else:
            req_disk_clean_enum = DiskCleanEnum.DISK_CLEAN_ONE
    if len(sys.argv) > 2:
        log.error("==== ！！！！参数过多！！！！ ====")
        err_msg = ">>>> 一次只能传入一个「应用」！！！"
        log.error(err_msg)
        exit(1)
    try:
        main(req_disk_clean_enum, req_pipline_name)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        traceback_str = traceback.format_exc()
        err_msg = ">>>> 执行异常(Exception) {}".format(traceback_str)
        log.error(err_msg)
        exit(1)
    finally:
        req_cost_time = get_cost_time(req_start_time)
        log.info("== 执行最终耗时（秒）：{}".format(req_cost_time))
        log.info("====================")
        log.info("====================\n\n")
