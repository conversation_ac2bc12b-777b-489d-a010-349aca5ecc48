# 基于Jenkins定时任务的「ramdisk」清理 zt@2024-10-14
# 版本
#   2024-10-14  zt  First release
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/workspaces/PycharmProjects/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/ramdisk_clean/
#   mkdir -p ~/data/ztst_logs/ramdisk_clean/
# 清理策略：
#     jkp-s1    /ramdisk/data/jenkins_ws_s1/workspace
#     jkp-s2    /ramdisk/data/jenkins_ws_s2/workspace


# ==== 1、环境变量 ====
import os
import sys
import traceback
print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler


FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "ramdisk_clean"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "jenkins_ramdisk_clean.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
import datetime
from enum import unique, Enum
import pymysql
from pymysql.cursors import DictCursor
import json
from settings import DATABASES, JAVA_BUILD_CONF


RAMDISK_CLEAN_HOURS = JAVA_BUILD_CONF["ramdisk_clean_hours"]

JOBS_ROOT_DIR = "/data/jenkins_ws/jobs"
CLEAN_DICT = {
    "schedule_cdc": 7,
    "tp_aio_cache": 1,
    "test_suite_init": 30,
    "iter_publish_scheduler": 7,
    "流帅线当前状态更新": 1,
    "app_android_template": 30,
    "clock_warning_robot_every10min": 7,
}

CLEAN_WORKSPACE_DIR_DICT = {
    "jenkins_ws_s1": "/ramdisk/data/jenkins_ws_s1/workspace",
    "jenkins_ws_s2": "/ramdisk/data/jenkins_ws_s2/workspace",
}

@unique
class RamDiskCleanEnum(Enum):
    RAM_DISK_CLEAN_ALL = ('ram_disk_clean_all', "清理此执行机下，所有Jenkins的ramdisk工作空间")
    RAM_DISK_CLEAN_ONE = ('ram_disk_clean_one', "清理此执行机下，指定Jenkins的ramdisk工作空间")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_APP_NONE = ('err_app_none', "无应用的模块信息。")
    ERR_TYPE_LIB_NONE = ('err_lib_none', "制品库信息未配置")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            json_str = obj.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(obj, datetime.date):
            json_str = obj.strftime("%Y-%m-%d")
        else:
            json_str = json.JSONEncoder.default(self, obj)

        return json_str

def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgre的数据连接关闭"""
    conn.close()

def get_cost_time(st, et=None):
    """获取耗时，单位「秒」zt@2022-11-09"""
    ct = 0
    if st:
        if not et:
            et = datetime.datetime.now()
        ct = round((et - st).total_seconds(), 3)
    return ct


def get_last_build_time_from_db(conn):
    """从日志中获取迭代应用的最后构建时间。zt@2024-10-14"""
    query_sql = '''
        select log_m.iteration_id,
               log_m.app_name,
               MAX(log_m.start_at) last_build_time
        from pipeline_log_main log_m
        where log_m.start_at > DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        group by log_m.iteration_id, log_m.app_name
        order by log_m.iteration_id, log_m.app_name
    '''.format()
    log.info(">>>> query_sql：{}".format(query_sql))

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(query_sql)
        result = cursor.fetchall()

    return result


def get_last_build_time_dict(last_build_time_list):
    last_build_time_dict = {}
    for last_build_time_obj in last_build_time_list:
        iteration_id = last_build_time_obj.get('iteration_id')
        app_name = last_build_time_obj.get('app_name')
        last_build_time = last_build_time_obj.get('last_build_time')

        if iteration_id and app_name and last_build_time:
            key = "{}_{}".format(iteration_id, app_name)
            last_build_time_dict[key] = last_build_time

    return last_build_time_dict


def clean_ram_disk(ram_disk_path, last_build_time_dict):
    """清理ramdisk工作空间 zt@2024-10-14"""
    log.info("==== 开始清理 ram_disk_path：{} ====".format(ram_disk_path))
    try:
        if not ram_disk_path or not os.path.exists(ram_disk_path):
            raise Exception("ram_disk_path路径不存在：{}".format(ram_disk_path))

        if not last_build_time_dict:
            raise Exception("完成没有查询到最近的构建信息！")
        else:
            log.info(">>>> last_build_time_dict = {}".format(json.dumps(last_build_time_dict,
                                                                        cls=DateEncoder,
                                                                        sort_keys=True,
                                                                        indent=4,
                                                                        ensure_ascii=False)))

        file_name_list = os.listdir(ram_disk_path)
        curr_time = datetime.datetime.now()
        for file_name in file_name_list:
            file = os.path.join(ram_disk_path, file_name)
            log.info(">>>> file = {}".format(file))
            log.info(">>>> isdir = {}".format(os.path.isdir(file)))
            log.info(">>>> islink = {}".format(os.path.islink(file)))
            if os.path.isdir(file) or os.path.islink(file):
                job_name = file_name.split('@')[0]
                log.info(">>>> 工作空间「{}」下的目录「{}」，对应的工作job：{}".format(ram_disk_path, file, job_name))

                if last_build_time_dict:
                    last_build_time = last_build_time_dict.get(job_name)
                    log.info(">>>>  对应的工作job「{}」，最后构建时间：{}。".format(job_name, last_build_time))
                    if last_build_time:
                        hours_diff = (curr_time - last_build_time).total_seconds() / 3600
                        log.info(">>>>  对应的工作job「{}」，间隔小时：{}。".format(job_name, hours_diff))
                        if hours_diff <= float(RAMDISK_CLEAN_HOURS):
                            log.info(
                                ">>>> 对应的工作job「{}」，最新构建的间隔时间：{}小时，跳过清理".format(job_name, hours_diff))
                            continue
            # 不跳过就清理：
            job_path = os.path.join(ram_disk_path, file)
            log.info(">>>> 清理job的工作空间：{}".format(job_path))
            clean_job_dir(job_path)
    except Exception as e:
        exec_err_msg = ">>>> 清理缓存盘出错（跳过不中断）：{}".format(e)
        log.error(exec_err_msg)


def clean_job_dir(job_path):
    if job_path:
        if len(job_path.split("/")) < 3:
            raise Exception("jenkins_job路径格式错误: 少于两层目录，因安全限定无法清理。")
        # 工作空间里不能随便放文件，会自动清理掉。
        if os.path.isfile(job_path):
            cmd = "rm -rf {}".format(job_path)
        else:
            cmd = "rm -rf {}/*".format(job_path)
        os.system(cmd)


def main(param_disk_clean_enum, param_pipeline_name):
    """「ramdisk清理」主方法 zt@2024-10-14"""
    log.info("======== 开始清理 time(start) {} ========".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    if param_disk_clean_enum == RamDiskCleanEnum.RAM_DISK_CLEAN_ALL:
        log.info("==== 『全量清理』：{}".format(param_disk_clean_enum.type_desc))
    else:
        log.info("==== 『单一清理』：{}".format(param_pipeline_name))

    scm_conn = connect_scm_mysql()

    try:
        query_st = datetime.datetime.now()
        last_build_time_list = get_last_build_time_from_db(scm_conn)
        query_cost_time = get_cost_time(query_st)
        last_build_time_count = len(last_build_time_list)
        log.info("==== 查询到『{}』笔数据，sql查询耗时：{}秒。".format(last_build_time_count, query_cost_time))
        # 只有近期构建过的job，才有可能不会被清理。
        last_build_time_dict = get_last_build_time_dict(last_build_time_list)

        clean_st = datetime.datetime.now()
        for ws_path in CLEAN_WORKSPACE_DIR_DICT.values():
            clean_ram_disk(ws_path, last_build_time_dict)

        clean_cost_time = get_cost_time(clean_st)
        log.info("==== 清理所有的ramdisk耗时：{}秒。".format(clean_cost_time))

    except Exception as e:
        exec_err_msg = ">>>> 清理缓存盘出错：{}".format(e)
        log.error(exec_err_msg)
        raise e
    finally:
        conn_close(scm_conn)


if __name__ == '__main__':
    """主入口，先判断参数"""
    req_start_time = datetime.datetime.now()
    # 请求参数处理
    req_ram_disk_clean_enum = RamDiskCleanEnum.RAM_DISK_CLEAN_ALL
    req_pipline_name = "ALL"
    if len(sys.argv) >= 1:
        if len(sys.argv) > 1:
            req_pipline_name = sys.argv[1]
        if req_pipline_name.upper() == 'ALL':
            req_ram_disk_clean_enum = RamDiskCleanEnum.RAM_DISK_CLEAN_ALL
        else:
            req_ram_disk_clean_enum = RamDiskCleanEnum.RAM_DISK_CLEAN_ONE
    if len(sys.argv) > 2:
        log.error("==== ！！！！参数过多！！！！ ====")
        err_msg = ">>>> 一次只能传入一个「应用」！！！"
        log.error(err_msg)
        exit(1)
    try:
        main(req_ram_disk_clean_enum, req_pipline_name)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        traceback_str = traceback.format_exc()
        err_msg = ">>>> 执行异常(Exception) {}".format(traceback_str)
        log.error(err_msg)
        exit(1)
    finally:
        req_cost_time = get_cost_time(req_start_time)
        log.info("== 执行最终耗时（秒）：{}".format(req_cost_time))
        log.info("====================")
        log.info("====================\n\n")
