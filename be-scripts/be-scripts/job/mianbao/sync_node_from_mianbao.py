# 节点对账功能 zt@2022-07-21
# 第1版 zt@2022-07-21
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/workspaces/PycharmProjects/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/sync_node_from_mianbao/

# ==== 1、环境变量 ====
import os
import sys
print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "sync_node_from_mianbao"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "sync_node_from_mianbao.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
from datetime import datetime, timedelta
from enum import unique, Enum
import requests
import pymysql
from pymysql.cursors import DictCursor
import json
from settings import DATABASES, MIANBAO_API

CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None
ZONE_CACHE_SUITE_DICT = {}


@unique
class SyncTypeEnum(Enum):
    SYNC_TYPE_ALL = ('sync_all', "同步所有信息")
    SYNC_TYPE_DAY = ('sync_day', "同步部分信息")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ProviderEnum(Enum):
    PROVIDER_ALL = ('ALL', '全部', 'pa/vvhosts')

    # PROVIDER_VSPHERE = ('vsphere', 'VMware', 'vsphere/vms')
    # PROVIDER_UCLOUD = ('ucloud', '优刻得', 'ucloud/uhost')
    # PROVIDER_QQCLOUD = ('qqcloud', '腾讯云', 'qqcloud/instance')

    def __init__(self, provider_code, provider_name, provider_url):
        self.provider_code = provider_code
        self.provider_name = provider_name
        self.provider_url = provider_url


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_NODE_NONE = ('err_node_none', "cmdb中无此节点信息（minion_id）。")
    ERR_TYPE_BIND_NONE = ('err_bind_none', "cmdb中无分组、无订单，不知道绑哪个应用。")
    ERR_TYPE_BIND_MODULE = ('err_bind_module', "cmdb中的res_code，在spider找不到对应的模块名，无法绑定。")
    ERR_TYPE_BIND_SUITE = ('err_bind_suite', "spider中没找到对应的环境套映射信息。")
    ERR_TYPE_GROUP = ('err_group', "spider缺少分组数据")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def __get_batch_code(conn):
    """获取spider中的分组信息"""
    spider_new_code_sql = '''select @DATE_STR:=DATE_FORMAT(sysdate(), '%y%m%d') as date_str,
       CONCAT('C', @DATE_STR, IF(v.max_code is null, '0001', LPAD(SUBSTRING(v.max_code, -4)+1, 4, 0))) as new_code
    from (
        select max(check_batch_code) as max_code
        from node_check_batch
        where check_batch_code like CONCAT('C', DATE_FORMAT(sysdate(), '%y%m%d'), '%')
             ) v, (select @DATE_STR:='')T1
    where 1=1;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_new_code_sql)
        result = cursor.fetchone()

    return result


def __ins_node_check_batch(conn, ins_dict, ins_time):
    ins_sql = """INSERT INTO node_check_batch(
    create_user,
    create_time,
    update_user,
    update_time,
    stamp,
    provider_code,
    check_batch_code,
    node_check_status,
    node_check_desc,
    node_check_req_ct,
    node_check_ins_ct
    )VALUES(
    %s, %s, %s, %s, %s,
    %s, %s, %s, %s, %s, %s
    );"""

    if not ins_time:
        ins_time = datetime.now()

    ins_tuple = (CREATE_USER, ins_time, UPDATE_USER, ins_time, CREATE_STAMP,
                 ins_dict.get('provider_code'),
                 ins_dict.get('check_batch_code'),
                 ins_dict.get('node_check_status'),
                 ins_dict.get('node_check_desc'),
                 ins_dict.get('node_check_req_ct'),
                 None)

    with conn.cursor(cursor=DictCursor) as cursor:
        ins_count = cursor.execute(ins_sql, ins_tuple)
        # 单条插入，只要成功肯定是1笔。
        log.info(">>>> 插入节点对账主表信息：{}笔。".format(ins_count))
        last_rowid = cursor.lastrowid

    return last_rowid


def __upd_node_check_batch(conn, upd_dict, upd_time):
    upd_sql = '''UPDATE
    node_check_batch
    SET
        update_user = %s,
        update_time = %s,
        node_check_status = %s,
        node_check_ins_ct = %s,
        node_ins_count = %s
    WHERE id = %s;'''

    if not upd_time:
        upd_time = datetime.now()

    upd_tuple = (UPDATE_USER, upd_time,
                 upd_dict.get('node_check_status'),
                 upd_dict.get('node_check_ins_ct'),
                 upd_dict.get('node_ins_count'),
                 upd_dict.get('pid'))

    with conn.cursor(cursor=DictCursor) as cursor:
        # 基于主键ID更新，如果成果只会有1笔。
        upd_count = cursor.execute(upd_sql, upd_tuple)
        log.info(">>>> 插入节点对账主表信息：{}笔。".format(upd_count))

    return upd_count


def __parse_ins_node_tuple_list(batch_id, batch_code, res_results, ins_time):
    ins_tuple_list = []

    if res_results:
        if not ins_time:
            ins_time = datetime.now()

    node_dict_list = sorted(res_results, key=lambda x: (x['IpAddress'] if x['IpAddress'] else json.dumps(x)))

    for node_dict in node_dict_list:
        # mianbao返回的字典映射
        node_ip = node_dict.get('IpAddress')
        node_cpu = node_dict.get('NumCpu')
        node_mem = node_dict.get('MemorySize')
        node_name = node_dict.get('Name')
        node_zone = node_dict.get('Prz')
        node_json = json.dumps(node_dict, sort_keys=True)

        ins_node_tuple = (
            CREATE_USER,
            ins_time,
            UPDATE_USER,
            ins_time,
            CREATE_STAMP,
            batch_id,
            batch_code,
            node_ip,
            node_cpu,
            node_mem,
            node_name,
            node_zone,
            node_json,
        )
        ins_tuple_list.append(ins_node_tuple)

    return ins_tuple_list


def __ins_node_check_detail(conn, batch_id, batch_code, res_results, ins_time):
    if res_results:
        ins_node_tuple_list = __parse_ins_node_tuple_list(batch_id, batch_code, res_results, ins_time)

        ins_sql = """
            INSERT INTO node_check_batch_detail(
                create_user, 
                create_time, 
                update_user, 
                update_time, 
                stamp,
                check_batch_id,
                check_batch_code,
                node_ip,
                node_cpu,
                node_mem,
                node_name,
                node_zone,
                node_json
                )VALUES(
                    %s, %s, %s, %s, %s, 
                    %s, %s, %s, %s, %s, %s, %s, %s
                );"""
        with conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_sql, ins_node_tuple_list)
        log.info(">>>> ins_node_count: {}".format(ins_count))
    return ins_count


def __get_api_url(provider_enum):
    mb_ip = MIANBAO_API['ip']
    mb_port = MIANBAO_API['port']
    mb_url = MIANBAO_API['url']
    mb_ver = MIANBAO_API['version']
    mb_limit = MIANBAO_API['limit']
    provider_code = provider_enum.provider_code
    provider_name = provider_enum.provider_name
    provider_url = provider_enum.provider_url

    url = 'http://{}:{}/{}/{}/{}?limit={}'.format(mb_ip, mb_port, mb_url, mb_ver, provider_url, mb_limit)
    log.info(">>>> 获取{}({})的接口地址：{}".format(provider_name, provider_code, url))
    return url


def __ins_node_by_provider(provider_enum):
    """ 根据供应商「批量」插入节点信息 zt@2022-07-25 """
    curr_time = datetime.now()
    # 1、数据解析
    req_url = __get_api_url(provider_enum)
    req_st = datetime.now()
    res = requests.get(req_url)
    req_et = datetime.now()
    req_timedelta = req_et - req_st
    req_cost_time = req_timedelta.seconds + req_timedelta.microseconds / 1000000

    json_dict = res.json()
    # 如果无返回，则打印错误日志后继续
    if not json_dict:
        raise ValueError("供应商「{}」接口返回异常：返回数据空（结构为空，非无数据）！".format(provider_enum.provider_code))

    res_count = json_dict.get('count')
    res_previous = json_dict.get('previous')
    res_next = json_dict.get('next')
    res_results = json_dict.get('results')

    # 打印耗时
    res_msg = ">>>> {}({})节点数据「耗时：{}秒」：count:{}, previous:{}, next:{}".format(provider_enum.provider_name,
                                                                              provider_enum.provider_code,
                                                                              req_cost_time,
                                                                              res_count,
                                                                              res_previous,
                                                                              res_next)
    if not res_results:
        res_msg = "{}, results（无数据）:{}".format(res_msg, res_results)
    log.info(res_msg)
    # 2、数据插入
    ins_st = datetime.now()
    scm_conn = connect_scm_mysql()
    try:
        # 2-1、获取批次号
        batch_code_dict = __get_batch_code(scm_conn)
        if not batch_code_dict:
            raise ValueError("供应商「{}」获取节点对账批次号异常：没有获取到返回字典！".format(provider_enum.provider_code))
        new_code = batch_code_dict.get('new_code')
        if not new_code:
            raise ValueError("供应商「{}」获取节点对账批次号异常：没有获取到新的批次号！".format(provider_enum.provider_code))
        log.info(">>>> new_code = {}".format(new_code))
        # 2-2、插入主表数据
        ins_dict = {
            "provider_code": provider_enum.provider_code,
            "check_batch_code": new_code,
            "node_check_status": 2,
            "node_check_desc": res_msg,
            "node_check_req_ct": req_cost_time,
        }

        pid = __ins_node_check_batch(scm_conn, ins_dict, curr_time)
        log.info(">>>> pid: {}".format(pid))
        # 2-3、批量插入子表数据
        ins_count = __ins_node_check_detail(scm_conn, pid, new_code, res_results, curr_time)

        ins_et = datetime.now()
        ins_timedelta = ins_et - ins_st
        ins_cost_time = ins_timedelta.seconds + ins_timedelta.microseconds / 1000000
        # 2-4、更新完成状态
        upd_time = datetime.now()
        upd_dict = {
            "pid": pid,
            "node_check_status": 1,
            "node_check_ins_ct": ins_cost_time,
            "node_ins_count": ins_count,
        }
        __upd_node_check_batch(scm_conn, upd_dict, upd_time)
        # 2-5、关闭数据库连接
        scm_conn.commit()
    except Exception as e:
        log.error(">>>> 数据插入异常：{}".format(e))
        scm_conn.rollback()

    scm_conn.close()


def __sync_all_node():
    """ 同步所有节点信息至DevOps平台 zt@2022-07-25 """
    sync_all_node_st = datetime.now()
    for provider_enum in ProviderEnum:
        __ins_node_by_provider(provider_enum)

    sync_all_node_et = datetime.now()
    sync_all_node_timedelta = sync_all_node_et - sync_all_node_st
    sync_all_node_cost_time = sync_all_node_timedelta.seconds + sync_all_node_timedelta.microseconds / 1000000
    log.info(">>>> 同步所有节点总耗时：{}秒。".format(sync_all_node_cost_time))


def __main(param_sync_type_enum):
    """「节点对账」主方法 zt@2022-07-21"""
    log.info("======== 开始同步 time(start) {} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    log.info("==== 『入参』：{}".format(param_sync_type_enum.name))
    # 1、同步所有节点
    __sync_all_node()


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_sync_type_enum = SyncTypeEnum.SYNC_TYPE_ALL
    if len(sys.argv) > 1:
        req_sync_type_str = sys.argv[1:]
        # 确定步骤
        try:
            req_sync_type_enum = SyncTypeEnum[req_sync_type_str]
        except KeyError:
            log.error("==== ！！！！参数异常！！！！ ====")
            err_msg = ">>>> 参数错误，只支持：空、sync_all、sync_day。"
            log.error(err_msg)
            exit(1)
    try:
        __main(req_sync_type_enum)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        e_time = datetime.now()
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        log.info("== 执行最终耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
