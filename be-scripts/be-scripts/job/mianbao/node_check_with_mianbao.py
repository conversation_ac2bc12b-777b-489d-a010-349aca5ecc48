# 和「mianbao」数据比对 zt@2022-08-01
# 第1版 zt@2022-08-01
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/workspaces/PycharmProjects/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/node_check_with_mianbao/

# ==== 1、环境变量 ====
import os
import sys
print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "sync_node_from_mianbao"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "sync_node_from_mianbao.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
from datetime import datetime, timedelta
from enum import unique, Enum
import pymysql
from pymysql.cursors import DictCursor
import json
from settings import DATABASES, MIANBAO_API
from common.email.send_email import SendMail

CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None
ZONE_CACHE_SUITE_DICT = {}


@unique
class SyncTypeEnum(Enum):
    SYNC_TYPE_ALL = ('sync_all', "同步所有信息")
    SYNC_TYPE_DAY = ('sync_day', "同步部分信息")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_NODE_IP_NONE = ('err_ip_none', "节点「IP」为None！")
    ERR_TYPE_NODE_NAME_NONE = ('err_name_none', "节点「名称」为None！")
    ERR_TYPE_NODE_ZONE_NONE = ('err_zone_none', "节点「可用区」为None！")
    ERR_TYPE_NODE_MIANBAO_ONLY = ('err_mianbao_only', "节点只存在于「mianbao」系统")
    ERR_TYPE_NODE_DEVOPS_ONLY = ('err_devops_only', "节点只存在于「devops」系统")
    ERR_TYPE_NODE_BIND_MORE = ('err_bind_more', "节点「一机多绑」")
    ERR_TYPE_NODE_DEVOPS_DB = ('err_devops_db', "「DevOps」问题数据")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def add_err_to_dict(enum, err_group_dict, err_node_dict):
    """ 添加错误至「错误字典」zt@2022-08-04"""
    err_node_list = err_group_dict.get(enum)
    if not err_node_list or len(err_node_list) == 0:
        err_node_list = [err_node_dict]
        err_group_dict[enum] = err_node_list
    else:
        err_node_list.append(err_node_dict)


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgre的数据连接关闭"""
    conn.close()


def __get_max_code(conn):
    """ 获取当天最大的「批次号」 zt@2022-08-02 """
    spider_max_code_sql = '''select max(check_batch_code) as max_code
    from node_check_batch
    where check_batch_code like CONCAT('C', DATE_FORMAT(sysdate(), '%y%m%d'), '%');'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_max_code_sql)
        result = cursor.fetchone()

    return result


def __get_node_list_by_code(conn, check_batch_code):
    """ 根据批次号获取机器列表 zt@2022-08-04 """
    spider_detail_sql = '''select m.node_ip,
           m.node_mem,
           m.node_name,
           m.node_zone
        from node_check_batch_detail m
        where m.check_batch_code = '{}'
        order by m.node_ip, m.node_name;'''.format(check_batch_code)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_detail_sql)
        result = cursor.fetchall()

    return result


def __get_m_node_list(conn):
    """ 从「mianbao」系统同步的节点列表 zt@2022-08-02 """
    get_m_node_st = datetime.now()
    max_code_dict = __get_max_code(conn)

    max_code = max_code_dict.get('max_code')
    if not max_code:
        raise ValueError(">>>> 获取当日最大批次号失败！")
    log.info(">>>> max_code = {}".format(max_code))

    m_node_list = __get_node_list_by_code(conn, max_code)
    if not m_node_list:
        raise ValueError(">>>> 批次号：{}，未获取到节点数据。".format(max_code))

    m_node_count = len(m_node_list)
    if m_node_count < 1000:
        raise ValueError(">>>> 批次号：{}，获取到的节点数据太少：{}。".format(max_code, m_node_count))
    log.info(">>>> 批次号：{}，节点数据：{}。".format(max_code, m_node_count))

    get_m_node_et = datetime.now()
    get_m_node_timedelta = get_m_node_et - get_m_node_st
    get_m_node_cost_time = get_m_node_timedelta.seconds + get_m_node_timedelta.microseconds / 1000000
    log.info(">>>> 获取当日节点：{}笔，耗时：{}秒。".format(m_node_count, get_m_node_cost_time))

    return m_node_list


def __del_recycled_node(conn):
    """ 清理节点&绑定 zt@2022-08-04 """
    del_sql = '''DELETE node, bind
    FROM env_mgt_node node
    LEFT JOIN env_mgt_node_bind bind ON bind.node_id = node.id
    WHERE node.node_status = 3;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        del_count = cursor.execute(del_sql)

    log.info(">>>> del_count: {}".format(del_count))

    return del_count


def __del_recycled_bind(conn):
    """ 清理无节点的绑定关系 zt@2022-08-04 """
    del_sql = '''DELETE bind
    FROM env_mgt_node_bind bind
    LEFT JOIN env_mgt_node node ON bind.node_id = node.id
    LEFT JOIN env_mgt_container c ON bind.node_docker = c.container_code
    WHERE (node.id IS NULL AND bind.node_id > 0)
    OR (c.container_is_active != 1 AND bind.node_id IS NULL);'''

    with conn.cursor(cursor=DictCursor) as cursor:
        del_count = cursor.execute(del_sql)

    log.info(">>>> del_count: {}".format(del_count))

    return del_count


def __clear_recycled_node(conn):
    """ 先清理「已回收」节点 zt@2022-08-04 """
    clear_node_st = datetime.now()

    # 1、清理「已回收」的绑定关系
    del_node_count = __del_recycled_node(conn)
    log.info(">>>> 清理已回收节点及绑定数据：{}条。".format(del_node_count))

    # 2、清理「无节点」的绑定关系
    del_bind_count = __del_recycled_bind(conn)
    log.info(">>>> 清理无节点对应的绑定数据：{}条。".format(del_bind_count))

    clear_node_et = datetime.now()
    clear_node_timedelta = clear_node_et - clear_node_st
    clear_node_cost_time = clear_node_timedelta.seconds + clear_node_timedelta.microseconds / 1000000
    log.info(">>>> 清理已回收：{}笔，清理空绑定：{}笔，总耗时：{}秒。".format(del_node_count,
                                                        del_bind_count,
                                                        clear_node_cost_time))


def __get_node_list_from_db(conn):
    """ 根据批次号获取机器列表 zt@2022-08-04 """
    query_sql = '''select m.node_ip,
       m.node_name,
       m.minion_id,
       m.region_id,
       m.node_status,
       m.node_desc,
       m.apply_order_code
    from env_mgt_node m
    where 1=1
    order by m.node_ip, m.node_name;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(query_sql)
        result = cursor.fetchall()

    return result


def __get_d_node_list(conn):
    """ 从「DevOps」获取节点列表 zt@2022-08-04 """
    get_d_node_st = datetime.now()
    d_node_list = __get_node_list_from_db(conn)
    if not d_node_list:
        raise ValueError(">>>> 「DevOps」未获取到节点数据！！！")
    d_node_count = len(d_node_list)
    get_d_node_et = datetime.now()
    get_d_node_timedelta = get_d_node_et - get_d_node_st
    get_d_node_cost_time = get_d_node_timedelta.seconds + get_d_node_timedelta.microseconds / 1000000
    log.info(">>>> 获取「DevOps」节点数据：{}笔，耗时：{}秒。".format(d_node_count, get_d_node_cost_time))

    return d_node_list


def __check_node_mail(m_node_list, d_node_list):
    """ 开始节点对账并发邮件 zt@2022-08-04"""
    if not m_node_list:
        raise ValueError(">>>> 从「mianbao」获取当日节点数据为空！！！")
    if not d_node_list:
        raise ValueError(">>>> 从「DevOps」获取到的节点数据为空！！！")

    m_node_count = len(m_node_list)
    d_node_count = len(d_node_list)
    if m_node_count < 1000:
        raise ValueError(">>>> 从「mianbao」获取当日节点：{}笔，数据量异常中止对账！".format(m_node_count))

    # 对比算法
    log.info(">>>> 开始比对 <<<<")
    # 以IP为比对基准
    comp_st = datetime.now()
    err_group_dict = {}
    previous_node_dict = None
    i = 0
    j = 0
    while i < m_node_count:
        m_node_dict = m_node_list[i]
        i = i + 1
        m_ip = m_node_dict.get('node_ip')
        m_name = m_node_dict.get('node_name')
        m_zone = m_node_dict.get('node_zone')
        # 1、不完整数据
        if not m_ip or not m_name or not m_zone:
            if not m_ip:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_IP_NONE, err_group_dict, m_node_dict)
            if not m_name:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_NAME_NONE, err_group_dict, m_node_dict)
            if not m_zone:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_ZONE_NONE, err_group_dict, m_node_dict)
            continue

        # 2、不匹配数据
        while j < d_node_count:
            d_node_dict = d_node_list[j]
            j = j + 1
            d_ip = d_node_dict.get('node_ip')
            if not d_ip:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_DEVOPS_DB, err_group_dict, d_node_dict)
                continue

            # 2.1 一机多绑检查
            if not previous_node_dict or d_ip != previous_node_dict.get('node_ip'):
                previous_node_dict = d_node_dict
            else:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_BIND_MORE, err_group_dict, d_node_dict)
                continue

            vs_msg_prefix = "『MianBao-->DevOps』:「{}」({} vs {})「{}」".format(m_ip, i - 1, j - 1, d_ip)
            if m_ip > d_ip:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_DEVOPS_ONLY, err_group_dict, d_node_dict)
                log.info(">>>> {} --> DevOps_ONLY".format(vs_msg_prefix))

                continue
            else:
                if m_ip < d_ip:
                    add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE_MIANBAO_ONLY, err_group_dict, m_node_dict)
                    vs_msg = ">>>> {} --> MianBao_ONLY".format(vs_msg_prefix)
                    # 算法核心 zt@2022-08-05
                    j = j - 1
                    # 是我是我还是我 zt@2022-08-05
                    previous_node_dict = None
                else:
                    vs_msg = ">>>> {} --> Good".format(vs_msg_prefix)
                log.info(vs_msg)
                break

        # 3、其它数据
    log.info(">>>> 结束比对 <<<<")
    comp_et = datetime.now()
    comp_timedelta = comp_et - comp_st
    comp_cost_time = comp_timedelta.seconds + comp_timedelta.microseconds / 1000000
    log.info(">>>> MianBao({}) VS DevOps({})，耗时：{}秒。".format(m_node_count, d_node_count, comp_cost_time))

    return err_group_dict


def __get_mail_content(err_group_dict):
    mail_content = ""
    if err_group_dict:
        for err_enum in err_group_dict:
            err_desc = err_enum.err_desc
            err_list = err_group_dict[err_enum]
            mail_content += "<hr><strong>{}：{}笔</strong></br>".format(err_desc, len(err_list))
            if err_enum == ErrTypeEnum.ERR_TYPE_NODE_IP_NONE \
                    or err_enum == ErrTypeEnum.ERR_TYPE_NODE_NAME_NONE\
                    or err_enum == ErrTypeEnum.ERR_TYPE_NODE_ZONE_NONE\
                    or err_enum == ErrTypeEnum.ERR_TYPE_NODE_MIANBAO_ONLY:
                table_tr_content = ""
                for node_dict in err_list:
                    node_ip = node_dict.get('node_ip')
                    node_mem = node_dict.get('node_mem')
                    node_name = node_dict.get('node_name')
                    node_zone = node_dict.get('node_zone')
                    table_tr_content += """<tr>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                    </tr>""".format(node_ip, node_mem, node_name, node_zone)

                mail_content += """<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse;margin-top: 1em">
                    <tr>
                        <th>node_ip</th>
                        <th>node_mem</th>
                        <th>node_name</th>
                        <th>node_zone</th>
                    </tr>
                    {}
                </table>""".format(table_tr_content)
            else:
                table_tr_content = ""
                for node_dict in err_list:
                    node_ip = node_dict.get('node_ip')
                    node_name = node_dict.get('node_name')
                    region_id = node_dict.get('minion_id')
                    node_status = node_dict.get('node_status')
                    apply_order_code = node_dict.get('apply_order_code')
                    node_desc = node_dict.get('node_desc')
                    table_tr_content += """<tr>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                        <td>{}</td>
                    </tr>""".format(node_ip, node_name, region_id, node_status, apply_order_code, node_desc)

                mail_content += """<table border="1" cellpadding="0" cellspacing="0" style="border-collapse: collapse;margin-top: 1em">
                                    <tr>
                                        <th>node_ip</th>
                                        <th>node_name</th>
                                        <th>region_id</th>
                                        <th>node_status</th>
                                        <th>apply_order_code</th>
                                        <th>node_desc</th>
                                    </tr>
                                    {}
                                </table>""".format(table_tr_content)

    return mail_content


def __main(param_sync_type_enum):
    """「节点对账」主方法 zt@2022-08-02 """
    log.info("======== 开始同步 time(start) {} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    log.info("==== 『入参』：{}".format(param_sync_type_enum.name))

    scm_conn = connect_scm_mysql()
    try:
        # 1、获取mianbao当日的节点列表
        m_node_list = __get_m_node_list(scm_conn)
        # 2、解绑 & 清理
        __clear_recycled_node(scm_conn)
        # 3、获取现有节点
        d_node_list = __get_d_node_list(scm_conn)
        scm_conn.commit()
        log.info(">>>> MianBao({}) VS DevOps({})".format(len(m_node_list), len(d_node_list)))
    finally:
        conn_close(scm_conn)

    # 4、对账
    err_group_dict = __check_node_mail(m_node_list, d_node_list)

    # 4-1、过滤掉「2、回收中」的节点信息 zt@2022-08-30
    # todo 确定需要优化，下个迭代再做。zt@2022-08-30

    for err_enum in err_group_dict:
        err_desc = err_enum.err_desc
        err_list = err_group_dict[err_enum]
        log.info(">>>> err_desc = {}：{}笔".format(err_desc, len(err_list)))
        log.info(">>>> err_list = {}".format(json.dumps(err_list, sort_keys=True, indent=4, ensure_ascii=False)))

    # 5、发邮件
    # mail_to = ['<EMAIL>']
    mail_sender = SendMail()
    mail_sender.set_to(MIANBAO_API['mail_to'])
    mail_sender.set_subject('「DevOps系统」和「Mianbao系统」节点对账结果')
    mail_sender.set_content(__get_mail_content(err_group_dict))
    mail_sender.send()


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_sync_type_enum = SyncTypeEnum.SYNC_TYPE_ALL
    if len(sys.argv) > 1:
        req_sync_type_str = sys.argv[1:]
        # 确定步骤
        try:
            req_sync_type_enum = SyncTypeEnum[req_sync_type_str]
        except KeyError:
            log.error("==== ！！！！参数异常！！！！ ====")
            err_msg = ">>>> 参数错误，只支持：空、sync_all、sync_day。"
            log.error(err_msg)
            exit(1)
    try:
        __main(req_sync_type_enum)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        e_time = datetime.now()
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        log.info("== 执行最终耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
