# 同步应用、分组、团队信息至宙斯系统
# 第1版 zt@2020-08-04
import os
import json
import sys

import pymysql
import datetime

import requests
from pymysql.cursors import DictCursor

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger, INTERFACE_URL, DATABASES


class CJsonEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, datetime.datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, datetime.date):
            return o.strftime("%Y-%m-%d")
        else:
            return json.JSONEncoder.default(self, o)


def connect_scm_mysql():
    conn = pymysql.connect(
        # host='**************',port=3306,database='spider',charset='utf8',user='scm',password='howbuyscm'
        host=DATABASES['IP'], port=DATABASES['PORT'], database=DATABASES['DB'], charset=DATABASES['CHARSET'],user=DATABASES['USER'], password=DATABASES['PASSWORD'])
    return conn


def connect_zeus_mysql():
    conn = pymysql.connect(
        host='**************', port=3306,
        database='zeus_nacos_new', charset='utf8',
        user='mring', password='howbuylpt')
    return conn


def conn_close(conn):
    conn.close()


def get_scm_app_data(conn):
    timeline = datetime.datetime.now() - datetime.timedelta(days=30)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
        SELECT
            v.app_name,
            v.app_group,
            IFNULL(ti.team_alias,'no_team') AS team_alias,
            v.cvs_type,
            CONCAT(IFNULL(ti.team_alias,'no_team'),'-',v.app_group) as new_group
        FROM(
            SELECT m.app_id, m.need_online,
                m.module_name AS app_name,
                app.git_url AS app_group,
                'GIT' as cvs_type
            FROM app_mgt_app_module m
            INNER JOIN app_mgt_app_info app ON m.app_id = app.id
            INNER JOIN tool_mgt_git_url gu ON app.git_url = gu.git_url
            UNION
            SELECT m.app_id, m.need_online,
                m.module_name,
                IF(INSTR(CONCAT(su.svn_url,IFNULL(app.svn_path,''),IFNULL(m.module_svn_path,'')),'/trunk') = 0,
                    'no_svn_trunk',
                    SUBSTRING_INDEX(SUBSTR(CONCAT(su.svn_url,IFNULL(app.svn_path,''),IFNULL(m.module_svn_path,'')),1,INSTR(CONCAT(su.svn_url,IFNULL(app.svn_path,''),IFNULL(m.module_svn_path,'')),'/trunk')-1),'/',-1)),
                    'SVN'
            FROM app_mgt_app_module m
            INNER JOIN app_mgt_app_info app ON m.app_id = app.id and app.git_url is null
            INNER JOIN tool_mgt_svn_url su ON app.svn_url = su.svn_name
        )v
        INNER JOIN app_mgt_app_build b ON b.module_name = v.app_name
            AND (b.package_type = 'war' OR b.package_type = 'tar' OR (b.package_type = 'jar' AND v.need_online = 1) OR b.package_type = 'py')
        LEFT JOIN(
            SELECT app_id, MAX(id)AS team_max_id
            FROM app_mgt_app_team
            GROUP BY app_id
        ) tv ON tv.app_id = v.app_id
        LEFT JOIN app_mgt_app_team t ON tv.team_max_id = t.id
        LEFT JOIN tool_mgt_team_info ti ON t.team_id = ti.id
        ORDER BY team_alias, new_group, app_name;
        '''.format(timeline.strftime("%Y-%m-%d %H:%M:%S")))

        result = cursor.fetchall()

    return result


def zeus_app_insert_or_update(zeus_url, scm_app_list):
    cur_time = datetime.datetime.now()
    cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")

    logger.info(">>>> 环境同步{} zeus_url = {}".format(cur_time_str, zeus_url))

    for row in scm_app_list:
        app_name = row['app_name']
        app_group = row['new_group']
        team_alias = row['team_alias']

        payload = {
            "originName": team_alias,
            "appGroup": app_group,
            "appName": app_name,
        }

        if team_alias == 'no_team':
            logger.info(">>>> 缺少团队信息，payload = {}".format(payload))
            continue

        res = requests.post(zeus_url, data=payload)

        result = res.json()

        if 'code' in result and result['code'] != '0000':
            message = result['message']
            logger.error(">>>> 环境同步异常：{}，payload = {}".format(message, payload))


def main():
    scm_conn = connect_scm_mysql()
    try:
        scm_app_list = get_scm_app_data(scm_conn)
    finally:
        conn_close(scm_conn)

    zeus_url = "{}:{}/{}{}".format(INTERFACE_URL['zeus'],
                                   INTERFACE_URL['zeus_port'],
                                   INTERFACE_URL['zeus_context'],
                                   INTERFACE_URL['zeus_app_sync_url'], )

    try:
        print('==========time(start): ' + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '==========')
        # 同步应用(zeus_env)的信息
        zeus_app_insert_or_update(zeus_url, scm_app_list)
        # 关闭宙斯的数据库连接
        print('==========time(end): ' + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '==========')
    finally:
        pass


if __name__ == '__main__':
    main()
