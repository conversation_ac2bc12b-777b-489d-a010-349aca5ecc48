import json
import os
import sys

import pymysql
import datetime
from pymysql.cursors import DictCursor
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES


class CJsonEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, datetime.datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, datetime.date):
            return o.strftime("%Y-%m-%d")
        else:
            return json.JSONEncoder.default(self, o)


def connect_scm_mysql():
    conn = pymysql.connect(
        # host='**************',port=3306,database='spider',charset='utf8',user='scm',password='howbuyscm'
        host=DATABASES['IP'], port=DATABASES['PORT'], database=DATABASES['DB'], charset=DATABASES['CHARSET'],user=DATABASES['USER'], password=DATABASES['PASSWORD'])
    return conn


def connect_zeus_mysql():
    conn = pymysql.connect(
        host='**************', port=3306,
        database='zeus_nacos_new', charset='utf8',
        user='mring', password='howbuylpt')
    return conn


def conn_close(conn):
    conn.close()


def get_scm_env_data(conn):
    timeline = datetime.datetime.now() - datetime.timedelta(days=30)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT
                m.id,
                m.suite_code,
                m.suite_name,
                m.suite_desc,
                r.addr_short_name,
                r.type_short_name,
                r.region_group
            FROM env_mgt_suite m
            INNER JOIN env_mgt_region r ON r.id = m.region_id AND r.region_is_active = 1
            WHERE m.suite_is_active = 1
            AND m.update_time > "{}"
            ORDER BY r.addr_short_name, m.suite_code;
            '''.format(timeline.strftime("%Y-%m-%d %H:%M:%S")))
        result = cursor.fetchall()

    return result


def zeus_env_insert_or_update(conn, scm_env_list):
    cur_time = datetime.datetime.now()
    cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    env_status = '使用中'
    user = 'scm_sync'
    stamp = 0

    with conn.cursor(cursor=DictCursor) as cursor:
        for row in scm_env_list:
            env_id = row['id']
            suite_code = row['suite_code'] if row['suite_code'] else None
            suite_name = row['suite_name'] if row['suite_name'] else None
            suite_desc = row['suite_desc'] if row['suite_desc'] else None
            addr_short_name = row['addr_short_name'] if row['addr_short_name'] else None
            type_short_name = row['type_short_name'] if row['type_short_name'] else None
            region_group = row['region_group'] if row['region_group'] else None

            cursor.execute('''
                select id, 
                    env_code, 
                    env_name, 
                    env_status, 
                    env_desc,
                    tenant_id, 
                    room_code,
                    stamp
                from zeus_env 
                where tenant_id = "{}"
                order by update_time desc;
            '''.format(suite_code))

            has_env = cursor.fetchone()
            if has_env:
                zeus_env_id = has_env['id']
                if env_id != zeus_env_id:
                    print('>>>>警告：zeus已存在的数据且ID不匹配，zeus_env_id=', zeus_env_id)
                    print('>>>>json(zeus): ' + json.dumps(has_env, cls=CJsonEncoder, ensure_ascii=False))
                    print('>>>>json(scm): ' + json.dumps(row, cls=CJsonEncoder, ensure_ascii=False))
                    print('>>>>=========')
                zeus_env_stamp = has_env['stamp']
                cursor.execute('''
                                update zeus_env 
                                set 
                                    `env_code` = "{}", 
                                    `env_name` = "{}", 
                                    `env_status` = "{}",
                                    `env_desc` = "{}",
                                    `room_code` = "{}",
                                    `update_user` = "{}",
                                    `update_time` = "{}",
                                    `stamp` = "{}"
                                where `tenant_id` = "{}"
                                and `stamp` = "{}"
                                '''.format(region_group,
                                           suite_name,
                                           env_status,
                                           suite_desc,
                                           addr_short_name,
                                           user,
                                           cur_time_str,
                                           zeus_env_stamp + 1,
                                           suite_code,
                                           zeus_env_stamp))
            else:
                cursor.execute('''
                                INSERT INTO zeus_env ( 
                                    id, 
                                    env_code, 
                                    env_name, 
                                    env_status, 
                                    env_desc, 
                                    tenant_id, 
                                    create_user, 
                                    create_time, 
                                    update_user, 
                                    update_time, 
                                    stamp, 
                                    room_code 
                                )VALUES({}, "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", {}, "{}")
                                '''.format(env_id,
                                           region_group,
                                           suite_name,
                                           env_status,
                                           suite_desc,
                                           suite_code,
                                           user,
                                           cur_time_str,
                                           user,
                                           cur_time_str,
                                           stamp,
                                           addr_short_name))

            conn.commit()


def main():
    scm_conn = connect_scm_mysql()
    try:
        scm_env_list = get_scm_env_data(scm_conn)
    finally:
        conn_close(scm_conn)

    zeus_conn = connect_zeus_mysql()

    try:
        print('==========time(start): ' + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '==========')
        # 插入环境表(zeus_env)的信息
        zeus_env_insert_or_update(zeus_conn, scm_env_list)
        # 关闭宙斯的数据库连接
        print('==========time(end): ' + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '==========')
    finally:
        conn_close(zeus_conn)


if __name__ == '__main__':
    main()
