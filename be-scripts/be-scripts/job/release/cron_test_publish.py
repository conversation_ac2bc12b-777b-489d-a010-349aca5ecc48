# 「测试环境初始化」定时扫描
# 第1版 zt@2020-08-15
import os
import json
import sys

import pymysql
import datetime
import requests
from pymysql.cursors import DictCursor

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger, INTERFACE_URL

test_template = 'ztst'


def connect_scm_mysql():
    conn = pymysql.connect(
        host='**************', port=3306,
        database='spider', charset='utf8',
        user='scm', password='howbuyscm')
    return conn


def conn_close(conn):
    conn.close()


def get_scm_app_data(conn, minutes):
    timeline = datetime.datetime.now() - datetime.timedelta(minutes=minutes)
    result = None

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
        SELECT 
            m.id,
            m.businessID,
            m.Operator,
            m.operateTime,
            m.operateType,
            m.finishTime,
            m.fullLog,
            m.singleExecStatus,
            m.totalExecStatus,
            m.buildID
        FROM django_scm.record_compileoperatehistory m
        WHERE m.businessID LIKE 'TMS_%'
        AND m.totalExecStatus = 'success'
        AND m.finishTime > '{}'
        ORDER BY id DESC;
        '''.format(timeline.strftime("%Y-%m-%d %H:%M:%S")))

        his_rst = cursor.fetchall()

    if his_rst and len(his_rst) > 0:
        his_set = set()
        for his_obj in his_rst:
            operate_type = his_obj['operateType']
            if not operate_type:
                continue
            else:
                pass
            json_str = json.loads(json.dumps(eval(operate_type)))
            app_str = json_str['appList']
            app_list = app_str.split(",")
            his_set.update(set(app_list))

        # with conn.cursor(cursor=DictCursor) as cursor:
        #     cursor.execute('''
        #     SELECT
        #         m.id,
        #         m.set_id,
        #         m.module_id,
        #         m.module_name,
        #         m.set_is_active,
        #         m.set_desc,
        #         m.create_user,
        #         m.create_time,
        #         m.update_user,
        #         m.update_time,
        #         m.stamp
        #     FROM app_mgt_test_set m
        #     INNER JOIN app_mgt_test_info i ON m.module_name = i.module_name
        #     INNER JOIN app_mgt_app_module am ON i.module_name = am.module_name
        #     WHERE m.set_is_active = 1
        #     ORDER BY id ASC;
        #     '''.format(timeline.strftime("%Y-%m-%d %H:%M:%S")))
        #
        #     ts_rst = cursor.fetchall()

        # if ts_rst and len(ts_rst) > 0:
        #     ts_set = set()
        #     for ts_obj in ts_rst:
        #         module_name = ts_obj['module_name']
        #         if not module_name:
        #             continue
        #         ts_set.add(module_name)

            # result = his_set & ts_set

    return result


def test_publish_all(spider_url, scm_app_list):
    cur_time = datetime.datetime.now()
    cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")

    if scm_app_list and len(scm_app_list) > 0:
        res = requests.post(spider_url, data=None)

        rst = res.json()

        if 'status' in rst and rst['status'] == 'success':
            data = rst['data']
            logger.info(">>>> 定时测试环境「全量」一键初始化启动：{}，时间：{}".format(data, cur_time_str))
        else:
            logger.error(">>>> 定时测试环境「全量」一键初始化异常：{}，时间：{}".format(rst, cur_time_str))


def main(minutes):
    # 获取数据连接
    scm_conn = connect_scm_mysql()
    try:
        # 获取需要测试的应用
        scm_app_list = get_scm_app_data(scm_conn, minutes)
    finally:
        conn_close(scm_conn)

    try:
        print('==========time(start): ' + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '==========')

        # 确定是不是需要执行初始化
        if scm_app_list and len(scm_app_list) > 0:

            spider_url = "{}:{}/{}{}".format(INTERFACE_URL['spider'],
                                             INTERFACE_URL['spider_port'],
                                             INTERFACE_URL['spider_context'],
                                             INTERFACE_URL['spider_test_publish'], )

            test_publish_all(spider_url, scm_app_list)
        else:
            logger.info(">>>> 不需要执行「测试环境初始化」{}".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

        print('==========time(end): ' + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + '==========')
    finally:
        pass


if __name__ == '__main__':
    if len(sys.argv) > 1:
        param_minutes = sys.argv[1]
        minutes_int = int(param_minutes)
        if minutes_int < 15:
            minutes_int = 15
    else:
        minutes_int = 15

    main(minutes_int)
