#
import sys
import paramiko
import subprocess


def ssh_client_conn(host, port, user, password):
    client = paramiko.SSHClient()
    client.load_system_host_keys()
    client.connect(host, port, user, password)
    return client


def ssh_client_close(client):
    client.close()


def ssh_client_exec(client, cmd):
    std_in, std_out, std_err = client.exec_command(cmd)
    return std_out.readlines(), std_err.readlines()


def update_background(client, repo_path):
    cmd = 'cd {} && git pull'.format(repo_path)
    out, err = ssh_client_exec(client, cmd)
    if err:
        print(err)
        raise Exception('update background error.')
    else:
        print('update background success.')


def update_web(client, repo_path):
    cmd = 'cd {} && git pull'.format(repo_path)
    out, err = ssh_client_exec(client, cmd)
    if err:
        print(err)
        raise Exception('update web error.')
    else:
        print('update web success.')


def update_sql(client, repo_path):
    cmd = 'cd {} && /root/Envs/pydp_prod/bin/python3.x manage.py migrate prod'.format(repo_path)
    out, err = ssh_client_exec(client, cmd)
    if err:
        print(err)
        raise Exception('update sql error.')
    else:
        print('update sql success.')


def update_be_script(repo_path):
    cmd = 'cd {} && git pull'.format(repo_path)
    code = subprocess.check_call(cmd, shell=True)
    if code != 0:
        raise Exception('update be-script error.')
    else:
        print('update be-script success.')


def run():
    web_path = '/data/new_website/website_web'
    background_path = '/data/new_website/website'
    be_script_path = '/usr/be-scripts'

    client = ssh_client_conn('***************', 22, 'root', 'howbuyscm')
    update_background(client, background_path)
    update_sql(client, background_path)
    update_web(client, web_path)
    ssh_client_close(client)

    update_be_script(be_script_path)


if __name__ == '__main__':
    run()
