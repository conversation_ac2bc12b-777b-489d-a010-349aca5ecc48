import os
import logging
local_path = "/data/pipeline_repo/"
branch = "0.0.0001"
"regular-remote"
lib_repos_list = ["high-order-center-search","high-order-center","interlayer-batch","interlayer-product-console",
                  "robot-order-center","tenpay"]

def find_jar(lib_dir):

    for file in os.listdir(lib_dir):
        if file == "WEB-INF":
            web_lib_dir = os.path.join(lib_dir, "WEB-INF", "lib")
            for file in os.listdir(web_lib_dir):
                if "howbuy-cachemanagement" in file:
                    print(os.path.join(web_lib_dir, file))
        else:
            if "howbuy-cachemanagement" in file:
                print(os.path.join(lib_dir, file))

for dir_name in lib_repos_list:
    lib_dir = os.path.join("/data/pipeline_repo",dir_name,"0.0.0001")
    find_jar(lib_dir)
