from common.files.xml.pom import PomModifier
import common.files.xml.ElementTree as ET
from common.files import search
from settings import logger, GITLAB_URL
import os

ET.register_namespace('', "http://maven.apache.org/POM/4.0.0")
ET.register_namespace('xsi', "http://www.w3.org/2001/XMLSchema-instance")


class MiddlewareUpdater:
    def __init__(self, workspace, package_name):
        self.package_name = package_name
        self.workspace = workspace
        self.xmlns = "{http://maven.apache.org/POM/4.0.0}"

    def get_code(self, code_path):
        logger.info(os.path.join(self.workspace, code_path.split("/")[-1]))
        if os.path.isdir(os.path.join(self.workspace, code_path.split("/")[-1])):
            pass
            #os.system("cd {} && git fetch")
            #os.system("cd {} && git checkout origin/{}".format("0.0.0001"))
        else:
            logger.info("cd {} && git clone {}:{}.git".format(self.workspace, GITLAB_URL, code_path))
            os.system("cd {} && git clone  {}:{}.git".format(self.workspace, GITLAB_URL, code_path))

    def get_lib(self):
        pass

    def find_old_dep_jar(self):
        for pom_path in search.file_search(self.workspace, file_name="pom.xml", ignore=[".git"]):
            #logger.info(pom_path)
            with open(pom_path, "r", encoding="utf-8") as f:
                tree = ET.parse(f)
                for node in tree.findall(".//" + self.xmlns + "dependency"):
                    if node.find("./" + self.xmlns + "artifactId").text == self.package_name:
                        logger.info("有依赖的pom{}".format(pom_path))
                        if node.find("./" + self.xmlns + "version") is not None:
                            logger.info(node.find("./" + self.xmlns + "version").text)



if __name__ == "__main__":
    repos_list = ["tms-simu/high-order-center",
    "tms-public/howbuy-interlayer-batch",
    "tms-public/howbuy-interlayer-console",
    "tms-gongmu/howbuy-regularfund",
    "tms-gongmu/robot-order-center",
    "tenpay/howbuy-trade-coop-tenpay",
                  "tms-common/tms-commons",
                  "tms-public/howbuy-interlayer-common",
                  "tms-public/tms-common"]
    "howbuy-cachemanagement"
    mu = MiddlewareUpdater("D:\workspace", "howbuy-cachemanagement")
    for repo in repos_list:
        mu.get_code(repo)
    mu.find_old_dep_jar()