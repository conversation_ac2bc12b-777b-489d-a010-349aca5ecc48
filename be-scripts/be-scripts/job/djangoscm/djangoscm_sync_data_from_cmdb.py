#
import json
import os
import sys

import pymysql
import psycopg2
import psycopg2.extras
from pymysql.cursors import DictCursor
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES, CMDBDATABASES


def connect_mysql():
    # 生产
    conn = pymysql.connect(
        #host='**************', port=3306, database='django_scm', charset='utf8', user='scm', password='howbuyscm')
        host=DATABASES['IP'], port=DATABASES['PORT'], database='django_scm', charset=DATABASES['CHARSET'], user=DATABASES['USER'], password=DATABASES['PASSWORD'])
    # 测试
    # conn = pymysql.connect(
    #     host='**************', port=3306, database='ops_deploy', charset='utf8', user='ops', password='123456')
    return conn


def connect_postgre():
    # 生产
    conn = psycopg2.connect(
        #host="**************", port=5432, database="witcher_cmdb", user="witcher_cmdb", password="LziV5E01t6E3dhWBzgQwfQ==")
        host=CMDBDATABASES['IP'], port=CMDBDATABASES['PORT'], database=CMDBDATABASES['DB'], user=CMDBDATABASES['USER'], password=CMDBDATABASES['PASSWORD'])
    # 测试
    # conn = psycopg2.connect(
    #     host="**************", port=5432, database="witcher_cmdb_sit", user="postgres", password="postgres")
    return conn


def conn_close(conn):
    conn.close()


def get_cmdb_data(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''
        SELECT R.code AS resource_code, R.attributes, R.name AS app_name, G.name AS group, G.code AS group_code, N.ip, N.status, N.minion_id, N.region_code AS region, N.environ_code AS environ
FROM "public"."resources_resource" AS R 
RIGHT JOIN "public"."resources_resourcegroup" AS G ON R.code=G.resource_code 
RIGHT JOIN "public"."resources_re_resourcegroup_server" AS S ON G.id=S.resource_group_id
LEFT JOIN "public"."resources_server" AS N ON S.server_id=N.id;
        ''')
        result = cursor.fetchall()
    
    return result


def current_scm_data(conn):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('select * from common_service_cmdbdata')
        result = cursor.fetchall()
    return result


def data_to_dict(data):
    tmp_dict = {}
    for obj in data:
        tmp_dict.update({(obj['resource_code'], obj['ip']): obj})
    return tmp_dict


def scm_db_inert(conn, index, data_dict):
    with conn.cursor(cursor=DictCursor) as cursor:
        for item in index:
            app_name = data_dict[item]['app_name']
            resource_code = data_dict[item]['resource_code']
            ip = data_dict[item]['ip']
            status = data_dict[item]['status']
            region = data_dict[item]['region']
            group = data_dict[item]['group']
            group_code = data_dict[item]['group_code']
            attributes = data_dict[item]['attributes']
            environ = data_dict[item]['environ']
            health_check_url = ''
            minion_id = ''
            dubbo_port = ''
            if data_dict[item]['minion_id']:
                minion_id = data_dict[item]['minion_id']
            if 'health_check_url' in attributes and attributes['health_check_url']:
                health_check_url = attributes['health_check_url']
            if 'dubbo_port' in attributes and attributes['dubbo_port']:
                dubbo_port = attributes['dubbo_port']

            cursor.execute('''
            insert into common_service_cmdbdata (`app_name`, `resource_code`, `ip`, `minion_id`, `region`, `group`, `group_code`, `health_check_url`, `dubbo_port`, `status`, `environ`)
            values ("{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}", "{}")
            '''.format(app_name, resource_code, ip, minion_id, region, group, group_code, health_check_url, dubbo_port, status, environ))
        conn.commit()


def scm_db_delete(conn, index):
    with conn.cursor(cursor=DictCursor) as cursor:
        for item in index:
            cursor.execute('''
            delete from common_service_cmdbdata where resource_code="{}" and ip="{}"
            '''.format(item[0], item[1]))
        conn.commit()


def scm_db_update(conn, index, data_dict):
    with conn.cursor(cursor=DictCursor) as cursor:
        for item in index:
            app_name = data_dict[item]['app_name']
            status = data_dict[item]['status']
            region = data_dict[item]['region']
            group = data_dict[item]['group']
            group_code = data_dict[item]['group_code']
            attributes = data_dict[item]['attributes']
            environ = data_dict[item]['environ']
            health_check_url = ''
            minion_id = ''
            dubbo_port = ''
            if data_dict[item]['minion_id']:
                minion_id = data_dict[item]['minion_id']
            if 'health_check_url' in attributes and attributes['health_check_url']:
                health_check_url = attributes['health_check_url']
            if 'dubbo_port' in attributes and attributes['dubbo_port']:
                dubbo_port = attributes['dubbo_port']

            cursor.execute('''
            update common_service_cmdbdata set `app_name`="{}", `minion_id`="{}", `region`="{}", `group`="{}", `group_code`="{}", `health_check_url`="{}", `dubbo_port`="{}", `status`="{}", `environ`="{}" where `resource_code`="{}" and `ip`="{}"
            '''.format(app_name, minion_id, region, group, group_code, health_check_url, dubbo_port, status, environ, item[0], item[1]))
        conn.commit()


def get_resource_code_list(conn):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT resourceCode FROM common_service_artifactinfo WHERE resourceCode is not null and resourceCode <> ""
            ''')
        result = cursor.fetchall()

    tmp_list = []
    for item in result:
        tmp_list.append(item['resourceCode'])

    return tmp_list


def filter_cmdb_data(data, res_code_list):
    filter_data = []
    for item in data:
        if item['resource_code'] in res_code_list:
            filter_data.append(item)
    return filter_data


def main():
    cmdb_conn = connect_postgre()
    scm_conn = connect_mysql()

    resource_code_list = get_resource_code_list(scm_conn)

    # 获取cmdb当前数据
    cmdb_data = get_cmdb_data(cmdb_conn)
    cmdb_data_filter = filter_cmdb_data(cmdb_data, resource_code_list)
    cmdb_dict = data_to_dict(cmdb_data_filter)
    conn_close(cmdb_conn)

    # 获取scm当前数据
    scm_data = current_scm_data(scm_conn)
    scm_dict = data_to_dict(scm_data)

    # scm 需要添加的数据
    add_index = set(cmdb_dict) - set(scm_dict)
    # scm 需要删除的数据
    del_index = set(scm_dict) - set(cmdb_dict)
    # scm 需要更新的数据
    update_index = set(cmdb_dict) & set(scm_dict)

    scm_db_inert(scm_conn, add_index, cmdb_dict)
    scm_db_delete(scm_conn, del_index)
    scm_db_update(scm_conn, update_index, cmdb_dict)
    conn_close(scm_conn)


if __name__ == '__main__':
    main()
