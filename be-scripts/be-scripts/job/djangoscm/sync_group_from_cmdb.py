# 分组同步脚本 zt@2021-12-06
# 第1版 zt@2021-12-06
import json
import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
from enum import unique, Enum

print(">>>> PATH: {}".format(sys.path))
print(">>>> SCM_BE_PATH: {}".format(os.getenv('SCM_BE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))

import pymysql
import psycopg2
from datetime import datetime, timedelta
from pymysql.cursors import DictCursor
from psycopg2.extras import RealDictCursor
from settings import DATABASES, CMDBDATABASES

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "sync_group_from_cmdb"
LOG_PATH = "/data/logs/" + LOG_TGT
LOG_NAME = "sync_group_from_cmdb.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========
CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None


@unique
class SyncTypeEnum(Enum):
    SYNC_TYPE_ALL = ('sync_all', "同步所有信息")
    SYNC_TYPE_DAY = ('sync_day', "同步部分信息")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_NODE = ('err_node', "spider缺少节点信息")
    ERR_TYPE_BIND_NONE = ('err_bind_none', "spider存在节点，但无绑定。（只有节点信息，其它都没有。）")
    ERR_TYPE_BIND_MODULE = ('err_bind_module', "spider已绑定节点，但模块名无对应的应用信息。（有绑定关系，但模块名对应的应用信息为空。）")
    ERR_TYPE_BIND_APP = ('err_bind_app', "spider已绑定节点，但没有和本应用绑定。（和其它应用有绑定，和此应用无绑定。）")
    ERR_TYPE_GROUP = ('err_group', "spider缺少分组数据")
    ERR_TYPE_CODE = ('err_code', "spider缺少module_code")
    ERR_TYPE_SPIDER = ('err_spider', "解析过程中，对应出错的spider数据。")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def add_err_to_dict(enum, err_group_dict, err_cmdb_obj, err_spider_obj=None):
    """添加错误信息至错误列表中"""
    err_list = err_group_dict.get(enum)
    if not err_list or len(err_list) == 0:
        err_list = [err_cmdb_obj]
        err_group_dict[enum] = err_list
    else:
        err_list.append(err_cmdb_obj)

    if err_spider_obj:
        spider_err_list = err_group_dict.get(ErrTypeEnum.ERR_TYPE_SPIDER)
        if not spider_err_list or len(spider_err_list) == 0:
            spider_err_list = [err_spider_obj]
            err_group_dict[ErrTypeEnum.ERR_TYPE_SPIDER] = spider_err_list
        else:
            spider_err_list.append(err_spider_obj)


def connect_cmdb_postgre():
    """获取cmdb的postgre数据库连接"""
    conn = psycopg2.connect(host=CMDBDATABASES['IP'],
                            port=CMDBDATABASES['PORT'],
                            database=CMDBDATABASES['DB'],
                            user=CMDBDATABASES['USER'],
                            password=CMDBDATABASES['PASSWORD'])
    return conn


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgre的数据连接关闭"""
    conn.close()


def get_cmdb_group_data(conn):
    """一次获取所有分组信息"""
    cmdb_server_sql = '''
        select
               g.code as group_code,
               g.name as group_name,
               g.desc as group_desc,
               g.resource_code as res_code,
               s.name as srv_name,
               s.ip as srv_ip,
               s.minion_id,
               s.status as srv_status
        from resources_resourcegroup g
            left join resources_re_resourcegroup_server r on r.resource_group_id = g.id
            left join resources_server s on r.server_id = s.id
        order by g.code, s.ip;'''

    with conn.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(cmdb_server_sql)
        result = cursor.fetchall()

    return result


def parse_cmdb_group_data(group_list):
    """转化cmdb分组信息为字典"""
    cmdb_group_dict = None
    cmdb_node_dict = None
    if group_list and len(group_list) > 0:
        cmdb_group_dict = {}
        cmdb_node_dict = {}

        tmp_code = None
        for group_obj in group_list:
            group_code = group_obj['group_code']
            group_name = group_obj['group_name']
            group_desc = group_obj['group_desc']
            res_code = group_obj['res_code']

            srv_name = group_obj['srv_name']
            srv_ip = group_obj['srv_ip']
            minion_id = group_obj['minion_id']
            srv_status = group_obj['srv_status']

            srv_obj_dict = None
            if srv_ip and minion_id:
                srv_obj_dict = {
                    'srv_name': srv_name,
                    'srv_ip': srv_ip,
                    'minion_id': minion_id,
                    'srv_status': srv_status,
                }

                # 一组多用优化 zt@2021-12-09
                group_code_obj = {
                    'srv_name': srv_name,
                    'srv_ip': srv_ip,
                    'minion_id': minion_id,
                    'srv_status': srv_status,
                    'group_code': group_code,
                    'res_code': res_code,
                }
                group_code_list = cmdb_node_dict.get(minion_id)
                if group_code_list:
                    group_code_list.append(group_code_obj)
                else:
                    cmdb_node_dict[minion_id] = [group_code_obj]

            # 快速组装优化 zt@2021-12-06
            if group_code != tmp_code:
                group_obj = {
                    'group_code': group_code,
                    'group_name': group_name,
                    'group_desc': group_desc,
                    'res_code': res_code,
                    'srv_list': None,
                }
                cmdb_group_dict[group_code] = group_obj

                tmp_code = group_code
            else:
                group_obj = cmdb_group_dict[group_code]

            if srv_obj_dict:
                srv_list = group_obj.get('srv_list')
                if srv_list:
                    srv_list.append(srv_obj_dict)
                else:
                    srv_list = [srv_obj_dict]
                    group_obj['srv_list'] = srv_list

    return cmdb_group_dict, cmdb_node_dict


def get_spider_group_data(conn):
    """获取spider中的分组信息"""
    spider_bind_sql = '''
        select
               g.deploy_group_code,
               g.deploy_group_name,
               g.deploy_group_desc
        from env_mgt_deploy_group g
        order by g.deploy_group_code;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_bind_sql)
        result = cursor.fetchall()

    return result


def get_spider_group_dict(conn):
    spider_group_dict = {}
    spider_group_list = get_spider_group_data(conn)
    if spider_group_list and len(spider_group_list) > 0:
        for group_tmp in spider_group_list:
            group_code = group_tmp['deploy_group_code']
            group_name = group_tmp['deploy_group_name']
            group_desc = group_tmp['deploy_group_desc']

            if group_code:
                group_obj = {
                    'group_name': group_name,
                    'group_desc': group_desc,
                }
                spider_group_dict[group_code] = group_obj

    return spider_group_dict


def get_ins_group_list(conn, cmdb_group_dict, err_group_dict):
    ins_group_list = []
    if cmdb_group_dict and len(cmdb_group_dict) > 0:
        spider_group_dict = get_spider_group_dict(conn)
        spider_module_dict = parse_spider_module_dict_with_cache(conn)
        for k, v in cmdb_group_dict.items():
            if k in spider_group_dict:
                continue

            # cmdb的res_code不可能为空
            res_code = v.get('res_code')
            module_name = spider_module_dict.get(res_code)
            if module_name:
                v['module_name'] = module_name
                # 分组插入信息
                ins_group_list.append(v)
            else:
                module_name_list = err_group_dict.get(ErrTypeEnum.ERR_TYPE_CODE)
                if not module_name_list or len(module_name_list) == 0:
                    module_name_list = [v]
                    err_group_dict[ErrTypeEnum.ERR_TYPE_CODE] = module_name_list
                else:
                    module_name_list.append(v)

    return ins_group_list


def get_spider_node_data(conn):
    """「DB」获取spider中的节点列表信息"""
    spider_bind_sql = '''
        select
               n.node_ip,
               n.minion_id,
               b.id as bind_id,
               b.module_name,
               m.module_code,
               g.deploy_group_code
        from env_mgt_node n
            left join env_mgt_node_bind b on b.node_id = n.id
            left join app_mgt_app_module m on m.module_name = b.module_name
            left join env_mgt_deploy_group g on g.id = b.deploy_group
        where n.node_ip is not null and trim(n.node_ip) <> ''
          and n.minion_id is not null and trim(n.minion_id) <> ''
        order by n.minion_id, m.module_code, g.deploy_group_code;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_bind_sql)
        result = cursor.fetchall()

    return result


def parse_spider_node_dict(conn, err_group_dict):
    """「组合」获取spider中的节点字典信息"""
    spider_node_dict = {}
    spider_node_list = get_spider_node_data(conn)
    if spider_node_list and len(spider_node_list) > 0:
        for node_tmp in spider_node_list:
            node_ip = node_tmp['node_ip']
            minion_id = node_tmp['minion_id']
            bind_id = node_tmp['bind_id']
            module_name = node_tmp['module_name']
            module_code = node_tmp['module_code']
            deploy_group_code = node_tmp['deploy_group_code']

            if node_ip and minion_id:
                # 一机多绑优化 zt@2021-12-09
                group_code_obj = {
                    'node_ip': node_ip,
                    'minion_id': minion_id,
                    'bind_id': bind_id,
                    'module_name': module_name,
                    'module_code': module_code,
                    'group_code': deploy_group_code,
                }

                minion_id_dict = spider_node_dict.get(minion_id)
                if minion_id_dict:
                    node_bind_list = minion_id_dict.get('node_bind_list')

                    if node_bind_list:
                        node_bind_list.append(group_code_obj)
                    else:
                        minion_id_dict['node_list'] = [group_code_obj]

                    if bind_id:
                        bind_id_list = minion_id_dict.get('bind_id_list')
                        if bind_id_list:
                            bind_id_list.append(bind_id)
                        else:
                            minion_id_dict['bind_id_list'] = [bind_id]

                    if module_code:
                        module_code_dict = minion_id_dict.get('module_code_dict')
                        if module_code_dict:
                            module_code_dict[module_code] = group_code_obj
                        else:
                            minion_id_dict['module_code_dict'] = {
                                module_code: group_code_obj
                            }
                else:
                    minion_id_dict = {
                        'node_bind_list': [group_code_obj]
                    }
                    if bind_id:
                        minion_id_dict['bind_id_list'] = [bind_id]
                    if module_code:
                        minion_id_dict['module_code_dict'] = {
                            module_code: group_code_obj
                        }

                    spider_node_dict[minion_id] = minion_id_dict

    return spider_node_dict


def query_spider_group_data(conn):
    """「DB」获取spider中的分组列表信息"""
    spider_bind_sql = '''
    select 
        g.id as group_id, 
        g.module_name, 
        g.deploy_group_code as group_code, 
        g.deploy_group_name as group_name, 
        g.deploy_group_desc as group_desc
    from env_mgt_deploy_group g
    order by g.deploy_group_code;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_bind_sql)
        result = cursor.fetchall()

    return result


def parse_spider_group_dict_with_cache(conn):
    global SPIDER_GROUP_ID_DICT
    if not SPIDER_GROUP_ID_DICT:
        spider_group_list = query_spider_group_data(conn)
        if spider_group_list and len(spider_group_list) > 0:
            SPIDER_GROUP_ID_DICT = {}
            for group_obj in spider_group_list:
                group_id = group_obj['group_id']
                module_name = group_obj['module_name']
                group_code = group_obj['group_code']
                group_name = group_obj['group_name']
                group_desc = group_obj['group_desc']

                if group_id and group_code:
                    SPIDER_GROUP_ID_DICT[group_code] = {
                        'group_id': group_id,
                        'module_name': module_name,
                        'group_name': group_name,
                        'group_desc': group_desc,
                    }
    return SPIDER_GROUP_ID_DICT


def get_upd_re_list(conn, cmdb_node_dict, err_group_dict):
    """「解析」获取spider中需要更新的节点列表信息"""
    upd_re_list = []
    if cmdb_node_dict and len(cmdb_node_dict) > 0:
        spider_node_dict = parse_spider_node_dict(conn, err_group_dict)
        spider_group_id_cache = parse_spider_group_dict_with_cache(conn)
        for k, v in cmdb_node_dict.items():
            # spider中存在此节点
            minion_id_dict = spider_node_dict.get(k)
            if not minion_id_dict:
                add_err_to_dict(ErrTypeEnum.ERR_TYPE_NODE, err_group_dict, v)
                # spider中无此节点信息，跳过
                continue

            node_bind_list = minion_id_dict.get('node_bind_list')
            bind_id_list = minion_id_dict.get('bind_id_list')
            module_code_dict = minion_id_dict.get('module_code_dict')

            err_spider_obj = {k: node_bind_list}

            if not bind_id_list or not module_code_dict:
                if not bind_id_list:
                    add_err_to_dict(ErrTypeEnum.ERR_TYPE_BIND_NONE, err_group_dict, v, err_spider_obj)
                    # spider中此节点无任何绑定信息，跳过
                    continue

                if not module_code_dict:
                    add_err_to_dict(ErrTypeEnum.ERR_TYPE_BIND_MODULE, err_group_dict, v, err_spider_obj)
                    # spider中此节点无任何应用信息，跳过
                    continue

            # 循环cmdb中的绑定关系
            for cmdb_group_code_obj in v:
                cmdb_res_code = cmdb_group_code_obj.get('res_code')
                cmdb_group_code = cmdb_group_code_obj.get('group_code')

                # spider中，此节点（k）和应用code（cmdb_res_code）有绑定关系
                spider_bind_group_obj = module_code_dict.get(cmdb_res_code)
                if not spider_bind_group_obj:
                    add_err_to_dict(ErrTypeEnum.ERR_TYPE_BIND_APP, err_group_dict, v, err_spider_obj)
                    # spider中节点和此应用无绑定关系，无法给节点分组，直接跳过。
                    continue

                # 如果存在绑定关系，则肯定会有「bind_id」
                spider_bind_id = spider_bind_group_obj.get('bind_id')
                # 存在绑定关系，但也有可能没有「分组」信息
                spider_group_code = spider_bind_group_obj.get('group_code')

                # 两个条件：1、绑定已存在；2、分组信息也要有。
                # 2-1、存在分组缓存数据（首次初始化分组信息时，就完全没有，会直接跳过，等待第二次同步时自动分组）
                if spider_group_id_cache:
                    # 2-2、从缓存中找到对应的分组obj
                    spider_group_id_obj = spider_group_id_cache.get(cmdb_group_code)
                    # 如果没有找到指定code的分组信息
                    if not spider_group_id_obj:
                        add_err_to_dict(ErrTypeEnum.ERR_TYPE_GROUP, err_group_dict, v, err_spider_obj)
                        # spider没有分组信息，无法继续自动分组
                        continue

                    # 2-3、都有，自动分组
                    if not spider_group_code or spider_group_code != cmdb_group_code:
                        # 获取分组ID
                        spider_group_id = spider_group_id_obj.get('group_id')
                        # 组装更新用的obj
                        re_obj = {
                            # 更新DB使用
                            'group_id': spider_group_id,
                            'bind_id': spider_bind_id,
                            # 排查使用
                            'cmdb_group_code': cmdb_group_code,
                            'cmdb_minion_id': k,
                        }
                        upd_re_list.append(re_obj)

    return upd_re_list


def query_spider_module_data(conn):
    """「DB」获取spider中的「模块」列表信息"""
    query_sql = '''
    select 
        m.module_name, 
        m.module_code
    from app_mgt_app_module m
    where m.module_code is not null
        and replace(m.module_code, char(9), '') <> ''
    order by m.module_code;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(query_sql)
        result = cursor.fetchall()

    return result


def parse_spider_module_dict_with_cache(conn):
    global SPIDER_MODULE_NAME_DICT
    if not SPIDER_MODULE_NAME_DICT:
        spider_module_list = query_spider_module_data(conn)
        if spider_module_list and len(spider_module_list) > 0:
            SPIDER_MODULE_NAME_DICT = {}
            for module_obj in spider_module_list:
                module_name = module_obj.get('module_name')
                module_code = module_obj.get('module_code')

                if module_code:
                    SPIDER_MODULE_NAME_DICT[module_code] = module_name

    return SPIDER_MODULE_NAME_DICT


def parse_group_data(conn, cmdb_group_dict, cmdb_node_dict):
    """获取分组同步要用到的分组、节点、错误信息"""
    ins_group_list = []
    upd_re_list = []
    err_group_dict = {}
    if cmdb_group_dict:
        ins_group_list = get_ins_group_list(conn, cmdb_group_dict, err_group_dict)
    if cmdb_node_dict:
        upd_re_list = get_upd_re_list(conn, cmdb_node_dict, err_group_dict)
    return ins_group_list, upd_re_list, err_group_dict


def parse_ins_group_tuple_list(ins_group_list, curr_time):
    """「解析」分组批量插入数据"""
    ins_group_tuple_list = []
    if ins_group_list:

        for ins_group in ins_group_list:

            res_code = ins_group.get('res_code')
            group_name = ins_group.get('group_name')
            group_code = ins_group.get('group_code')
            group_desc = ins_group.get('group_desc')
            module_name = ins_group.get('module_name')

            ins_group_tuple = (
                CREATE_USER,
                curr_time,
                UPDATE_USER,
                curr_time,
                CREATE_STAMP,
                module_name,
                res_code,
                group_name,
                group_code,
                group_desc,
            )
            ins_group_tuple_list.append(ins_group_tuple)

    return ins_group_tuple_list


def ins_group_to_db(conn, ins_group_list, curr_time):
    """分组批量插入DB"""
    if ins_group_list:
        ins_group_tuple_list = parse_ins_group_tuple_list(ins_group_list, curr_time)

        ins_sql = """
            INSERT INTO env_mgt_deploy_group(
                create_user, 
                create_time, 
                update_user, 
                update_time, 
                stamp,
                module_name, 
                module_code, 
                deploy_group_name, 
                deploy_group_code,
                deploy_group_desc
                )VALUES(
                    %s, %s, %s, %s, %s, 
                    %s, %s, %s, %s, %s
                );"""
        with conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_sql, ins_group_tuple_list)
            last_rowid = cursor.lastrowid
            for idx in range(0, len(ins_group_list)):
                group_id = last_rowid + idx
                group_obj = ins_group_list[idx]
                group_obj['group_id'] = group_id

        log.info(">>>> ins_group_count: {}".format(ins_count))


def get_upd_bind_tuple_list(upd_re_list, curr_time):
    """「解析」批量「节点绑定」信息"""
    upd_bind_tuple_list = []
    if upd_re_list:
        for upd_bind in upd_re_list:
            group_id = upd_bind.get('group_id')
            bind_id = upd_bind.get('bind_id')

            upd_bind_tuple = (
                UPDATE_USER,
                curr_time,
                group_id,
                bind_id,
            )
            upd_bind_tuple_list.append(upd_bind_tuple)

    return upd_bind_tuple_list


def upd_bind_to_db(conn, upd_re_list, curr_time):
    """绑定批量更新DB"""
    if upd_re_list:
        upd_bind_tuple_list = get_upd_bind_tuple_list(upd_re_list, curr_time)

        upd_sql = """
        UPDATE env_mgt_node_bind
        SET
            update_user = %s, 
            update_time = %s,
            deploy_group = %s
        WHERE
            id = %s"""

        with conn.cursor(cursor=DictCursor) as cursor:
            upd_count = cursor.executemany(upd_sql, upd_bind_tuple_list)

        log.info(">>>> upd_bind_count: {}".format(upd_count))


def save_group_to_db(conn, ins_group_list, upd_re_list, err_group_dict):
    """「保存」分组和绑定数据至DB"""
    curr_time = datetime.now()
    # 分组信息批量插入
    if ins_group_list:
        ins_group_to_db(conn, ins_group_list, curr_time)
    # 绑定信息指更新
    if upd_re_list:
        upd_bind_to_db(conn, upd_re_list, curr_time)

    # 处理完成
    conn.commit()


def sync_cmdb_group_data(conn, group_list):
    """scm中数据处理主方法"""
    if not group_list:
        raise ValueError("分组列表为空！")
    # 节点分组字典
    cmdb_group_dict, cmdb_node_dict = parse_cmdb_group_data(group_list)
    # 节点分组处理
    ins_group_list, upd_re_list, err_group_dict = parse_group_data(conn, cmdb_group_dict, cmdb_node_dict)
    # 保存数据
    save_group_to_db(conn, ins_group_list, upd_re_list, err_group_dict)

    # log.info(">>>> cmdb_group_dict = {}".format(json.dumps(cmdb_group_dict,
    #                                                        sort_keys=True,
    #                                                        indent=4,
    #                                                        ensure_ascii=False)))
    # log.info(">>>> cmdb_node_dict = {}".format(json.dumps(cmdb_node_dict,
    #                                                       sort_keys=True,
    #                                                       indent=4,
    #                                                       ensure_ascii=False)))

    log.info(">>>> ins_group_list = {}".format(json.dumps(ins_group_list,
                                                          sort_keys=True,
                                                          indent=4,
                                                          ensure_ascii=False)))
    log.info(">>>> upd_re_list = {}".format(json.dumps(upd_re_list,
                                                       sort_keys=True,
                                                       indent=4,
                                                       ensure_ascii=False)))
    for err_enum in err_group_dict:
        if err_enum == ErrTypeEnum.ERR_TYPE_SPIDER:
            continue
        err_desc = err_enum.err_desc
        err_list = err_group_dict[err_enum]
        log.info(">>>> err_desc = {}：{}".format(err_desc, len(err_list)))
        log.info(">>>> err_list = {}".format(json.dumps(err_list, sort_keys=True, indent=4, ensure_ascii=False)))


def main(param_sync_type_enum):
    """「分组同步」主方法 zt@2021-12-06"""
    log.info("======== 开始同步 time(start) {} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    log.info("==== 『入参』：{}".format(param_sync_type_enum.name))
    # 1、从cmdb获取节点信息
    cmdb_conn = connect_cmdb_postgre()
    try:
        group_list = get_cmdb_group_data(cmdb_conn)
    finally:
        conn_close(cmdb_conn)

    if group_list:
        scm_conn = connect_scm_mysql()
        try:
            sync_cmdb_group_data(scm_conn, group_list)
        finally:
            conn_close(scm_conn)
    else:
        log.warning(">>>> 未查到分组信息，同步跳过。")


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_sync_type_enum = SyncTypeEnum.SYNC_TYPE_ALL
    if len(sys.argv) > 1:
        req_sync_type_str = sys.argv[1:]
        # 确定步骤
        try:
            req_sync_type_enum = SyncTypeEnum[req_sync_type_str]
        except KeyError:
            log.error("==== ！！！！参数异常！！！！ ====")
            err_msg = ">>>> 参数错误，只支持：空、sync_all、sync_day。"
            log.error(err_msg)
            exit(1)
    try:
        main(req_sync_type_enum)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        e_time = datetime.now()
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        log.info("== 执行最终耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
