# 订单同步脚本 zt@2020-06-22
# 第2版 zt@2020-07-08
import os
import sys

import pymysql
import psycopg2
from datetime import datetime, timedelta
from pymysql.cursors import DictCursor
from psycopg2.extras import RealDictCursor
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES, CMDBDATABASES


def connect_scm_mysql():
    """
    获取scm的mysql数据库连接
    :return: mysql数据库连接
    """
    conn = pymysql.connect(
        # host='**************',port=3306,database='spider',charset='utf8',user='scm',password='howbuyscm'
        host=DATABASES['IP'], port=DATABASES['PORT'], database=DATABASES['DB'], charset=DATABASES['CHARSET'], user=DATABASES['USER'], password=DATABASES['PASSWORD'])

    return conn


def connect_cmdb_postgre():
    """
    获取cmdb的postgre数据库连接
    :return: postgre数据库连接
    """
    conn = psycopg2.connect(
        # host="**************",port=5432,database="witcher_cmdb",user="witcher_cmdb",password="LziV5E01t6E3dhWBzgQwfQ=="
        host=CMDBDATABASES['IP'], port=CMDBDATABASES['PORT'], database=CMDBDATABASES['DB'], user=CMDBDATABASES['USER'],password=CMDBDATABASES['PASSWORD'])
    return conn


def conn_close(conn):
    """
    mysql和postgre的数据连接关闭
    :param conn: 数据库连接
    :return:
    """
    conn.close()


def get_scm_order_data(conn):
    """
    先从scm中获取指定时间（暂定30天）「未完成」的订单
    :param conn: scm的mysql连接
    :return: scm中「未完成」的订单信息
    """
    timeline = datetime.now() - timedelta(days=30)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute("""
            SELECT 
                m.id,
                m.order_code,
                m.module_name,
                m.module_category_code,
                m.module_code,
                m.team_name,
                m.team_short_name,
                m.cmdb_team_name,
                m.region_name,
                m.suite_code,
                m.zone_id,
                m.zone_code,
                m.cmdb_provider_code,
                m.cmdb_region_code,
                m.cmdb_zone_code,
                m.cmdb_env_code,
                m.bread_domain_code,
                m.vm_id,
                m.vm_code,
                m.vm_count, 
                m.order_status,
                m.apply_user,
                m.apply_reason,
                m.reject_reason,
                m.created_at,
                m.updated_at,
                m.valid_at,
                m.invalid_at,
                m.distribute_policy,
                m.create_user,
                m.create_time,
                m.update_user,
                m.update_time,
                m.stamp
            FROM env_mgt_node_apply m
            WHERE m.update_time >= '{}'
            AND m.order_status != 3
            ORDER BY m.id DESC;
            """.format(timeline.strftime("%Y-%m-%d %H:%M:%S")))
        result = cursor.fetchall()

    return result


def get_cmdb_order_data(conn, scm_order_list):
    """
    从cmdb中找到「已完成」的指定的订单信息
    :param conn: cmdb的postgre数据库连接
    :param scm_order_list: 从scm中查出的「未完成」的订单
    :return: cmdb中「已完成」的订单数据
    """

    order_sql = """
    SELECT 
        m.id,
        m.resource_category_code,
        m.resource_code,
        m.order_code,
        m.server_count,
        m.provider_code,
        m.region_code,
        m.zone_code,
        m.order_status,
        m.apply_status,
        m.server_config_name,
        m.apply_reason,
        m.reject_reason,
        m.created_at,
        m.updated_at,
        m.valid_at,
        m.invalid_at,
        m.apply_user,
        m.resource_domain_name,
        m.distribute_policy,
        m.environ_code
    FROM order_order m
    WHERE m.apply_status in(4, 5, 6)
    AND m.order_code IN ({})
    ORDER BY m.id DESC;
    """.format(', '.join(["'%s'" % item["order_code"] for item in scm_order_list]))

    with conn.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(order_sql)
        order_list = cursor.fetchall()

    order_dict = None
    order_server_dict = None
    if order_list and len(order_list) > 0:
        order_dict = {item["order_code"]: item for item in order_list}

        server_sql = """
                SELECT
                    m.order_code,
                    s.id,
                    s.created_at,
                    s.updated_at,
                    s.name,
                    s.ip,
                    s.provider_code,
                    s.region_code,
                    s.zone_code,
                    s.os,
                    s.status,
                    s.recycled_at,
                    s.environ_code,
                    s.minion_id,
                    s.server_type,
                    s.sync_time,
                    s.sync_result,
                    s.server_cpu,
                    s.server_memory
                from order_order m
                INNER JOIN order_order_servers os on os.order_id = m.id
                INNER JOIN resources_server s on os.server_id = s.id
                WHERE m.order_code in ({})
                ORDER BY m.order_code DESC, s.ip;
                """.format(', '.join(["'%s'" % item["order_code"] for item in order_list]))

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(server_sql)
            server_list = cursor.fetchall()

        if server_list and len(server_list) > 0:
            order_server_dict = {}
            for order_server_obj in server_list:
                order_code = order_server_obj["order_code"]
                if order_code in order_server_dict.keys():
                    order_server_dict[order_code].append(order_server_obj)
                else:
                    order_server_list = [order_server_obj]
                    order_server_dict[order_code] = order_server_list
    else:
        print(">>>> cmdb中未找到「已交付」订单，等待下次同步")

    return order_dict, order_server_dict


def save_scm_order_date(conn, scm_order_list, order_dict, order_server_dict):
    """
    根据传入的cmdb信息，更新scm中的订单，并产生绑定数据
    :param conn: scm的数据库连接
    :param scm_order_list: scm中「未完成」的订单的列表
    :param order_dict: cmdb中「已完成」的订单字典
    :param order_server_dict: cmdb中对应订单的主机列表字典
    :return:
    """
    print("========== 3、开始数据保存 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    curr_time = datetime.now()
    user = 'scm_sync'
    stamp = 0

    # 1-1、先解析scm中的订单数据
    scm_order_dict = {item["order_code"]: item for item in scm_order_list}
    # 1-2、解析可用域ID
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT
                m.id,
                m.addr_name,
                m.addr_short_name,
                m.type_name,
                m.type_short_name,
                m.region_name,
                m.region_desc,
                m.region_is_active,
                m.region_group,
                m.create_user,
                m.create_time,
                m.update_user,
                m.update_time,
                m.stamp
            FROM env_mgt_region m
            ORDER BY m.id;
            ''')
        result = cursor.fetchall()
        region_name_dict = {item["region_name"]: item["id"] for item in result}

    # 1-3、解析环境套ID
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT
                m.id,
                m.region_id,
                m.suite_code,
                m.suite_name,
                m.suite_desc,
                m.suite_is_active,
                m.support_docker,
                m.test_group,
                m.create_user,
                m.create_time,
                m.update_user,
                m.update_time,
                m.stamp
            FROM env_mgt_suite m
            ORDER BY m.id;
            ''')
        result = cursor.fetchall()
        suite_code_dict = {item["suite_code"]: item["id"] for item in result}

    print("========== 3-1、获取scm中的订单信息结束 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    # 2、再解析需要操作的数据
    upd_order_list = []
    ins_bind_list = []
    for order_code, order_row in order_dict.items():

        # scm中的订单信息
        scm_order_obj = scm_order_dict[order_code]
        module_name = scm_order_obj["module_name"]
        region_name = scm_order_obj["region_name"]
        suite_code = scm_order_obj["suite_code"]
        # 取到对应的ID
        region_id = region_name_dict[region_name]
        suite_id = suite_code_dict[suite_code]

        # cmdb中订单数据
        provider_code = order_row["provider_code"]
        region_code = order_row["region_code"]
        zone_code = order_row["zone_code"]
        environ_code = order_row["environ_code"]
        resource_domain_name = order_row["resource_domain_name"]

        distribute_policy = order_row["distribute_policy"]
        reject_reason = order_row["reject_reason"]
        valid_at = order_row["valid_at"]
        invalid_at = order_row["invalid_at"]

        order_status = order_row["order_status"]
        apply_status = order_row["apply_status"]

        # 构建订单更新元组列表
        order_tuple = (provider_code,
                       region_code,
                       zone_code,
                       environ_code,
                       resource_domain_name,
                       distribute_policy,
                       reject_reason,
                       valid_at,
                       invalid_at,
                       order_status,
                       apply_status,
                       order_code)

        upd_order_list.append(order_tuple)

        ins_server_list = []
        tmp_server_list = order_server_dict.get(order_code)
        if tmp_server_list:
            for tmp_server_obj in tmp_server_list:
                node_name = tmp_server_obj["name"]
                node_ip = tmp_server_obj["ip"]
                minion_id = tmp_server_obj["minion_id"]
                # region_id = None
                node_os = tmp_server_obj["os"]
                node_status = tmp_server_obj["status"]
                node_recyled = tmp_server_obj["recycled_at"]
                node_desc = "「{}」中申请新增".format(order_code)
                create_user = user
                create_time = curr_time
                update_user = user
                update_time = curr_time

                server_tuple = (node_name,
                                node_ip,
                                minion_id,
                                region_id,      # 此申请单对应的可用域ID
                                node_os,
                                node_status,
                                node_recyled,
                                node_desc,
                                create_user,
                                create_time,
                                update_user,
                                update_time,
                                stamp)

                ins_server_list.append(server_tuple)

        ins_bind_obj = {
            "order_code": order_code,
            "module_name": module_name,
            "region_id": region_id,
            "suite_id": suite_id,
            "ins_server_list": ins_server_list,
        }

        ins_bind_list.append(ins_bind_obj)

    # 最后解析完所有订单，打印一下
    print("========== 3-2、解析cmdb中的订单信息结束 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    # 开始DB操作
    # 1、更新scm中的订单信息

    # 构建订单更新元组列表
    order_tuple = (provider_code,
                   region_code,
                   zone_code,
                   environ_code,
                   resource_domain_name,
                   distribute_policy,
                   reject_reason,
                   valid_at,
                   invalid_at,
                   order_status,
                   apply_status,
                   order_code)

    upd_order_list.append(order_tuple)

    upd_order_sql = """
        UPDATE
            env_mgt_node_apply 
        SET 
            cmdb_provider_code = %s, 
            cmdb_region_code = %s, 
            cmdb_zone_code = %s, 
            cmdb_env_code = %s, 
            bread_domain_code = %s, 
            distribute_policy = %s, 
            reject_reason = %s, 
            valid_at = %s, 
            invalid_at = %s, 
            order_status = %s, 
            apply_status = %s
        WHERE 
            order_code = %s;"""

    with conn.cursor(cursor=DictCursor) as cursor:
        upd_count = cursor.executemany(upd_order_sql, upd_order_list)

    print("========== 3-3、更新scm订单数据{}笔 {} ==========".format(upd_count,
                                                             datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    # 2、节点及绑定
    ins_server_sql = """
        INSERT INTO env_mgt_node(
            node_name, 
            node_ip, 
            minion_id, 
            region_id, 
            node_os, 
            node_status, 
            node_recyled, 
            node_desc, 
            create_user, 
            create_time, 
            update_user, 
            update_time, 
            stamp
        )VALUES(
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        );"""

    # 2-1、循环处理
    all_bind_list = []
    for ins_bind_obj in ins_bind_list:

        # 数据获取
        order_code = ins_bind_obj["order_code"]
        module_name = ins_bind_obj["module_name"]
        suite_id = ins_bind_obj["suite_id"]
        ins_server_list = ins_bind_obj["ins_server_list"]

        print("========== 3-4、处理订单「{}」，主机{}台 {}==========".format(order_code,
                                                                  len(ins_server_list),
                                                                  datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

        # 2-2、先批量插入
        if ins_server_list and len(ins_server_list) > 0:
            with conn.cursor(cursor=DictCursor) as cursor:
                cursor.executemany(ins_server_sql, ins_server_list)
                server_id = cursor.lastrowid
                for idx in range(0, len(ins_server_list)):
                    node_id = server_id + idx

                    tmp_bind_obj = (module_name,                            # module_name
                                    suite_id,                               # suite_id
                                    node_id,                                # node_id
                                    None,                                   # node_port
                                    None,                                   # node_docker
                                    "「{}」中绑定的".format(order_code),      # node_bind_desc
                                    1,                                      # deploy_group 默认分组
                                    1,                                      # deploy_type 虚拟机
                                    None,                                   # deploy_path
                                    None,                                   # health_check_url
                                    user,                                   # create_user
                                    curr_time,                              # create_time
                                    user,                                   # update_user
                                    curr_time,                              # update_time
                                    stamp,                                  # stamp
                                    )
                    all_bind_list.append(tmp_bind_obj)
    print("========== 3-4、所有订单处理完成 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    # 3、绑定主机和应用的关系
    ins_bind_sql = """
        INSERT INTO env_mgt_node_bind(
            module_name, 
            suite_id, 
            node_id, 
            node_port, 
            node_docker, 
            node_bind_desc, 
            deploy_group, 
            deploy_type, 
            deploy_path, 
            health_check_url, 
            create_user, 
            create_time, 
            update_user, 
            update_time, 
            stamp
            )VALUES(
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            );"""
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.executemany(ins_bind_sql, all_bind_list)

    print("========== 3-5、完成主机绑定{}台 {} ==========".format(len(all_bind_list),
                                                          datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    # 4、处理完成
    conn.commit()


def main():
    print("========== 开始订单同步 time(start) {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    # 1、先从scm中获取未完成的订单列表
    scm_conn = connect_scm_mysql()
    try:
        scm_order_list = get_scm_order_data(scm_conn)
    finally:
        conn_close(scm_conn)

    print("========== 1、获取需要同步的订单{}笔 {} ==========".format(len(scm_order_list),
                                                           datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    # 2、获取cmdb中已完成的订单
    if scm_order_list:
        cmdb_conn = connect_cmdb_postgre()
        try:
            order_dict, order_server_dict = get_cmdb_order_data(cmdb_conn, scm_order_list)
        finally:
            conn_close(cmdb_conn)
        print("========== 2、获取cmdb中的订单信息结束 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

        # 3、更新scm中的订单数据
        if order_dict:
            scm_conn = connect_scm_mysql()
            try:
                save_scm_order_date(scm_conn, scm_order_list, order_dict, order_server_dict)
            finally:
                conn_close(scm_conn)
    else:
        print(">>>> 非常好，所有订单已同步")

    print("========== 完成订单同步 time(end) {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))


if __name__ == '__main__':
    main()
