# 分组同步脚本 zt@2021-12-21
# 第1版 zt@2021-12-21
# 特别注意：因自动绑定需要和用户逐个确定节点信息，暂不启用 zt@2021-12-22
import json
import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
from enum import unique, Enum

print(">>>> PATH: {}".format(sys.path))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))

import pymysql
import psycopg2
from datetime import datetime, timedelta
from pymysql.cursors import DictCursor
from psycopg2.extras import RealDictCursor
from settings import DATABASES, CMDBDATABASES

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "sync_bind_from_cmdb"
LOG_PATH = "/data/logs/" + LOG_TGT
LOG_NAME = "sync_bind_from_cmdb.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========
CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None
ZONE_CACHE_SUITE_DICT = {}


@unique
class SyncTypeEnum(Enum):
    SYNC_TYPE_ALL = ('sync_all', "同步所有信息")
    SYNC_TYPE_DAY = ('sync_day', "同步部分信息")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_NODE_NONE = ('err_node_none', "cmdb中无此节点信息（minion_id）。")
    ERR_TYPE_BIND_NONE = ('err_bind_none', "cmdb中无分组、无订单，不知道绑哪个应用。")
    ERR_TYPE_BIND_MODULE = ('err_bind_module', "cmdb中的res_code，在spider找不到对应的模块名，无法绑定。")
    ERR_TYPE_BIND_SUITE = ('err_bind_suite', "spider中没找到对应的环境套映射信息。")
    ERR_TYPE_GROUP = ('err_group', "spider缺少分组数据")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def add_err_to_dict(enum, err_bind_dict, err_cmdb_obj):
    """添加错误信息至错误列表中"""
    err_list = err_bind_dict.get(enum)
    if not err_list or len(err_list) == 0:
        err_list = [err_cmdb_obj]
        err_bind_dict[enum] = err_list
    else:
        err_list.append(err_cmdb_obj)


def connect_cmdb_postgre():
    """获取cmdb的postgre数据库连接"""
    conn = psycopg2.connect(host=CMDBDATABASES['IP'],
                            port=CMDBDATABASES['PORT'],
                            database=CMDBDATABASES['DB'],
                            user=CMDBDATABASES['USER'],
                            password=CMDBDATABASES['PASSWORD'])
    return conn


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgre的数据连接关闭"""
    conn.close()


def get_suite_by_zone(conn, zone_code):
    """通过zone获取最高绑定优化的环境套id，并缓存"""
    global ZONE_CACHE_SUITE_DICT
    if ZONE_CACHE_SUITE_DICT is None or len(ZONE_CACHE_SUITE_DICT) == 0:
        scm_zone_sql = '''
            select
                z.zone_code,
                s.id as suite_id
            from env_mgt_zone z
            left join env_mgt_region r on r.region_name = z.relative_region_code
            left join (
                select region_id,
                       IFNULL(max(node_bind_level), 0) as max_level
                from env_mgt_suite
                where suite_is_active = true
                group by region_id
                )v on v.region_id = r.id
            left join env_mgt_suite s on s.region_id = r.id and s.suite_is_active = true 
                and IFNULL(s.node_bind_level, 0) = v.max_level
            order by z.zone_code;'''

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(scm_zone_sql)
            result = cursor.fetchall()
            ZONE_CACHE_SUITE_DICT = {
                item['zone_code']: item['suite_id'] for item in result if item['zone_code'] is not None
            }

    suite_id = ZONE_CACHE_SUITE_DICT[zone_code]

    return suite_id


def query_spider_group_data(conn):
    """「DB」获取spider中的分组列表信息"""
    spider_bind_sql = '''
    select 
        g.id as group_id, 
        g.module_name, 
        g.deploy_group_code as group_code, 
        g.deploy_group_name as group_name, 
        g.deploy_group_desc as group_desc
    from env_mgt_deploy_group g
    order by g.deploy_group_code;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_bind_sql)
        result = cursor.fetchall()

    return result


def parse_spider_group_dict_with_cache(conn):
    global SPIDER_GROUP_ID_DICT
    if not SPIDER_GROUP_ID_DICT:
        spider_group_list = query_spider_group_data(conn)
        if spider_group_list and len(spider_group_list) > 0:
            SPIDER_GROUP_ID_DICT = {}
            for group_obj in spider_group_list:
                group_id = group_obj['group_id']
                module_name = group_obj['module_name']
                group_code = group_obj['group_code']
                group_name = group_obj['group_name']
                group_desc = group_obj['group_desc']

                if group_id and group_code:
                    SPIDER_GROUP_ID_DICT[group_code] = {
                        'group_id': group_id,
                        'module_name': module_name,
                        'group_name': group_name,
                        'group_desc': group_desc,
                    }
    return SPIDER_GROUP_ID_DICT


def query_spider_module_data(conn):
    """「DB」获取spider中的「模块」列表信息"""
    query_sql = '''
    select 
        m.module_name, 
        m.module_code
    from app_mgt_app_module m
    where m.module_code is not null
        and replace(m.module_code, char(9), '') <> ''
    order by m.module_code;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(query_sql)
        result = cursor.fetchall()

    return result


def parse_spider_module_dict_with_cache(conn):
    global SPIDER_MODULE_NAME_DICT
    if not SPIDER_MODULE_NAME_DICT:
        spider_module_list = query_spider_module_data(conn)
        if spider_module_list and len(spider_module_list) > 0:
            SPIDER_MODULE_NAME_DICT = {}
            for module_obj in spider_module_list:
                module_name = module_obj.get('module_name')
                module_code = module_obj.get('module_code')

                if module_code:
                    SPIDER_MODULE_NAME_DICT[module_code] = module_name

    return SPIDER_MODULE_NAME_DICT


def get_cmdb_server_data(conn):
    """一次获取所有节点信息"""
    cmdb_server_sql = '''
        select
               s.minion_id,
               s.ip,
               s.name,
               s.status,
               s.zone_code as server_zone_code,
               g.code as group_code,
               g.resource_code as group_res_code,
               o.order_code,
               o.resource_code as order_res_code,
               o.zone_code as order_zone_code
        from resources_server s
            left join resources_re_resourcegroup_server rs on rs.server_id = s.id
            left join resources_resourcegroup g on g.id = rs.resource_group_id
            left join order_order_servers os on os .server_id = s.id
            left join order_order o on o.id = os.order_id
        where s.minion_id is not null
            and trim(s.minion_id) <> ''
        order by s.minion_id, g.resource_code, o.resource_code;'''

    with conn.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(cmdb_server_sql)
        result = cursor.fetchall()

    return result


def parse_cmdb_server_data(server_list):
    """转化cmdb节点信息为字典"""
    cmdb_server_dict = None
    if server_list and len(server_list) > 0:
        cmdb_server_dict = {}

        tmp_id = None
        filter_set = set()
        for server_obj in server_list:
            srv_id = server_obj['minion_id']

            srv_ip = server_obj['ip']
            srv_name = server_obj['name']
            srv_status = server_obj['status']
            srv_zone_code = server_obj['server_zone_code']

            group_code = server_obj['group_code']
            group_res_code = server_obj['group_res_code']

            order_code = server_obj['order_code']
            order_res_code = server_obj['order_res_code']
            order_zone_code = server_obj['order_zone_code']

            srv_obj_dict = {
                'srv_id': srv_id,

                'srv_ip': srv_ip,
                'srv_name': srv_name,
                'srv_status': srv_status,
                'srv_zone_code': srv_zone_code,

                'group_code': group_code,
                'group_res_code': group_res_code,

                'order_code': order_code,
                'order_res_code': order_res_code,
                'order_zone_code': order_zone_code,
            }

            res_code = group_res_code
            if not res_code:
                res_code = order_res_code
            if res_code:
                filter_key = '{}@#@{}'.format(srv_id, res_code)
                if filter_key in filter_set:
                    logging.error("cmdb中的重复数据：{}".format(json.dumps(srv_obj_dict,
                                                                    sort_keys=True,
                                                                    indent=4,
                                                                    ensure_ascii=False)))
                    continue
                else:
                    filter_set.add(filter_key)

            # 快速组装优化 zt@2021-12-22
            if srv_id != tmp_id:
                cmdb_server_dict[srv_id] = [srv_obj_dict]
                tmp_id = srv_id
            else:
                srv_list = cmdb_server_dict.get(tmp_id)
                srv_list.append(srv_obj_dict)

    return cmdb_server_dict


def get_spider_nobind_data(conn):
    """获取spider中未绑定数据"""
    spider_nobind_sql = '''
        select 
               n.id as srv_id,
               n.node_name,
               n.node_ip,
               n.minion_id,
               n.node_status
        from env_mgt_node n
            left join env_mgt_node_bind b on b.node_id = n.id
        where n.minion_id is not null
            and trim(n.minion_id) <> ''
          and b.id is null;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_nobind_sql)
        result = cursor.fetchall()

    return result


def get_spider_nobind_dict(conn):
    spider_nobind_dict = {}
    spider_nobind_list = get_spider_nobind_data(conn)
    if spider_nobind_list and len(spider_nobind_list) > 0:
        for nobind_tmp in spider_nobind_list:
            minion_id = nobind_tmp['minion_id']

            srv_id = nobind_tmp['srv_id']
            node_name = nobind_tmp['node_name']
            node_ip = nobind_tmp['node_ip']
            node_status = nobind_tmp['node_status']

            spider_nobind_dict[minion_id] = {
                'minion_id': minion_id,
                'srv_id': srv_id,
                'node_name': node_name,
                'node_ip': node_ip,
                'node_status': node_status,
            }

    return spider_nobind_dict


def parse_cmdb_server_obj(conn, cmdb_server_obj, err_bind_dict):
    ins_bind_obj = None
    if cmdb_server_obj:
        srv_id = cmdb_server_obj.get('srv_id')

        srv_ip = cmdb_server_obj.get('srv_ip')
        srv_name = cmdb_server_obj.get('srv_name')
        srv_status = cmdb_server_obj.get('srv_status')
        srv_zone_code = cmdb_server_obj.get('srv_zone_code')

        group_code = cmdb_server_obj.get('group_code')
        group_res_code = cmdb_server_obj.get('group_res_code')

        order_code = cmdb_server_obj.get('order_code')
        order_res_code = cmdb_server_obj.get('order_res_code')
        order_zone_code = cmdb_server_obj.get('order_zone_code')

        if group_res_code or order_res_code:
            spider_module_dict = parse_spider_module_dict_with_cache(conn)

            spider_suite_id = None
            spider_group_id = None
            if group_res_code:
                spider_bind_desc = "根据分组「{}」自动绑定。".format(group_code)

                spider_module_name = spider_module_dict.get(group_res_code)

                if srv_zone_code:
                    spider_suite_id = get_suite_by_zone(conn, srv_zone_code)

                if group_code:
                    spider_group_id_cache = parse_spider_group_dict_with_cache(conn)
                    if spider_group_id_cache:
                        spider_group_obj = spider_group_id_cache.get(group_code)
                        if spider_group_obj:
                            spider_group_id = spider_group_obj.get('group_id')
                        else:
                            add_err_to_dict(ErrTypeEnum.ERR_TYPE_GROUP, err_bind_dict, cmdb_server_obj)
            else:
                spider_bind_desc = "根据订单「{}」自动绑定。".format(order_code)

                spider_module_name = spider_module_dict.get(order_res_code)

                if order_zone_code:
                    spider_suite_id = get_suite_by_zone(conn, order_zone_code)

            if spider_module_name and spider_suite_id:
                ins_bind_obj = {
                    'module_name': spider_module_name,
                    'node_bind_desc': spider_bind_desc,
                    'suite_id': spider_suite_id,
                    'deploy_group': spider_group_id,
                }
            else:
                if not spider_module_name:
                    add_err_to_dict(ErrTypeEnum.ERR_TYPE_BIND_MODULE, err_bind_dict, cmdb_server_obj)

                if not spider_suite_id:
                    add_err_to_dict(ErrTypeEnum.ERR_TYPE_BIND_SUITE, err_bind_dict, cmdb_server_obj)
    else:
        add_err_to_dict(ErrTypeEnum.ERR_TYPE_BIND_NONE, err_bind_dict, cmdb_server_obj)

    return ins_bind_obj


def get_ins_bind_list(conn, cmdb_server_dict, err_bind_dict):
    ins_bind_list = None

    spider_nobind_dict = get_spider_nobind_dict(conn)
    if spider_nobind_dict and len(spider_nobind_dict) > 0 and cmdb_server_dict and len(cmdb_server_dict) > 0:
        ins_bind_list = []
        spider_nobind_dict = get_spider_nobind_dict(conn)

        for k, v in spider_nobind_dict.items():
            cmdb_server_list = cmdb_server_dict.get(k)

            if not cmdb_server_list or len(cmdb_server_list) == 0:
                node_list = err_bind_dict.get(ErrTypeEnum.ERR_TYPE_NODE_NONE)
                if not node_list or len(node_list) == 0:
                    node_list = [v]
                    err_bind_dict[ErrTypeEnum.ERR_TYPE_NODE_NONE] = node_list
                else:
                    node_list.append(v)
                # 记录错误后，跳过
                continue

            for cmdb_server_obj in cmdb_server_list:
                ins_bind_obj = parse_cmdb_server_obj(conn, cmdb_server_obj, err_bind_dict)
                if ins_bind_obj:
                    srv_id = v.get('srv_id')
                    ins_bind_obj['node_id'] = srv_id

                    ins_bind_list.append(ins_bind_obj)

    return ins_bind_list


def parse_bind_data(conn, cmdb_server_dict):
    """获取分组同步要用到的绑定、分组、错误信息"""
    ins_bind_list = []
    err_bind_dict = {}
    if cmdb_server_dict:
        ins_bind_list = get_ins_bind_list(conn, cmdb_server_dict, err_bind_dict)

    return ins_bind_list, err_bind_dict


def parse_ins_bind_tuple_list(ins_bind_list, curr_time):
    """「解析」分组批量插入数据"""
    ins_bind_tuple_list = []
    if ins_bind_list:

        for ins_bind in ins_bind_list:

            module_name = ins_bind.get('module_name')
            suite_id = ins_bind.get('suite_id')
            node_id = ins_bind.get('node_id')
            node_bind_desc = ins_bind.get('node_bind_desc')
            deploy_group = ins_bind.get('deploy_group')

            if node_bind_desc:
                node_bind_desc = "{}zt@{}".format(node_bind_desc, curr_time.strftime(FMT_TIME_STR))

            ins_bind_tuple = (
                CREATE_USER,
                curr_time,
                UPDATE_USER,
                curr_time,
                CREATE_STAMP,
                module_name,
                suite_id,
                node_id,
                node_bind_desc,
                deploy_group,
            )
            ins_bind_tuple_list.append(ins_bind_tuple)

    return ins_bind_tuple_list


def ins_bind_to_db(conn, ins_bind_list, curr_time):
    """分组批量插入DB"""
    if ins_bind_list:
        ins_bind_tuple_list = parse_ins_bind_tuple_list(ins_bind_list, curr_time)

        ins_bind_sql = """
            INSERT INTO env_mgt_node_bind(
                create_user, 
                create_time, 
                update_user, 
                update_time, 
                stamp,
                module_name, 
                suite_id, 
                node_id, 
                node_bind_desc,
                deploy_group
                )VALUES(
                    %s, %s, %s, %s, %s, 
                    %s, %s, %s, %s, %s
                );"""
        with conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_bind_sql, ins_bind_tuple_list)
            last_rowid = cursor.lastrowid
            for idx in range(0, len(ins_bind_list)):
                bind_id = last_rowid + idx
                bind_obj = ins_bind_list[idx]
                bind_obj['bind_id'] = bind_id

        log.info(">>>> ins_group_count: {}".format(ins_count))


def save_bind_to_db(conn, ins_bind_list):
    """「保存」绑定和分组数据至DB"""
    curr_time = datetime.now()
    # 分组信息批量插入
    if ins_bind_list:
        ins_bind_to_db(conn, ins_bind_list, curr_time)

    # 处理完成
    conn.commit()


def sync_cmdb_nobind_data(conn, cmdb_server_list):
    """scm中数据处理主方法"""
    if not cmdb_server_list:
        raise ValueError("cmdb节点列表为空！")
    # 节点的分组&订单字典
    cmdb_server_dict = parse_cmdb_server_data(cmdb_server_list)
    # 节点绑定&分组处理
    ins_bind_list, err_bind_dict = parse_bind_data(conn, cmdb_server_dict)
    # 保存数据
    save_bind_to_db(conn, ins_bind_list)

    # log.info(">>>> cmdb_server_dict = {}".format(json.dumps(cmdb_server_dict,
    #                                                         sort_keys=True,
    #                                                         indent=4,
    #                                                         ensure_ascii=False)))
    log.info(">>>> ins_bind_list = {}".format(json.dumps(ins_bind_list,
                                                         sort_keys=True,
                                                         indent=4,
                                                         ensure_ascii=False)))
    for err_enum in err_bind_dict:
        err_desc = err_enum.err_desc
        err_list = err_bind_dict[err_enum]
        log.info(">>>> err_desc = {}：{}".format(err_desc, len(err_list)))
        log.info(">>>> err_list = {}".format(json.dumps(err_list, sort_keys=True, indent=4, ensure_ascii=False)))


def main(param_sync_type_enum):
    """「自动绑定」主方法 zt@2021-12-22"""
    log.info("======== 开始同步 time(start) {} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    log.info("==== 『入参』：{}".format(param_sync_type_enum.name))
    # 1、从cmdb获取节点信息
    cmdb_conn = connect_cmdb_postgre()
    try:
        cmdb_server_list = get_cmdb_server_data(cmdb_conn)
    finally:
        conn_close(cmdb_conn)

    if cmdb_server_list:
        scm_conn = connect_scm_mysql()
        try:
            sync_cmdb_nobind_data(scm_conn, cmdb_server_list)
        finally:
            conn_close(scm_conn)
    else:
        log.warning(">>>> 未查到需要自动绑定的数据，同步跳过。")


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_sync_type_enum = SyncTypeEnum.SYNC_TYPE_ALL
    if len(sys.argv) > 1:
        req_sync_type_str = sys.argv[1:]
        # 确定步骤
        try:
            req_sync_type_enum = SyncTypeEnum[req_sync_type_str]
        except KeyError:
            log.error("==== ！！！！参数异常！！！！ ====")
            err_msg = ">>>> 参数错误，只支持：空、sync_all、sync_day。"
            log.error(err_msg)
            exit(1)
    try:
        main(req_sync_type_enum)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        e_time = datetime.now()
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        log.info("== 执行最终耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
