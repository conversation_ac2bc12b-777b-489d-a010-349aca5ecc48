# 「回收单」同步 zt@2020-07-21

import pymysql
import psycopg2
from datetime import datetime, timedelta
from pymysql.cursors import DictCursor
from psycopg2.extras import RealDictCursor


def connect_scm_mysql():
    """
    获取scm的mysql数据库连接
    :return: mysql数据库连接
    """
    conn = pymysql.connect(
        host='**************',
        port=3306,
        database='spider',
        charset='utf8',
        user='ops',
        password='ops'
    )

    return conn


def connect_cmdb_postgre():
    """
    获取cmdb的postgre数据库连接
    :return: postgre数据库连接
    """
    conn = psycopg2.connect(
        host="**************",
        port=5432,
        database="witcher_cmdb_sit",
        user="postgres",
        password="postgres")
    return conn


def conn_close(conn):
    """
    mysql和postgre的数据连接关闭
    :param conn: 数据库连接
    :return:
    """
    conn.close()


def get_scm_recycle_data(conn):
    """
    先从scm中获取指定时间（暂定30天）「未完成」的「回收单」
    :param conn: scm的mysql连接
    :return: scm中「未完成」的订单信息
    """
    timeline = datetime.now() - timedelta(days=30)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute("""
            SELECT 
                m.id,
                m.recycle_order_code,
                m.recycle_time,
                m.recycle_order_status,
                m.recycle_order_user,
                m.recycle_order_reason,
                m.recycle_order_desc,
                m.create_user,
                m.create_time,
                m.update_user,
                m.update_time,
                m.stamp
            FROM env_mgt_recycle_order m
            WHERE m.update_time >= '{}'
            AND m.recycle_order_status = 4
            ORDER BY m.id DESC;
            """.format(timeline.strftime("%Y-%m-%d %H:%M:%S")))
        result = cursor.fetchall()

    return result


def get_cmdb_recycle_data(conn, scm_recycle_list):
    """
    从cmdb中找到「已完成」的指定的「回收单」
    :param conn: cmdb的postgre数据库连接
    :param scm_recycle_list: 从scm中查出的「未完成」的「回收单」
    :return: cmdb中「已完成」的订单数据
    """

    recycle_sql = """
        SELECT 
            m.id,
            m.code,
            m.created_at,
            m.updated_at,
            m.recycled_at,
            m.apply_user,
            m.order_status,
            m.recycle_order_reason,
            m.recycle_order_desc,
            m.recycle_reject_reason
        FROM order_recycleorder m
        WHERE m.order_status = 3
        AND m.code IN ({})
        ORDER BY m.id DESC;
        """.format(', '.join(["'%s'" % item["recycle_order_code"] for item in scm_recycle_list]))
    print(">>>> recycle_sql = ", recycle_sql)

    with conn.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(recycle_sql)
        recycle_list = cursor.fetchall()

    recycle_dict = None
    recycle_server_dict = None
    if recycle_list and len(recycle_list) > 0:
        recycle_dict = {item["code"]: item for item in recycle_list}

        server_sql = """
            SELECT
                m.code,
                ros.operate_status,
                s.id,
                s.created_at,
                s.updated_at,
                s.name,
                s.ip,
                s.provider_code,
                s.region_code,
                s.zone_code,
                s.os,
                s.status,
                s.recycled_at,
                s.environ_code,
                s.minion_id,
                s.server_type,
                s.sync_time,
                s.sync_result,
                s.server_cpu,
                s.server_memory
            FROM order_recycleorder m
            INNER JOIN order_recycleorder_server ros on ros.recycle_order_id = m.id
            INNER JOIN resources_server s on ros.server_id = s.id
            WHERE m.code in ({})
            ORDER BY m.code DESC, s.ip ASC;
            """.format(', '.join(["'%s'" % item["code"] for item in recycle_list]))
        print(">>>> server_sql = ", server_sql)
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(server_sql)
            server_list = cursor.fetchall()

        if server_list and len(server_list) > 0:
            recycle_server_dict = {}
            for recycle_server_obj in server_list:
                recycle_code = recycle_server_obj["code"]
                if recycle_code in recycle_server_dict.keys():
                    recycle_server_dict[recycle_code].append(recycle_server_obj)
                else:
                    recycle_server_list = [recycle_server_obj]
                    recycle_server_dict[recycle_code] = recycle_server_list
    else:
        print(">>>> cmdb中未找到「已完成」回收单，等待下次同步")

    return recycle_dict, recycle_server_dict


def save_scm_recycle_date(conn, scm_recycle_list, recycle_dict, recycle_server_dict):
    """
    根据传入的cmdb信息，更新scm中的「回收单」，并清除绑定数据
    :param conn: scm的数据库连接
    :param scm_recycle_list: scm中「未完成」的「回收单」的列表
    :param recycle_dict: cmdb中「已完成」的「回收单」字典
    :param recycle_server_dict: cmdb中对应「回收单」的主机列表字典
    :return:
    """
    print("========== 3、开始数据保存 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    curr_time = datetime.now()
    user = 'scm_sync'

    # 1、直接解析「回收单」服务器字典
    upd_recycle_list = []
    del_bind_list = []
    upd_server_list = []
    for recycle_code, recycle_server_list in recycle_server_dict.items():

        if recycle_server_list and len(recycle_server_list) > 0:
            for recycle_server_obj in recycle_server_list:
                operate_status = recycle_server_obj['operate_status']
                if operate_status == 2 or operate_status == 3:
                    # 构建「回收单」更新元组列表
                    upd_recycle_obj = recycle_dict[recycle_code]
                    recycled_at = upd_recycle_obj['recycled_at']
                    apply_user = upd_recycle_obj['apply_user']
                    order_status = upd_recycle_obj['order_status']
                    recycle_order_reason = upd_recycle_obj['recycle_order_reason']
                    recycle_order_desc = upd_recycle_obj['recycle_order_desc']
                    recycle_reject_reason = upd_recycle_obj['recycle_reject_reason']

                    upd_recycle_tuple = (recycled_at,
                                         order_status,
                                         apply_user,
                                         recycle_order_reason,
                                         recycle_order_desc,
                                         recycle_reject_reason,
                                         user,
                                         curr_time,
                                         recycle_code,)
                    upd_recycle_list.append(upd_recycle_tuple)

                    # 处理「已完成」回收单
                    if operate_status == 3:
                        name = recycle_server_obj['name']
                        ip = recycle_server_obj['ip']
                        recycled_at = recycle_server_obj['recycled_at']
                        status = recycle_server_obj['status']
                        node_desc = "通过「{}」回收".format(recycle_code)

                        del_bind_tuple = (name, ip)
                        upd_server_tuple = (status,
                                            recycled_at,
                                            node_desc,
                                            user,
                                            curr_time,
                                            name,
                                            ip)

                        del_bind_list.append(del_bind_tuple)
                        upd_server_list.append(upd_server_tuple)

                else:
                    print(">>>> 「回收状态异常」：CMDB中回收单号「{}」已完成，但回收操作状态「{}」异常。"
                          .format(recycle_code, operate_status))

    # 2、处理数据
    # 2-1、先解除绑定关系（暂不删除）
    if len(del_bind_list) > 0:
        print(">>>> 3-1「解除绑定列表」：", del_bind_list)

    # 2-2、批量更新节点状态为「已回收」
    if len(upd_server_list) > 0:
        upd_server_sql = """
            update 
                env_mgt_node 
            set 
                node_status = %s, 
                node_recyled = %s, 
                node_desc = %s, 
                update_user = %s, 
                update_time = %s 
            where 
                node_name = %s 
                and node_ip = %s;"""

        with conn.cursor(cursor=DictCursor) as cursor:
            upd_count = cursor.executemany(upd_server_sql, upd_server_list)
            print(">>>> 3-2「回收节点」：", upd_count)

    # 2-3、批量完成「回收单」
    if len(upd_recycle_list) > 0:

        upd_recycle_sql = """
            UPDATE
                env_mgt_recycle_order 
            SET 
                recycle_time = %s, 
                recycle_order_status = %s, 
                recycle_order_user = %s, 
                recycle_order_reason = %s, 
                recycle_order_desc = %s, 
                recycle_reject_reason = %s, 
                update_user = %s, 
                update_time = %s
            WHERE 
                recycle_order_code = %s;"""

        with conn.cursor(cursor=DictCursor) as cursor:
            upd_count = cursor.executemany(upd_recycle_sql, upd_recycle_list)
            print(">>>> 3-3「完成回收单」：", upd_count)

    # 3、处理完成
    conn.commit()


def main():
    print("========== 开始同步「回收单」 time(start) {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    # 1、先从scm中获取未完成的「回收单」列表
    scm_conn = connect_scm_mysql()
    try:
        scm_recycle_list = get_scm_recycle_data(scm_conn)
    finally:
        conn_close(scm_conn)

    print("========== 1、获取需要同步的「回收单」{}笔 {} ==========".format(len(scm_recycle_list),
                                                              datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

    # 2、获取cmdb中已完成的「回收单」
    if scm_recycle_list:
        cmdb_conn = connect_cmdb_postgre()
        try:
            recycle_dict, recycle_server_dict = get_cmdb_recycle_data(cmdb_conn, scm_recycle_list)
        finally:
            conn_close(cmdb_conn)
        print("========== 2、获取cmdb中的「回收单」数据 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))

        # 3、更新scm中的「回收单」数据
        if recycle_dict:
            scm_conn = connect_scm_mysql()
            try:
                save_scm_recycle_date(scm_conn, scm_recycle_list, recycle_dict, recycle_server_dict)
            finally:
                conn_close(scm_conn)
        print("========== 3、同步「回收单」结束 {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    else:
        print(">>>> 非常好，所有「回收单」已同步")

    print("========== 完成「回收单」同步 time(end) {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))


if __name__ == '__main__':
    main()
