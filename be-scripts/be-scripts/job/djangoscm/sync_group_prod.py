# 分组信息同步
import os
import sys

import pymysql
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
from pymysql.cursors import DictCursor
from psycopg2.extras import RealDictCursor
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES, CMDBDATABASES


def connect_scm_mysql():
    """
    获取scm的mysql数据库连接
    :return: mysql数据库连接
    """
    conn = pymysql.connect(
        #host='**************',port=3306,database='spider',charset='utf8',user='scm',password='howbuyscm'
        host=DATABASES['IP'], port=DATABASES['PORT'], database=DATABASES['DB'], charset=DATABASES['CHARSET'],user=DATABASES['USER'], password=DATABASES['PASSWORD'])

    return conn


def connect_cmdb_postgre():
    """
    获取cmdb的postgre数据库连接
    :return: postgre数据库连接
    """
    conn = psycopg2.connect(
        #host="**************",port=5432,database="witcher_cmdb",user="witcher_cmdb",password="LziV5E01t6E3dhWBzgQwfQ=="
        host=CMDBDATABASES['IP'], port=CMDBDATABASES['PORT'], database=CMDBDATABASES['DB'], user=CMDBDATABASES['USER'],password=CMDBDATABASES['PASSWORD']
    )
    return conn


def conn_close(conn):
    """
    mysql和postgre的数据连接关闭
    :param conn: 数据库连接
    :return:
    """
    conn.close()


def get_cmdb_group_info(conn):
    """获取分组信息带与应用关系"""
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''select 
                        gp."id" as "id",
                        rs."name" as "module_name",
                        rs.code as "module_code",
                        gp."name" as "deploy_group_name",
                        gp.code as "deploy_group_code",
                        gp."desc" as "deploy_group_desc"
                    from 
                        resources_resourcegroup gp 
                    left join resources_resource rs on gp.resource_code = rs.code''')
        result = cursor.fetchall()

    node_group_info = {}
    for item in result:
        if item['id'] not in node_group_info:
            node_group_info[item['id']] = {'module_name': item['module_name'],
                                           'module_code': item['module_code'],
                                           'deploy_group_name': item['deploy_group_name'],
                                           'deploy_group_code': item['deploy_group_code'],
                                           'deploy_group_desc': item['deploy_group_desc']}
        else:
            print('xxxxxxxxxxxxxx')

    return node_group_info


def get_cmdb_group_server_info(conn):
    """获取分组与节点绑定关系"""
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT
                            rgs."id",
                            rr."name" AS module_name,
                            rg.resource_code AS module_code,
                            rg."name" AS deploy_group_name,
                            rg.CODE AS deploy_group_code,
                            rs.ip,
                            rs."name" as node_name,
                            rs.minion_id,
                            rr."attributes" 
                        FROM
                            "resources_re_resourcegroup_server" rgs
                            LEFT JOIN resources_resourcegroup rg ON rgs.resource_group_id = rg."id"
                            LEFT JOIN resources_server rs ON rgs.server_id = rs."id"
                            LEFT JOIN resources_resource rr ON rg.resource_code = rr.CODE 
                        ORDER BY
                            rgs."id" ASC''')
        result = cursor.fetchall()

    node_group_server_info = {}
    for item in result:
        if item['id'] not in node_group_server_info:
            node_group_server_info[item['id']] = {'module_name': item['module_name'],
                                                  'module_code': item['module_code'],
                                                  'deploy_group_name': item['deploy_group_name'],
                                                  'deploy_group_code': item['deploy_group_code'],
                                                  'ip': item['ip'],
                                                  'node_name': item['node_name'],
                                                  'minion_id': item['minion_id'],
                                                  'attributes': item['attributes'],
                                                  }
        else:
            print('xxxxxxxxxxxxxx')

    return node_group_server_info


def sync_group_to_spider(conn, group_info):
    """ 同步分组信息 """
    for item in group_info:
        print("   "+str(item))
        module_name = group_info[item]["module_name"]
        module_code = group_info[item]["module_code"]
        deploy_group_name = group_info[item]["deploy_group_name"]
        deploy_group_code = group_info[item]["deploy_group_code"]
        deploy_group_desc = group_info[item]["deploy_group_desc"]
        print("        " + str(module_name)+"/"+str(module_code))
        print("        " + str(deploy_group_name)+"/"+str(deploy_group_code)+"/"+str(deploy_group_desc))

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''
                DELETE from env_mgt_deploy_group where module_code='{}' and deploy_group_code = '{}'
            '''.format(module_code,deploy_group_code))
            conn.commit()

            cur_time = datetime.now()
            create_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                            INSERT INTO `spider`.`env_mgt_deploy_group` (
                                `id`,
                                `module_name`,
                                `module_code`,
                                `deploy_group_name`,
                                `deploy_group_code`,
                                `deploy_group_desc`,
                                `create_user`,
                                `create_time`,
                                `update_user`,
                                `update_time`,
                                `stamp`
                            )VALUES(
                                    {},
                                    '{}',
                                    '{}',
                                    '{}',
                                    '{}',
                                    '{}',
                                    'be-script',
                                    '{}',
                                    'be-script',
                                    '{}',
                                    '0'
                            )
                            """.format(item, module_name, module_code, deploy_group_name, deploy_group_code,
                                       deploy_group_desc,
                                       create_time, create_time)
                           )
            conn.commit()


def sync_group_server_to_spider(conn, group_server_info):
    """ 同步分组与节点绑定关系信息 """
    for item in group_server_info:
        print("   "+str(item))
        module_name = group_server_info[item]["module_name"]
        module_code = group_server_info[item]["module_code"]
        deploy_group_name = group_server_info[item]["deploy_group_name"]
        deploy_group_code = group_server_info[item]["deploy_group_code"]
        ip = group_server_info[item]["ip"]
        minion_id = group_server_info[item]["minion_id"]
        node_name = group_server_info[item]["node_name"]
        attributes = group_server_info[item]["attributes"]
        try:
            node_port = attributes["dubbo_port"]
            health_check_url = attributes["health_check_url"]
        except:
            node_port = None
            health_check_url = None
        print("        " + str(module_name)+"/"+str(module_code))
        print("        " + str(deploy_group_name)+"/"+str(deploy_group_code)+"/"+str(ip))
        print("        " + str(minion_id)+"/"+str(node_port)+"/"+str(health_check_url))

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''
                                SELECT
                                    emnb.id,
                                    amam.module_name,
                                    amam.module_code,
                                    emdg.deploy_group_name,
                                    emn.node_ip,
                                    emnb.deploy_group,
                                    emdg.deploy_group_code, 
                                    emnb.node_port,
                                    emnb.health_check_url
                                FROM
                                    `env_mgt_node_bind` emnb
                                    LEFT JOIN app_mgt_app_module amam ON emnb.module_name = amam.module_name
                                    LEFT JOIN env_mgt_node emn ON emnb.node_id = emn.id
                                    LEFT JOIN env_mgt_deploy_group emdg ON emnb.deploy_group = emdg.id 
                                WHERE
                                    amam.module_code = '{}' 
                                    AND emn.node_ip = '{}'
            '''.format(module_code, ip))
            result = cursor.fetchone()
            if result:
                bind_id = result['id']
                # 同步deploy_group
                if not str(result['deploy_group_code']) == str(deploy_group_code):
                    cursor.execute("""
                        SELECT id FROM `env_mgt_deploy_group` WHERE module_code='{}' AND deploy_group_code='{}'
                    """.format(module_code, deploy_group_code))
                    result2 = cursor.fetchone()
                    group_id = result2['id']
                    cur_time = datetime.now()
                    update_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
                    cursor.execute("""
                    UPDATE env_mgt_node_bind SET deploy_group={},update_user='{}',update_time='{}' WHERE id={}
                                    """.format(group_id, 'be-script', update_time, bind_id)
                                   )
                    cursor.fetchall()
                    conn.commit()
                # 同步node_port,health_check_url
                if node_port is None or not node_port:
                    node_port = 0
                if not str(result['node_port']) == str(node_port) or not str(result['health_check_url']) == str(health_check_url):
                    cur_time = datetime.now()
                    update_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
                    cursor.execute("""
                    UPDATE env_mgt_node_bind SET 
                        node_port={},
                        health_check_url='{}',
                        update_user='{}',
                        update_time='{}' 
                    WHERE id={}
                                    """.format(node_port, health_check_url, 'be-script', update_time, bind_id)
                                   )
                    cursor.fetchall()
                    conn.commit()
            else:
                pass
            # 同步minion_id
            cursor.execute('''SELECT * FROM `env_mgt_node` where node_ip='{}' and node_name='{}'
                        '''.format(ip, node_name))
            result = cursor.fetchone()
            if result:
                node_id = result['id']
                if not str(result['minion_id']) == str(minion_id):
                    cur_time = datetime.now()
                    update_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
                    cursor.execute("""
                    UPDATE `env_mgt_node` SET minion_id='{}',update_user='{}',update_time='{}' WHERE id={}
                                    """.format(minion_id, 'be-script', update_time, node_id)
                                   )
                    cursor.fetchall()
                    conn.commit()


def main():
    print("========== 开始同步分组与节点信息（env_mgt_node_bind） time(start) {} ==========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    # 1、获取数据库连接
    my_conn = connect_scm_mysql()
    pg_conn = connect_cmdb_postgre()

    # 2、获取分组信息并同步到spider
    group_info = get_cmdb_group_info(pg_conn)
    sync_group_to_spider(my_conn, group_info)

    # 3、获取节点与分组关系数据并同步到spider
    group_server_info = get_cmdb_group_server_info(pg_conn)
    sync_group_server_to_spider(my_conn, group_server_info)
    conn_close(pg_conn)
    conn_close(my_conn)


if __name__ == '__main__':
    main()
