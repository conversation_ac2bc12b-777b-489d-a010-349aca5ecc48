# 节点同步脚本 zt@2021-11-29
# 第1版 zt@2021-11-29
import json
import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
from enum import unique, Enum

print(">>>> PATH: {}".format(sys.path))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))

import pymysql
import psycopg2
from datetime import datetime, timedelta
from pymysql.cursors import DictCursor
from psycopg2.extras import RealDictCursor
from settings import DATABASES, CMDBDATABASES

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "sync_node_from_cmdb"
LOG_PATH = "/data/logs/" + LOG_TGT
LOG_NAME = "sync_node_from_cmdb.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========
CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
ZONE_CACHE_REGION_DICT = {}
ZONE_CACHE_SUITE_DICT = {}


@unique
class SyncTypeEnum(Enum):
    SYNC_TYPE_ALL = ('sync_all', "同步所有信息")
    SYNC_TYPE_NODE = ('sync_node', "只同步结节")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_DATA = ('err_data', "数据不完整")
    ERR_TYPE_REGION = ('err_region', "缺少对应的可用域")
    ERR_TYPE_SUITE = ('err_suite', "缺少对应的环境套")
    ERR_TYPE_MODULE = ('err_module', "缺少对应的应用信息")
    ERR_TYPE_BIND = ('err_bind', "绑定时缺少数据")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def connect_cmdb_postgre():
    """获取cmdb的postgre数据库连接"""
    conn = psycopg2.connect(host=CMDBDATABASES['IP'],
                            port=CMDBDATABASES['PORT'],
                            database=CMDBDATABASES['DB'],
                            user=CMDBDATABASES['USER'],
                            password=CMDBDATABASES['PASSWORD'])
    return conn


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgre的数据连接关闭"""
    conn.close()


def get_cmdb_node_data(conn):
    """获取cmdb中「近3天变化」的节点信息"""
    cmdb_server_sql = '''
    select 
        s.updated_at,
        s.ip, 
        s.zone_code, 
        s.minion_id, 
        s.status,
        o.resource_code
    from resources_server s
    left join order_order_servers os on os.server_id = s.id
    left join order_order o on o.id = os.order_id
    where s.updated_at > now() - interval '2 D'
    order by s.ip asc, s.status asc, s.updated_at desc;'''

    with conn.cursor(cursor_factory=RealDictCursor) as cursor:
        cursor.execute(cmdb_server_sql)
        server_list = cursor.fetchall()

    return server_list


def get_scm_bind_info(conn, module_name, suite_id, node_id):
    """获取spider中的绑定信息"""
    scm_bind_sql = '''
    select m.id
    from env_mgt_node_bind m
    where m.module_name = '{}'
    and suite_id = {}
    and node_id = {}
    order by m.id desc;
    '''.format(module_name, suite_id, node_id)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(scm_bind_sql)
        result = cursor.fetchone()

    return result


def get_region_by_zone(conn, zone_code):
    """"通过zone获取region映射，并缓存"""
    global ZONE_CACHE_REGION_DICT
    if ZONE_CACHE_REGION_DICT is None or len(ZONE_CACHE_REGION_DICT) == 0:
        scm_zone_sql = '''
            select 
                z.zone_code, 
                r.id as region_id
            from env_mgt_zone z
            left join env_mgt_region r on r.region_name = z.relative_region_code
            order by z.zone_code;'''

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(scm_zone_sql)
            result = cursor.fetchall()
            ZONE_CACHE_REGION_DICT = {
                item['zone_code']: item['region_id'] for item in result if item['zone_code'] is not None
            }
    # 优化cmdb新增可用域「KeyError」问题 zt@2022-03-04
    # region_id = ZONE_CACHE_REGION_DICT[zone_code]
    region_id = ZONE_CACHE_REGION_DICT.get(zone_code)

    return region_id


def get_suite_by_zone(conn, zone_code):
    """通过zone获取最高绑定优化的环境套id，并缓存"""
    global ZONE_CACHE_SUITE_DICT
    if ZONE_CACHE_SUITE_DICT is None or len(ZONE_CACHE_SUITE_DICT) == 0:
        scm_zone_sql = '''
            select
                z.zone_code,
                s.id as suite_id
            from env_mgt_zone z
            left join env_mgt_region r on r.region_name = z.relative_region_code
            left join (
                select region_id,
                       IFNULL(max(node_bind_level), 0) as max_level
                from env_mgt_suite
                where suite_is_active = true
                group by region_id
                )v on v.region_id = r.id
            left join env_mgt_suite s on s.region_id = r.id and s.suite_is_active = true 
                and IFNULL(s.node_bind_level, 0) = v.max_level
            order by z.zone_code;'''

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(scm_zone_sql)
            result = cursor.fetchall()
            ZONE_CACHE_SUITE_DICT = {
                item['zone_code']: item['suite_id'] for item in result if item['zone_code'] is not None
            }
    # 优化cmdb新增可用域「KeyError」问题 zt@2022-03-04
    # suite_id = ZONE_CACHE_SUITE_DICT[zone_code]
    suite_id = ZONE_CACHE_SUITE_DICT.get(zone_code)

    return suite_id


def get_name_by_code(conn, res_code):
    """根据「res_code」获取模块信息，动态查询不缓存"""
    module_name = None

    if res_code:
        scm_module_sql = '''
            select m.module_name
            from app_mgt_app_module m
            where m.module_code = '{}'
            order by id desc;'''.format(res_code)

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute(scm_module_sql)
            result = cursor.fetchone()

        if result:
            module_name = result['module_name']

    return module_name


def get_node_data(conn, server_list):
    """把有变化的节点信息分类：新增、修改、检查（无变化）、异常数据"""
    ins_node_list = []
    upd_node_list = []
    chk_node_list = []
    err_node_dict = {}
    for server_item in server_list:
        # 节点信息
        ip = server_item['ip']
        zone_code = server_item['zone_code']
        minion_id = server_item['minion_id']
        status = server_item['status']
        resource_code = server_item['resource_code']
        # 节点信息
        node_info_dict = {
            'ip': ip,
            'zone_code': zone_code,
            'minion_id': minion_id,
            'status': status,
            'resource_code': resource_code,
        }

        # 检查数据
        if ip is None or zone_code is None or minion_id is None or status is None or resource_code is None:
            node_data_list = err_node_dict.get(ErrTypeEnum.ERR_TYPE_DATA)
            if not node_data_list or len(node_data_list) == 0:
                node_data_list = [node_info_dict]
                err_node_dict[ErrTypeEnum.ERR_TYPE_DATA] = node_data_list
            else:
                node_data_list.append(node_info_dict)
            continue

        # 关联信息
        is_relative = get_relative_data(conn, node_info_dict, err_node_dict)

        # 关联信息完整，才会同步 zt@2022-03-04
        if is_relative:
            # scm中对应节点信息
            ins_node, upd_node, chk_node = get_opt_node_list(conn, ip, minion_id, node_info_dict)

            if ins_node:
                ins_node_list.append(ins_node)
            elif upd_node:
                upd_node_list.append(upd_node)
            else:
                chk_node_list.append(chk_node)
            # ins upd chk end!
        # if end!
    # for end!
    return ins_node_list, upd_node_list, chk_node_list, err_node_dict


def get_relative_data(conn, node_info_dict, err_node_dict):
    """获取节点已存在的关联信息"""
    zone_code = node_info_dict['zone_code']
    # 根据可用区查找可用域信息
    region_id = get_region_by_zone(conn, zone_code)

    is_relative = True
    if not region_id:
        log.info(">>>> 节点缺少关联的可用域信息（跳过同步） ： {}".format(json.dumps(node_info_dict, sort_keys=True, indent=4)))
        is_relative = False

        node_region_list = err_node_dict.get(ErrTypeEnum.ERR_TYPE_REGION)
        if not node_region_list or len(node_region_list) == 0:
            node_region_list = [node_info_dict]
            err_node_dict[ErrTypeEnum.ERR_TYPE_REGION] = node_region_list
        else:
            node_region_list.append(node_info_dict)
    else:
        node_info_dict['region_id'] = region_id

    # 根据可用区查找环境套信息
    suite_id = get_suite_by_zone(conn, zone_code)
    if not suite_id:
        log.info(">>>> 节点缺少关联的环境套信息（跳过同步） ： {}".format(json.dumps(node_info_dict, sort_keys=True, indent=4)))
        is_relative = False

        node_suite_list = err_node_dict.get(ErrTypeEnum.ERR_TYPE_SUITE)
        if not node_suite_list or len(node_suite_list) == 0:
            node_suite_list = [node_info_dict]
            err_node_dict[ErrTypeEnum.ERR_TYPE_SUITE] = node_suite_list
        else:
            node_suite_list.append(node_info_dict)
    else:
        node_info_dict['suite_id'] = suite_id

    # 根据「resource_code」查找模块信息
    resource_code = node_info_dict['resource_code']
    module_name = get_name_by_code(conn, resource_code)
    if not module_name:
        log.info(">>>> 节点缺少关联的模块信息（跳过同步） ： {}".format(json.dumps(node_info_dict, sort_keys=True, indent=4)))
        is_relative = False

        node_module_list = err_node_dict.get(ErrTypeEnum.ERR_TYPE_MODULE)
        if not node_module_list or len(node_module_list) == 0:
            node_module_list = [node_info_dict]
            err_node_dict[ErrTypeEnum.ERR_TYPE_MODULE] = node_module_list
        else:
            node_module_list.append(node_info_dict)
    else:
        node_info_dict['module_name'] = module_name

    return is_relative


def get_opt_node_list(conn, ip, minion_id, node_info_dict):
    """对需要操作的节点信息进行分类"""
    ins_node = None
    upd_node = None
    chk_node = None

    scm_node_sql = '''
        select m.id,
            m.node_ip,
            m.minion_id,
            m.region_id,
            m.node_status
        from env_mgt_node m
        where m.node_ip = '{}'
        and m.minion_id = '{}'
        order by m.id desc'''.format(ip, minion_id)

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(scm_node_sql)
        result = cursor.fetchone()

    if result:
        scm_node_id = result['id']
        node_info_dict['scm_node_id'] = scm_node_id

        # 属性对比
        scm_region_id = result['region_id']
        scm_node_status = result['node_status']

        is_chk = node_info_dict is not None
        if is_chk:
            cmdb_region_id = node_info_dict['region_id']
            cmdb_node_status = node_info_dict['status']

            is_chk = scm_region_id == cmdb_region_id and scm_node_status == cmdb_node_status

        if is_chk:
            chk_node = node_info_dict
        else:
            upd_node = node_info_dict

    else:
        ins_node = node_info_dict

    return ins_node, upd_node, chk_node


def get_bind_data(conn, ins_node_list, err_node_dict):
    """对新增的节点，获取绑定信息"""
    ins_bind_list = []
    for node_info_dict in ins_node_list:
        module_name = node_info_dict.get('module_name')
        suite_id = node_info_dict.get('suite_id')
        node_id = node_info_dict.get('scm_node_id')

        if not module_name or not suite_id or not node_id:
            node_bind_list = err_node_dict.get(ErrTypeEnum.ERR_TYPE_BIND)
            if not node_bind_list or len(node_bind_list) == 0:
                node_bind_list = [node_info_dict]
                err_node_dict[ErrTypeEnum.ERR_TYPE_BIND] = node_bind_list
            else:
                node_bind_list.append(node_info_dict)
            continue

        bind_info = get_scm_bind_info(conn, module_name, suite_id, node_id)
        if not bind_info:
            ins_bind_list.append(node_info_dict)
        else:
            bind_id = bind_info['id']
            node_info_dict['scm_bind_id'] = bind_id

    return ins_bind_list


def get_ins_node_tuple_list(ins_node_list, curr_time):
    """节点批量插入"""
    ins_node_tuple_list = []
    if ins_node_list:
        for ins_node in ins_node_list:
            ip = ins_node['ip']
            minion_id = ins_node['minion_id']
            region_id = ins_node['region_id']
            status = ins_node['status']

            ins_node_tuple = (
                ip,
                minion_id,
                region_id,
                status,
                CREATE_USER,
                curr_time,
                UPDATE_USER,
                curr_time,
                CREATE_STAMP,
            )
            ins_node_tuple_list.append(ins_node_tuple)

    return ins_node_tuple_list


def get_upd_node_tuple_list(upd_node_list, curr_time):
    """节点批量更新"""
    upd_node_tuple_list = []
    if upd_node_list:
        for upd_node in upd_node_list:
            ip = upd_node['ip']
            minion_id = upd_node['minion_id']
            region_id = upd_node['region_id']
            status = upd_node['status']
            scm_node_id = upd_node['scm_node_id']

            upd_node_tuple = (
                ip,
                minion_id,
                region_id,
                status,
                UPDATE_USER,
                curr_time,
                scm_node_id,
            )
            upd_node_tuple_list.append(upd_node_tuple)

    return upd_node_tuple_list


def get_ins_bind_tuple_list(ins_node_list, curr_time):
    """"绑定批量插入"""
    ins_bind_tuple_list = []
    node_bind_desc = "cmdb节点同步「自动绑定」。zt@{}".format(curr_time.strftime(FMT_TIME_STR))
    if ins_node_list:
        for ins_node in ins_node_list:
            module_name = ins_node['module_name']
            suite_id = ins_node['suite_id']
            node_id = ins_node['scm_node_id']

            ins_node_tuple = (
                module_name,
                suite_id,
                node_id,
                node_bind_desc,
                CREATE_USER,
                curr_time,
                UPDATE_USER,
                curr_time,
                CREATE_STAMP,
            )
            ins_bind_tuple_list.append(ins_node_tuple)

    return ins_bind_tuple_list


def ins_node_to_db(conn, ins_node_list, curr_time):
    """节点批量插入DB"""
    if ins_node_list:
        ins_node_tuple_list = get_ins_node_tuple_list(ins_node_list, curr_time)

        ins_node_sql = """
            INSERT INTO env_mgt_node(
                node_ip, 
                minion_id, 
                region_id, 
                node_status,
                create_user, 
                create_time, 
                update_user, 
                update_time, 
                stamp
                )VALUES(
                    %s, %s, %s, %s, 
                    %s, %s, %s, %s, %s
                );"""
        with conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_node_sql, ins_node_tuple_list)
            server_id = cursor.lastrowid
            for idx in range(0, len(ins_node_tuple_list)):
                node_id = server_id + idx
                ins_node = ins_node_list[idx]
                ins_node['scm_node_id'] = node_id

        log.info(">>>> ins_node_count: {}".format(ins_count))


def ins_bind_to_db(conn, ins_node_list, err_node_dict, curr_time):
    """绑定批量插入DB"""
    if ins_node_list:
        ins_bind_list = get_bind_data(conn, ins_node_list, err_node_dict)
        if ins_bind_list and len(ins_bind_list) > 0:
            ins_bind_tuple_list = get_ins_bind_tuple_list(ins_bind_list, curr_time)

            ins_bind_sql = """
                        INSERT INTO env_mgt_node_bind(
                            module_name, 
                            suite_id, 
                            node_id, 
                            node_bind_desc,
                            create_user, 
                            create_time, 
                            update_user, 
                            update_time, 
                            stamp
                            )VALUES(
                                %s, %s, %s, %s, 
                                %s, %s, %s, %s, %s
                            );"""
            with conn.cursor(cursor=DictCursor) as cursor:
                ins_count = cursor.executemany(ins_bind_sql, ins_bind_tuple_list)
                bind_id = cursor.lastrowid
                for idx in range(0, len(ins_bind_tuple_list)):
                    bind_id = bind_id + idx
                    ins_bind = ins_bind_list[idx]
                    ins_bind['scm_bind_id'] = bind_id

            log.info(">>>> ins_bind_count: {}".format(ins_count))


def upd_node_to_db(conn, upd_node_list, curr_time):
    """节点批量更新DB"""
    if upd_node_list:
        upd_node_tuple_list = get_upd_node_tuple_list(upd_node_list, curr_time)

        upd_node_sql = """
                UPDATE
                    env_mgt_node 
                SET 
                    node_ip = %s, 
                    minion_id = %s, 
                    region_id = %s, 
                    node_status = %s, 
                    update_user = %s, 
                    update_time = %s
                WHERE 
                    id = %s"""
        with conn.cursor(cursor=DictCursor) as cursor:
            upd_count = cursor.executemany(upd_node_sql, upd_node_tuple_list)
        log.info(">>>> upd_node_count: {}".format(upd_count))


def save_node_to_db(conn, ins_node_list, upd_node_list, err_node_dict):
    """scm中数据进DB方法"""
    curr_time = datetime.now()
    # 绑定主机和应用的关系
    if ins_node_list:
        ins_node_to_db(conn, ins_node_list, curr_time)
        ins_bind_to_db(conn, ins_node_list, err_node_dict, curr_time)
    # 节点更新
    if upd_node_list:
        upd_node_to_db(conn, upd_node_list, curr_time)

    # 处理完成
    conn.commit()


def sync_cmdb_node_data(conn, server_list):
    """scm中数据处理主方法"""
    if not server_list:
        raise ValueError("节点列表为空！")
    # 节点处理
    ins_node_list, upd_node_list, chk_node_list, err_node_dict = get_node_data(conn, server_list)

    # DB处理
    save_node_to_db(conn, ins_node_list, upd_node_list, err_node_dict)

    # 结果打印
    log.info(">>>> ins_node_list = {}".format(json.dumps(ins_node_list, sort_keys=True, indent=4)))
    log.info(">>>> upd_node_list = {}".format(json.dumps(upd_node_list, sort_keys=True, indent=4)))
    log.info(">>>> chk_node_list = {}".format(json.dumps(chk_node_list, sort_keys=True, indent=4)))
    for err_enum in err_node_dict:
        err_desc = err_enum.err_desc
        err_list = err_node_dict[err_enum]
        log.info(">>>> err_desc = {}：".format(err_desc))
        log.info(">>>> err_list = {}".format(json.dumps(err_list, sort_keys=True, indent=4)))


def main(param_sync_type_enum):
    """「节点同步」主方法 zt@2021-11-29"""
    log.info("======== 开始同步 time(start) {} ========".format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    log.info("==== 『入参』：{}".format(param_sync_type_enum.name))
    # 1、从cmdb获取节点信息
    cmdb_conn = connect_cmdb_postgre()
    try:
        server_list = get_cmdb_node_data(cmdb_conn)
    finally:
        conn_close(cmdb_conn)

    if server_list:
        scm_conn = connect_scm_mysql()

        try:
            sync_cmdb_node_data(scm_conn, server_list)
        finally:
            conn_close(scm_conn)
    else:
        log.warning(">>>> 近3天内节点数据无更新，同步跳过。")


if __name__ == '__main__':
    """主入口，先判断参数"""
    s_time = datetime.now()
    # 请求参数处理
    req_sync_type_enum = SyncTypeEnum.SYNC_TYPE_ALL
    if len(sys.argv) > 1:
        req_sync_type_str = sys.argv[1:]
        # 确定步骤
        try:
            req_sync_type_enum = SyncTypeEnum[req_sync_type_str]
        except KeyError:
            log.error("==== ！！！！参数异常！！！！ ====")
            err_msg = ">>>> 参数错误，只支持：空、sync_all、sync_node。"
            log.error(err_msg)
            exit(1)
    try:
        main(req_sync_type_enum)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        exit(1)
    finally:
        e_time = datetime.now()
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        log.info("== 执行最耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
