import json
import re
import subprocess

import paramiko
import pymysql
import datetime
from pymysql.cursors import DictCursor
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import PIPELINE_TEST_PUBLISH as PTP


def connect_mysql(database):
    conn = pymysql.connect(
        host='***************', port=3306,
        database=database, charset='utf8',
        user='root', password='123456')
    return conn


def conn_close(conn):
    conn.close()


def get_info_for_dict(text, env):
    dict_zk = {
        'zk-fin': {
            'tms19': '{"zkUrl":"**************:2182"}',
            'tms18': '{"zkUrl":"**************:2182"}',
            'tms17': '{"zkUrl":"**************:2182"}',
            'tms16': '{"zkUrl":"***************:2182"}',
            'tms15': '{"zkUrl":"***************:2182"}',
            'it01': '{"zkUrl":"zookeeper:2181"}',
            'it02': '{"zkUrl":"zookeeper:2181"}',
            'it03': '{"zkUrl":"zookeeper:2181"}',
            'it04': '{"zkUrl":"zookeeper:2181"}',
            'it05': '{"zkUrl":"zookeeper:2181"}',
            'it06': '{"zkUrl":"zookeeper:2181"}',
            'it07': '{"zkUrl":"zookeeper:2181"}',
            'it08': '{"zkUrl":"zookeeper:2181"}',
            'it09': '{"zkUrl":"zookeeper:2181"}',
            'it10': '{"zkUrl":"zookeeper:2181"}',
            'it11': '{"zkUrl":"zookeeper:2181"}',
            'it12': '{"zkUrl":"zookeeper:2181"}',
            'it13': '{"zkUrl":"zookeeper:2181"}',
            'it14': '{"zkUrl":"zookeeper:2181"}',
            'it15': '{"zkUrl":"zookeeper:2181"}',
            'it16': '{"zkUrl":"zookeeper:2181"}',
            'it17': '{"zkUrl":"zookeeper:2181"}',
            'it18': '{"zkUrl":"zookeeper:2181"}',
            'it19': '{"zkUrl":"zookeeper:2181"}',
            'it20': '{"zkUrl":"zookeeper:2181"}',
            'it21': '{"zkUrl":"zookeeper:2181"}',
            'it22': '{"zkUrl":"zookeeper:2181"}',
            'it23': '{"zkUrl":"zookeeper:2181"}',
            'it24': '{"zkUrl":"zookeeper:2181"}',
            'it25': '{"zkUrl":"zookeeper:2181"}',
            'it26': '{"zkUrl":"zookeeper:2181"}',
            'it27': '{"zkUrl":"zookeeper:2181"}',
            'it28': '{"zkUrl":"zookeeper:2181"}',
            'it29': '{"zkUrl":"zookeeper:2181"}',
            'it30': '{"zkUrl":"zookeeper:2181"}'
        },
        'zk-tp-dubbo': {
            'tms19': '{"zkUrl":"**************:2182"}',
            'tms18': '{"zkUrl":"**************:2182"}',
            'tms17': '{"zkUrl":"**************:2182"}',
            'tms16': '{"zkUrl":"***************:2182"}',
            'tms15': '{"zkUrl":"***************:2182"}',
            'it01': '{"zkUrl":"zookeeper:2181"}',
            'it02': '{"zkUrl":"zookeeper:2181"}',
            'it03': '{"zkUrl":"zookeeper:2181"}',
            'it04': '{"zkUrl":"zookeeper:2181"}',
            'it05': '{"zkUrl":"zookeeper:2181"}',
            'it06': '{"zkUrl":"zookeeper:2181"}',
            'it07': '{"zkUrl":"zookeeper:2181"}',
            'it08': '{"zkUrl":"zookeeper:2181"}',
            'it09': '{"zkUrl":"zookeeper:2181"}',
            'it10': '{"zkUrl":"zookeeper:2181"}',
            'it11': '{"zkUrl":"zookeeper:2181"}',
            'it12': '{"zkUrl":"zookeeper:2181"}',
            'it13': '{"zkUrl":"zookeeper:2181"}',
            'it14': '{"zkUrl":"zookeeper:2181"}',
            'it15': '{"zkUrl":"zookeeper:2181"}',
            'it16': '{"zkUrl":"zookeeper:2181"}',
            'it17': '{"zkUrl":"zookeeper:2181"}',
            'it18': '{"zkUrl":"zookeeper:2181"}',
            'it19': '{"zkUrl":"zookeeper:2181"}',
            'it20': '{"zkUrl":"zookeeper:2181"}',
            'it21': '{"zkUrl":"zookeeper:2181"}',
            'it22': '{"zkUrl":"zookeeper:2181"}',
            'it23': '{"zkUrl":"zookeeper:2181"}',
            'it24': '{"zkUrl":"zookeeper:2181"}',
            'it25': '{"zkUrl":"zookeeper:2181"}',
            'it26': '{"zkUrl":"zookeeper:2181"}',
            'it27': '{"zkUrl":"zookeeper:2181"}',
            'it28': '{"zkUrl":"zookeeper:2181"}',
            'it29': '{"zkUrl":"zookeeper:2181"}',
            'it30': '{"zkUrl":"zookeeper:2181"}'
        },
        'zk-acc-center': {
            'tms19': '{"zkUrl":"**************:2182"}',
            'tms18': '{"zkUrl":"**************:2182"}',
            'tms17': '{"zkUrl":"**************:2182"}',
            'tms16': '{"zkUrl":"***************:2182"}',
            'tms15': '{"zkUrl":"***************:2182"}',
            'it01': '{"zkUrl":"zookeeper:2181"}',
            'it02': '{"zkUrl":"zookeeper:2181"}',
            'it03': '{"zkUrl":"zookeeper:2181"}',
            'it04': '{"zkUrl":"zookeeper:2181"}',
            'it05': '{"zkUrl":"zookeeper:2181"}',
            'it06': '{"zkUrl":"zookeeper:2181"}',
            'it07': '{"zkUrl":"zookeeper:2181"}',
            'it08': '{"zkUrl":"zookeeper:2181"}',
            'it09': '{"zkUrl":"zookeeper:2181"}',
            'it10': '{"zkUrl":"zookeeper:2181"}',
            'it11': '{"zkUrl":"zookeeper:2181"}',
            'it12': '{"zkUrl":"zookeeper:2181"}',
            'it13': '{"zkUrl":"zookeeper:2181"}',
            'it14': '{"zkUrl":"zookeeper:2181"}',
            'it15': '{"zkUrl":"zookeeper:2181"}',
            'it16': '{"zkUrl":"zookeeper:2181"}',
            'it17': '{"zkUrl":"zookeeper:2181"}',
            'it18': '{"zkUrl":"zookeeper:2181"}',
            'it19': '{"zkUrl":"zookeeper:2181"}',
            'it20': '{"zkUrl":"zookeeper:2181"}',
            'it21': '{"zkUrl":"zookeeper:2181"}',
            'it22': '{"zkUrl":"zookeeper:2181"}',
            'it23': '{"zkUrl":"zookeeper:2181"}',
            'it24': '{"zkUrl":"zookeeper:2181"}',
            'it25': '{"zkUrl":"zookeeper:2181"}',
            'it26': '{"zkUrl":"zookeeper:2181"}',
            'it27': '{"zkUrl":"zookeeper:2181"}',
            'it28': '{"zkUrl":"zookeeper:2181"}',
            'it29': '{"zkUrl":"zookeeper:2181"}',
            'it30': '{"zkUrl":"zookeeper:2181"}',
            'it31': '{"zkUrl":"zookeeper:2181"}',
            'it32': '{"zkUrl":"zookeeper:2181"}',
            'it33': '{"zkUrl":"zookeeper:2181"}',
            'it34': '{"zkUrl":"zookeeper:2181"}',
            'it35': '{"zkUrl":"zookeeper:2181"}',
            'it36': '{"zkUrl":"zookeeper:2181"}',
            'it37': '{"zkUrl":"zookeeper:2181"}',
            'it38': '{"zkUrl":"zookeeper:2181"}',
            'it39': '{"zkUrl":"zookeeper:2181"}',
            'it40': '{"zkUrl":"zookeeper:2181"}'
        },
        'zk_asset_dubbo': {
            'tms19': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms18': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms17': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms16': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms15': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'it01': '{"zkUrl":"zookeeper:2181"}',
            'it02': '{"zkUrl":"zookeeper:2181"}',
            'it03': '{"zkUrl":"zookeeper:2181"}',
            'it04': '{"zkUrl":"zookeeper:2181"}',
            'it05': '{"zkUrl":"zookeeper:2181"}',
            'it06': '{"zkUrl":"zookeeper:2181"}',
            'it07': '{"zkUrl":"zookeeper:2181"}',
            'it08': '{"zkUrl":"zookeeper:2181"}',
            'it09': '{"zkUrl":"zookeeper:2181"}',
            'it10': '{"zkUrl":"zookeeper:2181"}',
            'it11': '{"zkUrl":"zookeeper:2181"}',
            'it12': '{"zkUrl":"zookeeper:2181"}',
            'it13': '{"zkUrl":"zookeeper:2181"}',
            'it14': '{"zkUrl":"zookeeper:2181"}',
            'it15': '{"zkUrl":"zookeeper:2181"}',
            'it16': '{"zkUrl":"zookeeper:2181"}',
            'it17': '{"zkUrl":"zookeeper:2181"}',
            'it18': '{"zkUrl":"zookeeper:2181"}',
            'it19': '{"zkUrl":"zookeeper:2181"}',
            'it20': '{"zkUrl":"zookeeper:2181"}',
            'it21': '{"zkUrl":"zookeeper:2181"}',
            'it22': '{"zkUrl":"zookeeper:2181"}',
            'it23': '{"zkUrl":"zookeeper:2181"}',
            'it24': '{"zkUrl":"zookeeper:2181"}',
            'it25': '{"zkUrl":"zookeeper:2181"}',
            'it26': '{"zkUrl":"zookeeper:2181"}',
            'it27': '{"zkUrl":"zookeeper:2181"}',
            'it28': '{"zkUrl":"zookeeper:2181"}',
            'it29': '{"zkUrl":"zookeeper:2181"}',
            'it30': '{"zkUrl":"zookeeper:2181"}',
            'it31': '{"zkUrl":"zookeeper:2181"}',
            'it32': '{"zkUrl":"zookeeper:2181"}',
            'it33': '{"zkUrl":"zookeeper:2181"}',
            'it34': '{"zkUrl":"zookeeper:2181"}',
            'it35': '{"zkUrl":"zookeeper:2181"}',
            'it36': '{"zkUrl":"zookeeper:2181"}',
            'it37': '{"zkUrl":"zookeeper:2181"}',
            'it38': '{"zkUrl":"zookeeper:2181"}',
            'it39': '{"zkUrl":"zookeeper:2181"}',
            'it40': '{"zkUrl":"zookeeper:2181"}'
        },
        'cc_center_zk': {
            'tms19': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms18': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms17': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms16': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms15': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'it01': '{"zkUrl":"zookeeper:2181"}',
            'it02': '{"zkUrl":"zookeeper:2181"}',
            'it03': '{"zkUrl":"zookeeper:2181"}',
            'it04': '{"zkUrl":"zookeeper:2181"}',
            'it05': '{"zkUrl":"zookeeper:2181"}',
            'it06': '{"zkUrl":"zookeeper:2181"}',
            'it07': '{"zkUrl":"zookeeper:2181"}',
            'it08': '{"zkUrl":"zookeeper:2181"}',
            'it09': '{"zkUrl":"zookeeper:2181"}',
            'it10': '{"zkUrl":"zookeeper:2181"}',
            'it11': '{"zkUrl":"zookeeper:2181"}',
            'it12': '{"zkUrl":"zookeeper:2181"}',
            'it13': '{"zkUrl":"zookeeper:2181"}',
            'it14': '{"zkUrl":"zookeeper:2181"}',
            'it15': '{"zkUrl":"zookeeper:2181"}',
            'it16': '{"zkUrl":"zookeeper:2181"}',
            'it17': '{"zkUrl":"zookeeper:2181"}',
            'it18': '{"zkUrl":"zookeeper:2181"}',
            'it19': '{"zkUrl":"zookeeper:2181"}',
            'it20': '{"zkUrl":"zookeeper:2181"}',
            'it21': '{"zkUrl":"zookeeper:2181"}',
            'it22': '{"zkUrl":"zookeeper:2181"}',
            'it23': '{"zkUrl":"zookeeper:2181"}',
            'it24': '{"zkUrl":"zookeeper:2181"}',
            'it25': '{"zkUrl":"zookeeper:2181"}',
            'it26': '{"zkUrl":"zookeeper:2181"}',
            'it27': '{"zkUrl":"zookeeper:2181"}',
            'it28': '{"zkUrl":"zookeeper:2181"}',
            'it29': '{"zkUrl":"zookeeper:2181"}',
            'it30': '{"zkUrl":"zookeeper:2181"}',
            'it31': '{"zkUrl":"zookeeper:2181"}',
            'it32': '{"zkUrl":"zookeeper:2181"}',
            'it33': '{"zkUrl":"zookeeper:2181"}',
            'it34': '{"zkUrl":"zookeeper:2181"}',
            'it35': '{"zkUrl":"zookeeper:2181"}',
            'it36': '{"zkUrl":"zookeeper:2181"}',
            'it37': '{"zkUrl":"zookeeper:2181"}',
            'it38': '{"zkUrl":"zookeeper:2181"}',
            'it39': '{"zkUrl":"zookeeper:2181"}',
            'it40': '{"zkUrl":"zookeeper:2181"}'
        },
        'cc_hbone_zk': {
            'tms19': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms18': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms17': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms16': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'tms15': '{"zkUrl":"192.168.221.136:2181,192.168.221.137:2181,192.168.221.138:2181"}',
            'it01': '{"zkUrl":"zookeeper:2181"}',
            'it02': '{"zkUrl":"zookeeper:2181"}',
            'it03': '{"zkUrl":"zookeeper:2181"}',
            'it04': '{"zkUrl":"zookeeper:2181"}',
            'it05': '{"zkUrl":"zookeeper:2181"}',
            'it06': '{"zkUrl":"zookeeper:2181"}',
            'it07': '{"zkUrl":"zookeeper:2181"}',
            'it08': '{"zkUrl":"zookeeper:2181"}',
            'it09': '{"zkUrl":"zookeeper:2181"}',
            'it10': '{"zkUrl":"zookeeper:2181"}',
            'it11': '{"zkUrl":"zookeeper:2181"}',
            'it12': '{"zkUrl":"zookeeper:2181"}',
            'it13': '{"zkUrl":"zookeeper:2181"}',
            'it14': '{"zkUrl":"zookeeper:2181"}',
            'it15': '{"zkUrl":"zookeeper:2181"}',
            'it16': '{"zkUrl":"zookeeper:2181"}',
            'it17': '{"zkUrl":"zookeeper:2181"}',
            'it18': '{"zkUrl":"zookeeper:2181"}',
            'it19': '{"zkUrl":"zookeeper:2181"}',
            'it20': '{"zkUrl":"zookeeper:2181"}',
            'it21': '{"zkUrl":"zookeeper:2181"}',
            'it22': '{"zkUrl":"zookeeper:2181"}',
            'it23': '{"zkUrl":"zookeeper:2181"}',
            'it24': '{"zkUrl":"zookeeper:2181"}',
            'it25': '{"zkUrl":"zookeeper:2181"}',
            'it26': '{"zkUrl":"zookeeper:2181"}',
            'it27': '{"zkUrl":"zookeeper:2181"}',
            'it28': '{"zkUrl":"zookeeper:2181"}',
            'it29': '{"zkUrl":"zookeeper:2181"}',
            'it30': '{"zkUrl":"zookeeper:2181"}',
            'it31': '{"zkUrl":"zookeeper:2181"}',
            'it32': '{"zkUrl":"zookeeper:2181"}',
            'it33': '{"zkUrl":"zookeeper:2181"}',
            'it34': '{"zkUrl":"zookeeper:2181"}',
            'it35': '{"zkUrl":"zookeeper:2181"}',
            'it36': '{"zkUrl":"zookeeper:2181"}',
            'it37': '{"zkUrl":"zookeeper:2181"}',
            'it38': '{"zkUrl":"zookeeper:2181"}',
            'it39': '{"zkUrl":"zookeeper:2181"}',
            'it40': '{"zkUrl":"zookeeper:2181"}'
        }
    }
    if text in dict_zk:
        if env in dict_zk.get(text):
            return dict_zk.get(text).get(env)
    return None


def update_zk_info(conn,env_code):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT
                *
            FROM t_sys_code
            ''')
        results = cursor.fetchall()
        for row in results:
            text = row.get('TEXT')
            if get_info_for_dict(text, env_code):
                sql = '''update t_sys_code set value = '{}' where text = '{}' '''.format(get_info_for_dict(text,
                                                                                                           env_code
                                                                                                           ),
                                                                                         text)
                cursor.execute(sql)
        conn.commit()


def main():
    logging.info('刷新数据库pabase_1_{}开始'.format(env_codes))
    envCodes = env_codes.split(",")
    for env_code in envCodes:
        conn = connect_mysql('pabase_1_{}'.format(env_code))
        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''update t_sys_code set value = '{"zkUrl":"zookeeper:2181"}' where code='ZK_DS_CODE' ''')
            result = cursor.fetchall()
            logging.info('刷新t_sys_code数据结果：{}'.format(result))

            # cursor.execute('''update t_sys_code set org = 'CC' ''')
            # result = cursor.fetchall()
            # logging.info('刷新t_sys_code数据结果：{}'.format(result))

            cursor.execute('''update t_scheduler_task set status ='02' ''')
            result = cursor.fetchall()
            logging.info('刷新t_scheduler_task数据结果：{}'.format(result))

            cursor.execute('''update t_scheduler_kettle set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute('''update t_scheduler_label set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute('''update t_scheduler_dubbo set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute('''update t_monitor_server set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute('''update t_monitor_agent set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute('''update t_sys_code set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute('''update t_scheduler_task set org='CC' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))

            cursor.execute(''' update t_scheduler_task set warn_obj = null ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))





            cursor.execute('''update qrtz_triggers  set trigger_state  ='PAUSED' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_triggers数据结果：{}'.format(result))
            cursor.close()
        conn.commit()

        conn_close(conn)
        logging.info('刷新数据库pabase_1_{}结束'.format(env_code))


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17'] '''
    params = sys.argv
    logging.info(params)
    env_codes = params[1]
    main()
