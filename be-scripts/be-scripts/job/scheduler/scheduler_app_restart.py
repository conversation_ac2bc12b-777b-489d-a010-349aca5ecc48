import json
import re
import subprocess

import paramiko
import pymysql
import datetime
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)


from pymysql.cursors import DictCursor
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import get_tomcat_password, \
    exec_local_cmd_by_sshpass_tomcat


def connect_mysql():
    conn = pymysql.connect(
        host='**************', port=3306,
        database='spider', charset='utf8',
        user='ops', password='123456'
    )
    return conn


def conn_close(conn):
    conn.close()


def restartApp(node_ip, script_name):
    """获取应用所在机器ip的tomcat密码"""
    password, msg = get_tomcat_password(node_ip, cmd=None, password=None)
    cmd = 'sh ' + script_name
    """远程执行启动命令"""
    exec_local_cmd_by_sshpass_tomcat(node_ip, cmd, password)


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms19'] '''
    params = sys.argv
    conn = connect_mysql()
    with conn.cursor(cursor=DictCursor) as cursor:
        """获取应用所在机器ip、启动脚本"""
        cursor.execute(''' SELECT
                             emnb.deploy_path,
                             emn.node_ip,
                             emnb.script_path,
                             emnb.script_name
                          FROM
                             `env_mgt_node_bind` emnb
                          LEFT JOIN env_mgt_suite ems ON emnb.suite_id = ems.id
                          LEFT JOIN env_mgt_node emn on emnb.node_id = emn.id
                          WHERE
                              emnb.module_name = '{}' 
                          AND ems.suite_code = '{}'  '''.format(params[1], params[3]))
    result = cursor.fetchall()
    node_ip = result[0].get('node_ip')
    script_name = result[0].get('script_path') + '/' + result[0].get('script_name')
    conn_close(conn)

    restartApp(node_ip, script_name)


