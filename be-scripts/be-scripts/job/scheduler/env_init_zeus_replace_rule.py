import json
import re
import subprocess

import paramiko
import pymysql
import datetime
from pymysql.cursors import DictCursor
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import PIPELINE_TEST_PUBLISH as PTP
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd


def main():
    logging.info('初始化**************-zeus_nacos数据库config_replace_toppriority_rule环境数据开始'.format(env_codes))
    envCodes = env_codes.split(",")
    for env_code in envCodes:
        cmd = 'sshpass -p howbuylpt ssh root@************** \'mysql  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 zeus_nacos -e \"drop table IF EXISTS config_replace_toppriority_rule_{}\"\''.format(
            source_env)
        exec_local_cmd(cmd)
        logging.info('删除表：config_replace_toppriority_rule_{}结束:{}'.format(source_env, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \'mysql  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 zeus_nacos -e \"create table config_replace_toppriority_rule_{} like config_replace_toppriority_rule\"\''.format(
            source_env)
        exec_local_cmd(cmd)
        logging.info('创建表：config_replace_toppriority_rule_{}结束:{}'.format(source_env, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \"mysql  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 zeus_nacos -e \'insert into config_replace_toppriority_rule_{} select * from config_replace_toppriority_rule where env=\\"{}\\"\'\"'.format(
            source_env, source_env)
        exec_local_cmd(cmd)
        logging.info('表：config_replace_toppriority_rule_{} 录入数据结束:{}'.format(source_env, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \'mysql  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 zeus_nacos -e "alter table config_replace_toppriority_rule_{} drop column id"\''.format(
            source_env)
        exec_local_cmd(cmd)
        logging.info('表：config_replace_toppriority_rule_{} DROPid字段结束:{}'.format(source_env, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \'mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 --no-create-info --compact --complete-insert --skip-extended-insert ' \
              '--databases zeus_nacos --tables  config_replace_toppriority_rule_{}  > config_replace_toppriority_rule_{}.sql\''.format(
            source_env, source_env)
        exec_local_cmd(cmd)
        logging.info('导出config_replace_toppriority_rule_{}表数据结束:{}'.format(source_env, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \'sed -e \'s/{}/{}/g\'  config_replace_toppriority_rule_{}.sql >config_replace_toppriority_rule_{}.sql\''.format(
            source_env, env_code, source_env, env_code)
        exec_local_cmd(cmd)
        logging.info('替换{}为{}结束:{}'.format(source_env, env_code, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \'sed -i \'s/{}/{}/g\'  config_replace_toppriority_rule_{}.sql\''.format(
            source_env.upper(),
            env_code.upper(), env_code)
        exec_local_cmd(cmd)
        logging.info('替换{}为{}结束:{}'.format(source_env, env_code, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \'sed -i \'s/config_replace_toppriority_rule_{}/config_replace_toppriority_rule/g\'  config_replace_toppriority_rule_{}.sql \''.format(
            env_code, env_code)
        exec_local_cmd(cmd)
        logging.info('替换config_replace_toppriority_rule_{}为config_replace_toppriority_rule结束:{}'.format(env_code, cmd))
        cmd = 'sshpass -p howbuylpt ssh root@************** \"mysql  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 zeus_nacos -e \'delete from config_replace_toppriority_rule where env=\\"{}\\"\'\"'.format(
            env_code)
        exec_local_cmd(cmd)
        logging.info('删除config_replace_toppriority_rule表中env为{}的数据:{}'.format(env_code, cmd))

        cmd = 'sshpass -p howbuylpt ssh root@************** \'mysql  -h ************** -P 3306 -uzeus_nacos -p58ZADE7drFy9 zeus_nacos -e \"source config_replace_toppriority_rule_{}.sql\"\''.format(
            env_code)
        exec_local_cmd(cmd)
        logging.info('导入env为{}的数据到config_replace_toppriority_rule表中:{}'.format(env_code, cmd))
        logging.info("--------************—————————")

    print(envCodes)


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17'] '''
    params = sys.argv
    logging.info(params)
    env_codes = params[1]
    source_env = params[2]
    main()
