import json
import re
import subprocess

import paramiko
import pymysql
import datetime
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from pymysql.cursors import DictCursor
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import detection_service_is_normal


def connect_mysql():
    conn = pymysql.connect(
        host='**************', port=3306,
        database='spider', charset='utf8',
        user='ops', password='123456')
    return conn


def conn_close(conn):
    conn.close()


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17']  '''
    params = sys.argv
    conn = connect_mysql()
    with conn.cursor(cursor=DictCursor) as cursor:
        """获取应用所在机器ip、容器路径"""
        cursor.execute(''' select emnb.tomcat_path, emn.node_ip from env_mgt_node_bind emnb 
                           left join env_mgt_suite ems on emnb.suite_id = ems.id
                           left join env_mgt_node emn on emnb.node_id = emn.id
                           where emnb.module_name ='{}' and ems.suite_code = '{}' '''.format(params[1], params[3]))
        result = cursor.fetchall()
        node_ip = result[0].get('node_ip')
        container_name = result[0].get('tomcat_path')
    conn_close(conn)

    """调用检测服务启动方法，如果启动失败，中断流水线"""
    is_normal = detection_service_is_normal(node_ip, container_name)
    if not is_normal:
        sys.exit(-1)
