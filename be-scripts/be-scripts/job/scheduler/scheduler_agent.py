import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
import logging
from settings import INTERFACE_URL
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import http_request_post
from dao.get.mysql.app_info import get_log_warn_log_info


class ScheduleAgent(object):
    def query_app_info(self):
        logs = get_log_warn_log_info()
        for log_bo in logs:
            # payload = {}
            logging.info("{} 准备执行".format(log_bo))
            if len(str(log_bo.get("success_publish_suite_name"))) == 0:
                logging.info("{} 一个环境都没部署成功 无法自动触发历史推".format(log_bo))
                continue
            mock_type = 0
            jacoco_type = 0
            api_type = 0
            sharding_type = 0
            try:
                agents = str(log_bo.get("agents")).split(",")
                logging.info("agents:{}".format(agents))
                for agent in str(log_bo.get("agents")).split(","):
                    logging.info("agent:{}".format(agent))
                    if "howbuy-interface-scan-agent" in agent:
                        api_type = 1
                    elif "shardingsphere-agent" in agent:
                        sharding_type = 1
            except Exception as exx:
                logging.error(exx)

            payload = {"pipeline_id": log_bo.get("iteration_id"), "app_name": log_bo.get("module_name"),
                       "br_name": log_bo.get("branch"),
                       "suite_list": [log_bo.get("success_publish_suite_name")], "jacoco_type": jacoco_type,
                       "mock_type": mock_type,
                       "api_type": api_type, "sharding_type": sharding_type,
                       "ptp_type": "H"}
            logging.info("{} 执行开始".format(payload))
            self.execute(payload)
            logging.info("{} 执行完毕".format(payload))

    def execute(self, payload):
        logging.info("{} 执行中。。。".format(payload))
        url = INTERFACE_URL['spider'] + INTERFACE_URL['spider_context'] + INTERFACE_URL[
            'pipeline_test_publish_suite']
        try:
            res = http_request_post(url, payload, headers={'Content-Type': 'application/json',
                                                           "Authorization": INTERFACE_URL['spider_api_Authorization']})
            result = res.json()
            logging.info(result)
            if "status" in result and result["status"] == "success":
                # update_warn_log_info(payload.get("app_name"), payload.get("br_name"), "running", result['msg'])
                return True
            else:
                # update_warn_log_info(payload.get("app_name"), payload.get("br_name"), "pause", "历史推发生错误")
                return "历史推发生错误"
        except Exception as ex:
            logging.error(ex)
            # update_warn_log_info(payload.get("app_name"), payload.get("br_name"), "pause", ex)
            return "历史推发生错误"


# 每日中午12点 每日晚9点 每次运行最多20个应用，
if __name__ == '__main__':
    schedule = ScheduleAgent()
    schedule.query_app_info()
