import json
import re
import subprocess

import paramiko
import pymysql
import datetime
from pymysql.cursors import DictCursor
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import PIPELINE_TEST_PUBLISH as PTP


def connect_mysql(database):
    conn = pymysql.connect(
        host='***************', port=3306,
        database=database, charset='utf8',
        user='root', password='123456')
    return conn


def conn_close(conn):
    conn.close()


def main():
    logging.info('刷新数据库portal_{}开始'.format(env_codes))
    envCodes = env_codes.split(",")
    for env_code in envCodes:
        conn = connect_mysql('portal_{}'.format(env_code))
        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''
            update t_server_info set 
            db_url='****************************************_{}?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=true',
            zk_address='zookeeper:2181',
            orgs=',TP,' '''.format(env_code))
            result = cursor.fetchall()
            logging.info('刷新t_server_info数据结果：{}'.format(result))


            cursor.execute('''
                       INSERT INTO t_server_info (`guid`, `slice_name`, `db_url`, `db_username`, `db_password`, `zk_address`, `orgs`, `show_name`) VALUES ('2', 'slice_pabase_b', '****************************************_1_{}?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=true', 'root', '123456', 'zookeeper:2181', ',CC,', NULL)'''.format(env_code))
            result = cursor.fetchall()
            logging.info('刷新t_server_info数据结果：{}'.format(result))

            cursor.execute('''
                                INSERT INTO t_server_info (`guid`, `slice_name`, `db_url`, `db_username`, `db_password`, `zk_address`, `orgs`, `show_name`) VALUES ('3', 'slice_pabase_c', '******************************************_{}?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=true', 'root', '123456', 'zookeeper:2181', ',CD,PA,', NULL)'''.format(
                env_code))
            result = cursor.fetchall()
            logging.info('刷新t_server_info数据结果：{}'.format(result))

            cursor.execute('''update t_sys_role set ORG_CODE='TP' where ROLE_CODE='QA_MANGER' ''')
            result = cursor.fetchall()
            logging.info('刷新t_sys_usr 数据结果：{}'.format(result))

            cursor.execute('''update t_sys_role set ORG_CODE='CC' where ROLE_CODE='PM_PMM' ''')
            result = cursor.fetchall()
            logging.info('刷新t_sys_usr 数据结果：{}'.format(result))

            cursor.execute('''update t_sys_role set ORG_CODE='PA' where ROLE_CODE='FDC_MANGER' ''')
            result = cursor.fetchall()
            logging.info('刷新t_sys_usr 数据结果：{}'.format(result))


            cursor.close()
        conn.commit()
        conn_close(conn)
        logging.info('刷新数据库portal_{}开始'.format(env_code))


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17'] '''
    params = sys.argv
    logging.info(params)
    env_codes = params[1]
    main()
