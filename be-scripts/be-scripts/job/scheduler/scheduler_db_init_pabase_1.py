import json
import re
import subprocess

import paramiko
import pymysql
import datetime
from pymysql.cursors import DictCursor
import requests
import logging
import os
import sys


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import PIPELINE_TEST_PUBLISH as PTP
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd



def connect_mysql(database):
    conn = pymysql.connect(
        host='***************', port=3306,
        database=database, charset='utf8',
        user='root', password='123456')
    return conn


def conn_close(conn):
    conn.close()


def main():

    logging.info('{}环境初始化调度相关数据库begin'.format(env_codes))
    logging.info('初始化数据库pabase_1_{}开始'.format(env_codes))
    envCodes = env_codes.split(",")
    for env_code in envCodes:
        conn = connect_mysql('portal')
        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''DROP DATABASE IF EXISTS pabase_1_{}'''.format(env_code))
            result = cursor.fetchall()
            cursor.execute('''CREATE DATABASE pabase_1_{}'''.format(env_code))
            result = cursor.fetchall()
            logging.info('创建pabase库结果：{}'.format(result))
            cursor.close()
        conn_close(conn)
        cmd = 'sshpass -p howbuylpt ssh root@************** ' \
              '\'mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob -h *************** -P 3306 -u root -p123456 pabase_online2_1 | ' \
              'mysql -h *************** -P 3306 -u root -p123456 pabase_1_{}\'' \
            .format(env_code)
        exec_local_cmd(cmd)
        logging.info('初始化数据库pabase_1_{}结束'.format(env_code))


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17'] '''
    params = sys.argv
    logging.info(params)
    env_codes = params[1]
    main()
