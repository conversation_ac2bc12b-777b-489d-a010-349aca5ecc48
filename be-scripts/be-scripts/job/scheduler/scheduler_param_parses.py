import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17'] '''
    params = sys.argv
    logging.info(params)
    try:
        if len(params) < 1:
            raise ValueError("缺少必要参数：{}".format(params))
        # 正常返回
        sys.exit()
    except ValueError as e:
        # 打印错误信息
        err_msg = "ValueError：{}".format(e)
        logging.error(err_msg)
        # 异常返回
        sys.exit(-1)
    except Exception as ex:
        # 记录错误信息
        err_msg = "系统级异常：{}".format(ex)
        logging.error(err_msg)
        # 打印错误信息
        # 异常返回
        sys.exit(-1)
