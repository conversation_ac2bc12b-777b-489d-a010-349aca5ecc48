import json
import re
import subprocess

import paramiko
import pymysql
import datetime
from pymysql.cursors import DictCursor
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import PIPELINE_TEST_PUBLISH as PTP


def connect_mysql(database):
    conn = pymysql.connect(
        host='**************', port=3306,
        database=database, charset='utf8',
        user='howbuy_quartz', password='77>yW6pScL7)')
    return conn


def conn_close(conn):
    conn.close()


def main():
    logging.info('刷新数据库quartz_{}开始'.format(env_codes))
    envCodes = env_codes.split(",")
    for env_code in envCodes:
        conn = connect_mysql('howbuy_quartz_{}'.format(env_code))
        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''update qrtz_trigger set status=1 where channel='url' ''')
            result = cursor.fetchall()
            logging.info('刷新qrtz_trigger 数据结果：{}'.format(result))
            cursor.close()
        conn.commit()
        conn_close(conn)
        logging.info('刷新数据库qrtz_trigger_{}开始'.format(env_code))


if __name__ == '__main__':
    ''' ['/home/<USER>/be-scripts/be-scripts/job/scheduler/scheduler_db_init.py', 'scheduler', '1.0.0', 'tms17'] '''
    params = sys.argv
    logging.info(params)
    env_codes = params[1]
    main()
