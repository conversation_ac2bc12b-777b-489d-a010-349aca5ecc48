import json
import re
import subprocess

import paramiko
import pymysql
import datetime
from pymysql.cursors import DictCursor
import requests
import logging
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import PIPELINE_TEST_PUBLISH as PTP
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd, get_tomcat_password, \
    exec_local_cmd_by_sshpass_tomcat


def restartApp(node_ip, script_name):
    """获取应用所在机器ip的tomcat密码"""
    password, msg = get_tomcat_password(node_ip, cmd=None, password=None)
    cmd = 'sh ' + script_name
    """远程执行启动命令"""
    exec_local_cmd_by_sshpass_tomcat(node_ip, cmd, password)


def main():
    cmd = 'sshpass -p howbuy2015 ssh tomcat@************** ' \
          '\'sed -i "$ s/$/,{}/" /usr/local/tomcat-ccms/webapps/ccms/WEB-INF/classes/properties/admin.properties\'' \
        .format(users)
    exec_local_cmd(cmd)

    cmd = 'sshpass -p howbuy2015 ssh tomcat@************** ' \
          '\'cat /usr/local/tomcat-ccms/webapps/ccms/WEB-INF/classes/properties/admin.properties\''
    exec_local_cmd(cmd)

    logging.info('追加白名单正常')
    cmd = 'sshpass -p howbuy2015 ssh tomcat@************** ' \
          '\'sh /usr/local/tomcat-ccms/bin/shutdown.sh\''
    exec_local_cmd(cmd)

    cmd = 'sshpass -p howbuy2015 ssh tomcat@************** ' \
          '\'sh /usr/local/tomcat-ccms/bin/startup.sh\''
    exec_local_cmd(cmd)

if __name__ == '__main__':
    ''' test1 '''
    params = sys.argv
    logging.info(params)
    users = params[1]
    main()
