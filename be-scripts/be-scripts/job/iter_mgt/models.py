from peewee import *
from playhouse.mysql_ext import J<PERSON><PERSON><PERSON>
from dao.base_model import BaseModel


class TaskMgtRocketmqInfoLog(BaseModel):
    name_server = Char<PERSON>ield(verbose_name='namesrvIP地址和端口', max_length=50)
    name_server_domain = CharField(verbose_name='namesrvI域名', max_length=200)
    group = CharField(verbose_name='生产组名', max_length=100)
    topic = CharField(verbose_name='topic名称', max_length=100)
    message = CharField(verbose_name='消息内容', max_length=1000)
    status = CharField(verbose_name='返回状态', max_length=50)
    msg_id = CharField(verbose_name='返回消息ID', max_length=100)
    offset = CharField(verbose_name='消息偏移量', max_length=100)
    creator = CharField(verbose_name='创建者', max_length=50)
    create_time = DateTimeField(verbose_name='创建时间')

    class Meta:
        table_name = 'task_mgt_rocketmq_info_log'
        verbose_name = 'RMQ日志信息表'


class IterMgtArchiveLog(BaseModel):
    iteration_id = CharField(verbose_name='迭代版本', max_length=200)
    step_order = CharField(verbose_name='步骤顺序', max_length=10)
    step_name = CharField(verbose_name='步骤名称', max_length=50)
    step_desc = CharField(verbose_name='步骤描述', max_length=500)
    step_status = CharField(verbose_name='步骤结果', max_length=50)
    request_params = JSONField(verbose_name='请求参数')
    response_result = JSONField(verbose_name='返回结果')
    opt_time = DateTimeField(verbose_name='创建时间')
    opt_user = CharField(verbose_name='创建人', max_length=100)

    class Meta:
        db_table = 'iter_mgt_archive_log'
        verbose_name = '迭代归档日志表'
