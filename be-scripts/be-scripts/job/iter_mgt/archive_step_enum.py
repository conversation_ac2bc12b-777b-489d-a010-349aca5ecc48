from enum import Enum


class ArchiveStepEnum(Enum):
    CHECK_CONFIG = ('1-1-0', "check_config", '宙斯配置检查')
    ARCHIVE_CONFIG = ('1-1-1', "archive_config", '宙斯配置归档')
    PRE_ITER_ARCHIVE = ('2-1-0', "pre_iter_archive", '迭代预归档')
    COMMON_PRE_ITER_ARCHIVE = ('2-1-1', "common_pre_iter_archive", '依赖包迭代预归档')
    CODE_MERGE = ('2-2-0', "code_merge", '代码合并')
    ITER_DATA_UPDATE = ('2-2-1', "iter_data_update", '迭代数据更新')
    DELETE_JENKINS_PIPELINE = ('2-2-2', "delete_jenkins_pipeline", '删除Jenkins流水线')
    UPDATE_ARCHIVE_MSG = ('2-2-3', "update_archive_msg", '更新归档信息')
    COMMON_CODE_MERGE = ('2-3-1', "common_code_merge", '依赖包代码合并')
    COMMON_ITER_DATA_UPDATE = ('2-3-2', "common_iter_data_update", '依赖包迭代数据更新')
    UPDATE_COMMON_ARCHIVE_MSG = ('2-3-3', "update_common_archive_msg", '更新依赖包归档信息')
    SEND_ARCHIVE_INFO_TO_RMQ = ('2-4-0', "send_archive_info_to_rmq", '发送归档信息到RMQ')
    ITER_SQL_ARCHIVE = ('2-4-1', "iter_sql_archive", '迭代sql归档')
