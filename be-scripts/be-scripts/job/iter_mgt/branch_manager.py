import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from utils.form.branch_del import BranchModifyForm
from utils.form.branch_apply import BranchApplyForm, FeatureApplyForm, IterMgt, NonMvnBranchApplyForm, \
    NonMvnFeatureApplyForm
from settings import logger, JENKINS_INFO
from job.jenkins import jenkins_xml
from dao.get.mysql.branch_apply import get_release_app_name
from dao.get.mysql import iterative_pipeline_branches as iter_br
from common.log.record_log import record_log
from dao.get.mysql.app_info import app_package_type
from common.ext_cmd.shell_cmd import clean_workspace
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt


class BranchManager:
    workspace_root = '/data/platform'

    def __init__(self, send_log=record_log("jenkins", 0)):
        """
        分支管理功能，jenkins调用不用穿send_log
        :param send_log:
        """
        self.acceptor = {
            "del_repos": self.del_repos,  # 删除仓库
            "add_repos": self.add_repos,  # 追加仓库
            "iter_apply": self.iter_apply,  # 迭代申请
        }
        self.send_log = send_log
        # self.send_log.send(None)

    def del_repos(self, workspace, iteration_id, repos_path_list, proposer, *args):
        """
        删除仓库功能
        :param workspace:
        :param iteration_id: 迭代号
        :param repos_path_list: gitlab仓库相对路径裂变 ps ["mojie/mring","mojie/mrint-itest"]
        :param proposer: 申请人
        :param args:
        :return:
        """

        self.send_log.send({"step": "删除迭代内仓库"})
        try:
            self.send_log.send({"step": "删除迭代内仓库"})
            br_mod_fm = BranchModifyForm(workspace, iteration_id, repos_path_list, proposer)
            app_list = br_mod_fm.commit()
            jenkins_job_mgt = JenkinsJobMgt()
            for row in app_package_type(app_list):
                # 动态删除 jenkins job
                if row.get("package_type") not in ("android-com", "ios-com"):
                    jenkins_job_mgt.delete_pipeline(iteration_id, row["module_name"])

            self.send_log.send({"status": "success", "step": "删除迭代内仓库", "content": "删除迭代内仓库"})
        except Exception as e:
            self.send_log.send({"status": "failure", "step": "删除迭代内仓库", "content": str(e)})

        self.send_log.close()

    def add_repos(self, workspace, iteration_id, repos_path_list, proposer, *args):
        """
        追加仓库功能
         :param workspace:
        :param iteration_id: 迭代号
        :param repos_path_list: gitlab仓库相对路径裂变 ps ["mojie/mring","mojie/mrint-itest"]
        :param proposer: 申请人
        :param args:
        :return:
        """
        self.send_log.send({"step": "追加仓库"})
        try:
            add_repos_form = IterMgt(iteration_id, repos_path_list, workspace, proposer)
            add_repos_form.commit()
            self.send_log.send({"status": "success", "step": "追加仓库", "content": "追加仓库成功"})
        except Exception as e:
            self.send_log.send({"status": "failure", "step": "追加仓库", "content": str(e)})
        self.send_log.close()
        j_xml = jenkins_xml.JenkinsXml()
        tapd_id = iter_br.get_tapd_id(iteration_id)
        for app_name in get_release_app_name(repos_path_list):
            j_xml.create_pipeline(iteration_id + "_" + app_name, tapd_id, app_name)

    def iter_apply(self, workspace, git_group, br_name, repos_path_list, proposer, branch_type, deadline, tapd_id,
                   desc, is_update_out_dep):
        """
        迭代申请功能
        :param workspace: 工作空间
        :param git_group: gitlab组
        :param br_name: 分支名称
        :param repos_path_list: gitlab仓库相对路径裂变 ps ["mojie/mring","mojie/mrint-itest"]
        :param proposer: 申请人
        :param branch_type: PS release，bugfix，featrue
        :param deadline: 截止日期
        :param tapd_id: 绑定tapd
        :param desc: 迭代描述
        :return:
        """
        self.send_log.send({"step": "创建新迭代分支"})

        try:
            link = {"feature": (FeatureApplyForm, NonMvnFeatureApplyForm),
                    "release": (BranchApplyForm, NonMvnBranchApplyForm),
                    "bugfix": (BranchApplyForm, NonMvnBranchApplyForm)}
            my_node_list = []
            # 实例化所有node
            for node in link[branch_type]:
                my_node_list.append(node(br_name, branch_type, is_update_out_dep, git_group,
                                         deadline, repos_path_list, desc, workspace, proposer, tapd_id))
            # 给每个node设置下一个调用的node
            for node_index in range(len(my_node_list) - 1):
                my_node_list[node_index].set_successor(my_node_list[node_index + 1])
            # 调用第一个的commit方法，开启责任链调用模式
            my_node_list[0].commit()
            self.send_log.send({"status": "success", "step": "创建新迭代分支",
                                "content": "分支拉成功:分支名称为{}".format(br_name)})
        except Exception as e:
            import traceback
            logger.error(traceback.format_exc())
            self.send_log.send({"status": "failure", "step": "创建新迭代分支", "content": str(e)})
        self.send_log.close()

    def platform_call(self, params):
        """
        平台调用分发器
        :param params: dict
        :return:
        """
        if "iteration_id" in params:
            iteration_id = params["iteration_id"]
        else:
            iteration_id = "{}_{}".format(params["gitlab_group"], params["branch_name"])
        workspace = self.workspace_root + "/{}_{}".format(params["task"], iteration_id)
        # repos_path_list = params["repos_str"].split(",")
        if os.path.join(workspace):
            os.system("mkdir -p {}".format(workspace))
        if params["task"] == "iter_apply":
            self.acceptor[params["task"]](workspace, params["gitlab_group"], params["branch_name"],
                                          params["repos_str"], params["proposer"], params["branch_type"],
                                          params["deadline"], params["tapd_id"], params["desc"], params["is_update_out_dep"])
        else:
            self.acceptor[params["task"]](workspace, iteration_id, params["repos_str"], params["proposer"])
        # clean_workspace(workspace)

    def call(self, params):
        """
        jenkins调用
        :param params:
        :return:
        """
        group_name = params[2].split("_")[0]
        repos_path_list = params[4].split(",")
        logger.info(repos_path_list)
        self.acceptor[params[0]](params[1], group_name, params[3], repos_path_list, params[5])


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    bm = BranchManager()
    bm.call(sys.argv[1:])
