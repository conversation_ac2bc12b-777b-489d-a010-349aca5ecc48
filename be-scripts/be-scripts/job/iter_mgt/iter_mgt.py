import os
import sys
import json

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from dao.get.mysql.script_log import get_args
from settings import logger
from common.log.record_log import record_log

from job.iter_mgt import branch_manager
from job.app_mgt import app_mgt


class ControlCenter:
    router = {"del_repos": branch_manager.BranchManager,
                 "add_repos": branch_manager.BranchManager,
                 "iter_apply": branch_manager.BranchManager,
                 "svn_analysis": app_mgt.AppMgt,
                 "git_analysis": app_mgt.AppMgt
                 }

    def __init__(self):
        pass

    def dispatch(self, params):
        caller = params[0]
        if caller == "platform":
            sid = params[1]
            record = record_log(caller, sid)  # 创建消费者对象
            self.platform_dispatch(record, sid)
        else:
            pass

    def jenkins_dispatch(self):
        pass

    def platform_dispatch(self, record, sid):
        exec_parameter = json.loads(get_args(sid))
        logger.info(exec_parameter)
        rt = self.router[exec_parameter["task"]](send_log=record)
        rt.platform_call(exec_parameter)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    cc = ControlCenter()
    cc.dispatch(sys.argv[1:])

