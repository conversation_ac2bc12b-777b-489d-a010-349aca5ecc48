import datetime
import json
import sys
import os
import gitlab

from functools import wraps
from common.call_api.gitlab.merge import Merger
from job.iter_mgt.archive_step_enum import ArchiveStepEnum
from job.iter_mgt.models import TaskMgtRocketmqInfoLog, IterMgtArchiveLog
from settings import ROCKETMQ, ES, GITLAB_HTTP, GITLAB_TOKEN, GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN
from dao.get.mysql.iter_mgt_iter_app_info import get_iter_app_info_for_archive
from settings import logger
from utils.form import archive_apply
from utils.public_utils import GetCommonDep
from common.routers.router import Result
from dao.get.mysql.iter_mgt_email import get_mail_list
from common.email import send_email
from rocketmq.client import Producer, Message
from utils.form.models import EsMgtAppBind
from ci_pipeline.ci_pipeline_models.iter_models import IterAppInfoModel, BranchesModel


class ArchiveApplyForm:

    @classmethod
    def as_view(cls, request):
        publish_form = ArchiveApplyForm()
        return publish_form.archive_apply_form(request)

    def _merge_es(self, iteration_id, gitlab_http, gitlab_token, merge_to):
        """合并ES仓库主干到分支"""
        branch_info = BranchesModel.select(BranchesModel.project_group, BranchesModel.br_name).where(
            BranchesModel.pipeline_id == iteration_id).first()
        project_group = branch_info.project_group
        br_name = branch_info.br_name

        if gitlab_http == GITLAB_HTTP:
            es_code_git_repos = f"{project_group}/{ES.get('git_repo_name')}"
        else:
            es_code_git_repos = f"{ES.get('gitlib_group_name')}/{project_group}"

        logger.info(f"开始ES仓库合并，分支: {br_name}, git_url: {gitlab_http}, 仓库: {es_code_git_repos}")

        merge = Merger(br_name, [es_code_git_repos],
                       gitlab_http=gitlab_http,
                       gitlab_token=gitlab_token)
        if merge_to == "branch":
            merge_status_dic = merge.merge_master_to_branch()
        else:
            merge_status_dic = merge.merge_trunk_full_process(iteration_id=iteration_id)

        for repo, status in merge_status_dic.items():
            if "不可合并" in status:
                error_msg = f"ES仓库{repo}, 分支 {br_name} 归档失败, 原因: 有冲突，{status}"
                logger.error(error_msg)
                return Result.failed_dict(error_msg)

        logger.info(f"合并成功，结束ES仓库合并，分支: {br_name}, git_url: {gitlab_http}, 仓库: {es_code_git_repos}")

        return None

    def _prepare_iter_archive(self, iter_archive, iteration_id, com_dep, es_iter_flag=False):
        """准备迭代归档前的检查"""
        # 预检主迭代
        if (pre_result := iter_archive.pre_iter_archive()).status == Result.failed:
            return pre_result

        # 预检依赖迭代
        for common_iter_id in com_dep.common_info:
            logger.info(f"开始依赖包迭代 {common_iter_id} 归档预检测")
            common_iter_archive = archive_apply.CommonIterArchive(common_iter_id)
            if (common_pre_result := common_iter_archive.pre_iter_archive()).status == Result.failed:
                logger.info(f"依赖包迭代 {common_iter_id} 归档预检测失败")
                return common_pre_result
            logger.info(f"依赖包迭代 {common_iter_id} 归档预检测结束")

        # 处理ES合并 暂时下线 20250526 by fwm
        # ES仓库跟迭代是在同分组，所以不需要重复处理mater回合到分支 20250604 by fwm
        # if es_iter_flag:
        #     git_configs = [
        #         (GITLAB_HTTP, GITLAB_TOKEN),
        #         (GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN)
        #     ]
        #
        #     for http, token in git_configs:
        #         if (result := self._merge_es(iteration_id, http, token, "branch")) is not None:
        #             return result

        return None

    def _get_es_iter_flag_by_iteration_id(self, iteration_id):
        # 使用Peewee ORM进行JOIN查询
        query = (EsMgtAppBind
                 .select()
                 .join(IterAppInfoModel, on=(EsMgtAppBind.module_name == IterAppInfoModel.appName))
                 .where(IterAppInfoModel.pipeline_id == iteration_id)
                 .limit(1))

        # 检查是否有匹配的记录
        es_iter_flag = query.exists()

        if es_iter_flag:
            # 根据iteration_id查询iter_mgt_iter_info表获取br_name和project_group
            try:
                branch_info = BranchesModel.get(BranchesModel.pipeline_id == iteration_id)
                br_name = branch_info.br_name
                project_group = branch_info.project_group
                
                # 构建ES仓库路径
                es_repo_path = f"{project_group}/es-script"
                
                # 检查ES仓库是否存在br_name分支
                es_iter_flag = self._check_es_branch_exists(es_repo_path, br_name)
                
            except BranchesModel.DoesNotExist:
                logger.error(f"未找到iteration_id为{iteration_id}的分支信息")
                es_iter_flag = False
            except Exception as e:
                logger.error(f"检查ES分支时发生错误: {str(e)}")
                es_iter_flag = False
            
        return es_iter_flag

    def _check_es_branch_exists(self, es_repo_path, br_name):
        """
        检查ES仓库中是否存在指定的分支
        :param es_repo_path: ES仓库路径，格式为 project_group/es-script
        :param br_name: 分支名称
        :return: True如果分支存在，False如果不存在
        """
        git_configs = [
            (GITLAB_HTTP, GITLAB_TOKEN),
        ]
        
        for gitlab_http, gitlab_token in git_configs:
            try:
                gl = gitlab.Gitlab(gitlab_http, gitlab_token)
                
                # 查找ES仓库项目
                try:
                    # 直接通过路径获取项目
                    es_project = gl.projects.get(es_repo_path, lazy=True)
                except gitlab.exceptions.GitlabGetError:
                    # 如果直接获取失败，尝试通过搜索获取
                    projects = gl.projects.list(search=es_repo_path.split('/')[-1], all=True)
                    es_project = None
                    
                    for project in projects:
                        if project.path_with_namespace == es_repo_path:
                            es_project = project
                            break
                
                if es_project is None:
                    logger.warning(f"在{gitlab_http}中未找到ES仓库: {es_repo_path}")
                    continue
                
                # 检查分支是否存在
                try:
                    branch = es_project.branches.get(br_name)
                    if branch:
                        logger.info(f"在ES仓库{es_repo_path}中找到分支: {br_name}")
                        return True
                except gitlab.exceptions.GitlabGetError:
                    logger.info(f"在ES仓库{es_repo_path}中未找到分支: {br_name}")
                    continue
                    
            except Exception as e:
                logger.error(f"检查ES仓库{es_repo_path}分支{br_name}时发生错误: {str(e)}")
                continue
        
        # 如果在所有GitLab实例中都没有找到分支，返回False
        logger.warning(f"在所有GitLab实例中都未找到ES仓库{es_repo_path}的分支{br_name}")
        return False

    def _iter_archive_es(self, iteration_id):
        git_configs = [
            (GITLAB_HTTP, GITLAB_TOKEN),
            (GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN)
        ]

        for http, token in git_configs:
            if (result := self._merge_es(iteration_id, http, token, "master")) is not None:
                return result

    def archive_apply_form(self, request):
        logger.info("发布申请请求参数 {}".format(request))
        iteration_id = request["iteration_id"]
        iter_archive = archive_apply.IterArchive(iteration_id)

        es_iter_flag = self._get_es_iter_flag_by_iteration_id(iteration_id)

        """正式归档之前，先进行预检，预检通过后再正式归档"""
        logger.info("开始迭代{}归档预检测".format(iteration_id))
        com_dep = GetCommonDep.instance(iteration_id)

        prepare_result = self._prepare_iter_archive(iter_archive, iteration_id, com_dep, es_iter_flag)
        if prepare_result:
            return prepare_result
        logger.info("迭代{}归档预检测结束".format(iteration_id))
        """预检结束，开始正式归档"""
        logger.info("开始迭代{}正式归档".format(iteration_id))
        try:
            sql_result = iter_archive.iter_sql_archive()
            logger.info("迭代sql{}归档结束,结果日志{}".format(iteration_id, sql_result))
        except Exception as e:
            logger.error(str(e))
            sql_msg = '迭代sql归档异常：{}'.format(str(e))
            return Result.success_dict(sql_msg)
        result = iter_archive.iter_archive()
        if result.status == Result.failed:
            return result
        for common_iter_id in com_dep.common_info:
            logger.info("开始依赖包迭代{}正式归档".format(common_iter_id))
            common_iter_archive = archive_apply.CommonIterArchive(common_iter_id)
            common_result = common_iter_archive.iter_archive()
            logger.info("依赖包迭代{}正式归档结束".format(common_iter_id))
            if common_result.status == Result.failed:
                return common_result

        # ES源码分支和制品分支归档
        if es_iter_flag:
            self._iter_archive_es(iteration_id)

        # 归档成功后发送RMQ消息 20220926 by fwm
        self.send_archive_info_to_rmq(iteration_id)
        logger.info("迭代{}正式归档结束，开始发邮件通知".format(iteration_id))
        try:
            # 归档完成邮件通知
            mail_to = get_mail_list(iteration_id)
            if "<EMAIL>" not in mail_to:
                mail_to.append("<EMAIL>")
            self._inform(iteration_id, mail_to, result.msg[0])
        except Exception as e:
            logger.error(str(e))
            archive_msg = [{"msg": "归档邮件发送失败", "status": "failure"},
                           {"msg": "归档成功", "status": "success"}]
            archive_msg = archive_msg + result.msg[1]
            return Result.success_dict(archive_msg)
        archive_msg = [{"msg": "归档邮件发送成功", "status": "success"},
                       {"msg": "归档成功", "status": "success"}]
        archive_msg = archive_msg + result.msg[1]
        archive_msg = "{}||{}".format(archive_msg, result.msg[2])
        return Result.success_dict(archive_msg)

    @staticmethod
    def _inform(iteration_id, member_list, msg):
        sd = send_email.SendMail()
        sd.set_to(",".join(member_list))
        sd.set_subject('<运维部署平台>[{}] 归档完成'.format(iteration_id))
        sd.set_content('{}'.format(msg))
        sd.send()

    def send_archive_info_to_rmq(self, iteration_id):
        archive_info = get_iter_app_info_for_archive(iteration_id)
        logger.info("发送到rmq的归档信息为：{}".format(archive_info))
        topic = ROCKETMQ['archive_topic']
        namesrv = ROCKETMQ['namesrv'] if ROCKETMQ['namesrv'] else ""
        namesrv_domain = ROCKETMQ['namesrv_domain'] if ROCKETMQ['namesrv_domain'] else ""
        group_id = topic + "_GROUP"
        try:
            pd = Producer(group_id, timeout=6000)
            if namesrv:
                pd.set_namesrv_addr(namesrv)
            else:
                pd.set_namesrv_domain(namesrv_domain)
            pd.start()

            msg_body = json.dumps(archive_info).encode('utf-8')
            msg = Message(topic)
            msg.set_body(msg_body)

            ret = pd.send_sync(msg)
            # ret = pd.send_orderly(msg, 0)

            rmq_id = TaskMgtRocketmqInfoLog(name_server=namesrv, name_server_domain=namesrv_domain,
                                            group=group_id, topic=topic, message=msg_body, status=ret.status,
                                            msg_id=ret.msg_id, offset=ret.offset, creator="be-script",
                                            create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            rmq_id.save()
            pd.shutdown()
        except Exception as e:
            logger.error(str(e))
            rmq_id = TaskMgtRocketmqInfoLog(name_server=namesrv, name_server_domain=namesrv_domain,
                                            group=group_id, topic=topic, message="RMQ连接超时，超时时间6s", status="",
                                            msg_id="", offset="", creator="be-script",
                                            create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            rmq_id.save()
        finally:
            imal = IterMgtArchiveLog(iteration_id=iteration_id,
                                     step_order=ArchiveStepEnum.SEND_ARCHIVE_INFO_TO_RMQ.value[0],
                                     step_name=ArchiveStepEnum.SEND_ARCHIVE_INFO_TO_RMQ.value[1],
                                     step_desc=ArchiveStepEnum.SEND_ARCHIVE_INFO_TO_RMQ.value[2],
                                     step_status="success", request_params={"iteration_id": iteration_id},
                                     response_result={"msg": "发送RMQ消息给宙斯"}, opt_user='howbuyscm',
                                     opt_time=datetime.datetime.now())
            imal.save()


if __name__ == "__main__":
    # ArchiveApplyForm.as_view({"iteration_id": "scm_test_project_4.4.7"})
    iteration_id = sys.argv[1]
    aaf = ArchiveApplyForm()
    aaf.send_archive_info_to_rmq(iteration_id)
    iter_archive = archive_apply.IterArchive(iteration_id)
    sql_result = iter_archive.iter_sql_archive()
