import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from time import sleep
from dao.connect.mysql import DBConnectionManager
from job.jen<PERSON> import jenkins_xml
from settings import logger

def get_ready_to_delete_pipeline_info():
    sql = """
            select concat(t.pipeline_id, '_', i.appName) as jenkins_job_name from iter_mgt_iter_info t 
            left join iter_mgt_iter_app_info i on t.pipeline_id = i.pipeline_id
            left join app_mgt_app_module m on i.appName = m.module_name
            where t.br_status = 'close' and m.need_online = 1
            
            union
            
            select concat(t.pipeline_id, '_', i.appName) as jenkins_job_name from iter_mgt_iter_info t 
            left join iter_mgt_iter_app_info i on t.pipeline_id = i.pipeline_id
            left join app_mgt_app_module m on i.appName = m.module_name
            left join 
            (select max(create_time) as build_time, pmp.iteration_id, pmp.module_name from product_mgt_product_info pmp
            GROUP BY pmp.iteration_id, pmp.module_name) v 
            on t.pipeline_id = v.iteration_id and i.appName = v.module_name
            where t.br_status = 'open' and m.need_online = 1 and v.build_time < date_sub(now(),interval 6 month)
        """
    pipeline_info_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            pipeline_info_list.append(row["jenkins_job_name"])

    return pipeline_info_list


def can_not_delete_pipeline_info():
    sql = """
            select concat(t.pipeline_id, '_', i.appName) as jenkins_job_name from iter_mgt_iter_info t 
            left join iter_mgt_iter_app_info i on t.pipeline_id = i.pipeline_id
            left join app_mgt_app_module m on i.appName = m.module_name
            left join 
            (select max(create_time) as build_time, pmp.iteration_id, pmp.module_name from product_mgt_product_info pmp
            GROUP BY pmp.iteration_id, pmp.module_name) v 
            on t.pipeline_id = v.iteration_id and i.appName = v.module_name
            where t.br_status = 'open' and m.need_online = 1 and v.build_time >= date_sub(now(),interval 6 month)
        """
    pipeline_info_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            pipeline_info_list.append(row["jenkins_job_name"])

    return pipeline_info_list

def get_normal_pipeline_job_name(job_list):
    sql = """select git_url from tool_mgt_git_url
        """
    git_url_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        for row in db.cur.fetchall():
            git_url_list.append(row["git_url"])

    normal_pipeline_list = []
    for job in job_list:
        if job.split("_")[0] in git_url_list and job.split("_")[0] != 'test':
            normal_pipeline_list.append(job)
    return normal_pipeline_list


def get_jenkins_job_info():
    j_xml = jenkins_xml.JenkinsXml()
    all_jobs_li = j_xml.get_all_jobs()
    jenkins_job_list = []
    for item in all_jobs_li:
        jenkins_job_list.append(item['name'])
    return jenkins_job_list


def delete_jenkins_job(job_list):
    j_xml = jenkins_xml.JenkinsXml()
    for job_name in job_list:
        logger.info("开始删除jenkins_job: {}".format(job_name))
        j_xml.delete_job(job_name)
        sleep(3)
        logger.info("睡眠3s")
        logger.info("完成删除")


if __name__ == '__main__':
    pipeline_info_list = get_ready_to_delete_pipeline_info()
    logger.info("已归档的迭代和超过6个月未编译的迭代列表(共{})：{}".format(len(pipeline_info_list), pipeline_info_list))
    jenkins_job_list = get_jenkins_job_info()
    logger.info("jenkins_job_name列表(共{})：{}".format(len(jenkins_job_list), jenkins_job_list))
    unit_jenkins_job_list = []
    for item in jenkins_job_list:
        if '单元测试' in item:
            unit_jenkins_job_list.append(item)
    logger.info("待删除的“单元测试”job列表(共{})： {}".format(len(unit_jenkins_job_list), unit_jenkins_job_list))
    delete_job_list = list(set(pipeline_info_list) & set(jenkins_job_list))
    logger.info("待删除的job列表(共{})： {}".format(len(delete_job_list), delete_job_list))
    list1 = list(set(jenkins_job_list) - set(unit_jenkins_job_list))
    list2 = list(set(list1) - set(delete_job_list))
    logger.info("剩下暂时不能删除job列表(共{})： {}".format(len(list2), list2))
    can_not_delete_pipeline_list = can_not_delete_pipeline_info()
    logger.info("其中正在开发的6个月内有编译记录的(共{})： {}".format(len(can_not_delete_pipeline_list), can_not_delete_pipeline_list))
    list3 = list(set(list2) - set(can_not_delete_pipeline_list))
    logger.info("去掉正在开发的，剩余不能删除job列表(共{})： {}".format(len(list3), list3))

    normal_pipeline_list = get_normal_pipeline_job_name(list3)
    logger.info("其中符合迭代规则的待删除的(共{})： {}".format(len(normal_pipeline_list), normal_pipeline_list))
    final_leave_pipeline = list(set(list3)-set(normal_pipeline_list))
    final_leave_pipeline.extend(can_not_delete_pipeline_list)
    logger.info("最终留下来的job列表(共{})： {}".format(len(final_leave_pipeline), final_leave_pipeline))
    final_delete_job_list = list(set(jenkins_job_list) - set(final_leave_pipeline))
    given_do_not_delete_list = ['mojie_分支删除', 'mojie_分支申请', 'mojie_发布申请', 'mojie_归档', 'mojie_测试环境发布',
                                'mojie_环境绑定']
    final_delete_job_list = [x for x in final_delete_job_list if x not in given_do_not_delete_list]
    logger.info("最终需要删除的job列表(共{})：{}".format(len(final_delete_job_list), final_delete_job_list))
    delete_jenkins_job(final_delete_job_list)


