import os
import subprocess
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)


def shell_cmd(cmd):
    print(cmd)
    p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = p.communicate()
    rt_code = p.returncode
    stdout_info = stdout.decode()
    stderr_info = stderr.decode()
    print(stdout_info)
    return rt_code, stdout_info, stderr_info


def delete_jenkins_workspace(workspace_path, days):
    print("清理{}天前创建的jenkins_workspace".format(days))
    cmd = "find {} -maxdepth 1 -mindepth 1 -mtime +{} | xargs rm -rf".format(workspace_path, days)
    shell_cmd(cmd)


if __name__ == '__main__':
    workspace = sys.argv[1]
    days = sys.argv[2]

    delete_jenkins_workspace(workspace, days)
