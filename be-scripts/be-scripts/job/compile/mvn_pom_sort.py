import os
import sys
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from utils.compile.mvn import pom_sorter

if __name__ == '__main__':
    workspace, pipeline_id, app_name = sys.argv[1], sys.argv[2], sys.argv[3]

    pom_sorter = pom_sorter.PomSorter(
            workspace=workspace,
            pipeline_id=pipeline_id,
            app_name=app_name
        )
    res = pom_sorter.main()
    print(res)
