#!groovy
/********************************
# Descript：
#   「DevOps」系统自动发布脚本（测试环境）。
#   目前只支持新阵列的「be-scripts、hm-scripts」。
#   sh「多行模式」不支持「局部变量」只能使用「环境变量」。
#   sh「多行模式」不支持「env.、params.」写法。
# History:
# 	2022-11-21  Zt  First Release。
# 	2022-12-13  Zt  简化日志输出。
# 	2022-12-13  Zt  优化结果列表。
# 	2022-12-19  Zt  简化 & Debug模式。
# 	2023-04-17  Zt  新增「p-scripts」。
# 	2024-05-10  Zt  添加DevOps平台的服务类应用发布支持。
# 	2024-05-13  Zt  优化使用公共方法，减少jenkins_file的长度。
********************************/
def getDateStr() {
    return new Date().format('yyyyMMdd')
}

pipeline {
    agent {
        label 'nfs1'
    }
    environment {
        PYTHON_CMD="python3.x"
        //同步「根」目录（同步目录汇总，但本身并不会同步）
        SYNC_PATH="/data/devops_sync"
        //同步「环境」目录（具体的一个同步目录）
        SYNC_ENV_PATH="$SYNC_PATH/devops_sync_$PUBLISH_SUITE"
        // Git仓库地址：
        SCM_BES_GIT_URL="*************************:scm/be-scripts.git"
        SCM_HMS_GIT_URL="*************************:scm/hm-scripts.git"
        SCM_PS_GIT_URL="*************************:scm/be-scripts.git"
    }
    options {
        timeout(time: 90, unit: 'MINUTES')
        retry(1)
    }

    parameters {
        choice(
            name:'PUBLISH_APP',
            choices:['ALL', 'be-scripts', 'hm-scripts', 'p-scripts', '3-scripts', 'spider', 'mantis'],
            description:'需要发布的「应用名」，ALL代表所有应用。'
        )
        string(
            name: 'PUBLISH_BRANCH',
            defaultValue: '2.82.0',
            description: "需要发布的「分支」。"
        )
        choice(
            name:'PUBLISH_SUITE',
            choices:['test'],
            description:'需要发布的「环境」：test、prod'
        )
        booleanParam(
            name: 'IS_DEBUG',
            defaultValue: false,
            description: '调试模式：打印环境信息。'
        )
    }

    stages {

        stage('format') {
            steps {
                script {
                    manager.addShortText("${env.BUILD_USER}",'black','lightgreen','5px','yellow')
                    currentBuild.description = "发布应用: ${params.PUBLISH_APP}\n"
                    currentBuild.description += "发布环境: ${params.PUBLISH_SUITE}\n"
                    currentBuild.description += "发布版本: ${params.PUBLISH_BRANCH}"
                }
            }
        }

        stage('节点Debug') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false

            parallel {
                stage('01-node') {
                    steps {
                        println "==== NODE_NAME: ${env.NODE_NAME}"
                    }
                }
                stage('02-ssh') {
                    steps {
                        println "==== SSH_CONNECTION: ${env.SSH_CONNECTION}"
                    }
                }
            }
        }

        stage('环境Debug') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false

            parallel {
                stage('01-「PATH」') {
                    steps {
                        println "==== PATH(os): ${env.PATH}"
                    }
                }
                stage('02-环境变量') {
                    steps {
                        sh "printenv"
                    }
                }
                stage('03-全局变量') {
                    steps {
                        println "==== 同步「根」目录 SYNC_PATH: ${env.SYNC_PATH}"
                        println "==== 同步「环境」目录 SYNC_ENV_PATH: ${env.SYNC_ENV_PATH}"
                    }
                }
                stage('04-入参') {
                    steps {
                        println "==== 「应用名」 PUBLISH_APP: ${params.PUBLISH_APP}"
                        println "==== 「分支名」 PUBLISH_BRANCH: ${params.PUBLISH_BRANCH}"
                        println "==== 「发布环境」 PUBLISH_SUITE: ${params.PUBLISH_SUITE}"
                        println "==== 「调试模式」 IS_DEBUG: ${params.IS_DEBUG}"
                    }
                }
                stage('05-函数') {
                    steps {
                        ln_switch('spider-srv')
                    }
                }
            }
        }

        stage('前置处理') {
            failFast false

            parallel {
                stage('01-宙斯环境') {
                    steps {
                        script {
                            if ("${params.PUBLISH_SUITE}" == "prod") {
                                env.SUITE_CODE = "pd-prod"
                                println "==== 「产线(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                            } else if ("${params.PUBLISH_SUITE}" == "test") {
                                env.SUITE_CODE = "test"
                                println "==== 「测试(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                            } else {
                                env.SUITE_CODE = "${params.PUBLISH_SUITE}"
                                println "==== 「其它(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                            }
                        }
                    }
                }
            }
        }

        stage('制品准备') {
            parallel {
                stage('be-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: params.PUBLISH_APP
                            equals expected: '3-scripts', actual: params.PUBLISH_APP
                            equals expected: 'be-scripts', actual: params.PUBLISH_APP
                        }
                    }
                    //「be-scripts」的「文件夹」和「绝对路径」
                    environment {
                        BES_FOLDER = "be-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        BES_ABS_PATH = "$SYNC_ENV_PATH/$BES_FOLDER"
                        BES_LN_NAME = "be-scripts_${params.PUBLISH_SUITE}"
                        BES_ABS_LN = "$SYNC_ENV_PATH/$BES_LN_NAME"
                    }
                    stages {
                        stage('01-dir') {
                            steps {
                                mkdir_evn_path('be-scripts')
                            }
                        }
                        stage('02-bak') {
                            when {
                                anyOf {
                                    equals expected: 'prod', actual: PUBLISH_SUITE
                                }
                            }
                            //「be-scripts」的「备份目录」
                            environment {
                                _dateStr = getDateStr()
                                // 备份文件夹
                                BES_BAK_FOLDER = "bak_be-scripts_${_dateStr}"
                                // 备份绝对路径（父目录）
                                BES_BAK_PATH = "$SYNC_ENV_PATH/$BES_BAK_FOLDER"
                                // 备份绝对路径（具体目录，多放一级方便还原）
                                BES_BAK_ABS_PATH = "$BES_BAK_PATH/$BES_LN_NAME"
                            }
                            steps {
                                script {
                                    if (fileExists("${env.BES_BAK_ABS_PATH}") == false) {
                                        println "==== 当日无备份: ${env.BES_BAK_PATH}"
                                        if (fileExists("${env.BES_BAK_PATH}") == false) {
                                            sh "mkdir -p ${env.BES_BAK_PATH}"
                                            println "==== 创建当日备份目录: ${env.BES_BAK_PATH}"
                                        }
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            cp -a ${BES_LN_NAME}/ ${BES_BAK_FOLDER}/
                                        '''
                                        println "==== 成功备份目录: ${env.BES_BAK_FOLDER}"
                                    }
                                }
                            }
                        }
                        stage('03-pull') {
                            steps {
                                script {
                                    if (fileExists("${env.BES_ABS_PATH}")) {
                                        println "==== 老版本「重发」：${env.BES_FOLDER}"
                                        sh '''
                                            cd ${BES_ABS_PATH} && git status
                                            git checkout . && git pull
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.BES_FOLDER}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            git clone -b ${PUBLISH_BRANCH} ${SCM_BES_GIT_URL} ${BES_FOLDER}
                                        '''
                                    }
                                }
                            }
                        }
                        stage('04-conf') {
                            steps {
                                script {
                                    println "==== 重新生成「配置」：${env.BES_FOLDER}"
                                    sh '''
                                        cd ${BES_ABS_PATH}
                                        ${PYTHON_CMD} generate_config.py ${BES_ABS_PATH}/be-scripts/ ${PUBLISH_BRANCH} ${SUITE_CODE}
                                        git status
                                    '''
                                }
                            }
                        }
                    }
                }
                stage('hm-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: PUBLISH_APP
                            equals expected: '3-scripts', actual: params.PUBLISH_APP
                            equals expected: 'hm-scripts', actual: PUBLISH_APP
                        }
                    }
                    //hm-scripts」的「文件夹」和「绝对路径」
                    environment {
                        HMS_FOLDER = "hm-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        HMS_ABS_PATH = "$SYNC_ENV_PATH/$HMS_FOLDER"
                        HMS_LN_NAME = "hm-scripts_${params.PUBLISH_SUITE}"
                        HMS_ABS_LN = "$SYNC_ENV_PATH/$HMS_LN_NAME"
                    }
                    stages {
                        stage('01-dir') {
                            steps {
                                mkdir_evn_path('hm-scripts')
                            }
                        }
                        stage('02-bak') {
                            when {
                                anyOf {
                                    equals expected: 'prod', actual: PUBLISH_SUITE
                                }
                            }
                            //「hm-scripts」的「备份目录」
                            environment {
                                _dateStr = getDateStr()
                                // 备份文件夹
                                HMS_BAK_FOLDER = "bak_hm-scripts_${_dateStr}"
                                // 备份绝对路径（父目录）
                                HMS_BAK_PATH = "$SYNC_ENV_PATH/$HMS_BAK_FOLDER"
                                // 备份绝对路径（具体目录，多放一级方便还原）
                                HMS_BAK_ABS_PATH = "$HMS_BAK_PATH/$HMS_LN_NAME"
                            }
                            steps {
                                script {
                                    if (fileExists("${env.HMS_BAK_ABS_PATH}") == false) {
                                        println "==== 当日无备份: ${env.HMS_BAK_PATH}"
                                        if (fileExists("${env.HMS_BAK_PATH}") == false) {
                                            sh "mkdir -p ${env.HMS_BAK_PATH}"
                                            println "==== 创建当日备份目录: ${env.HMS_BAK_PATH}"
                                        }
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            cp -a ${HMS_LN_NAME}/ ${HMS_BAK_FOLDER}/
                                        '''
                                        println "==== 成功备份目录: ${env.HMS_BAK_FOLDER}"
                                    }
                                }
                            }
                        }
                        stage('03-pull') {
                            steps {
                                script {
                                    if (fileExists("${env.HMS_ABS_PATH}")) {
                                        println "==== 老版本「重发」：${env.HMS_FOLDER}"
                                        sh '''
                                            cd ${HMS_ABS_PATH} && git status
                                            git checkout . && git pull
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.HMS_FOLDER}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            git clone -b ${PUBLISH_BRANCH} ${SCM_HMS_GIT_URL} ${HMS_FOLDER}
                                        '''
                                    }
                                }
                            }
                        }
                        stage('04-conf') {
                            steps {
                                script {
                                    println "==== 重新生成「配置」：${env.HMS_FOLDER}"
                                    sh '''
                                        cd ${HMS_ABS_PATH}
                                        ${PYTHON_CMD} get_zeus_config.py ${PUBLISH_BRANCH} ${SUITE_CODE} ${HMS_ABS_PATH}
                                        git status
                                    '''
                                }
                            }
                        }
                    }
                }
                stage('p-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: params.PUBLISH_APP
                            equals expected: '3-scripts', actual: params.PUBLISH_APP
                            equals expected: 'p-scripts', actual: params.PUBLISH_APP
                        }
                    }
                    //「p-scripts」的「文件夹」和「绝对路径」
                    environment {
                        PS_FOLDER = "p-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        PS_ABS_PATH = "$SYNC_ENV_PATH/$PS_FOLDER"
                        PS_LN_NAME = "p-scripts_${params.PUBLISH_SUITE}"
                        PS_ABS_LN = "$SYNC_ENV_PATH/$PS_LN_NAME"
                    }
                    stages {
                        stage('01-dir') {
                            steps {
                                mkdir_evn_path('p-scripts')
                            }
                        }
                        stage('02-bak') {
                            when {
                                anyOf {
                                    equals expected: 'prod', actual: PUBLISH_SUITE
                                }
                            }
                            //「p-scripts」的「备份目录」
                            environment {
                                _dateStr = getDateStr()
                                // 备份文件夹
                                PS_BAK_FOLDER = "bak_p-scripts_${_dateStr}"
                                // 备份绝对路径（父目录）
                                PS_BAK_PATH = "$SYNC_ENV_PATH/$PS_BAK_FOLDER"
                                // 备份绝对路径（具体目录，多放一级方便还原）
                                PS_BAK_ABS_PATH = "$PS_BAK_PATH/$PS_LN_NAME"
                            }
                            steps {
                                script {
                                    if (fileExists("${env.PS_BAK_ABS_PATH}") == false) {
                                        println "==== 当日无备份: ${env.PS_BAK_PATH}"
                                        if (fileExists("${env.PS_BAK_PATH}") == false) {
                                            sh "mkdir -p ${env.PS_BAK_PATH}"
                                            println "==== 创建当日备份目录: ${env.PS_BAK_PATH}"
                                        }
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            cp -a ${PS_LN_NAME}/ ${PS_BAK_FOLDER}/
                                        '''
                                        println "==== 成功备份目录: ${env.PS_BAK_FOLDER}"
                                    }
                                }
                            }
                        }
                        stage('03-pull') {
                            steps {
                                script {
                                    if (fileExists("${env.PS_ABS_PATH}")) {
                                        println "==== 老版本「重发」：${env.PS_FOLDER}"
                                        sh '''
                                            cd ${PS_ABS_PATH} && git status
                                            git checkout . && git pull
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.PS_FOLDER}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            git clone -b ${PUBLISH_BRANCH} ${SCM_PS_GIT_URL} ${PS_FOLDER}
                                        '''
                                    }
                                }
                            }
                        }
                        stage('04-conf') {
                            steps {
                                script {
                                    println "==== 重新生成「配置」：${env.PS_FOLDER}"
                                    sh '''
                                        cd ${PS_ABS_PATH}
                                        ${PYTHON_CMD} generate_config.py ${PS_ABS_PATH}/be-scripts/ ${PUBLISH_BRANCH} ${SUITE_CODE}
                                        git status
                                    '''
                                }
                            }
                        }
                    }
                }

            }
        }

        stage('脚本直接发') {
            parallel {
                stage('be-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: params.PUBLISH_APP
                            equals expected: '3-scripts', actual: params.PUBLISH_APP
                            equals expected: 'be-scripts', actual: params.PUBLISH_APP
                        }
                    }
                    //「be-scripts」的「文件夹」和「绝对路径」
                    environment {
                        BES_FOLDER = "be-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        BES_ABS_PATH = "$SYNC_ENV_PATH/$BES_FOLDER"
                        BES_LN_NAME = "be-scripts_${params.PUBLISH_SUITE}"
                        BES_ABS_LN = "$SYNC_ENV_PATH/$BES_LN_NAME"
                    }
                    stages {
                        stage('05-ln') {
                            steps {
                                ln_switch('be-scripts')
                            }
                        }
                    }
                }
                stage('hm-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: PUBLISH_APP
                            equals expected: '3-scripts', actual: params.PUBLISH_APP
                            equals expected: 'hm-scripts', actual: PUBLISH_APP
                        }
                    }
                    //hm-scripts」的「文件夹」和「绝对路径」
                    environment {
                        HMS_FOLDER = "hm-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        HMS_ABS_PATH = "$SYNC_ENV_PATH/$HMS_FOLDER"
                        HMS_LN_NAME = "hm-scripts_${params.PUBLISH_SUITE}"
                        HMS_ABS_LN = "$SYNC_ENV_PATH/$HMS_LN_NAME"
                    }
                    stages {
                        stage('05-ln') {
                            steps {
                                script {
                                    if (fileExists("${env.HMS_ABS_LN}")) {
                                        println "==== 老版本「重发」：${env.HMS_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            rm -rf ${HMS_LN_NAME}
                                            ln -s ${HMS_FOLDER} ${HMS_LN_NAME}
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.HMS_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            ln -s ${HMS_FOLDER} ${HMS_LN_NAME}
                                        '''
                                    }
                                }
                            }
                        }
                    }
                }
                stage('p-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: params.PUBLISH_APP
                            equals expected: '3-scripts', actual: params.PUBLISH_APP
                            equals expected: 'p-scripts', actual: params.PUBLISH_APP
                        }
                    }
                    //「p-scripts」的「文件夹」和「绝对路径」
                    environment {
                        PS_FOLDER = "p-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        PS_ABS_PATH = "$SYNC_ENV_PATH/$PS_FOLDER"
                        PS_LN_NAME = "p-scripts_${params.PUBLISH_SUITE}"
                        PS_ABS_LN = "$SYNC_ENV_PATH/$PS_LN_NAME"
                    }
                    stages {
                        stage('05-ln') {
                            steps {
                                script {
                                    if (fileExists("${env.PS_ABS_LN}")) {
                                        println "==== 老版本「重发」：${env.PS_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            rm -rf ${PS_LN_NAME}
                                            ln -s ${PS_FOLDER} ${PS_LN_NAME}
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.PS_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            ln -s ${PS_FOLDER} ${PS_LN_NAME}
                                        '''
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }

        stage('服务发布==>') {

            stages {
                stage('服务A段发布') {
                    failFast false

                    parallel {
                        stage('spider-A') {
                            when {
                                anyOf {
                                    equals expected: 'ALL', actual: params.PUBLISH_APP
                                    equals expected: 'spider', actual: params.PUBLISH_APP
                                }
                            }
                            stages {
                                stage('01-发布') {
                                    steps {
                                        script {
                                            println "==== spider-A：发布 ===="
                                        }
                                    }
                                }
                                stage('02-检查') {
                                    steps {
                                        script {
                                            println "==== spider-A：检查 ===="
                                        }
                                        sleep 3
                                    }
                                }
                                stage('03-启动') {
                                    steps {
                                        script {
                                            println "==== spider-A：启动 ===="
                                        }
                                    }
                                }
                            }
                        }
                        stage('mantis-A') {
                            when {
                                anyOf {
                                    equals expected: 'ALL', actual: params.PUBLISH_APP
                                    equals expected: 'mantis', actual: params.PUBLISH_APP
                                }
                            }
                            stages {
                                stage('01-发布') {
                                    steps {
                                        script {
                                            println "==== mantis-A：发布 ===="
                                        }
                                    }
                                }
                                stage('02-检查') {
                                    steps {
                                        script {
                                            println "==== mantis-A：检查 ===="
                                        }
                                        sleep 3
                                    }
                                }
                                stage('03-启动') {
                                    steps {
                                        script {
                                            println "==== mantis-A：启动 ===="
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                stage('暂停15S') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: params.PUBLISH_APP
                            equals expected: 'spider', actual: params.PUBLISH_APP
                            equals expected: 'mantis', actual: params.PUBLISH_APP
                        }
                    }
                    steps {
                        sleep 5
                    }
                }

                stage('服务B段发布') {
                    failFast false

                    parallel {
                        stage('spider-B') {
                            when {
                                anyOf {
                                    equals expected: 'ALL', actual: params.PUBLISH_APP
                                    equals expected: 'spider', actual: params.PUBLISH_APP
                                }
                            }
                            stages {
                                stage('01-发布') {
                                    steps {
                                        script {
                                            println "==== spider-B：发布 ===="
                                        }
                                    }
                                }
                                stage('02-检查') {
                                    steps {
                                        script {
                                            println "==== spider-B：检查 ===="
                                        }
                                        sleep 3
                                    }
                                }
                                stage('03-启动') {
                                    steps {
                                        script {
                                            println "==== spider-B：启动 ===="
                                        }
                                    }
                                }
                            }
                        }
                        stage('mantis-B') {
                            when {
                                anyOf {
                                    equals expected: 'ALL', actual: params.PUBLISH_APP
                                    equals expected: 'mantis', actual: params.PUBLISH_APP
                                }
                            }
                            stages {
                                stage('01-发布') {
                                    steps {
                                        script {
                                            println "==== mantis-B：发布 ===="
                                        }
                                    }
                                }
                                stage('02-检查') {
                                    steps {
                                        script {
                                            println "==== mantis-B：检查 ===="
                                        }
                                        sleep 3
                                    }
                                }
                                stage('03-启动') {
                                    steps {
                                        script {
                                            println "==== mantis-B：启动 ===="
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
            }
        } //服务发布==>结束

        stage('后置处理') {
            failFast false

            parallel {
                stage('01-step1') {
                    steps {
                        sh "echo '>>>> step1:' $PYTHON_CMD"
                    }
                }
                stage('02-step2') {
                    steps {
                        sh "echo '>>>> 后置处理2:' $PYTHON_CMD"
                    }
                }
                stage('03-step3') {
                    steps {
                        sh "echo '>>>> 后置处理3:' $PYTHON_CMD"
                    }
                }
            }
        }
    }
    /*
    post {
        always {
            echo "========== zt@2022-11-21 =========="
        }
    }
    */
}

void mkdir_evn_path(para_app_name) {
    println "==== para_app_name: ${para_app_name}"
    if (fileExists("${env.SYNC_ENV_PATH}") == false) {
        println "==== 创建环境目录: ${env.SYNC_ENV_PATH}"
        sh "mkdir -p ${env.SYNC_ENV_PATH}"
    } else {
         println "==== 已存在环境目录: ${env.SYNC_ENV_PATH}"
    }
}

void ln_switch(para_app_name) {
    println "==== para_app_name: ${para_app_name}"
    def APP_VER_SUITE_FOLDER = "${para_app_name}_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
    def APP_ABS_PATH = "$SYNC_ENV_PATH/$APP_VER_SUITE_FOLDER"
    def APP_LN_NAME = "${para_app_name}_${params.PUBLISH_SUITE}"
    def APP_ABS_LN = "$SYNC_ENV_PATH/$APP_LN_NAME"
    println "==== APP_VER_SUITE_FOLDER: ${APP_VER_SUITE_FOLDER}"
    println "==== APP_ABS_PATH: ${APP_ABS_PATH}"
    println "==== APP_LN_NAME: ${APP_LN_NAME}"
    println "==== APP_ABS_LN: ${APP_ABS_LN}"

    if (fileExists("${APP_ABS_LN}")) {
        println "==== 老版本「重发」：${APP_ABS_LN}"
        sh """
            cd ${SYNC_ENV_PATH}
            rm -rf ${APP_ABS_LN}
            ln -s ${APP_VER_SUITE_FOLDER} ${APP_LN_NAME}
        """
    } else {
        println "==== 新版本「首发」：${APP_ABS_LN}"
        sh """
            cd ${SYNC_ENV_PATH}
            ln -sf ${APP_VER_SUITE_FOLDER} ${APP_LN_NAME}
        """
    }
}