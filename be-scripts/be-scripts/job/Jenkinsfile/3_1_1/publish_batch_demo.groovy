#!groovy
/********************************
# Descript：
#   DevOps服务端批量发布示例。
# History:
# 	2024-05-10  Zt  First Release。
********************************/
def getDateStr() {
    return new Date().format('yyyyMMdd')
}

pipeline {
    agent {
        label 'nfs1'
    }
    environment {
        PYTHON_CMD="python3.x"
        //同步「根」目录（同步目录汇总，但本身并不会同步）
        SYNC_PATH="/data/devops_sync"
        //同步「环境」目录（具体的一个同步目录）
        SYNC_ENV_PATH="$SYNC_PATH/devops_sync_$PUBLISH_SUITE"
    }
    options {
        timeout(time: 90, unit: 'MINUTES')
        retry(1)
    }

    parameters {
        booleanParam(
            name: 'IS_DEBUG', 
            defaultValue: false, 
            description: '调试模式：打印环境信息。'
        )
    }
    
    stages {

        stage('format') {
            steps {
                script {
                    manager.addShortText("${env.BUILD_USER}",'black','lightgreen','5px','yellow')
                    currentBuild.description = "IS_DEBUG: ${params.IS_DEBUG}"
                }
            }
        }

        stage('节点Debug') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false

            parallel {
                stage('01-node') {
                    steps {
                        println "==== NODE_NAME: ${env.NODE_NAME}"
                    }
                }
                stage('02-ssh') {
                    steps {
                        println "==== SSH_CONNECTION: ${env.SSH_CONNECTION}"
                    }
                }
            }
        }

        stage('环境Debug') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false

            parallel {
                stage('01-「PATH」') {
                    steps {
                        println "==== PATH(os): ${env.PATH}"
                    }
                }
                stage('02-环境变量') {
                    steps {
                        sh "printenv"
                    }
                }
                stage('03-全局变量') {
                    steps {
                        println "==== 同步「根」目录 SYNC_PATH: ${env.SYNC_PATH}"
                        println "==== 同步「环境」目录 SYNC_ENV_PATH: ${env.SYNC_ENV_PATH}"
                    }
                }
                stage('04-入参') {
                    steps {
                        println "==== 「应用名」 PUBLISH_APP: ${params.PUBLISH_APP}"
                        println "==== 「分支名」 PUBLISH_BRANCH: ${params.PUBLISH_BRANCH}"
                        println "==== 「发布环境」 PUBLISH_SUITE: ${params.PUBLISH_SUITE}"
                        println "==== 「调试模式」 IS_DEBUG: ${params.IS_DEBUG}"
                    }
                }
            }
        }

        stage('开始灾备发布-->') {
            failFast false

            stages {
                stage('灾备--1段') {
                    parallel {
                        stage('灾1-A') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-A节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-A节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-A节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('灾2-A') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾2-A节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾2-A节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾2-A节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
                stage('灾备--2段') {
                    parallel {
                        stage('灾1-B') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-B节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-B节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-B节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('灾1-C') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-C节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-C节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-C节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('灾1-D') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-D节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-D节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-D节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('灾1-E') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-E节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-E节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-E节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('灾1-F') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-F节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-F节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾1-F节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('灾2-B') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾2-B节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾2-B节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 灾备发布 >> 灾2-B节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }

        stage('开始产线发布-->') {
            failFast false

            stages {
                stage('产线--1段') {
                    parallel {
                        stage('b-169') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> bs-prod：169节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >>bs-prod：169节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> bs-prod：169节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('p-198') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：198节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：198节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：198节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('t-16_2') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >>tcloud-prod：16_2节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> tcloud-prod：16_2节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> tcloud-prod：16_2节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
                stage('产线--2段') {
                    parallel {
                        stage('b-122') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> bs-prod：122节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> bs-prod：122节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> bs-prod：122节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('p-171') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：171节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：171节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：171节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('p-151') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：151节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：151节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> prod：151节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('t-16_35') {
                            stages {
                                stage ('配置更新') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> tcloud-prod：16_35节点：配置更新！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('发布') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> tcloud-prod：16_35节点：发布！ ===="
                                        }
                                        
                                    }
                                }
                                stage ('服务验证') {
                                    steps {
                                        script {
                                            println "==== 生产发布 >> tcloud-prod：16_35节点：服务验证！ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }

        stage('后置处理') {
            failFast false

            parallel {
                stage('01-step1') {
                    steps {
                        sh "echo '>>>> step1:' $PYTHON_CMD"
                    }
                }
                stage('02-step2') {
                    steps {
                        sh "echo '>>>> 后置处理2:' $PYTHON_CMD"
                    }
                }
                stage('03-step3') {
                    steps {
                        sh "echo '>>>> 后置处理3:' $PYTHON_CMD"
                    }
                }
            }
        }
    }
    /*
    post {
        always {
            echo "========== zt@2022-11-21 =========="
        }
    }
    */
}