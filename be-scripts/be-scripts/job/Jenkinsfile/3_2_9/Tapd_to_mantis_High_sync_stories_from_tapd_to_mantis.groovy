#!groovy
pipeline {
    agent {
        label 'vmx'
    }
    environment {
        REQUEST_HOST="http://mantis.howbuy.pa/mantis/"
        REQUEST_URL = "${REQUEST_HOST}tapd_gateway/from_tapd/sync_data_from_tapd/"
    }
    stages {
		stage('平台产品1线-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 57711941"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "57711941","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "57711941","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
        stage('平台产品2线-test-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 50651707"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "50651707","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "50651707","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('研发部测试管理-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 55014084"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "55014084","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "55014084","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('交易后台-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
                    echo "workspace_id: 59626479"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "59626479","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "59626479","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('基础组-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 66914855"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "66914855","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "66914855","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('公募交易-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 52223238"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "52223238","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "52223238","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('H5敏捷项目-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 36243514"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "36243514","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "36243514","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('合作产品-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 64984051"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "64984051","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "64984051","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('爱码仕-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 40756777"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "40756777","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "40756777","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('爱码仕敏捷_FPS-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 63231183"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "63231183","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "63231183","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('CRM-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 36225275"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "36225275","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "36225275","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}

		stage('AI_产研-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 50982408"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "50982408","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "50982408","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}

		stage('AI_ai-prod-story') {
            steps {
				script {
				    echo "modified: "+modified
				    echo "workspace_id: 53224393"
					def iterationData
                    if (modified == null || modified.isEmpty()) {
                        iterationData = '{"workspace_id": "53224393","biz_type":"story"}'
                    } else {
                        iterationData = '{"workspace_id": "53224393","biz_type":"story", "modified": "' + modified + '"}'
                    }
					def iterationResponse = httpRequest(url: env.REQUEST_URL,
														 contentType: 'APPLICATION_JSON',
														 httpMode: 'POST',
														 requestBody: iterationData)
					if (iterationResponse.status != 200) {
						error("Failed to get Bugs data")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}

		stage('零售-story') {
    		steps {
    			script {
    				echo "modified: "+modified
    				echo "workspace_id: 59354735"
    				def iterationData
    				if (modified == null || modified.isEmpty()) {
    					iterationData = '{"workspace_id": "59354735","biz_type":"story"}'
    				} else {
    					iterationData = '{"workspace_id": "59354735","biz_type":"story", "modified": "' + modified + '"}'
    				}
    				def iterationResponse = httpRequest(url: env.REQUEST_URL,
    													 contentType: 'APPLICATION_JSON',
    													 httpMode: 'POST',
    													 requestBody: iterationData)
    				if (iterationResponse.status != 200) {
    					error("Failed to get Bugs data")
    				}
    				def res = readJSON text: iterationResponse.content
    				echo "返回消息: $res"
    				def status = res.status

    				if (status != "success") {
    					error("请求异常")
    				}
    			}
    		}
		}

    }
}