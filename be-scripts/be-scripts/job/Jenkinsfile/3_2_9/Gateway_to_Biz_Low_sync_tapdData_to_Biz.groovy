#!groovy
pipeline {
    agent {label 'vm'}
    environment {
        REQUEST_HOST="http://mantis.howbuy.pa/mantis/"
        REQUEST_URL = "${REQUEST_HOST}dev_effective/"
    }
    stages {
        stage('1.sync tapdTask to testTask') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=task&env_type=test"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to ync tapdTask to bizTask")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('2.sync tapdTask to devTask') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=task&env_type=dev"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to ync tapdTask to bizTask")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('3.sync tapdBug to testBug') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=bug&env_type=test"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to ync tapdTask to bizTask")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('4.sync tapdLaunchForm to bizLaunchForm') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=launch_form&env_type=test"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to ync tapdTask to bizTask")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('5.sync tapdStroy to devStory') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=story&env_type=dev"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to ync tapdTask to bizTask")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('6.sync teststory to dev submit') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=story&env_type=test"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to ync tapdTask to bizTask")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('7.sync tapdTimesheet to devTimesheet') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=timesheet&env_type=dev"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to sync tapdTimesheet to devTimesheet")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
		stage('8.sync tapdTimesheet to testTimesheet') {
            steps {
				script {
				    request_url = env.REQUEST_URL + "sync_data_from_gateway/?entry_type=timesheet&env_type=test"
					def iterationResponse = httpRequest(url: request_url)
					if (iterationResponse.status != 200) {
						error("Failed to sync tapdTimesheet to testTimesheet")
					}
					def res = readJSON text: iterationResponse.content
					echo "返回消息: $res"
					def status = res.status

					if (status != "success") {
						error("请求异常")
					}
				}
			}
		}
    }
}
