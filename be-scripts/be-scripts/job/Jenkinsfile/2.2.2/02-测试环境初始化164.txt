pipeline {
    agent any

    stages {
        stage("初始化"){
            failFast true
            parallel {
                stage('数据库'){
                    stages{
                        stage('DB初始化'){
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/getNewDB.py " + OLD_ENV + ' ' + ENV_NAME
                            }
                        }
                    }
                }
                stage('ccms') {
                    stages{
                        stage('下载产线ccms'){
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ccms_download.py " + ENV_NAME
                            }
                        }
                        stage('ccms配置替换'){
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ccms_replace.py " + ENV_NAME
                            }
                        }
                        stage('上传测试ccms'){
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ccms_upload.py " + ENV_NAME
                            }
                        }
                    }
                }
                stage('tms') {
                    stages{
                        stage('应用初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_app_init.py " + ENV_NAME + " " + TMS_LIST
                            }
                        }
                        stage('配置初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_config_init.py " + ENV_NAME + " " + TMS_LIST
                            }
                        }
                        stage('制作镜像') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_build_image.py " + ENV_NAME + " " + TMS_LIST
                            }
                        }
                    }
                }
                stage('pipeline') {
                    stages{
                        stage('流水线初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ztst_pipeline_init.py " + ENV_NAME + " " + PIPELINE_LIST
                            }
                        }
                    }
                }
                stage('tp') {
                    stages{
                        stage('应用初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tp_app_init.py " + ENV_NAME
                            }
                        }
                        stage('配置初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tp_config_init.py " + ENV_NAME
                            }
                        }
                    }
                }
            }
        }
        stage('设置环境时间') {
            steps {
                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/set_env_time.py " + ENV_NAME + " " + DATE_VALUE + " " + TIME_VALUE
            }
        }
        stage('清理缓存') {
            steps {
               sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/cache_clean.py " + ENV_NAME
            }
        }
        stage('tp应用重启') {
            steps {
                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tp_restart.py " + ENV_NAME
            }
        }
        stage('tms应用重启') {
            steps {
                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_restart.py " + ENV_NAME + " " + TMS_LIST
            }
        }
        stage('等待应用重启完成') {
            steps {
               sh "sleep 300"
            }
        }
        stage('接口测试') {
            steps {
               sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/itest.py " + TEST_SET
            }
        }
    }
    post{
        failure{
            sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/send_result_mail.py " + ENV_NAME + " 失败"
        }
        aborted {
            sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/send_result_mail.py " + ENV_NAME + " 取消"
        }
        success{
            sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/send_result_mail.py " + ENV_NAME + " 成功"
        }
    }
}