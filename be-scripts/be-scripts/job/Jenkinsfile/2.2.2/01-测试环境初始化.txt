pipeline {
    agent any

    stages {
        stage("初始化"){
            failFast true
            parallel {
                stage('DB') {
                    stages{
                        stage('数据库初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ztst_db_init.py " + ENV_NAME
                            }
                        }
                    }
                }
                stage('pa') {
                    stages{
                        stage('pa初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ztst_pa_init.py " + ENV_NAME + " " + PA_LIST
                            }
                        }
                    }
                }
                stage('tp') {
                    stages{
                        stage('tp初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ztst_tp_init.py " + ENV_NAME + " " + TP_LIST
                            }
                        }
                    }
                }
                stage('tms') {
                    stages{
                        stage('应用初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_app_init.py " + ENV_NAME + " " + TMS_LIST
                            }
                        }
                        stage('配置初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_config_init.py " + ENV_NAME + " " + TMS_LIST
                            }
                        }
                        stage('制作镜像') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_build_image.py " + ENV_NAME + " " + TMS_LIST
                            }
                        }
                    }
                }
                stage('pipeline') {
                    stages{
                        stage('流水线初始化') {
                            steps {
                                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/ztst_pipeline_init.py " + ENV_NAME + " " + PIPELINE_LIST
                            }
                        }
                    }
                }
            }
        }
        stage('tms应用重启') {
            steps {
                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/tms_restart.py " + ENV_NAME
            }
        }
    }
    post{
        failure{
            sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/send_result_mail.py " + ENV_NAME + " 失败"
        }
        aborted {
            sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/send_result_mail.py " + ENV_NAME + " 取消"
        }
        success{
            sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/send_result_mail.py " + ENV_NAME + " 成功"
        }
    }
}