pipeline {
    agent any
    stages {
        stage('构建ID') {
            steps {
                echo "buildId = " + BUILD_ID
            }
        }
        stage('流水线') {
            steps {
                sh "python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_env/pipeline_test_publish_for_main.py " + ptp_batch_num + " " + ptp_type + " " + app_name + " " + suite_code + " " + br_name
            }
        }
    }
}