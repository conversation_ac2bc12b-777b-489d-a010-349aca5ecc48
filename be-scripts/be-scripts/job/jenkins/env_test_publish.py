# coding: utf-8
import datetime
import json
import logging
import os
import re
import subprocess
import sys
from functools import wraps
from time import sleep

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from settings import logger, PRODUCT_STORE_URL, KUBECTL_TOMCAT, MIRROR_FACTORY_TOMCAT
from common.ext_cmd.shell_cmd import shell_cmd
from dao.get.mysql.validator import ValidatorMysqlWorker
from dao.get.mysql.env_info import get_whether_app_suite_bind, get_whether_app_use_zeus
from dao.get.mysql.env_info import get_app_type, get_app_container_name, get_suite_code_by_node, get_paths_by_app_node
from utils.release.docker.rebuild_docker import ImageBuild
from utils.release.docker.restart_docker import DockerRestart
from utils.release.virtual.restart_env import restart as virtual_restart
from common.log.record_pipeline_info import record_minor_log
from dao.update.mysql.pipeline_log import update_pipeline_main_log
from dao.insert.mysql.pipeline_log import insert_pipeline_log_main
from dao.get.mysql.branch_info import get_iteration_id
from utils.config.nacos_file_rules import ConfigModifier
from utils.config.tms_config_rule import order_plan_config_replace
from utils.zeus.config_publish import ZeusAPI
from dao.insert.mysql.publish_order import insert_test_publish_order
from dao.get.mysql import iterative_pipeline_branchincludesys

# 获取产线版本应用的目录
PRODUCT_PATH = "/data/env_repo/"
# 暂时先切换到/usr/local下，后续会维护在DB的bind表中 zt@2020-08-19
VIRTUAL_TOMCAT_BASE_PATH = "/usr/local/"
# VIRTUAL_TOMCAT_BASE_PATH = "/data/app/"
VIRTUAL_JAR_BASE_PATH = "/data/app/"
VIRTUAL_TOMCAT_CONFIG_PATH = "/data/app_resource/"
VIRTUAL_JAR_CONFIG_PATH = "/data/app_resource/"
IMG_BASE = '/home/<USER>/img-factory-online/'


# vm_jar_apps = {
#     'mring-itest-service': ('mring-itest-service', 'mring-itest-service'),
#     'mring-performance-service': ('mring-performance-service', 'mring-performance-service'),
#     'zeus-service': ('zeus-service', 'zeus-service'),
# }
# vm_tomcat_apps = {
#     'mring': ('tomcat-mring', 'mring'),
#     'lct-console': ('tomcat-lct-console', 'lct-console'),
#     'lct-online': ('tomcat-lct-online', 'lct-online'),
# }
# vm_apps = dict(vm_jar_apps, **vm_tomcat_apps)
#
# k8s_jar_apps = {
#     'asset-batch-center-remote': ('asset-batch-center-remote', 'asset-batch-center-remote'),
#     'asset-center-remote': ('asset-center-remote', 'asset-center-remote'),
#     'adviser-center-remote': ('adviser-center-remote', 'adviser-center-remote'),
#     'adviser-batch-center-remote': ('adviser-batch-center-remote', 'adviser-batch-center-remote'),
# }
# k8s_tomcat_apps = {}
# k8s_apps = dict(k8s_jar_apps, **k8s_tomcat_apps)

# all_apps = dict(vm_apps, **k8s_apps)


class PublishError(ValueError):
    pass


def step_descorator(name):
    def descorator(func):
        @wraps(func)
        def des(*args, **kwargs):
            try:
                logger.info("Step: {}".format(name))
                func(*args, **kwargs)
                logger.info("{}完成".format(name))
            except Exception as e:
                logger.error("{}失败: {}".format(name, str(e)))
                raise PublishError("{}失败: {}".format(name, str(e)))

        return des

    return descorator


def exec_cmd(cmd):
    logger.info("Exec command: {}".format(cmd))
    code, msg = shell_cmd(cmd)
    if code != 0:
        raise Exception(msg)


def exec_cmd_return_result(cmd):
    logger.info("Exec command: {}".format(cmd))
    code, msg = shell_cmd(cmd)
    if code != 0:
        return ''
    else:
        return msg


class AppHandler(object):
    def __init__(self, app_name, br_name, env_address, is_mock_compile):
        self.app_name = app_name
        self.br_name = br_name
        self.env_address = env_address
        self.is_mock_compile = is_mock_compile
        repo_path = self.get_artifact_repo()
        if not repo_path:
            logger.error('No git code path')
            raise PublishError('No git code path')
        self.git_repo_path = PRODUCT_STORE_URL + '/' + repo_path + '.git'
        self.repo_name = repo_path.split('/')[-1]

    @staticmethod
    def file_content_replace(pattern, replace_obj, file_path):
        if os.path.exists(file_path):
            with open(file_path, 'r') as fl:
                content = fl.read()
                new_content = re.sub(pattern, replace_obj, content)
            with open(file_path, 'w') as fl:
                fl.write(new_content)
        else:
            logging.debug(file_path + '不存在')

    def zeus_config_replace(self, config_dir):
        replace_rules = {
            'spring.cloud.nacos.config.namespace=.*': 'spring.cloud.nacos.config.namespace={}'.format(self.env_address),
            'spring.cloud.nacos.config.group=.*': 'spring.cloud.nacos.config.group={}'.format(self.br_name),
            'nacos.app.namespace=.*': 'nacos.app.namespace={}'.format(self.env_address),
            'nacos.app.version=.*': 'nacos.app.version={}'.format(self.br_name),
        }

        file_paths = os.popen('find {} -type f'.format(config_dir)).read().split()
        for fl in file_paths:
            for rule in replace_rules:
                self.file_content_replace(rule, replace_rules[rule], fl)

    def find_test_config_dir(self):
        cmd = 'find /data/config_store/app_resource/test -not -path "./.git/*" -not -path "./.git" -maxdepth 2'
        conf_path = exec_cmd_return_result(cmd)
        for d in conf_path.split():
            if d.endswith(self.app_name):
                return d
        return ''

    @staticmethod
    def update_config_repo():
        cmd = "cd /data/config_store/app_resource && git checkout ."
        os.system(cmd)
        cmd = "cd /data/config_store/app_resource && git pull"
        os.system(cmd)

    # 拉取最新的应用
    @step_descorator('拉包')
    def app_pull(self):
        os.chdir(PRODUCT_PATH)
        if not os.path.isdir(self.repo_name):
            os.system('mkdir -p {}'.format(self.repo_name))

        os.chdir(self.repo_name)
        if not os.path.isdir(self.br_name):
            os.system('git clone -b {} --depth 1 {} {}'.format(self.br_name, self.git_repo_path, self.br_name))
        else:
            os.chdir(self.br_name)
            os.system('git pull origin {}'.format(self.br_name))

    def get_artifact_repo(self):
        with ValidatorMysqlWorker() as mysql_worker:
            return mysql_worker.get_artifact_repo_path(self.app_name)

    # 配置文件处理
    @step_descorator('配置更新')
    def config_update(self):
        if not get_whether_app_use_zeus(self.app_name):
            logger.info('{}未接入宙斯配置'.format(self.app_name))
            return
        self.update_config_repo()

        # order-plan-center-remote中关于宙斯的外移配置替换 zt@2020-08-04
        if 'order-plan-center-remote' == self.app_name:
            order_plan_config_replace(self.env_address)

        # zeus配置文件替换
        config_dir = self.find_test_config_dir()
        # 根据要发布的节点，反向找到「环境套」 zt@2020-08-21
        suite_code = self.env_address
        if not self.env_address.startswith('tms'):
            suite_code = get_suite_code_by_node(self.app_name, self.env_address)

        logger.info(">>>>配置更新：应用『{}』，分支『{}』，环境套『{}』，节点『{}』".format(self.app_name,
                                                                   self.br_name,
                                                                   suite_code,
                                                                   self.env_address))

        if config_dir:
            # self.zeus_config_replace(config_dir)
            if self.env_address.startswith('tms'):
                cm = ConfigModifier([self.app_name], version=self.br_name, env=suite_code)
            else:
                cm = ConfigModifier([self.app_name], version=self.br_name, env=suite_code)
            cm.alter_config_file()

        # zeus接口同步、发布
        if self.env_address.startswith('tms'):
            zeus_work = ZeusAPI(self.app_name, self.br_name, suite_code)
        else:
            zeus_work = ZeusAPI(self.app_name, self.br_name, suite_code)
        logger.info('调用宙斯同步接口:')
        sync_log = zeus_work.env_sync()
        logger.info(sync_log)
        logger.info('调用宙斯发布接口:')
        publish_lob = zeus_work.release()
        logger.info(publish_lob)


def test_get_app_node_paths():
    app_name = 'ftx-online-search-web'
    env_address = '************'
    default_deploy_path = VIRTUAL_JAR_BASE_PATH + app_name + '/lib/'
    app_node_paths = get_paths_by_app_node(app_name, env_address, default_deploy_path, '')
    print(app_node_paths.tomcat_path)


class VirtualMachineHandler(AppHandler):
    # 拷贝应用到测试服务器
    @step_descorator('推送')
    def scp(self):
        if get_app_type(self.app_name) == 'jar':
            default_deploy_path = VIRTUAL_JAR_BASE_PATH + self.app_name + '/lib/'
            app_node_paths = get_paths_by_app_node(self.app_name, self.env_address, default_deploy_path, '')
            cmd = "rsync --delete --timeout=120 -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
                os.path.join(PRODUCT_PATH, self.repo_name, self.br_name), self.env_address,
                app_node_paths.deploy_path)
            logger.info(cmd)
        else:
            # cmd = "rsync --timeout=120 -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
            #     os.path.join(PRODUCT_PATH, self.repo_name, self.br_name), self.env_address,
            #     VIRTUAL_TOMCAT_BASE_PATH + get_app_container_name(self.app_name) + '/' + self.app_name + '/')
            default_deploy_path = '{}{}/webapps/{}/'.format(VIRTUAL_TOMCAT_BASE_PATH,
                                                            get_app_container_name(self.app_name),
                                                            self.app_name)
            app_node_paths = get_paths_by_app_node(self.app_name, self.env_address, default_deploy_path, '')
            cmd = "rsync --delete --timeout=120 -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
                os.path.join(PRODUCT_PATH, self.repo_name, self.br_name), self.env_address, app_node_paths.deploy_path)
            logger.info(cmd)

        logger.info('虚机推送的路径是:{}'.format(app_node_paths.deploy_path))
        exec_cmd(cmd)

    @step_descorator('配置拷贝')
    def handle_config(self):
        # 配置拷贝
        test_dir = self.find_test_config_dir()
        if test_dir and os.path.isdir(test_dir):
            if get_app_type(self.app_name) == 'jar':
                # cmd = "rsync --timeout=120 -LPa {}/conf/* tomcat@{}:{}/conf/".format(
                #     test_dir, self.env_address, VIRTUAL_JAR_CONFIG_PATH + self.app_name)
                cmd = "rsync --timeout=120 -LPa {}/conf/* tomcat@{}:{}{}/conf/".format(
                    test_dir,
                    self.env_address,
                    VIRTUAL_JAR_BASE_PATH,
                    self.app_name)
            else:
                cmd = "rsync --timeout=120 -LPa {}/* tomcat@{}:{}/".format(
                    test_dir, self.env_address,
                    VIRTUAL_TOMCAT_CONFIG_PATH + get_app_container_name(self.app_name) + '/' + self.app_name)
            exec_cmd(cmd)

    # 测试环境中重启应用
    @step_descorator('重启')
    def restart(self):
        logger.info('restart ...')
        if get_app_type(self.app_name) == 'jar':
            virtual_restart(self.env_address, self.app_name, 'jar')
        else:
            virtual_restart(self.env_address, get_app_container_name(self.app_name), 'tomcat')

    def run(self):
        if self.is_mock_compile:
            self.config_update()
            self.scp()
            self.handle_config()
            self.restart()
        else:
            self.app_pull()
            self.config_update()
            self.scp()
            self.handle_config()
            self.restart()


class DockerHandler(AppHandler, ImageBuild, DockerRestart):
    # 拷贝应用到容器服务器
    @step_descorator('推送')
    def scp(self):
        logger.info('scp {}'.format(self.app_name))
        if get_app_type(self.app_name) == 'jar':
            cmd = """ssh {} "rm -rf {}/{}/{}/lib/*" """.format(
                MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)

            cmd = """scp -r {}/* {}:{}/{}/{}/lib/""".format(
                os.path.join(PRODUCT_PATH, self.repo_name, self.br_name),
                MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)
        else:
            cmd = """ssh {} "rm -rf {}/{}/{}/WEB-INF/lib/*" """.format(
                MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)

            cmd = """scp -r {}/WEB-INF/lib/* {}:{}/{}/{}/WEB-INF/lib/""".format(
                os.path.join(PRODUCT_PATH, self.repo_name, self.br_name),
                MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)

    @step_descorator('配置拷贝')
    def handle_config(self):
        # 配置拷贝
        test_dir = self.find_test_config_dir()
        if test_dir and os.path.isdir(test_dir):
            if get_app_type(self.app_name) == 'jar':
                cmd = "scp -r {}/conf/* {}:{}/conf/".format(
                    test_dir, MIRROR_FACTORY_TOMCAT,
                    os.path.join(IMG_BASE, self.env_address, self.app_name, self.app_name))
            else:
                cmd = "scp -r {}/* {}:{}/".format(
                    test_dir, MIRROR_FACTORY_TOMCAT,
                    os.path.join(IMG_BASE, self.env_address, get_app_container_name(self.app_name), self.app_name))
            exec_cmd(cmd)

    @step_descorator('初始化镜像库')
    def init_img_dir(self):
        cmd = """
              ssh {} \
              'rm -rf {}/{} && \
               cp -a {}/tms_basic/{} {}/'
        """.format(MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, IMG_BASE, self.app_name,
                   IMG_BASE + self.env_address)
        exec_cmd(cmd)

        cmd = """
              ssh {} \
              'cp -a /home/<USER>/tmp/tms/dp-{}.yaml /home/<USER>/script/deployment/{} && \
               sed -i "s/xxx/{}/g" /home/<USER>/script/deployment/{}/dp-{}.yaml'
        """.format(KUBECTL_TOMCAT, self.app_name, self.env_address, self.env_address, self.env_address, self.app_name)
        exec_cmd(cmd)

    @step_descorator('镜像制作')
    def make_img(self):
        self.make_image(self.env_address, self.app_name)

    @step_descorator('重启')
    def restart(self):
        # self.restart_pod(self.env_address, self.app_name)
        cmd = """ python3.x /home/<USER>/hb-k8s/k8s_build_script/k8s_app_deploy/k8s_kustomsize_build.py {} {}
        """.format(self.env_address, self.app_name)
        logger.info("Exec: " + cmd)
        p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
        code = p.returncode
        if code != 0:
            raise Exception(stderr)
        else:
            logger.info(stdout)

        sleep(10)

        cmd = """ python3.x /home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_pod_restart.py {} {}
        """.format(self.env_address, self.app_name)
        logger.info("Exec: " + cmd)
        p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
        code = p.returncode
        if code != 0:
            raise Exception(stderr)
        else:
            logger.info(stdout)

    def run(self):
        self.app_pull()
        self.config_update()
        # self.init_img_dir()
        self.scp()
        self.handle_config()
        self.make_img()
        self.restart()


@record_minor_log(step="发布测试环境")
def publish_env(app_name, br_name, env_name, sid, is_mock_compile=False):
    try:
        update_pipeline_main_log(sid, 'running', env_suite=env_name, end_at=datetime.datetime.now())
        if re.match(r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$", env_name):
            worker = VirtualMachineHandler(app_name, br_name, env_name, is_mock_compile)
        else:
            worker = DockerHandler(app_name, br_name, env_name, is_mock_compile)
        worker.run()
        update_pipeline_main_log(sid, 'success', env_suite=env_name, end_at=datetime.datetime.now())
        try:
            # 发布表插入记录
            logger.info("app_name:{},br_name:{}".format(app_name, br_name))
            pipeline_id = get_iteration_id(app_name, br_name)
            logger.info("pipeline_id:{}".format(pipeline_id))
            iter_info = iterative_pipeline_branchincludesys.get(pipeline_id, app_name)
            git_last_version = iter_info[0]['git_last_version']
            git_repo_version = iter_info[0]['git_repo_version']
            insert_test_publish_order(pipeline_id.split('_')[0], app_name, br_name, git_last_version, git_repo_version)
        except Exception as err:
            logger.error(str(err))
        return 'success', "{}环境发布完成".format(env_name)
    except Exception as err:
        logger.error(str(err))
        return 'failure', str(err)


if __name__ == '__main__':

    logger.info("调用开始")
    #  python3.x env_test_publish.py mring-performance-service 1.0.2 tms02
    app_name = sys.argv[1]
    br_name = sys.argv[2]
    env_name = sys.argv[3]

    if len(sys.argv) > 4:
        flag_file_dir = sys.argv[4]
        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
        sid = json_dict["sid"]
        is_mock_compile = json_dict["is_mock_compile"]
        if is_mock_compile:
            PRODUCT_PATH = "/data/mock_repo/"
    else:
        try:
            iteration_id = get_iteration_id(app_name, br_name)
            sid = insert_pipeline_log_main(exec_jenkins="platform", exec_parameter="",
                                           start_at=datetime.datetime.now(), iteration_id=iteration_id,
                                           app_name=app_name, status="running", suite_name=env_name)
            is_mock_compile = False
        except Exception as e:
            sid = None
            is_mock_compile = False
            logger.error(str(e))
    if get_whether_app_suite_bind(app_name, env_name):
        worker = publish_env(app_name, br_name, env_name, sid, is_mock_compile)
    else:
        logger.error("{} 未绑定".format(app_name))
    logger.info("调用结束")
