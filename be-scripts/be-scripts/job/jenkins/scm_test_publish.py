import datetime
import logging
import os
import re
import sys
import time
import paramiko

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import (exec_local_cmd)
from settings import SCM_TEST_NODE_INFO
from common.common_tool.common_tool import exec_local_cmd

class SSHConnectionManager:

    def __init__(self, ip, username, password):
        """
        :param ip:
        :param username:
        :param password:
        """
        self.ip = ip
        self.username = username
        self.password = password
        paramiko.util.log_to_file("upload.log")  # 记录日志文件

    def __enter__(self):
        logging.info("建立到{}的SSH连接".format(self.ip))
        ssh = paramiko.SSHClient()  # 创建ssh对象
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.SSH = ssh
        self.SSH.connect(self.ip, port=22, username=self.username,
                         password=self.password, compress=True)
        logging.info("{}连接成功".format(self.ip))

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.info("断开到{}的SSH连接".format(self.ip))
        self.SSH.close()
        logging.info("{}断开连接成功".format(self.ip))


class Prod_Publish:
    def __init__(self, br_name, env, apply, sftp):
        self.br_name = br_name
        self.env = env
        self.apply = apply
        self.sftp = sftp

    def login(self, hostname, up_list):
        paramiko.util.log_to_file("upload.log")  # 记录日志文件
        ssh = paramiko.SSHClient()  # 创建ssh对象
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # 允许连接不在know_hosts文件中的主机
        ssh.connect(hostname=hostname, port=int(up_list['port']), username=up_list['username'],
                    password=up_list['password'])
        return ssh

    @staticmethod
    def sshclient_execmd(execmd, ssh):
        """命令执行"""
        stdin, stdout, stderr = ssh.exec_command(execmd)
        resulto = stdout.readlines()
        resulte = stderr.readlines()
        stat = stdout.channel.recv_exit_status()
        logging.info('execmd : %s' % execmd)
        logging.info(resulto)
        if stat != 0:
            pass
            logging.error(resulte)
        else:
            logging.info('Exe %s successful' % execmd)
        return resulto, resulte, stat

    def scp(self, ssh, origin, target_ip, target_dir):
        execscpcmd = " scp -r {} tomcat@{}:{}".format(origin, target_ip, target_dir)
        resulto, resulte, stat = self.sshclient_execmd(execscpcmd, ssh)

    @staticmethod
    def try_except(message, stat):
        if stat != 0:
            raise Exception("{}更新失败".format(message))
            exit(1)
        else:
            logging.info("{}更新正常".format(message))


class Scm_publish:

    def __init__(self, app, br_name, env, version=None):
        self.app = app
        self.br_name = br_name
        self.env = env
        self.version = version if version else ''
        # if app !='all':
        self.ip = SCM_TEST_NODE_INFO[app].get('ip') if SCM_TEST_NODE_INFO[app].get('ip') else ''
        print(self.ip)
        self.username = SCM_TEST_NODE_INFO[app].get('username') if SCM_TEST_NODE_INFO[app].get('username') else ''
        self.password = SCM_TEST_NODE_INFO[app].get('password') if SCM_TEST_NODE_INFO[app].get('password') else ''
        self.path = SCM_TEST_NODE_INFO[app].get('paths') if SCM_TEST_NODE_INFO[app].get('paths') else ''

    def scm_backup(self, ssh):
        now_date = datetime.datetime.now().strftime('%Y%m%d')
        backup_dir = "/data/scm_backup/{}/{}".format(self.app, now_date)
        resulto, resulte, stat = Prod_Publish.sshclient_execmd("[ -d {} ] && echo yes || echo no".format(backup_dir),
                                                               ssh)
        if str(resulto) == "['no\\n']":
            resulto, resulte, stat = Prod_Publish.sshclient_execmd("mkdir -p {}".format(backup_dir), ssh)
            if stat == 0:
                Prod_Publish.sshclient_execmd("cp -r {} {}".format(self.path, backup_dir), ssh)
        else:
            logging.info("备份目录{}已存在，当天无需重复备份".format(backup_dir))

    def deploy_be_script(self):
        '''
        be-script发布
        '''
        logging.info('当前节点{}'.format(self.ip))

        if isinstance(self.ip, list):
            ip_list = self.ip
        else:
            ip_list = [self.ip]
        for ip in ip_list:
            with SSHConnectionManager(ip, self.username, self.password) as ssh:
                # self.scm_backup(ssh.SSH)
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                    "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
                Prod_Publish.try_except('be-script', stat)
                resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
                    "python3.x /home/<USER>/be-scripts/generate_config.py /home/<USER>/be-scripts/be-scripts/ {} {}".format(
                        self.br_name, self.env), ssh.SSH)
                Prod_Publish.try_except('be-script宙斯配置', stat)

    def deploy_qa_scripts(self):
        '''
        qa_script发布
        '''
        for ip in self.ip:
            logging.info('当前节点{}'.format(ip))
            with SSHConnectionManager(ip, self.username, self.password) as ssh:
                self.scm_backup(ssh.SSH)
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                    "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
                Prod_Publish.try_except('qa-scripts', stat)
                resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
                    "python3.x /home/<USER>/qa-scripts/generate_config.py /home/<USER>/qa-scripts/ {} {}".format(
                        self.br_name, self.env), ssh.SSH)
                Prod_Publish.try_except('qa-scripts宙斯配置', stat)

    def deploy_spider(self):
        '''
        spider发布
        '''
        logging.info('当前节点{}'.format(self.ip))
        with SSHConnectionManager(self.ip, self.username, self.password) as ssh:
            # self.scm_backup(ssh.SSH)
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
            Prod_Publish.try_except('spider', stat)
            # 尝试使用「uv」测试发布。zt@2025-04-29
            # resultoc, resultec, statc = Prod_Publish.sshclient_execmd(
            #     "cp -r /home/<USER>/spider-config/generate_config.py /home/<USER>/spider/", ssh.SSH)
            # Prod_Publish.try_except('copy--generate_config.py', statc)
            # resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
            #     "python3.x /home/<USER>/spider/generate_config.py /home/<USER>/spider/spider/ {} {}".format(
            #         self.br_name, self.env), ssh.SSH)
            generate_config_cmd = "cd {} && uv run generate_config.py {}/spider/ {} {}".format(
                self.path, self.path, self.br_name, self.env)
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(generate_config_cmd, ssh.SSH)
            Prod_Publish.try_except('spider宙斯配置', stat)

            # resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
            #     "cd {} && python3.x start.py".format(
            #         self.path), ssh.SSH)
            test_publish_cmd = "cd {} && python3.x start-uv.py".format(self.path)
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(test_publish_cmd, ssh.SSH)
            Prod_Publish.try_except('spider-uv发布', stat)

    def deploy_mantis(self):
        '''
        mantis发布
        '''
        logging.info('当前节点{}'.format(self.ip))
        with SSHConnectionManager(self.ip, self.username, self.password) as ssh:
            # self.scm_backup(ssh.SSH)
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
            Prod_Publish.try_except('mantis', stat)
            # 尝试使用「uv」测试发布。zt@2025-04-29
            # resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
            #     "python3.x /home/<USER>/mantis/generate_config.py /home/<USER>/mantis/mantis/ {} {}".format(
            #         self.br_name, self.env), ssh.SSH)
            generate_config_cmd = "cd {} && uv run generate_config.py {}/mantis/ {} {}".format(
                self.path, self.path, self.br_name, self.env)
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(generate_config_cmd, ssh.SSH)
            Prod_Publish.try_except('mantis宙斯配置', stat)

            # resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
            #     "cd {} && python3.x start.py".format(
            #         self.path), ssh.SSH)
            test_publish_cmd = "cd {} && python3.x start-uv.py".format(self.path)
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(test_publish_cmd, ssh.SSH)
            Prod_Publish.try_except('mantis-uv发布', stat)

    def deploy_website(self):
        '''
        老后端发布
        '''
        logging.info('当前节点{}'.format(self.ip))
        with SSHConnectionManager(self.ip, self.username, self.password) as ssh:
            # self.scm_backup(ssh.SSH)
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
            Prod_Publish.try_except('老后端', stat)
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd("sh {}/start.sh {}".format(
                self.path, 'sitrestart'), ssh.SSH)
            Prod_Publish.try_except('老后端重启', stat)

    def deploy_website_web(self):
        '''
        spider前端发布
        '''
        logging.info('当前节点{}'.format(self.ip))
        with SSHConnectionManager(self.ip, self.username, self.password) as ssh:
            # self.scm_backup(ssh.SSH)
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
            Prod_Publish.try_except('spider前端', stat)
            logging.info('========================开始打包========================')
            start_time = time.time()
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
                "cd " + self.path + "&& nvm use v12.22.12 && npm run build:{}".format(self.env), ssh.SSH)
            end_time = time.time()
            logging.warning("编译时长：{}".format(end_time - start_time))
            Prod_Publish.try_except('spider前端打包', stat)
            logging.info("cd " + self.path + ' && \scp -rf deploy-vue/* tomcat@**************:/data/app/deploy-vue/')
            resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
                "cd " + self.path + ' && scp -r deploy-vue/* root@**************:/data/app/deploy-vue/', ssh.SSH)
            Prod_Publish.try_except('拷贝前端包', stat)

    def deploy_hm_script(self):
        '''
        hm_script发布
        '''
        for ip in self.ip:
            logging.info('当前节点{}'.format(ip))
            with SSHConnectionManager(ip, self.username, self.password) as ssh:
                # self.scm_backup(ssh.SSH)
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(
                    "cd " + self.path + "&& git fetch && git checkout -f {} && git pull".format(self.br_name), ssh.SSH)
                Prod_Publish.try_except('hm_script', stat)
                resulto1, resulte1, stat = Prod_Publish.sshclient_execmd(
                    "python3.x {}/get_zeus_config.py {} {}".format(self.path, self.br_name, self.env), ssh.SSH)
                Prod_Publish.try_except('hm_script', stat)

    def deploy_spider_common_utils(self):
        '''
        spider_common_utils发布
        '''
        if not self.version:
            cmd = 'pip install spider-common-utils --upgrade'
            os.system(cmd)

            cmd = 'pip list | grep spider-common-utils'
            obj = exec_local_cmd(cmd)
            result = bytes.decode(obj.stdout)

            version_num_list = result.split(" ")[1].split('.')
            version_num_list[2] = str(int(version_num_list[2]) + 1)
            version = ".".join(version_num_list)

            logging.info("version==={}".format(version))
        else:
            version = self.version

        logging.info("version ==== {}".format(version))

        if os.path.exists(self.path):
            cmd = 'rm -rf {}'.format(self.path)
            os.system(cmd)

        cmd = 'git clone -<NAME_EMAIL>:scm/spider-common-utils.git {}'.format(self.path)
        os.system(cmd)

        os.chdir(self.path)
        with open("setup.py", "r+", encoding="utf-8") as f:
            file_content = f.read()
            file_content = re.sub("(version=).*", '\g<1>"{}",'.format(version), file_content)

            f.seek(0, 0)
            f.truncate()
            f.write(file_content)

        cmd = 'python3.x setup.py sdist bdist_wheel'
        logging.info(cmd)
        os.system(cmd)
        cmd = '/usr/local/python3.9/bin/twine upload -r nexus dist/*'
        logging.info(cmd)
        os.system(cmd)


class Main_deploy:

    def single_deploy(self, app):
        self.__factory_task(app)

    @staticmethod
    def __factory_task(app):
        logging.info('========================开始发布{}========================'.format(app))
        scm = Scm_publish(app, br_name, env, version)
        task_dict = {'be-script': scm.deploy_be_script, 'spider': scm.deploy_spider,
                     'mantis': scm.deploy_mantis,
                     'hm_script': scm.deploy_hm_script,
                     'old_django': scm.deploy_website, 'website_web': scm.deploy_website_web,
                     'qa-scripts': scm.deploy_qa_scripts, 'spider-common-utils': scm.deploy_spider_common_utils}
        return task_dict[app]()

    def deploy_all(self):
        '''
        所有应用发布
        '''
        for App in SCM_TEST_NODE_INFO:
            self.__factory_task(App)


if __name__ == '__main__':
    app = sys.argv[1]
    br_name = sys.argv[2]
    env = sys.argv[3]
    version = sys.argv[4]
    if version == '0':
        version = ''
    # app = 'spider-common-utils'
    # br_name = '2.20.1'
    # env = 'test'
    if app == 'all':
        main_deploy = Main_deploy()
        main_deploy.deploy_all()
    else:
        main_deploy = Main_deploy()
        main_deploy.single_deploy(app)