#
import pymysql


def mysql_conn():
    """链接 mysql"""
    host = '**************'
    port = 3306
    user = 'scm'
    password = 'howbuyscm'
    db = 'django_scm'
    charset = 'utf8'

    connection = pymysql.connect(host=host, port=port, user=user, passwd=password, db=db, charset=charset)
    return connection


def mysql_close(connection):
    connection.close()


def get_zb_apps(cur):
    """获取灾备应用列表"""
    cur.execute("select * from emergency_service_zaibeiavailableinfo where stat=1")
    zb_app_list = []
    for item in cur.fetchall():
        zb_app_list.append(item['appName'])
    return zb_app_list


def get_node_git_version(cur, app_name, ip):
    """根据应用名和ip获取发布的制品版本"""
    cur.execute(
        """select gitVersion from data_statistics_nodeconfigversion
           where appName='{}' and IP='{}'
           order by id desc limit 1
        """.format(app_name, ip))
    return cur.fetchone()


def get_app_nodes(cur, app_name):
    """获取应用的产线和灾备节点"""
    cur.execute(
        """select ip, environ from common_service_cmdbdata
           where `app_name`='{}' and `group` <> '默认分组' and `environ` in ('prod', 'bsprod')
        """.format(app_name))

    return cur.fetchall()


def compare_app_version(data):
    tmp_version = ''
    for item in data:
        if item['git_version'] and not tmp_version:
            tmp_version = item['git_version']
        elif item['git_version'] and item['git_version'] != tmp_version:
            return 1
    if not tmp_version:
        return 2
    return 0


def show_result(res, data):
    print('===========================')
    print('完全一致的应用：')
    print(res[0])
    print('===========================')
    print('不一致的应用：')
    print(res[1])
    print('===========================')
    print('未使用平台发布的应用：')
    print(res[2])
    print('===========================')


if __name__ == "__main__":
    my_conn = mysql_conn()
    my_cur = my_conn.cursor(cursor=pymysql.cursors.DictCursor)

    # 获取所有灾备应用
    zb_apps = get_zb_apps(my_cur)

    # 获取应用中的节点及其版本信息
    all_apps_info = dict()
    for app in zb_apps:
        app_info = []

        nodes = get_app_nodes(my_cur, app)
        for node in nodes:
            git_version = get_node_git_version(my_cur, app, node['ip'])
            node['git_version'] = git_version['gitVersion'] if git_version else ''
            app_info.append(node)
        all_apps_info[app] = app_info
    mysql_close(my_conn)

    # 比较版本信息
    result = {
        0: [],  # 完全一致
        1: [],  # 不一致
        2: [],  # 未使用平台发布
    }
    for app in all_apps_info:
        result[compare_app_version(all_apps_info[app])].append(app)

    # 打印节点版本比较结果
    show_result(result, all_apps_info)
