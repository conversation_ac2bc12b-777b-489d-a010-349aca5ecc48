import os
import sys
import requests
from datetime import datetime, timedelta

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import GITLAB_HTTP, GITLAB_TOKEN, INTERFACE_URL, logger


class UserCommit:
    def fetch_code(self, days, host):
        user_list = self._get_user_list()
        target_date = datetime.now() - timedelta(days=int(days))
        from_date_str = target_date.strftime("%Y-%m-%d")
        url = "http://{}/mantis/code_fetcher/git_commits_view/fetch_code_by_user/".format(host)
        #user_list.reverse()
        for user_id in user_list:
            data = {"user_id": user_id, "from_date_str": from_date_str}
            response = requests.get(url=url, data=data)
            try:
                logger.info(response.json())
            except Exception as err:
                logger.error("{}获取数据{}，出现问题{}".format(response.text, data, err))

    def fetch_code_ft(self, from_date_str, to_date_str, host):
        user_list = self._get_user_list()
        url = "http://{}/mantis/code_fetcher/git_commits_view/fetch_code_by_user_ft/".format(host)
        for user_id in user_list:
            data = {"user_id": user_id, "from_date_str": from_date_str, "to_date_str": to_date_str}
            response = requests.get(url=url, data=data)
            try:
                logger.info(response.json())
            except Exception as err:
                logger.error("{}获取数据{}，出现问题{}".format(response.text, data, err))

    def _get_user_list(self):
        url = '%s/api/v4/users?per_page=1&page=1' % GitHttpRequest.host()
        response = GitHttpRequest.get(url)
        users = response.json()
        first_user_id = users[0]['id']

        user_list = []
        i = 0
        while i < first_user_id:
            i += 1
            user_list.append(i)

        return user_list

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))

        acceptor = {
            "fetch_code": self.fetch_code,
            "fetch_code_ft": self.fetch_code_ft
        }

        if len(params) == 3:
            acceptor[params[0]](params[1], params[2])
        else:
            acceptor[params[0]](params[1], params[2], params[3])


class GitHttpRequest:
    @staticmethod
    def host():
        return GITLAB_HTTP

    @staticmethod
    def get(url):
        git_lab_token = GITLAB_TOKEN
        headers = {'PRIVATE-TOKEN': git_lab_token}
        return requests.get(url, headers=headers)


if __name__ == '__main__':
    uc = UserCommit()
    uc.call(sys.argv[1:])

"""
pipeline {
    agent any
    
    stages {
        stage('获取代码') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/gitlab_code/user_commit.py 'fetch_code' '30' '*********:8031' '''
            }
        }
        
        stage('剔除非首次提') {
            steps {
                script {
                    def response = httpRequest(url: 'http://mantis.howbuy.pa/mantis/code_fetcher/git_commits_view/remove_not_first_commit/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo '成功'
                    } else {
                        error '失败'
                    }
                }
            }
        }
        
        stage('中文人名') {
            steps {
                script {
                    def response = httpRequest(url: 'http://mantis.howbuy.pa/mantis/code_fetcher/git_commits_view/change_to_cn_name/', acceptType: 'APPLICATION_JSON')
                    def json = readJSON(text: response.content)
                    if (json.result) {
                        echo '成功'
                    } else {
                        error '失败'
                    }
                }
            }
        }
    }
}
"""
