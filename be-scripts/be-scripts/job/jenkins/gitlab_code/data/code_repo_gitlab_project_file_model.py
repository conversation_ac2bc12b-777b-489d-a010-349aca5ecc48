from sqlalchemy import Column, Integer, String, UniqueConstraint
from dao.sqlarchemy.database_pool import Base


class GitlabProjectFileModel(Base, object):
    __tablename__ = 'code_repo_gitlab_project_files'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, nullable=False)
    path = Column(String(250), nullable=False)
    name = Column(String(100))
    reversed_name = Column(String(100))
    path_type = Column(String(20))
    branch = Column(String(200))

    UniqueConstraint('project_id', 'path', 'branch')


class GitlabFileRawContentModel(Base, object):
    __tablename__ = 'code_repo_gitlab_project_files'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, nullable=False)
    path = Column(String(250), nullable=False)
    branch = Column(String(200))
    path_type = Column(String(20))
    raw_content = Column(String())
    name = Column(String(100))

    UniqueConstraint('project_id', 'path', 'branch')