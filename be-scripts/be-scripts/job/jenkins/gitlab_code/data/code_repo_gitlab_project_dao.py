from job.jenkins.gitlab_code.data.code_repo_gitlab_project_model import GitlabProjectModel
from dao.sqlarchemy.database_pool import instance_db_session
from sqlalchemy.sql import text


class GitlabProjectDao:
    __query_all_project = '''select * from code_repo_gitlab_projects'''

    __query_exist_project = '''
        SELECT
            *
        FROM
            code_repo_gitlab_projects
        WHERE path_with_namespace in 
        (
        SELECT
            distinct concat( ifnull(ai.git_url, ''), ifnull(ai.git_path, '') )
        FROM
            app_mgt_app_info ai
        where ai.id in 
        (
        SELECT
            distinct app_id
        FROM
            app_mgt_app_module am
        where am.module_status = '1' and need_check = '1' and is_agent = '0'
        ) 
        and ai.git_url is not null and ai.git_url != ''
        and ai.app_status = '1' and ai.third_party_middleware != 1
        )
    '''

    def query_all_project(self):
        db_session = instance_db_session()
        result = db_session.query(GitlabProjectModel).from_statement(
            text(self.__query_all_project)).all()
        db_session.close()
        return result

    def query_exist_project(self):
        db_session = instance_db_session()
        result = db_session.query(GitlabProjectModel).from_statement(
            text(self.__query_exist_project)).all()
        db_session.close()
        return result

    def insert_project_list(self, list):
        db_session = instance_db_session()
        try:
            for i in range(len(list)):
                db_session.add(list[i])

            db_session.commit()
        except Exception as e:
            db_session.close()
            raise e

        db_session.close()


if __name__ == '__main__':
    dao = GitlabProjectDao()
    model = GitlabProjectModel()
    model.project_id = 1
    model.default_branch = "master"
    model.visibility = "public"
    model.path_with_namespace = ""
    model1 = GitlabProjectModel()
    model1.project_id = 2
    model1.default_branch = "master"
    model1.visibility = "public"
    model1.path_with_namespace = ""
    list = [model, model1]
    test = dao.insert_project_list(list)
    a = test
