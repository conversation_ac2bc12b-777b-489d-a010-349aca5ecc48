from sqlalchemy import delete

from job.jenkins.gitlab_code.data.code_repo_gitlab_file_blame_model import GitlabFileBlameModel, GitlabLineDetailModel
from dao.sqlarchemy.database_pool import instance_db_session
from sqlalchemy.sql import text


class GitlabFileBlameDao:
    __query_exist_project = '''
        SELECT
            *
        FROM
            code_repo_gitlab_file_blames
    '''

    __delete_files_and_details = '''
        delete from code_repo_gitlab_file_blames where project_id = {}
    '''

    __query_lines_of_implement_of_api = '''
        select fb.id, fb.`lines`, fb.project_id, fb.path, fb.sequence
        from
            code_repo_gitlab_file_blames fb
        inner join
        (
        -- ==========找出实现方法所在行==============================
        select fb.project_id, fb.path, fb.sequence
        from
            code_repo_gitlab_file_blames fb
        inner join
        (
        select fb.project_id, fb.path
        from
            code_repo_gitlab_file_blames fb
        inner join
            code_repo_gitlab_file_line_detail ld
        on ld.fraction = '{class_name}'
        and fb.project_id = ld.project_id and fb.path = ld.path
        where fb.`lines` like '%class%{class_name}%'
        ) correct
        on fb.project_id = correct.project_id and fb.path = correct.path
        and fb.`lines` like '%public%{method_name}(%'
        -- ==============================找出实现方法所在行==========
        ) met
        on met.project_id = fb.project_id and met.path = fb.path and fb.sequence >= met.sequence
        where fb.effective = 'Y'
        order by sequence
        limit 200
    '''

    def query_exist_file_blame(self):
        db_session = instance_db_session()
        result = db_session.query(GitlabFileBlameModel).from_statement(
            text(self.__query_exist_project)).all()
        db_session.close()
        return result

    def query_lines_of_implement_of_api(self, class_name, method_name):
        db_session = instance_db_session()
        result = db_session.query(GitlabFileBlameModel).from_statement(
            text(self.__query_lines_of_implement_of_api.format(class_name=class_name, method_name=method_name))).all()
        db_session.close()
        return result

    def delete_files_and_details(self, project_id):
        db_session = instance_db_session()
        file_blame_table = GitlabFileBlameModel.__table__
        line_detail_table = GitlabLineDetailModel.__table__
        condition_file_blame = file_blame_table.c.project_id == project_id
        condition_line_detail = line_detail_table.c.project_id == project_id

        stmt = delete(file_blame_table).where(condition_file_blame)
        db_session.execute(stmt)
        stmt = delete(line_detail_table).where(condition_line_detail)
        db_session.execute(stmt)

        db_session.commit()

        db_session.close()

    def insert_file_blame_list(self, blame_list, line_detail_list):
        db_session = instance_db_session()
        try:
            for b in blame_list:
                db_session.add(b)

            for ld in line_detail_list:
                db_session.add(ld)

            db_session.commit()
        except Exception as e:
            db_session.close()
            raise e

        db_session.close()

    def insert_file_blame_with_opening_session(self, blame_line, line_detail_list, db_session):
        try:
            db_session.add(blame_line)

            for ld in line_detail_list:
                db_session.add(ld)

            db_session.commit()
        except Exception as e:
            print(e)

    def insert_file_blame(self, blame_line, line_detail_list):
        db_session = instance_db_session()
        try:
            db_session.add(blame_line)

            for ld in line_detail_list:
                db_session.add(ld)

            db_session.commit()
        except Exception as e:
            db_session.close()
            raise e

        db_session.close()

if __name__ == '__main__':
    dao = GitlabFileBlameDao()
    #a = dao.query_exist_file_blame()
    #b = a
    dao.delete_files_and_details("1092")
