from job.jenkins.gitlab_code.data.code_repo_gitlab_project_file_model import GitlabProjectFileModel, \
    GitlabFileRawContentModel
from dao.sqlarchemy.database_pool import instance_db_session
from sqlalchemy.sql import text


class GitlabProjectFileDao:
    __query_exist_project_file = '''
        SELECT
            *
        FROM
            code_repo_gitlab_project_files
        where name not like '.%'
    '''

    def query_exist_project_file(self):
        db_session = instance_db_session()
        result = db_session.query(GitlabProjectFileModel).from_statement(
            text(self.__query_exist_project_file)).all()
        db_session.close()
        return result

    def delete_project_files(self, project_id):
        db_session = instance_db_session()
        db_session.query(GitlabProjectFileModel).filter(GitlabProjectFileModel.project_id == project_id).delete()
        db_session.commit()
        db_session.close()

    def insert_project_file_list(self, list):
        db_session = instance_db_session()
        try:
            for i in range(len(list)):
                db_session.add(list[i])

            db_session.commit()
        except Exception as e:
            db_session.close()
            raise e

        db_session.close()


class GitlabFileRawContentDao:

    def query_file_list(self, project_id):
        db_session = instance_db_session()
        res = db_session.query(GitlabFileRawContentModel).filter(GitlabFileRawContentModel.project_id == project_id,
                                                                 GitlabFileRawContentModel.path_type == "blob").all()
        db_session.close()
        return res

    def query_file_with_content_list(self, project_id):
        db_session = instance_db_session()
        res = db_session.query(GitlabFileRawContentModel).filter(GitlabFileRawContentModel.project_id == project_id,
                                                                 GitlabFileRawContentModel.path_type == "blob",
                                                                 GitlabFileRawContentModel.raw_content is not None,
                                                                 GitlabFileRawContentModel.raw_content != "",
                                                                 GitlabFileRawContentModel.name.notlike(".%")
                                                                 ).all()
        db_session.close()
        return res

    def update_files_content(self, list):
        """

        @param list: list<GitlabFileRawContentModel>
        @return:
        """
        db_session = instance_db_session()
        try:
            for e in list:
                res = db_session.query(GitlabFileRawContentModel).filter(
                    GitlabFileRawContentModel.project_id == e.project_id,
                    GitlabFileRawContentModel.branch == e.branch,
                    GitlabFileRawContentModel.path == e.path).first()
                res.raw_content = e.raw_content

            db_session.commit()
        except:
            for e in list:
                try:
                    res = db_session.query(GitlabFileRawContentModel).filter(
                        GitlabFileRawContentModel.project_id == e.project_id,
                        GitlabFileRawContentModel.branch == e.branch,
                        GitlabFileRawContentModel.path == e.path).first()
                    res.raw_content = e.raw_content
                    db_session.commit()
                except:
                    print("写入失败【{}】".format(e.url))

        db_session.close()


if __name__ == '__main__':
    dao = GitlabProjectFileDao()
    a = dao.query_exist_project_file()
    b = a
