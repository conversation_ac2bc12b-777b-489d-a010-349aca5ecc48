from sqlalchemy import Column, Integer, String, UniqueConstraint, DateTime
from dao.sqlarchemy.database_pool import Base


class GitlabFileBlameModel(Base, object):
    __tablename__ = 'code_repo_gitlab_file_blames'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, nullable=False)
    branch = Column(String(200))
    path = Column(String(250), nullable=False)
    commit_id = Column(String(100))
    committed_date = Column(DateTime)
    committer_name = Column(String(50))
    committer_email = Column(String(100))
    lines = Column(String(2000))
    sequence = Column(Integer, nullable=False)
    effective = Column(String(20))

    UniqueConstraint('project_id', 'branch', 'path', 'sequence')


class GitlabLineDetailModel(Base, object):
    __tablename__ = 'code_repo_gitlab_file_line_detail'
    __table_args__ = {"extend_existing": True}

    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, nullable=False)
    branch = Column(String(200))
    path = Column(String(250), nullable=False)
    line_sequence = Column(Integer, nullable=False)
    sequence = Column(Integer, nullable=False)
    fraction = Column(String(500))

    UniqueConstraint('project_id', 'branch', 'path', 'line_sequence', 'sequence')

if __name__ == '__main__':
    line = " *dfafdsa"
    print(len(line.strip()))