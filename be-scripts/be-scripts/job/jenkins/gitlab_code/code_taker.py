import codecs
import datetime
import logging
import re
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import quote_plus

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

import requests
from settings import GITLAB_HTTP, GITLAB_TOKEN
from job.jenkins.gitlab_code.data.code_repo_gitlab_project_dao import GitlabProjectDao
from job.jenkins.gitlab_code.data.code_repo_gitlab_file_blame_dao import GitlabFileBlameDao
from job.jenkins.gitlab_code.data.code_repo_gitlab_file_blame_model import GitlabFileBlameModel, GitlabLineDetailModel
from job.jenkins.gitlab_code.data.code_repo_gitlab_project_file_dao import ********************, GitlabFileRawContentDao
from job.jenkins.gitlab_code.data.code_repo_gitlab_project_file_model import GitlabProjectFileModel, \
    GitlabFileRawContentModel
from job.jenkins.gitlab_code.data.code_repo_gitlab_project_model import GitlabProjectModel
from settings import logger


class Taker:
    projects_url = "{gitlab_http}/api/v4/projects?per_page=100&page={page}"
    trees_url = "{gitlab_http}/api/v4/projects/{project_id}/repository/tree?ref={branch}&recursive=true&per_page=100&page={page}"
    file_content_url = "{gitlab_http}/api/v4/projects/{project_id}/repository/files/{path}/raw?ref={branch}"
    file_blame_url = "{gitlab_http}/api/v4/projects/{project_id}/repository/files/{path}/blame?ref={branch}"

    headers = {
        "PRIVATE-TOKEN": GITLAB_TOKEN
    }

    project_dao = GitlabProjectDao()
    file_dao = ********************()
    content_dao = GitlabFileRawContentDao()
    file_blame_dao = GitlabFileBlameDao()

    def __get_gitlab_content_one_page(self, type, *args):
        if type == "file_raw":
            url = self.file_content_url.format(gitlab_http=GITLAB_HTTP, project_id=args[0], branch=args[1],
                                               path=args[2])
        elif type == "file_blame":
            url = self.file_blame_url.format(gitlab_http=GITLAB_HTTP, project_id=args[0], branch=args[1],
                                             path=args[2])

        try:
            response = requests.get(url, headers=self.headers)
            if type == "file_raw":
                res = response.content if response.status_code == 200 else []
            elif type == "file_blame":
                res = response.json() if response.status_code == 200 else []

        except Exception as e:
            res = []

        return res, url

    def __get_gitlab_content_all(self, type, *args):
        '''

        @return: dict
        '''
        all = []
        page = 1
        while page:
            page, content = self.__get_content_by_page(type, page, *args)
            all.extend(content)

        return all

    def __get_content_by_page(self, type, page, *args):
        '''

        @param page:
        @return: 下一页, 本页所有
        '''
        url = ""
        if type == "projects":
            url = self.projects_url.format(gitlab_http=GITLAB_HTTP, page=page)
        elif type == "trees":
            url = self.trees_url.format(gitlab_http=GITLAB_HTTP, project_id=args[0], branch=args[1], page=page)

        response = requests.get(url, headers=self.headers)

        try:
            next_page = response.headers["X-Next-Page"]
        except:
            next_page = None

        try:
            res = response.json()
        except:
            res = response.content if response.status_code == 200 else []

        return next_page, res

    def update_all_file_content(self):

        project_list = self.project_dao.query_exist_project()
        for e in project_list:
            file_list = self.content_dao.query_file_list(e.project_id)
            with ThreadPoolExecutor(max_workers=20) as t:
                list = []
                for f in file_list:
                    if len(list) < 20:
                        t.submit(self.__generate_file_content_model(e.project_id, f.branch, f.path, list))
                    else:
                        self.__update_files_content(list)
                        list = []

                self.__update_files_content(list)

            logging.info("完成文件内容【{}】【{}】".format(e.project_id, e.path_with_namespace))

    def __update_files_content(self, list):
        try:
            self.content_dao.update_files_content(list)
        except Exception as e:
            logging.error("记录过程出错【{}】【{}】【{}】".format(list[0].project_id, list[0].branch, list[0].path))

    def __generate_file_content_model(self, project_id, branch, path, list):
        '''

        @param project_id:
        @param branch:
        @param path:
        @param list: reference
        '''

        path_encode = quote_plus(path)
        content, url = self.__get_gitlab_content_one_page("file_raw", project_id, branch, path_encode)
        try:
            str_content = codecs.decode(content, 'utf-8')
            model = GitlabFileRawContentModel()
            model.project_id = project_id
            model.branch = branch
            model.path = path
            model.raw_content = str_content
            model.url = url
            list.append(model)
        except:
            return
            # print("非正常字符串【{}】【{}】【{}】".format(project_id, branch, path))

    def refresh_all_file_blame(self):
        project_list = self.project_dao.query_exist_project()
        for e in project_list:
            self.file_blame_dao.delete_files_and_details(e.project_id)
            file_list = self.content_dao.query_file_with_content_list(e.project_id)
            for f in file_list:
                self.__refresh_file_blame(e.project_id, f.branch, f.path)
            logger.info("{}完成代码行明细【{}】【{}】".format(datetime.datetime.now(), e.project_id, e.path_with_namespace))

    def __refresh_file_blame(self, project_id, branch, path):
        print("{} {} {} {}".format(datetime.datetime.now(), project_id, path, threading.get_ident()))
        path_encode = quote_plus(path)
        res, url = self.__get_gitlab_content_one_page("file_blame", project_id, branch, path_encode)
        exists_fractions = []
        lock = threading.Lock()

        i = 0

        with ThreadPoolExecutor(max_workers=3) as t:
            for paragraph in res:
                i = i + 1
                lines = paragraph.get("lines")
                if len(lines) > 10000:
                    print("{} {} {} 行数太大不处理".format(datetime.datetime.now(), project_id, path))
                    continue
                t.submit(self.__refresh_paragraph, paragraph, path, branch, project_id, i * 1000000, exists_fractions,
                         lock)

    def __refresh_paragraph(self, paragraph, path, branch, project_id, paragraph_seq, exists_fractions, lock):
        blame_list = []
        line_detail_list = []

        i = 0

        with ThreadPoolExecutor(max_workers=20) as t:
            for line in paragraph.get("lines"):
                i = i + 1
                t.submit(self.__parse_line(line, path, branch, project_id, paragraph.get("commit"), paragraph_seq + i,
                                           blame_list, line_detail_list, exists_fractions, lock))

        self.file_blame_dao.insert_file_blame_list(blame_list, line_detail_list)

    def __parse_line(self, line, path, branch, project_id, commit_obj, line_num, blame_list, line_detail_list,
                     exists_fractions, lock):

        if len(line) > 2000 or len(line.strip()) == 0:
            return
        blame_model = GitlabFileBlameModel()
        blame_model.path = path
        blame_model.branch = branch
        blame_model.project_id = project_id
        blame_model.commit_id = commit_obj.get("id")
        blame_model.committed_date = datetime.datetime.strptime(commit_obj.get("committed_date"),
                                                                "%Y-%m-%dT%H:%M:%S.%f+08:00")
        blame_model.committer_email = commit_obj.get("committer_email")
        blame_model.committer_name = commit_obj.get("committer_name")
        blame_model.sequence = line_num
        blame_model.lines = line
        try:
            if line.strip().find("*") == 0 or line.strip().find("/") == 0 or line.strip().find("<!") == 0 \
                    or line.strip().find("import") == 0 or line.strip().find("#") == 0:
                blame_model.effective = 'N'
            else:
                blame_model.effective = 'Y'
        except Exception as ex:
            blame_model.effective = 'Y'
        pattern = '[^\w]+'  # 使用正则表达式分隔字符串
        result = re.split(pattern, line)
        fraction_seq = 0
        for fraction in result:
            fraction_seq = fraction_seq + 1
            real_fraction = fraction if len(fraction) < 400 else "The fraction is too long!!!"
            symbol = "{}|{}|{}".format(project_id, path, real_fraction)
            lock.acquire()
            if symbol in exists_fractions:
                lock.release()
                continue
            else:
                exists_fractions.append(symbol)
                lock.release()

            line_detail_model = GitlabLineDetailModel()
            line_detail_model.path = path
            line_detail_model.branch = branch
            line_detail_model.project_id = project_id
            line_detail_model.line_sequence = line_num
            line_detail_model.sequence = fraction_seq
            line_detail_model.fraction = real_fraction
            if not self.__need_deprive_fraction(fraction):
                line_detail_list.append(line_detail_model)
        blame_list.append(blame_model)

    def __need_deprive_fraction(self, fraction):
        if fraction is not None and fraction != '' and len(fraction) > 4:
            java_deprived = ['abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char', 'class',
                             'continue', 'default', 'do', 'double', 'else', 'enum', 'extends', 'final', 'finally',
                             'float', 'for', 'if', 'implements', 'import', 'int', 'interface', 'instanceof', 'long',
                             'native', 'new', 'package', 'private', 'protected', 'public', 'return', 'short', 'static',
                             'strictfp', 'super', 'switch', 'synchronized', 'this', 'throw', 'throws', 'transient',
                             'try', 'void', 'volatile', 'while', 'true', 'false', 'null', 'goto', 'const', 'main']
            maven_deprived = ['distributionManagement', 'groupId', 'artifactId', 'dependency', 'SNAPSHOT', 'properties',
                              'exclusion', 'snapshots']
            type_deprived = ['Float', 'String', 'Integer', 'Long', 'Object', 'object', 'BigDecimal', 'integer']
            html_deprived = ['http', 'https', 'www', 'span', 'div', 'id', 'url', 'function', 'vue', 'jQuery', 'jackson',
                             'width', 'include', 'border', 'height', 'background']
            other_deprive = ['lock', 'mvn', 'maven', 'jar', 'xmlns', 'XMLSchema', 'xsd', 'Override', 'override', 'yes',
                             'no', 'default', 'apache', 'instance', 'encoding', 'RELEASE', 'version', 'spring',
                             'fastjson', 'alibaba', 'Alibaba', 'starter', 'common', 'value', 'timestamp', 'dubbo',
                             'config',
                             'CHARSET', 'charset', 'pattern', 'guava', 'powermock', 'mockito2', 'oracle',
                             'commons', 'jdbcType', 'column', 'lombok', 'Getter', 'Setter', 'howbuy', 'springframework',
                             'apiName', 'True', 'False', 'Value', 'using', 'exclusions', 'schemaLocation', 'servlet',
                             'log4j', 'Howbuy', 'encoding', 'system', 'System', 'cloud', 'Cloud', 'mybatis', 'Mybatis',
                             'javassist', 'scope']
            need_deprive = []
            # java_deprived + maven_deprived + type_deprived + html_deprived + other_deprive
            if fraction in need_deprive:
                return True

            return False
        else:
            return True

    def refresh_all_project_file(self):
        project_list = self.project_dao.query_exist_project()
        with ThreadPoolExecutor(max_workers=20) as t:
            for e in project_list:
                t.submit(self.__refresh_project_file, e.project_id, "master", e.path_with_namespace)

    def __refresh_project_file(self, project_id, branch, path_with_namespace):
        print("开始处理【{project_id}】【{path_with_namespace}】".format(project_id=project_id,
                                                                 path_with_namespace=path_with_namespace))
        project_files_gitlab_list = self.__get_gitlab_content_all("trees", project_id, branch)
        current_list = []
        for i in range(len(project_files_gitlab_list)):
            current_list.append(self.__convert_project_file(project_id, branch, project_files_gitlab_list[i]))

        self.file_dao.delete_project_files(project_id)
        self.file_dao.insert_project_file_list(current_list)
        logger.info("处理完毕【{project_id}】【{path_with_namespace}】".format(project_id=project_id,
                                                                       path_with_namespace=path_with_namespace))

    def __convert_project_file(self, project_id, branch, file_gitlab):
        model = GitlabProjectFileModel()
        model.project_id = project_id
        model.branch = branch
        model.path = file_gitlab.get("path")
        model.name = file_gitlab.get("name")
        model.reversed_name = model.name[::-1]
        model.path_type = file_gitlab.get("type")

        return model

    def __convert_project(self, project_gitlab):
        model = GitlabProjectModel()
        model.project_id = project_gitlab.get("id")
        model.default_branch = project_gitlab.get("default_branch")
        model.visibility = project_gitlab.get("visibility")
        model.path_with_namespace = project_gitlab.get("path_with_namespace")

        return model

    def refresh_all_project(self):
        projects_exist_list = self.project_dao.query_all_project()
        projects_gitlab_list = self.__get_gitlab_content_all("projects")
        current_list = []
        for i in range(len(projects_gitlab_list)):
            is_current = True
            for j in range(len(projects_exist_list)):
                if projects_gitlab_list[i].get("id") == projects_exist_list[j].project_id:
                    is_current = False
                    break

            if is_current:
                current_list.append(self.__convert_project(projects_gitlab_list[i]))

        self.project_dao.insert_project_list(current_list)

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))

        acceptor = {
            "refresh_all_project": self.refresh_all_project,
            "refresh_all_project_file": self.refresh_all_project_file,
            "update_all_file_content": self.update_all_file_content,
            "refresh_all_file_blame": self.refresh_all_file_blame
        }

        acceptor[params[0]]()


if __name__ == '__main__':
    taker = Taker()
    taker.call(sys.argv[1:])
    # taker.refresh_all_project()
    # taker.refresh_all_project_file()
    # taker.update_all_file_content()
    # taker.refresh_all_file_blame()
