import datetime

import paramiko
import logging
import os
import sys
import subprocess
import stat
import time

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import SCM_PROD_NODE_INFO, SCM_TEST_NODE_INFO


class SSHConnectionManager:

    def __init__(self, ip, username, password):
        """
        :param ip:
        :param username:
        :param password:
        """
        self.ip = ip
        self.username = username
        self.password = password
        paramiko.util.log_to_file("upload.log")  # 记录日志文件

    def __enter__(self):
        logging.info("建立到{}的SSH连接".format(self.ip))
        ssh = paramiko.SSHClient()  # 创建ssh对象
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self.SSH = ssh
        self.SSH.connect(self.ip, port=22, username=self.username,
                         password=self.password, compress=True)
        logging.info("{}连接成功".format(self.ip))

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.info("断开到{}的SSH连接".format(self.ip))
        self.SSH.close()
        logging.info("{}断开连接成功".format(self.ip))


class Prod_checker:
    def __init__(self, app):
        self.app = app
        self.ip = SCM_PROD_NODE_INFO[app]['ip']
        self.username = SCM_PROD_NODE_INFO[app]['username']
        self.password = SCM_PROD_NODE_INFO[app]['password']
        self.path = SCM_PROD_NODE_INFO[app]['paths']
        pass

    def sshclient_execmd(self, execmd, ssh):
        """命令执行"""
        stdin, stdout, stderr = ssh.exec_command(execmd)
        resulto = stdout.readlines()
        resulte = stderr.readlines()
        stat = stdout.channel.recv_exit_status()
        logging.info('execmd : %s' % execmd)
        logging.info(resulto)
        if stat != 0:
            pass
            logging.error(resulte)
        else:
            logging.info('Exe %s successful' % execmd)
        return resulto, resulte, stat

    def server_check(self):
        with SSHConnectionManager(self.ip, self.username, self.password) as ssh:
            cmd = "netstat -anlp | grep %s | awk '{print $6}'" % (SCM_PROD_NODE_INFO[self.app]['app_port'])
            resulto, resulte, stat = self.sshclient_execmd(cmd, ssh.SSH)
            if (resulto and 'LISTEN' not in resulto[0]) or not resulto:
                self.app_restart(app, ssh.SSH)
            else:
                logging.info('节点{}的应用{}的端口{}监听正常，无需重启！'.format(self.ip, self.app, SCM_PROD_NODE_INFO[self.app]['app_port']))

    def app_restart(self, app, ssh):
        restarter = App_restarter(app, ssh)
        if app == 'spider':
            restarter.spider_restart()
        elif app == 'old_django':
            restarter.old_django_restart()


class App_restarter():
    def __init__(self, app, ssh):
        self.app = app
        self.ssh = ssh
        self.ip = SCM_PROD_NODE_INFO[app]['ip']
        self.username = SCM_PROD_NODE_INFO[app]['username']
        self.password = SCM_PROD_NODE_INFO[app]['password']
        self.path = SCM_PROD_NODE_INFO[app]['paths']

    def spider_restart(self):
        logging.info('当前节点{}'.format(self.ip))
        cmd = "python3.x {}/start.py {}".format(self.path, 'restart')
        chk = Prod_checker(self.app)
        resulto, resulte, stat = chk.sshclient_execmd(cmd, self.ssh)
        if stat == 0:
            logging.info('节点{}的应用{}重启成功！'.format(self.ip, self.app))

    def old_django_restart(self):
        logging.info('当前节点{}'.format(self.ip))
        cmd = "sh {}/start.sh {} ".format(self.path, 'restart')
        chk = Prod_checker(self.app)
        resulto, resulte, stat = chk.sshclient_execmd(cmd, self.ssh)
        if stat == 0:
            logging.info('节点{}的应用{}重启成功！'.format(self.ip, self.app))


if __name__ == '__main__':
    app = sys.argv[1]
    # br_name = sys.argv[2]
    # env = sys.argv[3]
    # app = 'old_django'
    # br_name = '2.20.1'
    # env = 'test'
    checker = Prod_checker(app)
    checker.server_check()
