import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from concurrent.futures import ThreadPoolExecutor
from urllib3.exceptions import InsecureRequestWarning
from dao.insert.mysql import repo_files
import requests
from settings import logger as log

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class RepoFiles:

    def __init__(self):
        self.base_gitlab_url = 'http://gitlab-lib.howbuy.pa/api/v4/'
        self.headers = {"PRIVATE-TOKEN": "vS9BE-1LPVXyiFtyq_sG"}
        self.get_projects_temp = 'projects?per_page=100&page={}'
        self.get_project_repository_tree_temp = 'projects/{}/repository/tree?recursive=yes&ref={}&per_page=100&page={}'
        self.get_project_commits_temp = 'projects/{}/repository/commits?all=yes&per_page=100&page={}'
        self.get_commit_refs_temp = 'projects/{}/repository/commits/{}/refs'

    def _on_process(self):
        url = self.base_gitlab_url + self.get_projects_temp.format(1)
        result = requests.get(url=url, headers=self.headers)
        total = self.__get_total_number_from_response(result, url)
        if total < 0:
            return

        with ThreadPoolExecutor(max_workers=6) as t:
            for i in range(total):
                page = i + 1
                url = self.base_gitlab_url + self.get_projects_temp.format(page)
                result = requests.get(url=url, headers=self.headers)
                t.submit(self.__convert_projects_info, result.json())

    def _on_delete_history_data(self):
        all_batch_dates = repo_files.find_all_batch_dates()
        if all_batch_dates is None or all_batch_dates.__len__() < 3:
            return
        else:
            repo_files.delete_data_before_date(all_batch_dates[2]["data_date"])
            return

    def __convert_projects_info(self, projects_json):
        for project_json in projects_json:
            self.__convert_project_info(project_json)
            log.info("处理完 {}".format(project_json))

    def __get_total_number_from_response(self, result, url):
        if result.status_code != 200:
            log.info("这次获取没有结果 {} ！！！".format(url))
            return -1
        return int(result.headers.get('X-Total-Pages'))

    def __find_latest_branch_of_project(self, project_id):
        next_page = 100
        page = 1
        latest_branch = 'master'
        latest_commit = None

        while page < next_page:
            url = self.base_gitlab_url + self.get_project_commits_temp.format(project_id, page)
            result = requests.get(url=url, headers=self.headers)
            if result.status_code != 200:
                return latest_branch

            latest_commit = self.__find_latest_commit_by_compare(result.json(), latest_commit)
            if latest_commit is None:
                return latest_branch

            try:
                next_page = int(result.headers.get('X-Next-Page'))
            except Exception as e:
                next_page = 1

            page = page + 1

        return self.__find_latest_branch(project_id, latest_commit)

    def __find_latest_branch_by_repo_path(self, repo_path):
        latest_branch = repo_files.find_latest_prod_branch(repo_path)
        return latest_branch

    def __find_latest_branch(self, project_id, commit):
        if commit is None:
            return 'master'

        commit_id = commit.get('id')

        url = self.base_gitlab_url + self.get_commit_refs_temp.format(project_id, commit_id)
        result = requests.get(url=url, headers=self.headers)

        for ref in result.json():
            return ref.get('name')

        return 'master'

    def __find_latest_commit_by_compare(self, commits_json, latest_commit):
        for commit in commits_json:
            if latest_commit is None:
                latest_commit = commit
                continue
            if commit.get('created_at') > latest_commit.get('created_at'):
                latest_commit = commit

        return latest_commit

    def __convert_project_info(self, project_json):
        """
        从json转换单个项目信息
        @param project_json:
        @return:
        """
        repo_path = project_json.get('path')
        repo_namespace = project_json.get('path_with_namespace')
        project_id = project_json.get('id')
        branch_obj = self.__find_latest_branch_by_repo_path(repo_path)
        branch = self.__find_latest_branch_of_project(project_id) if branch_obj is None else branch_obj["lib_repo_branch"]
        return self.__process_files_in_project(project_id, branch, repo_namespace, repo_path)

    def __process_files_in_project(self, project_id, branch, repo_namespace, repo_path):
        url = self.base_gitlab_url + self.get_project_repository_tree_temp.format(project_id, branch, 1)
        result = requests.get(url=url, headers=self.headers)
        total = self.__get_total_number_from_response(result, url)
        if total < 0:
            return False

        for i in range(total):
            page = i + 1
            url = self.base_gitlab_url + self.get_project_repository_tree_temp.format(project_id, branch, page)
            result = requests.get(url=url, headers=self.headers)
            self.__convert_files_info(result.json(), project_id, repo_namespace, repo_path, branch)

        return True

    def __convert_files_info(self, files_json, project_id, repo_namespace, repo_path, branch):
        for file_json in files_json:
            self.__convert_file_info(file_json, project_id, repo_namespace, repo_path, branch)

    def __convert_file_info(self, file_json, project_id, repo_namespace, repo_path, branch):
        file_name = file_json.get('name')
        file_path = file_json.get('path')
        file_type = file_json.get('type')

        if file_type != 'tree':
            repo_files.insert_repo_files(project_id, repo_namespace, repo_path, file_name, file_path, file_type, branch)

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "public_on_process": self._on_process,
            "delete_history_data": self._on_delete_history_data
        }

        acceptor[params[0]]()


def local_testing():
    """

    @return:
    """

    p = RepoFiles()
    # print(p.__find_latest_branch_of_project('332'))
    # p.call(sys.argv[1:])


if __name__ == "__main__":
    p = RepoFiles()
    p.call(sys.argv[1:])


"""
pipeline {
    agent any
    stages{
        stage('清理') {
            steps {
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/repos/repo_files.py "delete_history_data"'
            }
        }
        stage('执行') {
            steps {
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/repos/repo_files.py "public_on_process"'
            }
        }
    }
}
"""