import sys
import os
import time

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from urllib3.exceptions import InsecureRequestWarning
from settings import logger as log
import requests
import json
from job.jenkins.info_warning.wechat_robot_markdown import WechatRobotMarkdown

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class RmqDashBoardMonitor:
    def __init__(self):
        self.base_dashborad_url = 'http://dashboard.intelnal.howbuy.com/'
        self.func_url_login = "/login/login.do?username=admin&password=paadmin"
        self.func_url_cluster_list = "/cluster/list.query"
        self.md = WechatRobotMarkdown()

        return

    def process(self):
        first_time = True
        while True:
            log.info("当前是第一次 {}".format(first_time))
            if self.__send_warning_message(not first_time):
                if not first_time:
                    break
                else:
                    first_time = False
            else:
                first_time = True
            time.sleep(20)
        return

    def __send_warning_message(self, just_send=False):
        res = self.__get_cluster_list_json()
        not_send, lines = self.__lines_with_status(res, 0.5)

        content = self.md.real_sending_content("RMQ产线瞬时监测数据异常", lines)
        log.info(content)

        if not not_send:
            if just_send:
                self.md.warn("c9a0b649-a5e0-4950-8c12-9bc1eef3df43", content)
            return True

        return False

    def __lines_with_status(self, res, reference_index):
        """
        返回发送内容和是否发送
        @param res: http返回值
        @param reference_index: 参照值，int，比如1，2
        @return: not_send, lines
        """

        not_send = True
        lines = [self.md.real_line_str('异常信息', '整套集群收/发TPS出现小于{}'.format(reference_index), 'warning')]
        lines.append(self.md.real_line_str("监测时间", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())))

        not_over_reference, line = self.__get_line_from_last_3_sec(
            self.__get_instant_broker_index(res.content, "a", "putTps"), reference_index, 'broker-a最近3个时段发送TPS')
        not_send = not_send and not_over_reference
        lines.append(line)

        not_over_reference, line = self.__get_line_from_last_3_sec(
            self.__get_instant_broker_index(res.content, "b", "putTps"), reference_index, 'broker-b最近3个时段发送TPS')
        not_send = not_send and not_over_reference
        lines.append(line)

        not_over_reference, line = self.__get_line_from_last_3_sec(
            self.__get_instant_broker_index(res.content, "a", "getTransferedTps"), reference_index,
            'broker-a最近3个时段消费TPS')
        not_send = not_send and not_over_reference
        lines.append(line)

        not_over_reference, line = self.__get_line_from_last_3_sec(
            self.__get_instant_broker_index(res.content, "b", "getTransferedTps"), reference_index,
            'broker-b最近3个时段消费TPS')
        not_send = not_send and not_over_reference
        lines.append(line)

        return not_send, lines

    def __get_line_from_last_3_sec(self, index_list, reference_index, line_title):
        """

        @param index_list:
        @param reference_index: 参照值，int，比如1，2
        @return: not_over_reference, line
        """
        not_over_reference = True
        value = ""
        if index_list:
            for i in range(0, len(index_list)):
                o = self.__number_str_remain_decimal(index_list[i], 2)
                value = "{} {}".format(value, o)
                not_over_reference = not_over_reference and float(o) >= reference_index

        return not_over_reference, self.md.real_line_str(line_title, value)

    def __number_str_remain_decimal(self, number_str, decimal):
        """
        输入字符，保留字符串
        @param number_str: 数字字符
        @param decimal: 保留位数
        @return:
        """
        return str(round(float(number_str), decimal))

    def __get_cluster_list_json(self):
        url = self.base_dashborad_url + self.func_url_login
        result = requests.post(url=url)
        url = self.base_dashborad_url + self.func_url_cluster_list
        result = requests.get(url=url, cookies=result.cookies)
        return result

    def __get_instant_broker_index(self, content, broker, index):
        """
        获取指定broker的瞬时生产TPS
        @param content:
        @param broker: a, b
        @param index: putTps, getTransferedTps
        @return:
        """
        obj = json.loads(content)
        try:
            str = obj.get("data").get("brokerServer").get("broker-{}".format(broker)).get("0").get(index)
        except Exception as e:
            print(e)
            print(obj.get("data"))
            return [5.0]
        str_list = str.split(" ")
        return str_list


if __name__ == '__main__':
    o = RmqDashBoardMonitor()
    o.process()
