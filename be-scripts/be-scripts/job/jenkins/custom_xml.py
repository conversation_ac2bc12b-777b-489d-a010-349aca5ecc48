#
import os
import sys
import json
import pickle
import jenkins
import treelib
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from utils.jenkins import pipeline
from utils.compile.mvn import pom_sorter
from settings import logger, JENKINS_INFO
from common.log.record_log import record_log
from dao.get.mysql.script_log import get_args
from job.jenkins.jenkins_xml import JenkinsXml
from dao.insert.mysql.pipeline_log import insert_pipeline_log_main, insert_pipeline_log_minor
from dao.update.mysql.pipeline_log import update_pipeline_minor_log

server = jen<PERSON>.<PERSON>(JENKINS_INFO["URL"], username=JEN<PERSON>INS_INFO["USER"], password=JENKINS_INFO["PASSWORD"])


class CustomXml(JenkinsXml):
    def __init__(self, sid):
        self.sid = sid
        self.send_log = record_log('platform', sid)

    def parse_app_pom(self, iteration_id, app_name, workspace):
        self.send_log.send({"step": "解析pom"})
        parser = pom_sorter.PomSorter(
            workspace=workspace,
            pipeline_id=iteration_id,
            app_name=app_name
        )
        pom_tree = parser.custom_main()
        with open('/tmp/{}_{}_tree.data'.format(iteration_id, app_name), 'wb') as f:
            pickle.dump(pom_tree, f)
        self.send_log.send({"status": "success", "step": "解析pom", "content": json.dumps(pom_tree.to_dict())})
        self.send_log.close()

    def update_pipeline_job(self, iteration_id, app_name, workspace, job_name, flag_path, compile_list):
        try:
            sid = insert_pipeline_log_main(exec_jenkins=JENKINS_INFO["URL"], exec_parameter=job_name,
                                           start_at=datetime.datetime.now(), iteration_id=iteration_id,
                                           app_name=app_name, status="running", suite_name="")
            insert_pipeline_log_minor(sid, "解析{}".format(job_name), datetime.datetime.now(), app_name)
        except Exception as e:
            logger.error(str(e))

        self.send_log.send({"step": "构建应用"})
        pom_tree = None
        try:
            with open('/tmp/{}_{}_tree.data'.format(iteration_id, app_name), 'rb') as f:
                pom_tree = pickle.load(f)
                f.seek(0)
                pom_tree_cp = pickle.load(f)
        except Exception as err:
            logger.error(err)

        if isinstance(pom_tree, treelib.tree.Tree):
            try:
                choice_compile_list = compile_list.split(',')

                all_compile_obj = set()
                depth = pom_tree.depth()
                while depth != 0:
                    for node in pom_tree.leaves():
                        tmp_app = node.data['app_name']
                        for item in pom_tree.all_nodes():
                            if item.identifier.startswith(node.tag):
                                pom_tree.link_past_node(item.identifier)
                        all_compile_obj.add(tmp_app)
                    depth = depth - 1
                root = pom_tree.get_node(pom_tree.root)
                all_compile_obj.add(root.data['app_name'])

                parser = pom_sorter.PomSorter(
                    workspace=workspace,
                    pipeline_id=iteration_id,
                    app_name=app_name
                )
                res = parser.public_batch_pom(pom_tree_cp)
                if set(choice_compile_list) == all_compile_obj:
                    pipeline_updater = pipeline.PipelineUpdater(res, server, job_name, flag_path)
                else:
                    choice_res = []
                    for item in res:
                        sub_app = dict()
                        for app in item:
                            if app in choice_compile_list:
                                sub_app.update({app: item[app]})
                        if sub_app:
                            choice_res.append(sub_app)
                    pipeline_updater = pipeline.PipelineUpdater(choice_res, server, job_name, flag_path)
                pipeline_updater.update_pipeline()
                self.send_log.send({"status": "success", "step": "构建应用", "content": ''})
            except Exception as err:
                logger.error(err)
                self.send_log.send({"status": "failure", "step": "构建应用", "content": str(err)})
        else:
            self.send_log.send({"status": "failure", "step": "构建应用", "content": '应用数据不正确'})
        self.send_log.close()
        with open(flag_path, "w") as f:
            f.write(json.dumps({"sid": sid}))
        try:
            update_pipeline_minor_log(sid, "解析{}".format(job_name), "解析流水线完成", datetime.datetime.now(),
                                      "success")
        except Exception as e:
            logger.error(str(e))

    def call(self):
        params = json.loads(get_args(self.sid))
        acceptor = {
            "parse_app_pom": self.parse_app_pom,
            "update_pipeline_job": self.update_pipeline_job,
        }
        job_name = params['job_name']
        args_list = job_name.split("_")
        # group_name = args_list[0]
        if params['func'] == "parse_app_pom":
            iteration_id = "_".join(args_list[:-1])
            app_name = args_list[-1]
            logger.info('----------' + params['func'] + '-----------')
            logger.info("迭代版本为{}".format(iteration_id))
            logger.info("应用为{}".format(app_name))
            acceptor[params['func']](iteration_id, app_name, params['workspace'])
        elif params['func'] == "update_pipeline_job":
            iteration_id = "_".join(args_list[:-1])
            app_name = args_list[-1]
            logger.info('----------' + params['func'] + '-----------')
            logger.info("迭代版本为{}".format(iteration_id))
            logger.info("应用为{}".format(app_name))
            acceptor[params['func']](iteration_id, app_name, params['workspace'], params['job_name'],
                                     params['flagPath'], params['compile_list'])


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    jenkins_xml = CustomXml(sys.argv[1])
    jenkins_xml.call()
