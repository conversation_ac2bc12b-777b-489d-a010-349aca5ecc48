# todo dockerfile模板,python版本为python3.10
uv_dockerfile_template = ''' 
# 使用指定的基础镜像
FROM {image_url}/{image_group}/{image_name}:{image_version}

# 设置环境变量
ENV UV_PYTHON=/usr/bin/python3.10
ENV PATH=$PATH:/root/.local/bin
ENV PYTHONPATH=/data/app

# 设置工作目录
WORKDIR /data/app

# 复制本地下载的uv安装包
COPY uv.tar.gz /tmp/
RUN cd /tmp && \
    tar xzf uv.tar.gz && \
    mv uv-x86_64-unknown-linux-gnu/uv /usr/local/bin/ && \
    chmod +x /usr/local/bin/uv && \
    rm -rf /tmp/uv-x86_64-unknown-linux-gnu* && \
    echo 'export PATH=$PATH:/root/.local/bin' >> ~/.bashrc && \
    source ~/.bashrc

# 验证uv安装
RUN uv --version

# 创建必要的目录
RUN mkdir -p /data/app/{app_name} \
    /data/logs/{app_name} \
    /home/<USER>
    /data/files/{app_name}\


# 设置工作目录
WORKDIR /data/app/{app_name}
# 复制requirements.txt到容器中
# COPY requirements.txt .
# 使用uv安装依赖
# RUN uv pip install -r requirements.txt -i http://pypi.howbuy.pa/simple --trusted-host pypi.howbuy.pa

# 复制整个项目到容器中
COPY . /data/app/mantis/

# 卸载指定的依赖包
#RUN /usr/local/bin/uv pip uninstall certifi cffi charset-normalizer click cryptography Django django-mcp djangorestframework greenlet h11 httpcore idna Jinja2 MarkupSafe mcp packaging pbr pipdeptree pycparser python-jenkins pytz simplejson six soupsieve spider-common-utils sqlparse sse-starlette typer urllib3 uvicorn wcwidth

# 安装requirements.txt中的依赖包
# RUN /usr/local/bin/uv pip install -r /data/app/mantis/requirements.txt -i http://pypi.howbuy.pa/simple --trusted-host pypi.howbuy.pa

# 使用main.py安装依赖
RUN /usr/local/bin/uv run /data/app/{app_name}/main.py

# 安装系统命令 基础镜像中已包含，不需要重复安装
# RUN yum -y install zip unzip wget 

# 建python3.x的软链
RUN ln -sf /usr/local/python3.10/bin/python3.10 /usr/local/bin/python3.x

# 复制启动脚本
COPY start.sh /home/<USER>/start.sh

# 设置目录权限
RUN chown -R tomcat:tomcat /home/<USER>/data/app/{app_name} /data/logs/{app_name} /data/files/{app_name} && \
    chmod -R 755 /home/<USER>/data/app/{app_name} && \
    chmod -R 777 /data/files/{app_name} /data/logs/{app_name} && \
    chmod +x /home/<USER>/start.sh

# 切换到tomcat用户
USER tomcat

# 设置容器启动命令
ENTRYPOINT ["/usr/bin/sh", "/home/<USER>/start.sh"]

'''


mantis_start_script = '''
#!/bin/bash

# 设置环境变量
export PYTHONPATH=/data/app
export DJANGO_SETTINGS_MODULE=mantis.settings
export CONFIG_PATH=/data/app/mantis/mantis

# 启动Django应用
cd /data/app/{app_name}
uv run manage.py runserver 0.0.0.0:8080 --config "/data/app/mantis/mantis" 2>&1 | tee /data/logs/mantis/mantis-web.log
'''

start_image_script = '''
docker run -d \
  --name {app_name} \
  -p 8080:8080 \
  -v /data/app/{app_name}:/data/app/{app_name} \
  -v /data/files/{app_name}:/data/files/{app_name} \
  -v /data/logs/{app_name}:/data/logs/{app_name} \
  -e DJANGO_SETTINGS_MODULE=mantis.settings \
  -e PYTHONPATH=/data/app \
  -e CONFIG_PATH=/data/app/{app_name}/mantis \
  {image_url}/{image_group}/{app_name}:{image_version}
'''