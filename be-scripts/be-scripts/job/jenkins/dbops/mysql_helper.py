import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

import pymysql
from settings import logger as log


class MysqlHelper:

    def execute_sql(self, params):
        host = params[0]
        port = int(params[1])
        user = params[2]
        password = params[3]
        dbname = params[4]
        sql = params[5]
        connection = pymysql.connect(host=host, port=port, user=user,
                                     passwd=password, db=dbname, charset='utf8')
        cur = connection.cursor(cursor=pymysql.cursors.DictCursor)
        cur.execute(sql)
        connection.commit()
        connection.close()
        log.info('{},{},{},{}'.format(host, port, dbname, sql))


if __name__ == '__main__':
    t = MysqlHelper()
    t.execute_sql(sys.argv[1:])
