import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

import pymysql
from settings import logger as log
from job.jenkins.info_warning.zepplin_reporter import ZepplinReporter


class MysqlForZepplineHelper:
    insert_template = "insert into {} ({}) values ('{}')"
    delete_template = "delete from {}"

    def __init__(self):
        self.zep = ZepplinReporter()

    def __prepare_sql(self, note_id, target_name):
        sql_list = [self.delete_template.format(target_name)]
        self.zep.runjob(note_id)
        zep_table = self.zep.get_table_data_rows_from_server(note_id)
        fields = None
        for row in zep_table:
            if row is None or row == '':
                continue
            if fields is None:
                fields = row.replace('\t', ' , ')
            else:
                values = row.replace('\t', "' , '").replace("'null'", "null")
                sql = self.insert_template.format(target_name, fields, values)
                sql_list.append(sql)

        return sql_list

    def execute_sql(self, params):
        host = params[0]
        port = int(params[1])
        user = params[2]
        password = params[3]
        dbname = params[4]
        target_name = params[5]
        node_id = params[6]
        sql_list = self.__prepare_sql(node_id, target_name)
        connection = pymysql.connect(host=host, port=port, user=user,
                                     passwd=password, db=dbname, charset='utf8')
        cur = connection.cursor(cursor=pymysql.cursors.DictCursor)
        for sql in sql_list:
            cur.execute(sql)
            cur.connection.commit()
        connection.close()
        log.info('{},{},{},{},{}'.format(host, port, dbname, target_name, node_id))


if __name__ == '__main__':
    t = MysqlForZepplineHelper()
    t.execute_sql(sys.argv[1:])
    # t.execute_sql(['***************', '3306', 'mring', '1y0vhz%2fEH', 'mring_base', 't_git_dever', '2J46YEPDR'])
