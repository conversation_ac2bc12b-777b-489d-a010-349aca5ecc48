import gitlab
import os
import sys
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql import DBConnectionManager
from settings import logger, GITLAB_HTTP, GITLAB_TOKEN

def get_team_group_info():
    team_group_list = []
    sql = '''
    SELECT DISTINCT i.git_url,t.team_owner,t.team_bak_owner FROM `app_mgt_app_info` i 
    LEFT JOIN `team_mgt_app_bind` b ON i.id = b.app_id 
    LEFT JOIN `tool_mgt_team_info` ti ON b.team_id = ti.id
    LEFT JOIN  `tool_mgt_team_info` t ON ti.parent_id = t.id WHERE i.git_url <> '' AND t.team_owner <> ''
    '''
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchall()
    for item in result:
        # print(item)
        team_group_list.append(item)
    return team_group_list

def modify_gitlab_access(user_name, bak_user_name,group_name):
    logger.info('user_name---------------->>>>>>>>>>>>>>>>>>:{}'.format(user_name))
    logger.info('bak_user_name---------------->>>>>>>>>>>>>>>>>>:{}'.format(bak_user_name))
    logger.info('group_name---------------->>>>>>>>>>>>>>>>>>:{}'.format(group_name))
    member_id_list = []
    gl = gitlab.Gitlab(GITLAB_HTTP, private_token=GITLAB_TOKEN)
    groups = gl.groups.list(search=group_name)
    for group in groups:
        if group.name == group_name:
            group_id = group.id
    group = gl.groups.get(group_id)
    members_list = group.members.list(all=True)
    for memben in members_list:
        member_id_list.append(memben.id)
    logger.info('member_id_list---------------->>>>>>>>>>>>>>>>>>:{}'.format(member_id_list))
    if user_name and bak_user_name:
        users = gl.users.list(search=user_name)
        for user in users:
            if user.username == user_name:
                user_id = user.id
        logger.info('user_id---------------->>>>>>>>>>>>>>>>>>:{}'.format(user_id))
        if user_id in member_id_list:
            mem = group.members.get(user_id)
            mem.access_level = gitlab.DEVELOPER_ACCESS
            mem.save()
        else:
            group.members.create({'user_id': user_id, 'access_level': gitlab.DEVELOPER_ACCESS})

    if bak_user_name:
        bak_users = gl.users.list(search=bak_user_name)
        for bak_user in bak_users:
            if bak_user.username == bak_user_name:
                bak_user_id = bak_user.id
        if bak_user_id in member_id_list:
            mem = group.members.get(bak_user_id)
            mem.access_level = gitlab.REPORTER_ACCESS
            mem.save()


if __name__ == '__main__':
    team_group_list = get_team_group_info()
    for item in team_group_list:
        print(item)
        modify_gitlab_access(item['team_owner'],item['team_bak_owner'],item['git_url'])
    # modify_gitlab_access('wei.liu','shuai.liu','scm_test_project')