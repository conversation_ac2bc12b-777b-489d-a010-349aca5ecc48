#!/usr/bin/env python
# -*- coding: UTF-8 -*-
# 制品更新后，分发至目标机
# zt@2021-01-06
import os
import json
import sys
import pymysql
import datetime
import traceback

from pymysql.cursors import DictCursor

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger as log
from settings import DATABASES, LIB_REPO
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd

TIME_STR = "%Y-%m-%d %H:%M:%S"


def get_time_str(param_time=None):
    if not param_time:
        param_time = datetime.datetime.now()
    return param_time.strftime(TIME_STR)


def connect_scm_mysql():
    conn = pymysql.connect(
        host=DATABASES['IP'], port=DATABASES['PORT'],
        database=DATABASES['DB'], charset=DATABASES['CHARSET'],
        user=DATABASES['USER'], password=DATABASES['PASSWORD'])
    return conn


def conn_close(conn):
    if conn:
        conn.close()


def get_scm_app_data(conn, app_name_str):
    if not app_name_str:
        app_name_str = LIB_REPO['def_app_name_str']

    app_name_str = ','.join('\'' + x + '\'' for x in app_name_str.split(','))

    with conn.cursor(cursor=DictCursor) as cursor:
        sql = '''SELECT 
    m.pipeline_id, 
    m.br_name, 
    m.br_end_date, 
    m.br_status,
    i.appName as app_name, 
    i.git_repo_version,
    a.lib_repo,
    IF(v.max_br_end_date IS NOT NULL, m.br_name, NULL) AS online_br_name
FROM iter_mgt_iter_info m
INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id AND i.git_repo_version IS NOT NULL
LEFT JOIN app_mgt_app_module a ON a.module_name = i.appName
LEFT JOIN(
    SELECT 
    i.appName, MAX(m.br_end_date) AS max_br_end_date
    FROM iter_mgt_iter_info m
    INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id AND i.git_repo_version IS NOT NULL
    WHERE m.br_status = 'close'
    GROUP BY i.appName
)v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
WHERE m.br_status = 'close'
AND i.appName in ({})
ORDER BY online_br_name DESC, pipeline_id DESC;'''.format(app_name_str)
        log.info(">>>> sql = {}".format(sql))
        cursor.execute(sql)
        result = cursor.fetchall()
    return result


def pull_lib_repo(scm_app_list):
    lr_user = LIB_REPO['lr_user']
    lr_host = LIB_REPO['lr_host']
    lr_root = LIB_REPO['lr_root']
    lr_suffix = LIB_REPO['lr_suffix']
    lr_online_br_name = LIB_REPO['lr_online_br_name']
    root_path = LIB_REPO['lr_cache_root_path']
    if not os.path.exists(root_path):
        cmd = 'mkdir -p {}'.format(root_path)
        exec_local_cmd(cmd)

    last_git_url = None
    last_app_name = None
    for row in scm_app_list:
        app_name = row['app_name']
        # log.info(">>>> app_name = {}".format(app_name))
        app_path = os.path.join(root_path, app_name)
        if not os.path.exists(app_path):
            cmd = 'mkdir -p {}'.format(app_path)
            exec_local_cmd(cmd)

        br_name = row['br_name']
        # log.info(">>>> br_name = {}".format(br_name))
        if not last_git_url or not last_app_name or last_app_name != app_name:
            lib_repo = row['lib_repo']
            git_url = '{}@{}:{}/{}{}'.format(lr_user, lr_host, lr_root, lib_repo, lr_suffix)
        else:
            git_url = last_git_url
        # log.info(">>>> git_url = {}".format(git_url))

        exec_for_clone(br_name, git_url, app_path)

        online_br_name = row['online_br_name']
        if online_br_name:
            exec_for_clone(online_br_name, git_url, app_path, lr_online_br_name)

        # log.info(">>>> =====")


def exec_for_clone(br_name, git_url, app_path, target_name=None):
    if not target_name:
        target_name = br_name
    br_path = os.path.join(app_path, target_name)

    if os.path.exists(br_path) and len(br_path) > len('/data/test_publish_aio/lib_repo_cache/*'):
        cmd = 'rm -rf {}'.format(br_path)
        exec_local_cmd(cmd)

    cmd = 'git clone -b {} --depth 1 {} {}'.format(br_name, git_url, br_path)
    s_time = datetime.datetime.now()
    exec_local_cmd(cmd, 1200)
    e_time = datetime.datetime.now()
    timedelta = e_time - s_time
    cost_time = timedelta.seconds * 1000 + timedelta.microseconds / 1000
    log.info("====「拉制品」{}，耗时(毫秒)：{}".format(git_url, cost_time))


def upd_iteration_ln(scm_app_list):
    log.info('==========创建「迭代」目录: {}=========='.format(get_time_str(datetime.datetime.now())))
    root_path = LIB_REPO['lr_cache_root_path']
    iteration_root_path = LIB_REPO['lr_iteration_root_path']
    iteration_path = os.path.join(root_path, iteration_root_path)
    if not os.path.exists(iteration_path):
        cmd = 'mkdir -p {}'.format(iteration_path)
        exec_local_cmd(cmd)

    for row in scm_app_list:
        app_name = row['app_name']
        pipeline_id = row['pipeline_id']
        br_name = row['br_name']

        br_path = os.path.join(root_path, app_name, br_name)

        if os.path.exists(br_path):
            iter_path = os.path.join(iteration_path, pipeline_id)
            if not os.path.exists(iter_path):
                cmd = 'mkdir -p {}'.format(iter_path)
                exec_local_cmd(cmd)

            iter_app_path = os.path.join(iter_path, app_name)
            cmd = 'ln -sf {} {}'.format(br_path, iter_app_path)
            exec_local_cmd(cmd)
        else:
            log.warn(">>>>「迭代制品缓存」不存在：{}".format(br_path))


def upd_online_ln(scm_app_list):
    log.info('==========创建「线上」目录: {}=========='.format(get_time_str(datetime.datetime.now())))
    root_path = LIB_REPO['lr_cache_root_path']
    lr_online_br_name = LIB_REPO['lr_online_br_name']
    lr_online_root_path = LIB_REPO['lr_online_root_path']
    online_root_path = os.path.join(root_path, lr_online_root_path)
    if not os.path.exists(online_root_path):
        cmd = 'mkdir -p {}'.format(online_root_path)
        exec_local_cmd(cmd)

    for row in scm_app_list:
        app_name = row['app_name']
        online_br_name = row['online_br_name']

        if online_br_name:
            online_cache_path = os.path.join(root_path, app_name, lr_online_br_name)
            if os.path.exists(online_cache_path):
                online_ln_path = os.path.join(online_root_path, app_name)
                cmd = 'ln -sf {} {}'.format(online_cache_path, online_ln_path)
                exec_local_cmd(cmd)
            else:
                log.warn(">>>>「线上制品缓存」不存在：{}".format(online_cache_path))


def main(app_name_str):
    scm_conn = None
    try:
        s_time = datetime.datetime.now()
        log.info('==========time(start): {}=========='.format(get_time_str(s_time)))
        # 1、获取DB连接
        scm_conn = connect_scm_mysql()
        # 2、获取迭代数据
        scm_app_list = get_scm_app_data(scm_conn, app_name_str)
        # 3、拉取制品至缓存
        pull_lib_repo(scm_app_list)

        # 4-1、创建迭代
        upd_iteration_ln(scm_app_list)
        # 4-2、创建线上
        upd_online_ln(scm_app_list)
        # 4-x、xxx
        e_time = datetime.datetime.now()
        log.info('==========time(end): {}=========='.format(get_time_str(e_time)))
        timedelta = e_time - s_time
        cost_time = timedelta.seconds + timedelta.microseconds / 1000000
        log.info('==========总耗时(秒): {}=========='.format(cost_time))
    finally:
        conn_close(scm_conn)


if __name__ == '__main__':
    log.info(">>>>定时更新制品{}".format(get_time_str(datetime.datetime.now())))

    param_list = sys.argv
    log.info(">>>>请求参数：{}".format(param_list))

    err_msg = None
    argv_str = None
    try:
        if len(param_list) > 1:
            argv_str = param_list[1]

        main(argv_str)
        # 正常返回
        sys.exit()
    except ValueError as e:
        # 打印错误信息
        err_msg = "ValueError：{}".format(e)
        log.error(err_msg)
        # 异常返回
        sys.exit(1)
    except Exception as ex:
        # 记录错误信息
        err_msg = "Exception：{}".format(ex)
        log.error(err_msg)
        # 打印错误信息
        traceback.print_exc()
        # 异常返回
        sys.exit(1)
