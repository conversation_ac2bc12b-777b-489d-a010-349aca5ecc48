import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from utils.test_env.test_env_lib import step_desc
from settings import logger as log
import git_file_tools


class Transfer:
    base_code_path = "code"
    base_local_path = '/data/temp'
    code_git_dict = {
        "hbshare": "*************************:meng.lv/hbshare.git"
    }
    ver_file_name = "hbshare/VERSION.txt"

    def __init__(self):
        return

    @step_desc("拉代码")
    def fetch_code(self, build_id, exec_path, app_name, branch="master"):
        log.info(">>>> BUILD_ID >>>> {}".format(build_id))
        local_path = os.path.join(exec_path, self.base_code_path, app_name)
        git_url = self.code_git_dict[app_name]

        log.info(">>>> 执行目录 {} 代码仓库 >>>> {}".format(local_path, git_url))

        git_file_tools.create_git_local_path_if_not_existed(git_url, local_path)
        git_file_tools.update_git_local_execute(local_path)

    @step_desc("升版本")
    def change_version(self, build_id, exec_path, app_name, branch="master"):
        log.info(">>>> BUILD_ID >>>> {}".format(build_id))
        local_path = os.path.join(exec_path, self.base_code_path, app_name)
        ver = git_file_tools.read_content_from_file(local_path, self.ver_file_name)
        log.info(ver)
        ver = self.__change_version_add1(ver)
        log.info(">>>> 版本升级到 >>>> {}".format(ver))
        git_file_tools.write_content_into_file(local_path, self.ver_file_name, ver)
        git_file_tools.commit_push_git_local_execute(local_path, ver)

    @step_desc("安装")
    def install(self, build_id, exec_path, app_name, branch="master"):
        log.info(">>>> BUILD_ID >>>> {}".format(build_id))
        local_path = os.path.join(exec_path, self.base_code_path, app_name)
        self.__install(local_path, "setup.py")

    def __change_version_add1(self, content):
        nums = content.split('.')
        if nums.__len__() != 3:
            return '1.0.0'

        if int(nums[2]) >= 99:
            nums[2] = 0
            if int(nums[1]) >= 99:
                nums[1] = 0
                nums[0] = int(nums[0]) + 1
            else:
                nums[1] = int(nums[1]) + 1
        else:
            nums[2] = int(nums[2]) + 1

        return "{}.{}.{}".format(nums[0], nums[1], nums[2])

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "fetch_code": self.fetch_code,
            "change_version": self.change_version
        }

        branch = "master"
        if len(params) > 4:
            branch = params[4]

        acceptor[params[0]](params[1], params[2], params[3], branch)


def local_testing():
    transfer = Transfer()
    transfer.base_local_path = "D:\\temp"
    transfer.code_git_dict = {
        "hbshare": "http://gitlab-code.howbuy.pa/meng.lv/hbshare.git"
    }
    # transfer.fetch_code(7, transfer.base_local_path, "hbshare")
    # transfer.change_version(7, transfer.base_local_path, "hbshare")
    transfer.install(7, transfer.base_local_path, "hbshare")


if __name__ == "__main__":
    # local_testing()

    log.info("调用 {}".format(sys.argv[1:]))
    transfer = Transfer()
    transfer.call(sys.argv[1:])

"""
pipeline {
    agent any
    stages{
        stage('拉代码') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/py2artifact.py "fetch_code" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME
            }
        }
        stage('升版本') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/py2artifact.py "change_version" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME
            }
        }
        stage('sdist') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x ${WORKSPACE}/code/hbshare/setup.py sdist'
            }
        }
        stage('install') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x ${WORKSPACE}/code/hbshare/setup.py install'
            }
        }
        stage('upload') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x ${WORKSPACE}/code/hbshare/setup.py sdist upload'
            }
        }
        stage('nexus') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x ${WORKSPACE}/code/hbshare/setup.py sdist upload -r nexus'
            }
        }
    }
}
"""
