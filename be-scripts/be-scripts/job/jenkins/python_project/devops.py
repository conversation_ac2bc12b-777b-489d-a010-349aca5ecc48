import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from job.jenkins.python_project import git_file_tools
from utils.test_env.test_env_lib import step_desc
from settings import logger as log, SALT_API, SALT_API_USER, SALT_API_PASSWORD, PUBLISH_TOOL
from common.call_api.saltstack.saltapi import SaltAPI
from dao.get.mysql.ops_type.salt_ops import get_salt_ops
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_remote_cmd
from dao.connect.mysql import DBConnectionManager


class Transfer:
    base_code_path = "code"
    base_artifact_path = "repos"
    salt_cmd_run = "cmd.run"
    code_git_dict = {
        "howbuy-risk-model": "*************************:quant/howbuy-risk-model.git",
        "otc-investor-static": "*************************:otc/otc-investor-static.git",
        "pa-management-sprint-service": "*************************:pa-management/pa-management-sprint.git",
        "param-static": "*************************:tradenew/param-static.git"
    }
    artifact_git_dict = {
        "howbuy-risk-model": "<EMAIL>:/data/git-code/howbuy-risk-model.git",
        "otc-investor-static": "<EMAIL>:/data/git-code/otc-investor-static.git",
        "pa-management-sprint-service": "<EMAIL>:/data/git-code/pa-management-sprint-service.git",
        "param-static": "<EMAIL>:/data/git-code/param-static.git"
    }

    spec_route_dict = {
        "param-static": "dist"
    }

    spec_master_dict = {
        "param-static": "master"
    }

    def __init__(self):
        self.app_name = ''
        self.base_local_path = '/data/temp'

    @step_desc("拉代码")
    def fetch_code(self, build_id, exec_path, app_name, suite_code_list, branch="master"):
        log.info(">>>> BUILD_ID >>>> {}".format(build_id))
        local_path = os.path.join(exec_path, self.base_code_path, app_name)
        git_url = self.code_git_dict[app_name]

        log.info(">>>> 执行目录 {} 代码仓库 >>>> {}".format(local_path, git_url))

        git_file_tools.create_git_local_path_if_not_existed(git_url, local_path)
        self.__update_git_local_current_branch(local_path, branch)

    def __update_git_local_current_branch(self, local_path, branch):
        git_file_tools.update_git_local_execute(local_path)

        cmd = 'cd {local_path} && git checkout -b {branch} origin/{branch}'.format(local_path=local_path, branch=branch)
        log.info(">>>> gene_cmd_get_git_specific_branch_update_existed >>>> {}".format(cmd))
        os.system(cmd)

        cmd = 'cd {local_path} && git checkout {branch}'.format(local_path=local_path, branch=branch)
        log.info(">>>> gene_cmd_get_git_specific_branch_update_existed >>>> {}".format(cmd))
        os.system(cmd)

        git_file_tools.update_git_local_execute(local_path)

    @step_desc("更新上线内容")
    def update_online_content(self, build_id, exec_path, app_name, suite_code_list, branch="master"):
        spec_route = ""
        if app_name in self.spec_route_dict:
            spec_route = self.spec_route_dict[app_name]
        if app_name in self.spec_master_dict:
            branch = "master"

        code_path = os.path.join(exec_path, self.base_code_path, app_name, spec_route)
        artifact_path = os.path.join(exec_path, self.base_artifact_path, app_name)
        git_url = self.artifact_git_dict[app_name]

        log.info(">>>> 执行目录 >>>> {} 制品仓库 >>>> {}".format(artifact_path, git_url))

        git_file_tools.create_git_local_path_if_not_existed(git_url, artifact_path)
        self.__create_repos_specific_branch(artifact_path, branch)
        self.__update_git_local_current_branch(artifact_path, branch)

        if os.path.exists(artifact_path):
            cmd = "cd {artifact_path} && rm * -rf".format(artifact_path=artifact_path)
            log.info(">>>> 执行命令 >>>> {}".format(cmd))
            os.system(cmd)

            cmd = "cp {code_path}/* {artifact_path} -rf".format(code_path=code_path, artifact_path=artifact_path)
            log.info(">>>> 执行命令 >>>> {}".format(cmd))
            os.system(cmd)

    def __create_repos_specific_branch(self, local_path, branch):
        git_file_tools.update_git_local_execute(local_path)

        cmd = 'cd {local_path} && git checkout -b {branch} origin/{branch}'.format(local_path=local_path, branch=branch)
        log.info(">>>> gene_cmd_create_repos_specific_branch >>>> {}".format(cmd))
        os.system(cmd)

        cmd = 'cd {local_path} && git checkout -b {branch}'.format(local_path=local_path, branch=branch)
        log.info(">>>> gene_cmd_create_repos_specific_branch >>>> {}".format(cmd))
        os.system(cmd)

        cmd = 'cd {local_path} && git push origin {branch}'.format(local_path=local_path, branch=branch)
        log.info(">>>> gene_cmd_create_repos_specific_branch >>>> {}".format(cmd))
        os.system(cmd)

        cmd = 'cd {local_path} && git branch --set-upstream {branch} origin/{branch}'.format(local_path=local_path,
                                                                                             branch=branch)
        log.info(">>>> gene_cmd_create_repos_specific_branch >>>> {}".format(cmd))
        os.system(cmd)

    @step_desc("推仓库和产线中转")
    def commit_push_artifact_git(self, build_id, exec_path, app_name, suite_code_list, branch="master"):
        if app_name in self.spec_master_dict:
            branch = "master"

        artifact_path = os.path.join(exec_path, self.base_artifact_path, app_name)

        git_file_tools.commit_push_git_local_execute(artifact_path, build_id, branch)

    @step_desc("产线更新")
    def the_end(self, build_id, exec_path, app_name, suite_code_list, branch="master"):
        publish_info_list = self.__get_app_prod_publish_info(app_name, suite_code_list)

        for publish_info in publish_info_list:
            node_ip = PUBLISH_TOOL['node_ip']
            param = '{{"suite_code": "{}", "app_name": "{}", "ip":"{}", "opt_type":"deploy", "res_type": "pkg", "action_id": "0"}}'.format(publish_info.get("suite_code"), app_name, publish_info.get("ip"))
            log.info("param={}".format(param))
            publish_cmd = "python3.x {} '''{}'''".format(PUBLISH_TOOL['publish_cmd'], param)

            exec_remote_cmd(node_ip, publish_cmd)

    def __get_app_prod_publish_info(self, app_name, suite_code_list):
        sql = '''
                SELECT t.node_ip, s.suite_code FROM env_mgt_node t
                LEFT JOIN env_mgt_node_bind nb ON t.id = nb.node_id
                LEFT JOIN env_mgt_suite s ON nb.suite_id = s.id
                WHERE s.suite_code in ('{}') AND nb.module_name = '{}'
              '''.format("','".join(suite_code_list), app_name)

        with DBConnectionManager() as db:
            db.cur.execute(sql)
        publish_info_list = []
        for row in db.cur.fetchall():
            result = {"ip": row.get("node_ip"), "suite_code": row.get("suite_code")}
            publish_info_list.append(result)
        return publish_info_list

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "fetch_code": self.fetch_code,
            "update_online_content": self.update_online_content,
            "commit_push_artifact_git": self.commit_push_artifact_git,
            "the_end": self.the_end
        }

        suite_code_list = params[4].split(',')

        branch = "master"
        if len(params) > 5:
            branch = params[5]

        acceptor[params[0]](params[1], params[2], params[3], suite_code_list, branch)


def local_testing():
    """
    测试方法
    @return:
    """

    cmd = get_salt_ops("deploy", "prod", "pa-management-sprint-service", "cmd.run")

    transfer = Transfer()
    transfer.base_local_path = "D:\\temp"
    transfer.code_git_dict = {
        "pa-management-sprint-service": "http://dev-gitlab.intelnal.howbuy.com/pa-management/pa-management-sprint.git"
    }

    # transfer.fetch_code(7, transfer.base_path, "pa-management-sprint-service")
    transfer.the_end(7, transfer.base_local_path, "pa-management-sprint-service")


if __name__ == "__main__":
    # local_testing()

    log.info("调用 {}".format(sys.argv[1:]))
    transfer = Transfer()
    transfer.call(sys.argv[1:])

"""


【for FPC till released】, PARAMS: APP_NAME
pipeline {
    agent any
    stages{
        stage('拉代码') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "fetch_code" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME
            }
        }
        stage('更新上线内容') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "update_online_content" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME
            }
        }
        stage('推仓库和产线中转') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "commit_push_artifact_git" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME
            }
        }
        stage('产线更新') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "the_end" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME
            }
        }
    }
}


【for OTC pushed only】, PARAMS: APP_NAME, VERSION
pipeline {
    agent any
    stages{
        stage('拉代码') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "fetch_code" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME + ' ' + VERSION
            }
        }
        stage('更新上线内容') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "update_online_content" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME + ' ' + VERSION
            }
        }
        stage('推仓库和产线中转') {
            steps {
                echo APP_NAME
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/python_project/devops.py "commit_push_artifact_git" ${BUILD_ID} ${WORKSPACE} ' + APP_NAME + ' ' + VERSION
            }
        }
    }
}

"""