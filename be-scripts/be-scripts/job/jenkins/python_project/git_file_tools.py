import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

import codecs
from settings import logger as log


def update_git_local_execute(local_path):
    cmd = 'cd {local_path} && git pull'.format(local_path=local_path)
    log.info(">>>> update_git_local_current_branch >>>> {}".format(cmd))
    os.system(cmd)


def commit_push_git_local_execute(local_path, comment, branch="master"):
    cmd = "cd {local_path} && git add --all && git commit -m '提交变更{comment}' && git push origin {branch}".format(
        local_path=local_path, comment=comment, branch=branch)
    log.info(">>>> 执行命令 >>>> {}".format(cmd))
    os.system(cmd)


def create_git_local_path_if_not_existed(git_url, local_path):
    if not os.path.exists(local_path):
        cmd = gene_cmd_get_git_clone(git_url, local_path)
        os.system(cmd)
    else:
        log.info(">>>> 已经存在目录。如果不是git目录请手工删除！！！")


def gene_cmd_get_git_clone(git_url, local_path):
    cmd = 'git clone {git_url} {local_path}'.format(
        branch_name="master",
        git_url=git_url,
        local_path=local_path)
    log.info(">>>> gene_cmd_get_git_clone >>>> {}".format(cmd))
    return cmd


def read_content_from_file(path, fname):
    return codecs.open(os.path.join(path, fname)).read()


def write_content_into_file(path, fname, content):
    file = codecs.open(os.path.join(path, fname), 'w')
    file.write(content)
    file.close()
