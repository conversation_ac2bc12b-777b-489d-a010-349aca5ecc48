import sys
import os
import json
import traceback

import requests

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from enum import Enum
from settings import logger, INTERFACE_URL, D<PERSON>LE
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd_by_sshpass_tomcat, \
    ssh_path_whether_exist
from ci_pipeline.pipeline_record.pipeline_record import PipelineStatus
from dao.connect.mysql import DBConnectionManager
from webdav3.client import Client


class DfileTypeEnum(Enum):
    NFS = "nfs"
    WEBDAV = "webdav"


class DfileDirCreator:
    def __init__(self, workspace, job_name, job_build_id, app_name_str, suite_code_str):
        self.workspace = workspace
        self.job_name = job_name
        self.job_build_id = job_build_id
        self.app_name_list = app_name_str.split(",") if app_name_str else []
        self.suite_code_list = suite_code_str.split(",") if suite_code_str else []
        self.pipeline_node_dict = {
            "get_dir_info": self.get_dir_info,
            "create_nginx_dir": self.create_nginx_dir,
            "create_webdav_dir": self.create_webdav_dir,
            "create_result": self.create_result
        }

    def get_dir_info(self):
        """
        :param step_param:
        :return:
        """
        try:
            app_list = self.app_name_list if self.app_name_list else self.__get_app_list()
            logger.info("app_list==={}".format(app_list))
            # app_list = ["zeus-service", "adviser-batch-center-remote", "howbuy-fpc-reptile"]
            suite_list = self.suite_code_list if self.suite_code_list else self.__get_suite_list()
            # suite_list = ['it01', 'it02', 'it03']
            logger.info("suite_list==={}".format(suite_list))
            request_address = INTERFACE_URL.get("zeus") + DFILE.get("zeus_api")
            param = {"appNameList": app_list}
            logger.info("=========开始请求宙斯接口=========")
            res = requests.post(url=request_address, data=json.dumps(param),
                                headers={'Content-Type': 'application/json'})
            logger.info("=========结束请求宙斯接口=========")

            logger.info(res.text)
            res_json = res.json()
            nfs_path_list = []
            webdav_path_list = []
            if res_json.get("code") == "200":
                res_data = res_json.get("data")
                for item in res_data:
                    for dfile_info in item.get("dfileList"):
                        dfile_info_path = dfile_info.get("path")
                        if not dfile_info_path:
                            logger.warning("异常数据：{}".format(item))
                            continue
                        if dfile_info.get("type").lower() == DfileTypeEnum.NFS.value:
                            nfs_path_list.append(dfile_info_path)
                        elif dfile_info.get("type").lower() == DfileTypeEnum.WEBDAV.value:
                            webdav_path_list.append(dfile_info_path)
            nfs_absolute_path = []
            webdav_absolute_path = {}
            webdav_absolute_path[1] = []
            for suite_code in suite_list:
                for nfs_path in nfs_path_list:
                    if not nfs_path:
                        continue
                    if nfs_path.endswith('/'):
                        nfs_path = nfs_path[:-1]
                    if nfs_path.startswith('/'):
                        nfs_path = nfs_path[1:]
                    absolute_path = os.path.join('/data/webdav', suite_code, 'files', nfs_path)
                    if absolute_path not in nfs_absolute_path:
                        nfs_absolute_path.append(absolute_path)
                webdav_absolute_path[1].append('/' + suite_code)
                for webdav_path in webdav_path_list:
                    if not webdav_path:
                        continue
                    if webdav_path.endswith('/'):
                        webdav_path = webdav_path[:-1]
                    if webdav_path.startswith('/'):
                        webdav_path = webdav_path[1:]
                    webdav_sub_path_list = self.__get_webdav_sub_path(webdav_path)
                    for webdav_sub_path in webdav_sub_path_list:
                        webdav_sub_absolute_path = os.path.join('/', suite_code, webdav_sub_path)
                        level = len(webdav_sub_absolute_path.split('/')) - 1
                        if level not in webdav_absolute_path:
                            webdav_absolute_path[level] = [webdav_sub_absolute_path]
                        else:
                            if webdav_sub_absolute_path not in webdav_absolute_path[level]:
                                webdav_absolute_path[level].append(webdav_sub_absolute_path)
            logger.info("nfs_absolute_path==={}".format(nfs_absolute_path))
            logger.info("webdav_absolute_path==={}".format(webdav_absolute_path))
            param_dict = {
                "nfs_absolute_path": nfs_absolute_path,
                "webdav_absolute_path": webdav_absolute_path
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + ".json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info("数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + ".json")))

            return PipelineStatus.success, "获取目录信息成功"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_nginx_dir(self):
        """
        清缓存
        :param step_param:
        :return:
        """
        try:
            param_data = self.parse_data()
            nfs_result = []
            nfs_absolute_path_list = param_data.get("nfs_absolute_path")
            for nfs_absolute_path in nfs_absolute_path_list:
                cmd = "mkdir -p {}".format(nfs_absolute_path)
                logger.info("创建目录命令：{}".format(cmd))
                try:
                    exec_local_cmd_by_sshpass_tomcat(DFILE.get("nfs_ip"), cmd)
                except Exception as e:
                    logger.info("创建目录失败！{}".format(str(e)))
                check_result, chk_msg = ssh_path_whether_exist(DFILE.get("nfs_ip"), nfs_absolute_path, is_file=False)
                if not check_result:
                    nfs_result.append(nfs_absolute_path)

            param_dict = {
                "nfs_result": nfs_result
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + "-nfs_result.json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + "-nfs_result.json")))

            return PipelineStatus.success, "创建NFS目录结束"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_webdav_dir(self):
        """
        删除文件
        :param step_param:
        :return:
        """
        try:
            param_data = self.parse_data()
            webdav_absolute_path = param_data.get("webdav_absolute_path")
            sorted_dict = {k: v for k, v in sorted(webdav_absolute_path.items(), key=lambda item: item[0])}

            options = {
                'webdav_hostname': DFILE.get("webdav_host"),
                'webdav_login': DFILE.get("webdav_login"),
                'webdav_password': DFILE.get("webdav_password")
            }
            client = Client(options)
            webdav_result = []
            for level, dir_info in sorted_dict.items():
                for dir_path in dir_info:
                    try:
                        client.mkdir(dir_path)
                    except Exception as e:
                        logger.info("目录{}创建失败！{}".format(dir_path, str(e)))
                    if client.check(dir_path):
                        logger.info("目录{}创建成功！".format(dir_path))
                    else:
                        webdav_result.append(dir_path)
            param_dict = {
                "webdav_result": webdav_result
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + "-webdav_result.json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + "-webdav_result.json")))

            return PipelineStatus.success, "创建webdav目录结束"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_result(self):
        param_data = self.parse_data("nfs_result")
        nfs_result = param_data.get("nfs_result")
        param_data = self.parse_data("webdav_result")
        webdav_result = param_data.get("webdav_result")
        if nfs_result or webdav_result:
            logger.error("nfs_result=={}".format(nfs_result))
            logger.error("webdav_result=={}".format(webdav_result))
            sys.exit(1)
            return PipelineStatus.failure, "创建有误，请检查！"
        else:
            return PipelineStatus.success, "创建目录结束"

    def __get_app_list(self):
        sql = """SELECT DISTINCT t.module_name FROM env_mgt_node_bind t
                INNER JOIN app_mgt_app_module m ON t.module_name = m.module_name
                INNER JOIN app_mgt_app_info i ON m.app_id = i.id
                INNER JOIN app_mgt_app_build b ON b.app_id = i.id
                WHERE t.deploy_type = 2 AND i.third_party_middleware = 0
                      AND b.package_type IN ('jar', 'war', 'tar')
            """
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        app_list = []
        for row in db.cur.fetchall():
            app_list.append(row.get("module_name"))

        return app_list

    def __get_suite_list(self):
        sql = """
                SELECT s.suite_code FROM env_mgt_suite s 
                INNER JOIN env_mgt_suite_use_bind b ON s.suite_code= b.suite_code 
                WHERE s.suite_is_active = 1 AND s.support_docker = 1 AND b.need_dfile_dir = 1;
              """
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        suite_code_list = []
        for row in db.cur.fetchall():
            suite_code_list.append(row.get("suite_code"))

        return suite_code_list

    def __get_webdav_sub_path(self, webdav_path):
        segments = []
        elements = webdav_path.split("/")
        current_path = ""
        for element in elements:
            if not element or element.strip() == "/":
                continue
            current_path += element
            segments.append(current_path)
            current_path += "/"
        return segments

    def parse_data(self, filename=None):
        """
        从缓存文件解析数据
        :return:
        """
        if filename:
            with open(os.path.join(self.workspace, self.job_name + "-{}.json".format(filename)), "r", encoding='utf-8') as f:
                param_dict = json.loads(f.read())
                logger.info("从缓存文件读数据: {}".format(
                    os.path.join(self.workspace, self.job_name + "-{}.json".format(filename))))
            return param_dict
        with open(os.path.join(self.workspace, self.job_name + ".json"), "r", encoding='utf-8') as f:
            param_dict = json.loads(f.read())
            logger.info("从缓存文件读数据: {}".format(os.path.join(self.workspace, self.job_name + ".json")))
        return param_dict

    def run_step(self, node_name):

        exec_status, exec_msg = self.pipeline_node_dict[node_name]()
        return exec_status, exec_msg

    def run(self, node_name):
        """
        运行入口程序
        :param node_name:
        :param step:
        :return:
        """
        self.run_step(node_name)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    business_name = sys.argv[1]
    job_name = sys.argv[2]
    workspace = sys.argv[3]
    job_build_id = sys.argv[4]
    app_name_str = sys.argv[5] if len(sys.argv) > 5 else ""
    suite_code_str = sys.argv[6] if len(sys.argv) > 6 else ""

    tep = DfileDirCreator(workspace, job_name, job_build_id, app_name_str, suite_code_str)
    tep.run(business_name)
