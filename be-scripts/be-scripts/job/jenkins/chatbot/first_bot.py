import json
import sys
import os

import requests

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)


class Bot:
    url = "http://192.168.152.49:8084/chat/ask"
    headers = {
        'Content-Type': 'application/json;charset=utf-8',
        'Access-Control-Allow-Origin': '*'
    }

    def ask(self, condition, question):
        data = {"condition": condition, "question": question}
        try:
            response = requests.post(url=self.url, headers=self.headers, data=json.dumps(data))
            response.encoding = response.apparent_encoding
            res = response.text

        except Exception as e:
            res = ""

        return res


if __name__ == '__main__':
    bot = Bot()
    res = bot.ask("我是张弋翔", "我叫什么名字？")
    print(res)
