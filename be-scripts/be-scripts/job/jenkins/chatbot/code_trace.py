import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from job.jenkins.gitlab_code.data.code_repo_gitlab_file_blame_dao import GitlabFileBlameDao
from job.jenkins.chatbot.first_bot import <PERSON><PERSON>
from settings import logger


class Tracer:

    def __init__(self):
        self.bot = Bot()

    def find_method_implement(self, class_name, method_name):
        logger.info("本次查询{}.{}".format(class_name, method_name))
        file_blame_dao = GitlabFileBlameDao()
        lines = file_blame_dao.query_lines_of_implement_of_api(class_name, method_name)
        condition = "代码片段是："
        for line in lines:
            condition = condition + line.lines

        res = self.bot.ask(condition, '只把这段代码的第一个方法完整截取出来')

        interpret = self.bot.ask(res, '''
        这段代码调用了哪些方法，用以下格式列出。重复的就不要列了。
        1 类名.方法名
        2 类名.方法名
        ''')

        print(res)

        answer = """
        涉及到的外部方法有：
        """ + interpret

        print(answer)
        # print(answer)
        # logger.info(answer)

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))

        acceptor = {
            "find_method_implement": self.find_method_implement,
        }

        acceptor[params[0]](params[1], params[2])


if __name__ == '__main__':
    t = Tracer()
    # t.find_method_implement('ICsTaskDefService', 'queryCsTaskDefAndStatus')
    t.call(sys.argv[1:])
