#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from utils.call_external_interface.call_http_api import SpiderHttpCaller
from dao.connect.mysql import DBConnectionManager
from settings import logger


def convert_data(data1, biz_code, flow_name):
    data2 = {
        "biz_code": biz_code,
        "flow_name": flow_name,
        "flow_content": {}
    }

    flow_content = {}
    for i, item in enumerate(data1['data'], start=1):
        actions = {}
        if item.get('deleteFile'):
            actions[str(len(actions) + 1)] = {
                "action": "delete_file",
                "action_name": "删文件",
                "param": {
                    "file_path": item['filePath']
                }
            }
        if item.get('changeTime'):
            actions[str(len(actions) + 1)] = {
                "action": "set_time",
                "action_name": "改时间",
                "param": {
                    "sys_datetime": item['time']
                }
            }
        if item.get('restartApp'):
            actions[str(len(actions) + 1)] = {
                "action": "restart_app",
                "action_name": "重启",
                "param": {
                    "app_list": item['appLists']
                }
            }
        if item.get('isChecked'):
            actions[str(len(actions) + 1)] = {
                "action": "is_checked",
                "action_name": "检测",
                "param": {
                    "isChecked": ""
                }
            }
        if item.get('wait_check'):
            actions[str(len(actions) + 1)] = {
                "action": "wait_check",
                "action_name": "等一会",
                "param": {
                    "wait_minutes": item['wait_minutes']
                }
            }
        if item.get('runTestSet'):
            actions[str(len(actions) + 1)] = {
                "action": "run_testset",
                "action_name": "测试集",
                "param": {
                    "test_set": item['testSetId']
                }
            }
        if item.get('makeDump'):
            actions[str(len(actions) + 1)] = {
                "action": "make_dump",
                "action_name": "测试集",
                "param": {
                    "dump_file_prefix_name": item['dumpPrefix']
                }
            }

        flow_content[str(i)] = actions

    data2['flow_content'] = flow_content
    return data2


if __name__ == "__main__":
    if len(sys.argv) < 2 or sys.argv[1] is None:
        biz_flow_name = None
    else:
        biz_flow_name = sys.argv[1]
    try:
        sql = "SELECT biz_code, biz_flow_name, biz_pipeline_name FROM biz_test_flow_info"
        if biz_flow_name:
            sql += ' where biz_pipeline_name="{}"'.format(biz_flow_name)

        with DBConnectionManager() as db:
            db.cur.execute(sql)

        for row in db.cur.fetchall():
            biz_pipeline_name = row.get("biz_pipeline_name")

            params = {
                'biz_pipeline_name': biz_pipeline_name
            }

            business_name = "get_biz_pipeline_detail"
            shc = SpiderHttpCaller()
            result = shc.spider_request_get(business_name, params)

            logger.info(result)
            data2 = convert_data(result, row.get("biz_code"), row.get("biz_flow_name"))
            logger.info(data2)

            business_name = "save_biz_pipeline"
            shc = SpiderHttpCaller()
            result = shc.spider_request_post(business_name, data2)
            logger.info(result)
    except Exception as e:
        logger.error(str(e))
        raise e
