# -*- coding: utf-8 -*-
import os
import sys
import stat
import time
import smtplib
import paramiko
import logging
import eventlet
import subprocess
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

eventlet.monkey_patch()
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import SCM_TEST_NODE_INFO_02

fromaddr = '<EMAIL>'
password = 'howbuy!@#'
toaddrs = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']


def send_email(result, br_name, env):
    Url = 'http://***************:8080/jenkins/job/SCM_TEST_PUBLISH_02/'
    Purl = 'http://appdeploy-test.intelnal.howbuy.com/login'
    mail_msg = """
            <!DOCTYPE html>    
    <html>    
    <head>    
    <meta charset="UTF-8">    
    <title>${ENV, var="JOB_NAME"}-SCM_TEST_PUBLISH_02</title>    
    </head>    

    <body leftmargin="8" marginwidth="0" topmargin="8" marginheight="4"    
        offset="0">    
        <table width="95%" cellpadding="0" cellspacing="0"  style="font-size: 11pt; font-family: Tahoma, Arial, Helvetica, sans-serif">    
            <tr>    
                本邮件由系统自动发出，无需回复！<br/>            
                各位同事，大家好，以下为SCM_TEST_PUBLISH_02项目构建信息</br>    
            </tr>    
            <tr>    
                <td><br />    
                <b><font color="#0B610B">构建信息</font></b>    
                <hr size="2" width="100%" align="center" /></td>    
            </tr>    
            <tr>    
                <td>    
                    <ul>    
                        <li>项目名称 ： SCM_TEST_PUBLISH_02 </li>               http://***************:8080/jenkins/view/scm/job/SCM_TEST_PUBLISH/4/console
                        <li>构建  Url ： <a href="http://***************:8080/jenkins/job/SCM_TEST_PUBLISH_02/">http://***************:8080/jenkins/job/SCM_TEST_PUBLISH_02/</a></li>        
                        <li>项目  Url ： <a href="http://***************:8080/jenkins/job/">http://***************:8080/jenkins/job/</a></li>

                    </ul>    
                </td>    
            </tr>    
        </table>    
    </body>    
    </html>
        """
    content = '本邮件由系统自动发出，无需回复！\n各位同事，大家好，以下为SCM_TEST_PUBLISH项目构建信息\n部署平台测试地址:{}\n构建结果:{}\n分支信息：{}\n环境信息：{}\n构建JOB:{}'.format(
        Purl, result, br_name, env, Url)
    textApart = MIMEText(content)
    m = MIMEMultipart()
    m.attach(textApart)
    m['Subject'] = '测试环境部署'
    try:
        server = smtplib.SMTP('smtp.howbuy.com')
        server.login(fromaddr, password)
        server.sendmail(fromaddr, toaddrs, m.as_string())
        print('success')
        server.quit()
    except smtplib.SMTPException:
        print('"Error: 发送邮件失败')


def login(hostname, up_list):
    paramiko.util.log_to_file("upload.log")  # 记录日志文件
    ssh = paramiko.SSHClient()  # 创建ssh对象
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())  # 允许连接不在know_hosts文件中的主机
    ssh.connect(hostname=hostname, port=int(up_list['port']), username=up_list['username'],
                password=up_list['password'])
    return ssh


def sshclient_execmd(execmd, ssh):
    try:
        stdin, stdout, stderr = ssh.exec_command(execmd)
        resulto = stdout.readline()
        resulte = stderr.readline()
        stat = stdout.channel.recv_exit_status()
        logging.info('execmd : %s' % execmd)
        logging.info(resulto)
        if stat != 0:
            pass
            logging.error(resulte)
        else:
            logging.info('Exe %s successful' % execmd)
    except Exception as err:
        print(err)
        exit(1)
    return resulto


def command_exec(cmd):
    logging.info('$ ' + cmd)
    p = subprocess.Popen(cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    build_log = str(p.stdout.read())
    if 'Build failed' in build_log:
        raise KeyError('FAILURE:BUILED failed')


def command_exec_result(cmd):
    print('$ ' + cmd)
    p = subprocess.Popen(cmd, stdin=subprocess.PIPE,
                         stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = p.communicate()
    return p.returncode, stdout, stderr


def is_server(port, path, ssh):
    exec_ps_cmd = 'netstat -nltp | grep %s' % port
    ser = {80: 'node', 9011: 'python'}
    if port == 80:
        logging.info('---------------Start run node server---------------')
        execruncmd = "sh %s/start.sh restart" % path
        sshclient_execmd(execruncmd, ssh)
    elif port == 9011:
        logging.info('---------------Start run django server---------------')
        execruncmd = "python3.x %s/start.py" % path
        # execruncmd = "cd " + path + ' && nohup python3.x /home/<USER>/spider/manage.py runserver 0.0.0.0:9011 &'
        sshclient_execmd(execruncmd, ssh)
    elif port == 9012:
        logging.info('---------------Start run django server---------------')
        execruncmd = "sh %s/start.sh sitrestart" % path
        sshclient_execmd(execruncmd, ssh)


def cover():
    server = '**************'
    ssh = login(server, SCM_TEST_NODE_INFO_02[server])
    # sftp = ssh_put(server, SCM_TEST_NODE_INFO[server])
    execsettingcmd = "find /home/<USER>/be-scripts/ -name generate_config.py"
    cofpath = sshclient_execmd(execsettingcmd, ssh).rstrip('\n')
    print('======================================================')
    print(cofpath)
    targetcofpath = '/home/<USER>/spider/'
    execsettingcmd = 'cp -r {}{}{}'.format(cofpath, ' ', targetcofpath)
    sshclient_execmd(execsettingcmd, ssh)
    print('cp -r {}{}{}'.format(cofpath, ' ', targetcofpath))


def ssh_put(hostname, up_list):
    s = paramiko.Transport((hostname, 22))
    s.connect(username=up_list['username'], password=up_list['password'])
    sftp = paramiko.SFTPClient.from_transport(s)
    return sftp


def build(path, server, env, ssh):
    logging.info(server)
    logging.info('---------------Start npm run build---------------')
    start_time = time.time()
    execnpmcmd = "cd " + path + "&&rm -rf deploy-vue02 &&npm run build:{}".format(env)
    sshclient_execmd(execnpmcmd, ssh)
    # command_exec("cd {} && npm run build:{}".format(path,env))
    end_time = time.time()
    logging.warning("编译时长：{}".format(end_time - start_time))
    execpcmd = "cd " + path + "&&rm -rf /data/app/deploy-vue02&&cp -a deploy-vue/. /data/app/deploy-vue02/"
    sshclient_execmd(execpcmd, ssh)


def deploy_vue_cp(path):
    os.chdir(path)
    command_exec('rm -rf /data/app/deploy-vue02&&cp -a deploy-vue02/. /data/app/deploy-vue02/')


def get_all_files_in_remote_dir(remote_dir, sftp):
    all_files = list()
    if remote_dir[-1] == '/':
        remote_dir = remote_dir[0:-1]
    files = sftp.listdir_attr(remote_dir)
    for x in files:
        filename = remote_dir + '/' + x.filename
        if stat.S_ISDIR(x.st_mode):
            all_files.extend(get_all_files_in_remote_dir(filename, sftp))
        else:
            all_files.append(filename)
    return all_files


def arrange_admin(path, ssh):
    logging.info(path)
    execrmcmd = "rm -rf /data/app/admin-static02"
    sshclient_execmd(execrmcmd, ssh)
    execruncmd = "cd " + path + ' && python3.x manage.py collectstatic '
    sshclient_execmd(execruncmd, ssh)
    execscpcmd = "scp -r /data/app/admin-static/* root@**************:/data/app/admin-static02/"
    sshclient_execmd(execscpcmd, ssh)


def main(br_name, env, apply):
    count = 1
    cover()
    if apply == 'spider' or apply == 'be-scripts':
        server = '**************'
        ssh = login(server, SCM_TEST_NODE_INFO_02[server])
        for path in SCM_TEST_NODE_INFO_02[server]['paths']:
            ser = path.split('/')
            try:
                if apply == 'spider' and apply in ser:
                    logging.info('---------start ************** spider ----------')
                    execheckcmd = "cd " + path + "&& git fetch && git checkout -f {} && git pull".format(br_name)
                    sshclient_execmd(execheckcmd, ssh)
                    try:
                        execsettingcmd = "python3.x /home/<USER>/spider/generate_config.py /home/<USER>/spider/spider/ {} {}".format(
                            br_name, env)
                        sshclient_execmd(execsettingcmd, ssh)
                    except Exception as e:
                        raise Exception("拉取配置失败")
                    arrange_admin(path, ssh)
                    port = 9011
                    is_server(port, path, ssh)
                    logging.info('--------- end ************** spider ----------')
            except Exception as e:
                logging.error(e)
                exit(1)

    elif apply == 'website_new':
        logging.info('---------start 192.168.220.142 website_new ----------')
        server = '192.168.220.142'
        ssh = login(server, SCM_TEST_NODE_INFO_02[server])
        for path in SCM_TEST_NODE_INFO_02[server]['paths']:
            ser = path.split('/')
            if apply in ser:
                execheckcmd = "cd " + path + "&& git fetch && git checkout -f {} && git pull".format(br_name)
                sshclient_execmd(execheckcmd, ssh)
        logging.info('--------- end 192.168.220.142 website_new ----------')
    elif apply == 'deploy_website':
        logging.info('--------- start ************** deploy_website ----------')
        server = '**************'
        ssh = login(server, SCM_TEST_NODE_INFO_02[server])
        for path in SCM_TEST_NODE_INFO_02[server]['paths']:
            ser = path.split('/')
            try:
                if apply in ser:
                    execheckcmd = "cd " + path + "&& git fetch && git checkout -f {} && git pull".format(br_name)
                    sshclient_execmd(execheckcmd, ssh)
                    build(path, server, env, ssh)
            except Exception as e:
                logging.error(e)
                exit(1)
        logging.info('--------- end ************** deploy_website ----------')
    elif apply == 'all':
        for server in SCM_TEST_NODE_INFO_02.keys():
            logging.info(server)
            ssh = login(server, SCM_TEST_NODE_INFO_02[server])
            for path in SCM_TEST_NODE_INFO_02[server]['paths']:
                try:
                    ser = path.split('/')
                    execheckcmd = "cd " + path + "/ && git branch"
                    if '* ' + br_name in sshclient_execmd(execheckcmd, ssh):
                        logging.info('Currently in ' + br_name)
                    else:
                        if server == '**************':
                            execheckcmd = "cd " + path + "&& git fetch && git checkout " + br_name
                        else:
                            execheckcmd = "cd " + path + "&& git fetch && git checkout -f " + br_name
                        sshclient_execmd(execheckcmd, ssh)
                    execmd = "cd " + path + " && git pull "
                    logging.info('Start Update %s' % path)
                    logging.info('execmd : %s' % execmd)
                    sshclient_execmd(execmd, ssh)

                    if 'spider' in ser and server == '**************':
                        logging.info('---------spider settings.ini----------')
                        try:
                            execsettingcmd = "python3.x /home/<USER>/spider/generate_config.py /home/<USER>/spider/spider/ {} {}".format(
                                br_name, env)
                            sshclient_execmd(execsettingcmd, ssh)
                        except Exception as e:
                            raise Exception("拉取配置失败")
                except Exception as e:
                    print(e)
                    exit(1)
                if 'deploy_website' in ser:
                    logging.info('--------- web ----------')
                    port = 80
                    build(path, server, env, ssh)
                    logging.info('--------- build ----------')
                elif 'spider' in ser:
                    logging.info('---------- spider ----------')
                    # arrange_admin(path, ssh)
                    port = 9011
                    execrequirementscmd = "find %s/ -name requirements.txt" % path
                    requirements = sshclient_execmd(execrequirementscmd, ssh).rstrip('\n').split('/')
                    if 'requirements.txt' in requirements:
                        execepipscmd = "/usr/local/python3.x/bin/pip install -r {}/requirements.txt".format(path)
                        sshclient_execmd(execepipscmd, ssh)
                    try:
                        exestart = "python3.x {}/start.py".format(path)
                        sshclient_execmd(exestart, ssh)
                    except Exception as e:
                        logging.error(e)
                        exit(1)
                    # is_server(port, path, ssh)
                elif 'website_new' in ser:
                    logging.info('---------- spider ----------')
                    port = 9012
                    is_server(port, path, ssh)
    ssh.close()


if __name__ == "__main__":
    try:
        result = 'SUCCESS'
        main(sys.argv[1], sys.argv[2], sys.argv[3])
    except Exception as e:
        result = 'FAILED'
    finally:
        send_email(result, sys.argv[1], sys.argv[2])
