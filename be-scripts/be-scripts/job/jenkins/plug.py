import sys
import os
import json
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql import branch_info, member_info, app_info
from settings import logger


class PluginInfoSupplier:
    """
    给jenkins插件提供 分支，应用，人员等信息
    """

    def __init__(self):
        pass

    @staticmethod
    def write_json(data, file_path):
        """
        将数据写入文件
        :param data: 数据
        :param file_path: 文件路径
        :return:
        """
        logger.info(data)
        with open(file_path, "w+") as f:
            f.write(json.dumps(data))

    @staticmethod
    def __file_path(key, workspace, git_group):
        """
        生成文件路径
        :param key: 通过key组合生成文件名
        :param workspace: 文件存放路径
        :param git_group: gitlab组
        :return: .string
        """
        return os.path.join(workspace, "{}_{}.json".format(git_group, key))

    def __produce_branch_info(self, file_path, git_group):
        """
        根据gitlab组生成分支信息
        :param file_path: 文件路径
        :param git_group: gitlab组
        :return:
        """
        br_list = branch_info.get_branch_list(git_group)
        self.write_json(br_list, file_path)

    def __produce_member_info(self, file_path, git_group):
        """
         根据gitlab组生成人员信息
        :param file_path: 文件路径
        :param git_group: gitlab组
        :return:
        """
        member_list = member_info.get_member_list(git_group)
        self.write_json(member_list, file_path)

    def __produce_app_info(self, file_path, git_group):
        """
         根据gitlab组生成应用信息
        :param file_path: 文件路径
        :param git_group: gitlab组
        :return:
        """
        app_list = app_info.get_member_list(git_group)
        self.write_json(app_list, file_path)

    def call(self, params):
        """
        外部调用方法
        :param params: 通过判断params[0],来调用不同的 方法 .list
        :return:
        """
        acceptor = {
            "br_info": self.__produce_branch_info,
            "member_info": self.__produce_member_info,
            "app_info": self.__produce_app_info,
        }
        file_path = self.__file_path(params[0], params[1], params[2])
        logger.info(file_path)
        acceptor[params[0]](file_path, params[2])


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    p_info = PluginInfoSupplier()
    p_info.call(sys.argv[1:])

