#
import os
import sys
import jenkins
import asyncio
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger, JENKINS_INFO
from dao.get.mysql.branch_info import get_open_pipeline_list
from dao.connect.mysql import DBConnectionManager
import xml.etree.ElementTree as ET


class JobPublisher:
    def __init__(self, src_server, target_server, job_list):
        self.src_server = src_server
        self.target_server = target_server
        self.job_name_list = job_list

    def sync_pipeline_job(self):
        template_job_name = 'pipeline_模板'
        job_xml = self.target_server.get_job_config(template_job_name)
        all_jobs = self.target_server.get_jobs()
        all_open_pipeline_list = get_open_pipeline_list()
        for pipeline_id in all_open_pipeline_list:
            for job_info in all_jobs:
                if job_info['fullname'].startswith(pipeline_id):
                    self.target_server.reconfig_job(job_info['fullname'], job_xml)

    def create_pipeline_script(self):
        for job_name in self.job_name_list:
            job_xml = self.src_server.get_job_config(job_name)
            if self.target_server.job_exists(job_name):
                self.target_server.reconfig_job(job_name, job_xml)
            else:
                self.target_server.create_job(job_name, job_xml)


class BatchPublishJenkins:
    def __init__(self, src_job, tgt_pkg_type_list):
        self.h5_ci_pipeline_template = src_job
        self.tgt_pkg_type_list = tgt_pkg_type_list
        logger.info(JENKINS_INFO["URL"])
        self.__jenkins = jenkins.Jenkins(JENKINS_INFO["URL"], username=JENKINS_INFO["USER"],
                        password=JENKINS_INFO["PASSWORD"])
        self.__tgt_jenkins = jenkins.Jenkins("http://jenkinstest-s1.howbuy.pa/jenkins/", username=JENKINS_INFO["USER"],
                        password=JENKINS_INFO["PASSWORD"])
        self.count =0
        self.n = 0

    def get_src_job_xml(self, job_name):
        return self.__jenkins.get_job_config(job_name)

    def get_tgt_job_xml(self, job_name):
        return self.__tgt_jenkins.get_job_config(job_name)

    def get_target_job(self):
        sql = """SELECT CONCAT(i.pipeline_id,"_",i.appName) AS job_name,j.tapd_id,j.pipeline_id,i.appName ,j.create_date
 FROM iter_mgt_iter_app_info i LEFT JOIN iter_mgt_iter_info j ON i.pipeline_id = j.pipeline_id
LEFT JOIN app_mgt_app_module m ON i.appName = m.module_name
LEFT JOIN app_mgt_app_build b ON b.module_name = m.module_name
LEFT JOIN jenkins_mgt_jenkins_job_info jb ON jb.job_name =  CONCAT(i.pipeline_id,"_",i.appName)
WHERE m.need_online = 1 AND j.br_status = "open" AND b.package_type IN
 ("{}") AND jb.jenkins_info_id =3 """.format('","'.join(self.tgt_pkg_type_list))
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            job_list = []
            for row in db.cur.fetchall():
                #logger.info(row["job_name"])
                job_list.append(row["job_name"])
        return job_list

    def set_target_job(self, job_name, job_xml):
        #if self.__jenkins.job_exists(job_name):
        self.__tgt_jenkins.reconfig_job(job_name, job_xml)

    async def get_target_job_task(self, src_script_node, src_properties_node, tgt_job_name):
        if self.__tgt_jenkins.job_exists(tgt_job_name):
            try:
                logger.info("开始更新job {}".format(tgt_job_name))
                tgt_config = self.get_tgt_job_xml(tgt_job_name)
                tgt_root = ET.fromstring(tgt_config)
                tgt_node = tgt_root.find('properties')
                tgt_root.remove(tgt_node)
                tgt_root.insert(3, src_properties_node)

                tgt_definition_node = tgt_root.find("definition")
                # for row in tgt_definition_node.findall("./"):
                #     logger.info(row)
                    # tgt_script_node=tgt_root.find("script")
                    # break
                tgt_script_node = tgt_definition_node.find("script")
                logger.info(tgt_script_node)
                tgt_definition_node.remove(tgt_script_node)
                tgt_definition_node.insert(0, src_script_node)

                # if "JsonSlurperClassic" not in script_node:
                #     script_node = "import groovy.json.JsonSlurperClassic"
                # tgt_xml = ET.tostring(tgt_root, encoding="utf-8").decode("utf-8")
                tgt_bytes = ET.tostring(tgt_root, encoding="utf-8")
                tgt_xml = tgt_bytes.decode("utf-8")
                #logger.info(tgt_xml)
                return tgt_xml
                #logger.info(tgt_xml)
            except Exception as e:
                logger.error(str(e))
                logger.error("解析失败的 job为： {}".format(tgt_job_name))
            # try:
            #     if "//process_product" not in tgt_xml:
            #         tgt_xml_list = tgt_xml.split("//#####")
            #         tgt_xml = "//#####".join(
            #             [tgt_xml_list[0], tgt_xml_list[1], "\n//process_product\n" + tgt_xml_list[2]])
            #         tgt_xml_list = tgt_xml.split("//publish_env")
            #         tgt_xml = "//publish_env".join(
            #             [tgt_xml_list[0] + "\n//process_product\n", tgt_xml_list[1], tgt_xml_list[2]])
            #
            #     scr_per_phase = src_config.split("//#####")[0].split("//bind_suite")[2]
            #     tgt_xml_list = tgt_xml.split("//#####")
            #     # 将bind_suite 到#### 之间的内容 替换，将 ####中间可变的部分 置为空
            #     tgt_xml = "//#####".join(["//bind_suite".join([tgt_xml_list[0].split("//bind_suite")[0],
            #                         tgt_xml_list[0].split("//bind_suite")[1],
            #                         scr_per_phase]), "\n", tgt_xml_list[2]])
            #
            #
            #     # if "+is_junit" not in tgt_xml_list[0]:
            #     #     tgt_xml = "//#####".join([tgt_xml_list[0].replace("+suite_code", "+suite_code+' '+is_junit"),
            #     #                               tgt_xml_list[1], tgt_xml_list[2]])
            #     # if "+is_mock_agent" not in tgt_xml_list[0]:
            #     #     tgt_xml = "//#####".join([tgt_xml_list[0].replace("+is_junit", "+is_junit+' '+is_mock_agent"),
            #     #                               tgt_xml_list[1], tgt_xml_list[2]])
            #     # if "+is_code_scan" not in tgt_xml_list[0]:
            #     #     tgt_xml = "//#####".join([tgt_xml_list[0].replace("+is_mock_agent", "+is_mock_agent+' '+is_code_scan"),
            #     #                               tgt_xml_list[1], tgt_xml_list[2]])
            #
            #     tgt_xml_list= tgt_xml.split("//publish_env")
            #     tgt_xml = "//publish_env".join([tgt_xml_list[0],tgt_xml_list[1], src_config.split("//publish_env")[2]])
            #
            #     tgt_xml_list = tgt_xml.split("//process_product")
            #     tgt_xml = "//process_product".join([tgt_xml_list[0], src_config.split("//process_product")[1], tgt_xml_list[2]])
            #
            #     return tgt_xml
            # except Exception as e:
            #     logger.error(str(e))
            #     logger.error("更新失败的 job为： {}".format(tgt_job_name))
        #self.__jenkins.create_job(tgt_job_name, src_config)
        return ""

    async def set_target_job_task(self, src_script_node, src_properties_node, tgt_job_name):
        tgt_xml = await self.get_target_job_task(src_script_node, src_properties_node, tgt_job_name)
        self.count =self.count + 1
        if tgt_xml:
            self.set_target_job(tgt_job_name, tgt_xml)
            self.n = self.n+1
            return tgt_job_name
        return ""

    async def create_new_job_xml(self):
        src_config = self.get_src_job_xml(self.h5_ci_pipeline_template)

        src_root = ET.fromstring(src_config)
        src_properties_node = src_root.find('properties')


        src_definition_node = src_root.find("definition")
        # for row in tgt_definition_node.findall("./"):
        #     logger.info(row)
        # tgt_script_node=tgt_root.find("script")
        # break
        src_script_node = src_definition_node.find("script")
        logger.info(src_script_node)
        finished, pending = await asyncio.wait([self.set_target_job_task(src_script_node, src_properties_node, tgt_job_name)
                                                for tgt_job_name in self.get_target_job()])

        logger.info([task.result() for task in finished if task.result()])
        logger.info(len(pending))
        logger.info("job 总个数{}".format(self.count))
        logger.info("模板更新个数{}".format(self.n))


if __name__ == "__main__":
    src_job = "pipeline_模板"
    tgt_pkg_type_list = ["war", "jar"]
    import time
    bpj = BatchPublishJenkins(src_job, tgt_pkg_type_list)
    start = time.perf_counter()
    asyncio.run(bpj.create_new_job_xml())
    logger.info(time.perf_counter()-start)

    # res = bpj.get_tgt_job_xml("ftx_0.0.00001_ftx-console-web")
    # logger.info(res)
    # for row in bpj.get_target_job():
    #     logger.info(row)
    #bpj.create_new_job_xml()

    # src_job = "app_android_template"
    # tgt_pkg_type_list = ["android"]
    # bpj = BatchPublishJenkins(src_job, tgt_pkg_type_list)
    # bpj.create_new_job_xml()
    #
    # src_job = "app_ios_template"
    # tgt_pkg_type_list = ["ios"]
    # bpj = BatchPublishJenkins(src_job, tgt_pkg_type_list)
    # bpj.create_new_job_xml()

