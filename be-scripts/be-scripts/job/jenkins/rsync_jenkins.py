import jenkins
import sys
import os
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger,JENKINS_INFO
from job.jenkins.publish_job import JobPublisher

server = jen<PERSON><PERSON>(JENKINS_INFO["URL"], username=JENKINS_INFO["USER"], password=JENKINS_INFO["PASSWORD"])


class JobDuplicator:
    def __init__(self, src_server, target_server):
        self.src_server = src_server
        self.target_server = target_server

    def get_job_list(self):
        job_list = []
        for job in self.src_server.get_all_jobs():
            logger.info(job["name"])
            job_list.append(job["name"])
        return job_list

    def rsync_jenkins(self):
        # 同步job
        jp = JobPublisher(self.src_server, self.target_server, self.get_job_list())
        jp.create_pipeline_script()


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    target_url = sys.argv[1]
    target_server = jenkins.Jenkins("http://"+target_url+":8080/jenkins", username=JENKINS_INFO["USER"], password=JENKINS_INFO["PASSWORD"])
    job_dtr = JobDuplicator(server, target_server)
    job_dtr.rsync_jenkins()
