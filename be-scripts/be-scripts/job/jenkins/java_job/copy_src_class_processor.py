import re
import sys
import os


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from utils.test_env.test_env_lib import step_desc
from settings import logger as log
from dao.get.mysql.iter_info import get_need_jacoco_test, get_app_git_path
from settings import TEST_PUBLISH_AIO as TP_AIO
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd, exec_local_scp_to_remote, \
    ssh_path_whether_exist, exec_local_cmd_by_sshpass_tomcat, exec_remote_cmd


class Copyer:

    @step_desc("拷贝源码字节码")
    def copy_src_class(self, job_name, workspace):
        param_list = job_name.split("_")
        app_name = param_list[-1]
        branch_name = param_list[-2]
        if "@" in workspace:
            workspace = re.sub(r'@.*$', "", workspace)

        if get_need_jacoco_test(app_name):
            app_src_class_cache_path = '{}/{}/{}/'.format(TP_AIO['jacoco_cache_path'], app_name, branch_name)
            app_src_class_target_path = '{}/{}/{}/'.format(TP_AIO['app_src_class_path'], app_name, branch_name)
            app_git_path = get_app_git_path(app_name)
            app_git_dir_name = app_git_path.replace('/', '')
            app_src_class_source_path = workspace + app_git_path
            if not os.path.exists(app_src_class_cache_path):
                cmd = 'mkdir -p {}'.format(app_src_class_cache_path)
                exec_local_cmd(cmd)
            """将编译临时空间的内容过滤后放入临时目录"""
            cmd = "rm -rf {}* && rsync -av --delete --include='*.java' --include='*.class' " \
                  "--exclude={{.git/,\\~/,*.jar,*.pom,*.xml,*.sql,*.properties,*.txt,*.html,*.war,*.lst,*.css,*.png," \
                  "*.js,*.jsp,*.prop,*.sh,*.bat,.gitignore}} {} {}".format(app_src_class_cache_path, app_src_class_source_path, app_src_class_cache_path)
            exec_local_cmd(cmd)
            file_server_ip = TP_AIO['file_server_ip']
            chk_status, chk_msg = ssh_path_whether_exist(file_server_ip, app_src_class_target_path)
            if not chk_status:
                cmd = "mkdir -p {}".format(app_src_class_target_path)
                exec_local_cmd_by_sshpass_tomcat(file_server_ip, cmd)

            """本地压缩"""
            os.chdir(app_src_class_cache_path)
            cmd = "zip -rq {}.zip {}".format(app_git_dir_name, app_git_dir_name)
            exec_local_cmd(cmd)
            """将临时目录的内容拷贝到文件服务器"""
            exec_local_scp_to_remote(file_server_ip, app_src_class_cache_path+'{}.zip'.format(app_git_dir_name), app_src_class_target_path)
            """远程执行解压操作"""
            """与燕伟联调后 去掉远程解压的操作 20211115 by fwm
            cmd = "unzip -o {}{}.zip -d {}".format(app_src_class_target_path, app_git_dir_name, app_src_class_target_path)
            exec_remote_cmd(file_server_ip, cmd)
            """
        else:
            log.info("该应用的代码覆盖率测试开关未打开，如需要，请联系SCM配置！（配置表test_mgt_app_test）")

        return 'success'

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "copy_src_class": self.copy_src_class
        }

        acceptor[params[0]](params[1], params[2])


if __name__ == "__main__":

    log.info("调用 {}".format(sys.argv[1:]))
    Copyer = Copyer()
    Copyer.call(sys.argv[1:])
