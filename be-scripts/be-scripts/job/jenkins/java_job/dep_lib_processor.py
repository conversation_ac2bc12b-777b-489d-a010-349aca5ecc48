import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from utils.test_env.test_env_lib import step_desc
from settings import logger as log
from dao.get.mysql.iter_info import get_iter_dep_repos_without_online_app
from utils.compile.mvn import compiler


class Installer:
    @step_desc("部署纯依赖包")
    def deploy_dep_libs(self, job_name, workspace):
        compile_list = get_iter_dep_repos_without_online_app(job_name)
        log.info('需要额外编译的外部依赖仓库有 {} 个'.format(len(compile_list)))

        for row in compile_list:
            log.info('{} {} {}'.format(row['module_name'], row['pom_path'], row['jdk_version']))

            pom_path = os.path.join(workspace, row['pom_path'])
            jdk_version = row['jdk_version']
            module_name = row['module_name']
            iteration_id = row['pipeline_id']
            if os.path.exists(pom_path):
                log.info('存在 {}'.format(pom_path))
                comp = compiler.Compiler(
                    iteration_id=iteration_id,
                    app_name=module_name,
                    pom_path=pom_path,
                    is_mock_compile=False
                )
                status, output = comp.compile_common()
                if status == 'failure':
                    return 'failure'
                log.info('{} {}'.format(status, output))
            else:
                log.info('不存在 {}'.format(pom_path))

        return 'success'

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "deploy_dep_libs": self.deploy_dep_libs
        }

        acceptor[params[0]](params[1], params[2])


def local_testing():
    return


if __name__ == "__main__":
    # local_testing()

    log.info("调用 {}".format(sys.argv[1:]))
    installer = Installer()
    installer.call(sys.argv[1:])
