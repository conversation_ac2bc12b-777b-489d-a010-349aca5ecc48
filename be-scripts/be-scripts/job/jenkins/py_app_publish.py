import traceback
import logging
import os
import sys
import time

from coreschema import Boolean

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql import common_service_artifactinfo as mysql_common_service_artifactinfo
from settings import PIPELINE_TEST_PUBLISH, DOCKER_SCM_TEST_NODE_INFO
from settings import GITLAB_URL, MIRROR_FACTORY
from test_publish_aio.test_publish_aio_exec import test_publish_aio_util as publish_util
from job.jenkins.nacos_util.generate_config import GenerateConfig
from job.jenkins.py_image_template import uv_dockerfile_template, mantis_start_script, start_image_script
from job.jenkins.scm_test_publish import SSHConnectionManager, Prod_Publish
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd_by_sshpass
from utils.release.java_artifact import JavaArtifact
from dao.get.mysql.app_info import get_iteration_id


class PyAppTestPublish:

    def __init__(self, module_name, br_name, suite_code, delete_image):
        self.module_name = module_name
        self.br_name = br_name
        self.suite_code = suite_code
        self.workspace = "/data/app/{}/".format(self.module_name)
        self.delete_image = delete_image
        self.image_url = DOCKER_SCM_TEST_NODE_INFO[module_name].get('image_url')
        self.image_group = DOCKER_SCM_TEST_NODE_INFO[module_name].get('image_group')
        self.base_image_name = DOCKER_SCM_TEST_NODE_INFO[module_name].get('base_image_name')
        self.base_image_version = DOCKER_SCM_TEST_NODE_INFO[module_name].get('base_image_version')
        self.image_host = DOCKER_SCM_TEST_NODE_INFO[module_name].get('image_host')
        self.image_username = DOCKER_SCM_TEST_NODE_INFO[module_name].get('image_username')
        self.image_password = DOCKER_SCM_TEST_NODE_INFO[module_name].get('image_password')
        self.publish_host = DOCKER_SCM_TEST_NODE_INFO[module_name].get('publish_host')
        self.publish_username = DOCKER_SCM_TEST_NODE_INFO[module_name].get('publish_username')
        self.publish_password = DOCKER_SCM_TEST_NODE_INFO[module_name].get('publish_password')
        self.setting_file = DOCKER_SCM_TEST_NODE_INFO[module_name].get('setting_file')
        self.app_path = os.path.join(self.workspace, (self.suite_code + '_' + self.br_name))


    def pull_zeus_config(self):
        config_path = os.path.join(self.app_path, "conf")
        if not os.path.exists(config_path):
            os.mkdir(config_path)
        else:
            os.chdir(config_path)
            os.system('rm -rf {}/*'.format(config_path))

        gc = GenerateConfig(self.module_name, self.br_name, self.suite_code, config_path, self.setting_file)
        file_path = gc.create_settings()
        logging.info('--------生成配置的文件位置:{}'.format(file_path))

    def push_zeus_config(self):
        logging.info('推送宙斯配置到中转库')
        local_path = os.path.join(self.app_path, "conf")
        config_target_ip = PIPELINE_TEST_PUBLISH["config_map_ip"]
        deploy_config_path = os.path.join(PIPELINE_TEST_PUBLISH['config_map_conf_path'],
                                          self.suite_code,
                                          self.module_name, "conf")
        publish_util.push_local_to_other_rsync(config_target_ip,
                                               local_path,
                                               deploy_config_path,
                                               is_delete=True)

    def pull_code(self, host, username, password):
        try:
            if os.path.exists(self.app_path):
                os.system('rm -rf {}/*'.format(self.app_path))
            rows = mysql_common_service_artifactinfo.get_scm_app()
            code_path = ''
            for row in rows:
                if row['appName'] == self.module_name:
                    code_path = row['gitCodePath']
            if not code_path:
                raise Exception('代码库信息为空')
            code_url = GITLAB_URL + ':' + code_path + '.git'
            local_path = os.path.join(self.app_path, self.module_name)

            cmd = 'git clone -b {branch_name} {code_url} {local_path} '.format(
                branch_name=self.br_name,
                code_url=code_url,
                local_path=local_path)
            logging.info('获取应用代码: {}'.format(cmd))
            os.system(cmd)
            if self.delete_image:
                self.__copy_code_to_image_machine(host, username, password)

        except Exception as e:
            logging.error('拉取代码失败: {}'.format(str(e)))
            raise Exception('拉取代码失败: {}'.format(str(e)))

    def __copy_code_to_image_machine(self, host, username, password):
        local_path = os.path.join(self.app_path, self.module_name)
        # 拷贝 requirements.txt 到远程主机
        # requirements_path = os.path.join(local_path, 'requirements.txt')
        # if os.path.exists(requirements_path):
        with SSHConnectionManager(host, username, password) as ssh:
            remote_path = '/data/app/{}'.format(self.module_name)
            # 确保远程目录存在
            cmd = 'mkdir -p {}'.format(remote_path)
            logging.info('创建远程目录: {}'.format(cmd))
            Prod_Publish.sshclient_execmd(cmd, ssh.SSH)

            # 添加SSH主机密钥到known_hosts
            ssh_scan_cmd = "ssh-keyscan -H {} >> ~/.ssh/known_hosts".format(host)
            logging.info('添加SSH主机密钥: {}'.format(ssh_scan_cmd))
            os.system(ssh_scan_cmd)

            # 使用-o StrictHostKeyChecking=no选项禁用主机密钥检查
            rsync_cmd = "ionice -c3 rsync --delete -LPa --timeout=300 -e 'ssh -o StrictHostKeyChecking=no' {}/* {}@{}:{}/".format(
                local_path,
                username,
                host,
                remote_path)

            logging.info("执行rsync命令:{}".format(rsync_cmd))
            try:
                completed_process_obj1 = exec_local_cmd_by_sshpass(host, rsync_cmd, password)
                logging.info("rsync成功：{}".format(bytes.decode(completed_process_obj1.stdout)))
            except Exception as e:
                logging.error("rsync失败：{}".format(e))
                raise Exception('rsync失败：{}'.format(e))
        # else:
        #     raise Exception('未找到 requirements.txt 文件')

    def create_dockerfile(self, host, username, password):
        logging.info('制作Dockerfile')
        if not self.delete_image:
            logging.info('不删除镜像，跳过制作Dockerfile')
            return
        with SSHConnectionManager(host, username, password) as ssh:
            dockerfile_content = uv_dockerfile_template.format(image_url=self.image_url,
                                          image_group=self.image_group,
                                          image_name=self.base_image_name,
                                          image_version=self.base_image_version,
                                          app_name=self.module_name)
            
            # 将 Dockerfile 内容写入远程主机
            cmd = "cd {} && echo '{}' > Dockerfile".format(self.workspace, dockerfile_content.replace("'", "'\\''"))
            logging.info('写入新的 Dockerfile')
            Prod_Publish.sshclient_execmd(cmd, ssh.SSH)
            
            # 备份新的 Dockerfile 并替换版本号
            cmd = "cd {} && cp Dockerfile Dockerfile.bak && sed -i 's/PA_VERSION/{}/g' Dockerfile".format(
                self.workspace, self.br_name)
            logging.info('备份新的 Dockerfile 并替换版本号: {}'.format(cmd))
            Prod_Publish.sshclient_execmd(cmd, ssh.SSH)

    def make_start_script(self, host, username, password):
        logging.info('制作启动脚本')
        if not self.delete_image:
            logging.info('不删除镜像，跳过制作启动脚本')
            return
        # todo 从git上拉取启动脚本，然后上传到远程主机
        with SSHConnectionManager(host, username, password) as ssh:
            # 检查start.sh文件是否存在，如果存在则删除
            check_cmd = "cd {} && test -f start.sh".format(self.workspace)
            logging.info('检查start.sh文件是否存在: {}'.format(check_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
            if stat == 0:
                logging.info('start.sh文件已存在，准备删除')
                delete_cmd = "cd {} && rm -f start.sh".format(self.workspace)
                logging.info('删除已存在的start.sh文件: {}'.format(delete_cmd))
                Prod_Publish.sshclient_execmd(delete_cmd, ssh.SSH)
            
            start_script_content = mantis_start_script.format(app_name=self.module_name)
            
            # 将启动脚本内容写入远程主机
            cmd = "cd {} && echo '{}' > start.sh".format(self.workspace, start_script_content.replace("'", "'\\''"))
            logging.info('写入启动脚本')
            Prod_Publish.sshclient_execmd(cmd, ssh.SSH)
            
            # 添加执行权限
            cmd = "cd {} && chmod +x start.sh".format(self.workspace)
            logging.info('添加执行权限')
            Prod_Publish.sshclient_execmd(cmd, ssh.SSH)

    def make_image(self, host, username, password):
        logging.info('制作镜像')
        if not self.delete_image:
            logging.info('不删除镜像，跳过制作镜像')
            return
        with SSHConnectionManager(host, username, password) as ssh:
            # 检查requirements.txt文件是否存在
            requirements_cmd = "cd {} && test -f requirements.txt".format(self.workspace)
            logging.info('检查requirements.txt文件是否存在: {}'.format(requirements_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(requirements_cmd, ssh.SSH)
            if stat != 0:
                raise Exception('requirements.txt文件不存在，请确保文件已正确上传')

            # 检查start.sh文件是否存在
            start_script_cmd = "cd {} && test -f start.sh".format(self.workspace)
            logging.info('检查start.sh文件是否存在: {}'.format(start_script_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(start_script_cmd, ssh.SSH)
            if stat != 0:
                raise Exception('start.sh文件不存在，请确保文件已正确上传')
            
            login_cmd = "cd {} && docker login {}".format(self.workspace, self.image_url)
            logging.info('登录镜像仓库: {}'.format(login_cmd))
            Prod_Publish.sshclient_execmd(login_cmd, ssh.SSH)
            
            # 检查镜像是否已存在
            image_name = "{}/{}/{}:{}".format(self.image_url, self.image_group, self.module_name, (self.module_name + "_" + self.br_name))
            check_cmd = "docker images | grep {}".format((self.module_name + "_" + self.br_name))
            logging.info('检查镜像是否存在: {}'.format(check_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
            
            if stat == 0 and resulto:
                if self.delete_image:
                    # 删除None镜像
                    self.__delete_image(ssh)
                    logging.info('镜像已存在，准备删除: {}'.format(image_name))
                    delete_cmd = "docker rmi {}".format(image_name)
                    Prod_Publish.sshclient_execmd(delete_cmd, ssh.SSH)
                    base_image = "{}/{}/{}:{}".format(self.image_url, self.image_group, self.base_image_name, self.base_image_version)
                    logging.info('拉取基础镜像: {}'.format(base_image))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
                    logging.info('检查镜像是否存在stat: {}'.format(stat))
                    logging.info('检查镜像输出结果: {}'.format(resulto))
                else:
                    logging.info('镜像已存在，跳过构建: {}'.format(image_name))
                    return

            build_cmd = "cd {} && docker build --no-cache -t {} .".format(self.workspace, image_name)
            logging.info('制作镜像: {}'.format(build_cmd))
            Prod_Publish.sshclient_execmd(build_cmd, ssh.SSH)

            # 再次检查镜像是否构建成功
            check_cmd = "docker images | grep {}".format((self.module_name + "_" + self.br_name))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
            logging.info('检查镜像是否存在stat: {}'.format(stat))
            logging.info('检查镜像是否存在resulto: {}'.format(resulto))
            logging.info('检查镜像是否存在resulte: {}'.format(resulte))
            if stat != 0 or not resulto:
                raise Exception('镜像构建失败')
            logging.info('镜像构建成功: {}'.format(image_name))

    def push_product(self):
        logging.info('推送到制品库')
        try:
            local_path = os.path.join(self.app_path, self.module_name)
            iteration_id = get_iteration_id(self.module_name, self.br_name)
            java_artifact = JavaArtifact(iteration_id, self.module_name, local_path, self.br_name)
            java_artifact.push_to_repo("推送到制品库")
            
            logging.info('推送制品库完成')
        except Exception as e:
            logging.error('推送制品库失败: {}'.format(str(e)))
            raise Exception('推送制品库失败: {}'.format(str(e)))

    def push_image_product(self, host, username, password):
        logging.info('推送镜像到镜像制品库')
        if not self.delete_image:
            logging.info('不删除镜像，跳过推送镜像到镜像制品库')
            return
        image_name = "{}/{}/{}:{}".format(self.image_url, self.image_group, self.module_name,
                                          (self.module_name + "_" + self.br_name))
        with SSHConnectionManager(host, username, password) as ssh:
            push_cmd = "cd {} && docker push {}".format(self.workspace, image_name)
            logging.info('推送镜像到镜像仓库: {}'.format(push_cmd))
            Prod_Publish.sshclient_execmd(push_cmd, ssh.SSH)

    def push_code_repo(self):
        logging.info('代码推送到中转库')
        local_path = os.path.join(self.app_path, self.module_name)
        deploy_target_ip = PIPELINE_TEST_PUBLISH["mirror_factory_ip"]
        lib_cache_repo_path = os.path.join(MIRROR_FACTORY["MIRROR_PATH"], self.suite_code, self.module_name,
                                           self.module_name)
        logging.info("推送中转库地址 {}".format(lib_cache_repo_path))
        publish_util.push_local_to_other_rsync(deploy_target_ip,
                                               local_path,
                                               lib_cache_repo_path,
                                               is_delete=True)

    def push_image_repo(self, host, username, password):
        if not self.delete_image:
            logging.info('不删除镜像，跳过拉新容器')
            return
        with SSHConnectionManager(host, username, password) as ssh:
            image_script = "docker pull {image_url}/{image_group}/{app_name}:{image_version}".format(
                image_url=self.image_url,
                image_group=self.image_group,
                image_version=(self.module_name + "_" + self.br_name),
                app_name=self.module_name
            )
            logging.info('拉新容器: {}'.format(image_script))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(image_script, ssh.SSH)
            logging.info(stat)
            logging.info(resulto)


    def publish_app(self, host, username, password):
        logging.info('开始部署 {} 应用'.format(self.module_name))
        try:
            image_script = start_image_script.format(image_url=self.image_url,
                                                     image_group=self.image_group,
                                                     image_version=(self.module_name + "_" + self.br_name),
                                                     app_name=self.module_name)
            code_target_path = self.workspace
            code_source_path = os.path.join(self.app_path, self.module_name)
            config_source_path = os.path.join(self.app_path, "conf")
            config_target_path = os.path.join(code_target_path, self.module_name)
            
            with SSHConnectionManager(host, username, password) as ssh:
                # 检查并创建目标目录
                check_dir_cmd = "test -d {}".format(code_target_path)
                logging.info('检查目标目录是否存在: {}'.format(check_dir_cmd))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_dir_cmd, ssh.SSH)
                if stat != 0:
                    logging.info('目标目录不存在，开始创建')
                    create_dir_cmd = "mkdir -p {}".format(code_target_path)
                    logging.info('创建目标目录: {}'.format(create_dir_cmd))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(create_dir_cmd, ssh.SSH)
                    if stat != 0:
                        raise Exception('创建目标目录失败: {}'.format(resulte))
                    logging.info('目标目录创建成功')
                
                # 上传代码文件
                logging.info('开始上传代码文件')
                self.__upload_code_and_config(code_source_path, code_target_path, config_source_path, config_target_path,
                                            host, password, username)

                self.__check_code_and_config(code_target_path, config_target_path, ssh)

                # 检查容器是否存在
                check_container_cmd = "docker ps -a | grep {}".format(self.module_name)
                logging.info('检查容器是否存在: {}'.format(check_container_cmd))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_container_cmd, ssh.SSH)
                
                if stat == 0 and resulto:
                    # 容器存在，执行重启
                    logging.info('容器已存在，执行重启')
                    self.__remove_container(ssh)

                    # 启动新容器
                    logging.info('启动新容器: {}'.format(image_script))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(image_script, ssh.SSH)
                    if stat != 0:
                        raise Exception('启动容器失败: {}'.format(resulte))
                else:
                    # 容器不存在，直接启动
                    logging.info('容器不存在，执行启动')
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(image_script, ssh.SSH)
                    if stat != 0:
                        raise Exception('启动容器失败: {}'.format(resulte))
                
                # 等待30秒，让容器完全启动
                logging.info('等待30秒，让应用完全启动...')
                time.sleep(30)
                
                # 检查容器是否成功启动
                check_container_cmd = "docker ps | grep {}".format(self.module_name)
                logging.info('检查容器是否启动: {}'.format(check_container_cmd))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_container_cmd, ssh.SSH)
                if stat != 0 or not resulto:
                    raise Exception('容器启动失败')
                
                # 检查应用是否正常响应
                check_app_cmd = "curl -s http://{}:8080".format(host)
                logging.info('检查应用是否正常响应: {}'.format(check_app_cmd))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_app_cmd, ssh.SSH)
                if stat != 0 or not resulto:
                    raise Exception('应用未正常响应')
                
            logging.info('应用部署完成')
        except Exception as e:
            logging.error('应用部署失败: {}'.format(str(e)))
            traceback.print_exc()
            raise Exception('应用部署失败: {}'.format(str(e)))

    def __upload_code_and_config(self, code_source_path, code_target_path, config_source_path, config_target_path, host,
                               password, username):
        if not os.path.exists(code_source_path):
            raise Exception('本地代码目录不存在: {}'.format(code_source_path))
        # 使用sshpass和scp上传代码文件
        scp_code_cmd = "sshpass -p '{}' scp -o StrictHostKeyChecking=no -r {}/* {}@{}:{}/".format(
            password, code_source_path, username, host, code_target_path)
        logging.info('执行代码文件上传: {}'.format(scp_code_cmd))
        os.system(scp_code_cmd)
        # 上传配置文件
        logging.info('开始上传配置文件')
        settings_ini_path = os.path.join(config_source_path, 'settings.ini')
        if not os.path.exists(settings_ini_path):
            raise Exception('本地配置文件不存在: {}'.format(settings_ini_path))
        # 使用sshpass和scp上传配置文件
        scp_config_cmd = "sshpass -p '{}' scp -o StrictHostKeyChecking=no {} {}@{}:{}/".format(
            password, settings_ini_path, username, host, config_target_path)
        logging.info('执行配置文件上传: {}'.format(scp_config_cmd))
        os.system(scp_config_cmd)

    def __check_code_and_config(self, code_target_path, config_target_path, ssh):
        # 检查文件是否成功上传
        check_cmd = "test -d {} && ls -l {}".format(code_target_path, code_target_path)
        logging.info('检查文件是否存在: {}'.format(check_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
        if stat != 0 or not resulto:
            raise Exception('文件上传失败或目录为空')
        # 检查关键文件是否存在
        key_files = ['manage.py', 'requirements.txt']
        for file in key_files:
            check_file_cmd = "test -e {}/{}".format(code_target_path, file)
            logging.info('检查关键文件是否存在: {}'.format(check_file_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_file_cmd, ssh.SSH)
            if stat != 0:
                raise Exception('关键文件 {} 不存在'.format(file))
        # 检查配置文件是否存在
        check_settings_cmd = "test -e {}/settings.ini".format(config_target_path)
        logging.info('检查配置文件是否存在: {}'.format(check_settings_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_settings_cmd, ssh.SSH)
        if stat != 0:
            raise Exception('配置文件 settings.ini 不存在')

    def __remove_container(self, ssh):
        stop_cmd = "docker stop {}".format(self.module_name)
        logging.info('停止容器: {}'.format(stop_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(stop_cmd, ssh.SSH)
        if stat != 0:
            raise Exception('停止容器失败: {}'.format(resulte))
        rm_cmd = "docker rm -f {}".format(self.module_name)
        logging.info('删除容器: {}'.format(rm_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(rm_cmd, ssh.SSH)
        if stat != 0:
            raise Exception('删除容器失败: {}'.format(resulte))
        if self.delete_image:
            # 查询镜像ID，并删除镜像
            image_tag = "{}_{}".format(self.module_name, self.br_name)
            self.__delete_image(ssh, image_tag)

    def __delete_image(self, ssh, image_tag=None):
        try:
            if image_tag:
                check_image_cmd = "docker images | grep '{}'".format(image_tag)
                logging.info('查询镜像ID: {}'.format(check_image_cmd))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_image_cmd, ssh.SSH)
                if stat == 0 and resulto:
                    # 解析镜像ID（resulto是列表，取第一个元素）
                    if resulto and len(resulto) > 0:
                        image_line = resulto[0].strip()
                        image_info = image_line.split()
                        if len(image_info) >= 3:
                            image_id = image_info[2]
                            logging.info('找到镜像ID: {}'.format(image_id))

                            # 删除镜像
                            try:
                                delete_image_cmd = "docker rmi -f {}".format(image_id)
                                logging.info('删除镜像: {}'.format(delete_image_cmd))
                                resulto, resulte, stat = Prod_Publish.sshclient_execmd(delete_image_cmd, ssh.SSH)
                                if stat != 0:
                                    logging.warning('删除镜像失败: {}'.format(resulte))
                                else:
                                    logging.info('镜像删除成功')
                            except Exception as e:
                                logging.error('删除镜像失败: {}'.format(str(e)))
                        else:
                            logging.warning('无法解析镜像信息: {}'.format(image_line))
                    else:
                        logging.warning('未找到镜像信息')
            else:
                delete_none_cmd = "docker images -f dangling=true -q | xargs -r docker rmi -f"
                Prod_Publish.sshclient_execmd(delete_none_cmd, ssh.SSH)
        except Exception as e:
            logging.error('删除镜像失败: {}'.format(str(e)))

    def deploy_app(self, op_type):
        '''
        mantis发布到容器
        :param op_type: 操作类型，可选值：
            - pull_code: 拉取代码
            - pull_zeus_config: 拉取宙斯配置
            - create_dockerfile: 创建Dockerfile
            - make_start_script: 创建启动脚本
            - make_image: 构建镜像
            - push_product: 推送到制品库
            - push_repo: 推送到中转库
            - push_zeus_config: 推送宙斯配置
            - publish_app: 发布应用
        '''
        logging.info('发布应用:{}'.format(self.module_name))
        logging.info('发布环境:{}'.format(self.suite_code))
        logging.info('发布分支:{}'.format(self.br_name))

        # 定义操作映射字典
        operation_map = {
            'pull_code': lambda: self.pull_code(self.image_host, self.image_username, self.image_password),
            'pull_zeus_config': self.pull_zeus_config,
            'create_dockerfile': lambda: self.create_dockerfile(self.image_host, self.image_username, self.image_password),
            'make_start_script': lambda: self.make_start_script(self.image_host, self.image_username, self.image_password),
            'make_image': lambda: self.make_image(self.image_host, self.image_username, self.image_password),
            'push_product': self.push_product,
            'push_image_product': lambda: self.push_image_product(self.image_host, self.image_username, self.image_password),
            'push_code_repo': lambda: self.push_code_repo(),
            'push_image_repo': lambda: self.push_image_repo(self.publish_host, self.publish_username,
                                                                  self.publish_password),
            'push_zeus_config': lambda: self.push_zeus_config(),
            'publish_app': lambda: self.publish_app(self.publish_host, self.publish_username, self.publish_password)
        }

        try:
            # 检查操作类型是否有效
            if op_type not in operation_map:
                raise ValueError(f'无效的操作类型: {op_type}')

            # 执行对应的操作
            operation_map[op_type]()
            logging.info(f'操作 {op_type} 执行成功')

        except Exception as e:
            logging.error(f'操作 {op_type} 执行失败: {str(e)}')
            raise


if __name__ == '__main__':
    logging.info("调用 {}".format(sys.argv[1:]))
    op_type = sys.argv[1]
    module_name = sys.argv[2]
    br_name = sys.argv[3]
    suite_code = sys.argv[4]
    delete_image_str = sys.argv[5]
    # 将字符串转换为布尔值
    delete_image = delete_image_str.lower() in ['true', '1', 'yes', 'on']
    # op_type = 'push_zeus_config'
    # module_name = 'mantis'
    # br_name = '2.107.2'
    # suite_code = 'it29'
    app = PyAppTestPublish(module_name, br_name, suite_code, delete_image)
    app.deploy_app(op_type)
