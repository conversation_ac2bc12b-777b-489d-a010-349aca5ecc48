import datetime
import json
import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from job.jenkins.nacos_util.nacos_publisher import NacosPublisher
from dao.get.mysql.iter_info import get_env_info
from settings import logger


def get_sync_env_content():
    env_list = []
    for row in get_env_info():
        env_dict = {
            "id": row["id"],
            "envCode": row["envCode"],
            "envName": row["envName"],
            "tenantId": row["tenantId"],
            "roomCode": row["roomCode"]
        }
        env_list.append(env_dict)

    now = datetime.datetime.now()

    content = "time={}\nenv-info={}".format(now.strftime("%Y-%m-%d %H:%M:%S"), json.dumps(env_list, ensure_ascii=False))

    logger.info(content)
    return content


if __name__ == "__main__":
    data_id = sys.argv[1]
    br_name = sys.argv[2]
    env = sys.argv[3]
    content = get_sync_env_content()
    # np = NacosPublisher("howbuyscm-env-all", "1.0.0", "dev", content)
    np = NacosPublisher(data_id, br_name, env, content)
    np.publish_content_to_nacos()
