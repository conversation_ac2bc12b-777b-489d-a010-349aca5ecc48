import datetime
import json
import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from job.jenkins.nacos_util.nacos_publisher import NacosPublisher
from dao.get.mysql.iter_info import get_app_branch_info
from settings import logger


def get_sync_content(days=0):
    app_info_list = []

    for row in get_app_branch_info(days):
        app_info_dict = {
            "originName": row["team_alias"],
            "appGroup": row["new_group"],
            "name": row["app_name"]
        }
        branch_list = []
        for row1 in row["br_name"].split(","):
            branch_dict = {"iterationNumber": row1}
            branch_list.append(branch_dict)
        app_info_dict.update({"branchList": branch_list})
        app_info_list.append(app_info_dict)

    now = datetime.datetime.now()

    content = "time={}\napp-info={}".format(now.strftime("%Y-%m-%d %H:%M:%S"), json.dumps(app_info_list, ensure_ascii=False))

    logger.info(content)
    return content


if __name__ == "__main__":
    data_id = sys.argv[1]
    br_name = sys.argv[2]
    env = sys.argv[3]
    days = sys.argv[4]
    content = get_sync_content(int(days))
    np = NacosPublisher(data_id, br_name, env, content)
    np.publish_content_to_nacos()