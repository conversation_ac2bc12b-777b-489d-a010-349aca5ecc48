import sys
import os
import nacos
import logging
from settings import TEST_PUBLISH_AIO as TP_AIO

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

SERVER_ADDRESSES = TP_AIO["nacos_base_url"]


class NacosPublisher:
    def __init__(self, data_id, group, env, content):
        """
        发布宙斯配置
        :param data_id: data_id
        :param group: group（版本）
        :param env: 环境名称 test，prod
        :param content: 发布信息
        """
        self.data_id = data_id
        self.group = group
        self.env = env
        self.content = content

    def publish_content_to_nacos(self):
        client = nacos.NacosClient(SERVER_ADDRESSES, namespace=self.env)
        return client.publish_config(self.data_id, self.group, self.content, timeout=300)


if __name__ == "__main__":
    np = NacosPublisher("howbuyscm-app-30", "1.0.0", "dev", "abc")
    np.publish_content_to_nacos()
