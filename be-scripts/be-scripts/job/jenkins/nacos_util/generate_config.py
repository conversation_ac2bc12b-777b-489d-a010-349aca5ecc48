import os
import configparser
import logging
import sys

# 添加调试信息
logging.info("Python path: %s", sys.path)
logging.info("Current working directory: %s", os.getcwd())

# 确保使用系统安装的nacos包
nacos_path = '/usr/local/python3.9/lib/python3.9/site-packages'
if nacos_path not in sys.path:
    sys.path.insert(0, nacos_path)

# 移除当前目录下的nacos模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir in sys.path:
    sys.path.remove(current_dir)

try:
    # 强制重新加载nacos模块
    if 'nacos' in sys.modules:
        del sys.modules['nacos']
    import nacos
    logging.info("Successfully imported nacos module")
    print("Nacos module path:", nacos.__file__)
    # 检查nacos模块的内容
    logging.info("Nacos module contents: %s", dir(nacos))
    
    # 确保使用正确的NacosClient
    if not hasattr(nacos, 'NacosClient'):
        # 尝试从client模块导入
        from nacos.client import NacosClient
        nacos.NacosClient = NacosClient
        logging.info("Successfully imported NacosClient from nacos.client")
except ImportError as e:
    logging.error("Failed to import nacos: %s", str(e))
    raise
except AttributeError as e:
    logging.error("Failed to get NacosClient from nacos module: %s", str(e))
    raise

from settings import TEST_PUBLISH_AIO as TP_AIO

SERVER_ADDRESSES = "hk.nacos.howbuy.com:80"
# SERVER_ADDRESSES = TP_AIO["nacos_base_url"]
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class GenerateConfig:
    def __init__(self, project_name, version, env, path, file_name):
        """
        生成配置文件
        :param project_name: 项目名称
        :param version: 分支版本
        :param env: 环境名称 dev，test，prod
        """
        self.data_id = project_name+".properties"
        self.version = version
        self.env = env
        self.conf = configparser.ConfigParser()
        self.path = path
        self.file_name = file_name

    def get_nacos_config(self):
        try:
            logging.info("Creating NacosClient with server: %s, namespace: %s", SERVER_ADDRESSES, self.env)
            # 使用nacos模块中的NacosClient
            client = nacos.NacosClient(SERVER_ADDRESSES, namespace=self.env)
            logging.info("Getting config for data_id: %s, version: %s", self.data_id, self.version)
            content = client.get_config(self.data_id, self.version)
            if not content:
                logging.info('---------{}----------'.format(SERVER_ADDRESSES))
                raise Exception('宙斯配置不能为空，应用：{}, 分支：{}， 环境：{}'.format(self.data_id, self.version, self.env))
            return content
        except Exception as e:
            logging.error("获取nacos配置失败: %s", str(e))
            raise

    def create_settings(self):
        #self.conf.read(os.path.join(BASE_DIR, "test.ini"))
        group_list = []
        content = self.get_nacos_config()
        for i in content.split("\n"):
            if i.strip() and not i.strip().startswith("#"):
                i_list = i.strip().split("=")
                g_key = i_list[0]
                value = "=".join(i_list[1:])
                group, key = g_key.split(".")
                if group not in group_list:
                    self.conf.add_section(group)
                    group_list.append(group)
                self.conf.set(group, key, value)
                logging.info(f"group={group}, key={key}, value={value}")
        with open(os.path.join(self.path, self.file_name), "w+") as f:
            self.conf.write(f)
        originpath = os.path.join(self.path, self.file_name)
        return originpath


if __name__ == "__main__":
    # workspace = sys.argv[1]
    # module_name = sys.argv[2]
    # br_name = sys.argv[3]
    # suite_code = sys.argv[4]
    # gc = GenerateConfig("mantis", sys.argv[2], sys.argv[3], sys.argv[1])
    gc = GenerateConfig("mantis", "2.107.2", "it29", "./data/", "settings.ini")
    gc.create_settings()
