#! /usr/bin/env python3
# --------------------------------------
# File   : ztst_jenkins_job_delete_and_create_with_copy.py
# Author : zt
# Email  : z<PERSON><PERSON><PERSON><EMAIL>
# History:
#    2025-06-27    Zt    First release.
# --------------------------------------

# 通过条件指定在途迭代，删除并创建新的job。 zt@2025-06-27
# 版本
#     2025-06-27    zt    First release.
# 简写说明：
#     FMT：format
#     TGT：target
# 特别注意：
#     dev: export SCM_BEE_PATH=/workspaces/zt-work/howbuy-py/be-scripts/be-scripts
#     test:
#     prod:
#         export PYTHONPATH=/home/<USER>/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/jenkins_create/
#   mkdir -p ~/data/ztst_logs/jenkins_create/

# ==== 1、环境变量 ====
import os
import sys
import traceback
print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler
FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%d %H:%M:%S'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "jenkins_recreate"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
if not os.path.exists(LOG_PATH):
    os.makedirs(LOG_PATH)
LOG_NAME = "jenkins_recreate.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# 控制台输出（调试时使用）
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
import enum
import datetime
import pymysql
from pymysql.cursors import DictCursor
import json
import requests
import jenkins
from settings import DATABASES
from settings import JENKINS_INFO

@enum.unique
class JenkinsRecreateEnum(enum.Enum):
    JENKINS_RECREATE_ALL = ('jenkins_recreate_all', "批量重建所有在途迭代")
    JENKINS_RECREATE_ONE = ('jenkins_recreate_one', "重建指定job任务")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@enum.unique
class ErrTypeEnum(enum.Enum):
    ERR_TYPE_APP_NONE = ('err_app_none', "无应用的模块信息。")
    ERR_TYPE_LIB_NONE = ('err_lib_none', "制品库信息未配置")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            json_str = obj.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(obj, datetime.date):
            json_str = obj.strftime("%Y-%m-%d")
        else:
            json_str = json.JSONEncoder.default(self, obj)

        return json_str

def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn


def conn_close(conn):
    """mysql和postgresql的数据连接关闭"""
    conn.close()

def get_cost_time(st, et=None):
    """获取耗时，单位「秒」zt@2022-11-09"""
    ct = 0
    if st:
        if not et:
            et = datetime.datetime.now()
        ct = round((et - st).total_seconds(), 3)
    return ct


def get_jenkins_job_info(jenkins_url, job_name):
    """获取Jenkins job信息 zt@2025-06-27"""
    try:
        # 构建API URL
        api_url = jenkins_url.rstrip('/') + "/job/" + job_name + "/api/json"
        # log.info(">>>> 请求Jenkins API: {}".format(api_url))
        
        # 发送GET请求获取job信息
        # 注意：这里可能需要认证，根据实际情况调整
        response = requests.get(api_url, timeout=30)
        
        if response.status_code == 200:
            job_info = response.json()
            return job_info
        elif response.status_code == 404:
            log.warning(">>>> Jenkins job不存在: {}".format(job_name))
            return None
        else:
            log.error(">>>> Jenkins API请求失败，状态码: {}, 响应: {}".format(response.status_code, response.text))
            return None
            
    except requests.exceptions.Timeout:
        log.error(">>>> Jenkins API请求超时: {}".format(api_url))
        return None
    except requests.exceptions.RequestException as e:
        log.error(">>>> Jenkins API请求异常: {} - {}".format(api_url, str(e)))
        return None
    except Exception as e:
        log.error(">>>> 获取Jenkins job信息异常: {}".format(str(e)))
        return None


def rename_jenkins_job_with_library(jenkins_url, old_job_name, new_job_name):
    """使用jenkins库重命名Jenkins job"""
    try:
        # 连接Jenkins服务器
        server = jenkins.Jenkins(
            jenkins_url,
            username=JENKINS_INFO["USER"], 
            password=JENKINS_INFO["PASSWORD"]
        )
        
        # 检查原job是否存在
        if not server.job_exists(old_job_name):
            print(f"原job '{old_job_name}' 不存在")
            return False
            
        # 检查新job名是否已存在
        if server.job_exists(new_job_name):
            print(f"目标job '{new_job_name}' 已存在")
            return False
            
        # 获取原job配置
        config_xml = server.get_job_config(old_job_name)
        
        # 创建新job
        server.create_job(new_job_name, config_xml)
        print(f"成功创建新job: {new_job_name}")
        
        # 删除原job
        server.delete_job(old_job_name)
        print(f"成功删除原job: {old_job_name}")
        
        return True
        
    except Exception as e:
        print(f"重命名job失败: {str(e)}")
        return False


def create_job_from_template(jenkins_url, template_job_name, new_job_name):
    """使用jenkins库从模板复制创建新的Jenkins job"""
    try:
        # 连接Jenkins服务器
        server = jenkins.Jenkins(
            jenkins_url,
            username=JENKINS_INFO["USER"], 
            password=JENKINS_INFO["PASSWORD"]
        )
        
        # 检查模板job是否存在
        if not server.job_exists(template_job_name):
            log.error(f">>>> 模板job '{template_job_name}' 不存在")
            return False
            
        # 检查新job名是否已存在
        if server.job_exists(new_job_name):
            log.warning(f">>>> 目标job '{new_job_name}' 已存在，跳过创建")
            return True
            
        # 获取模板job配置
        config_xml = server.get_job_config(template_job_name)
        
        # 创建新job
        server.create_job(new_job_name, config_xml)
        log.info(f">>>> 成功从模板 '{template_job_name}' 创建新job: {new_job_name}")
        
        return True
        
    except Exception as e:
        log.error(f">>>> 从模板创建job失败: {str(e)}")
        return False



def get_all_remote_iter_app_from_db(conn):
    """获取所有h5-remote的迭代应用信息。zt@2025-06-27"""
    query_sql = '''
        select
            app_i.git_url,
            iter_i.br_name,
            iter_i.project_group,
            iter_i.br_style,
            iter_i.br_start_date,
            iter_i.br_status,
            iter_i.description,
            iter_a.pipeline_id,
            iter_a.appName,
            iter_a.sys_status,
            iter_a.proposer,
            iter_a.pom_path,
            iter_a.git_last_version,
            iter_a.git_repo_version,
            jenkins_srv.jenkins_url
        from app_mgt_app_info app_i
        inner join app_mgt_app_module app_m on app_m.app_id = app_i.id
        inner join app_mgt_app_build app_b on app_b.app_id = app_m.app_id and app_b.module_name = app_m.module_name
        inner join iter_mgt_iter_app_info iter_a on iter_a.appName = app_m.module_name
        inner join iter_mgt_iter_info iter_i on iter_i.pipeline_id = iter_a.pipeline_id and iter_i.br_status = 'open'
        left join jenkins_mgt_jenkins_job_info job_info on job_info.iteration_id = iter_i.pipeline_id
                                                               and job_info.app_name = iter_a.appName
        left join jenkins_mgt_jenkins_info jenkins_srv on jenkins_srv.id = job_info.jenkins_info_id
        where app_b.package_type = 'remote'
          and iter_a.sys_status = '测试中'
        order by app_i.git_url, app_i.app_name, iter_i.pipeline_id, iter_a.appName
        ;
    '''.format()
    log.info(">>>> query_sql：{}".format(query_sql))

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(query_sql)
        result = cursor.fetchall()

    return result


def main(param_jenkins_recreate_enum, param_jenkins_job_name):
    """「job任务重建」主方法 zt@2025-06-27"""
    log.info("======== 开始重建 time(start) {} ========".format(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
    if param_jenkins_recreate_enum == JenkinsRecreateEnum.JENKINS_RECREATE_ALL:
        log.info("==== 『全量重建』：{}".format(param_jenkins_recreate_enum.type_desc))
    else:
        log.info("==== 『单一重建』：{}".format(param_jenkins_job_name))

    scm_conn = connect_scm_mysql()

    try:
        query_st = datetime.datetime.now()
        app_list = get_all_remote_iter_app_from_db(scm_conn)
        query_cost_time = get_cost_time(query_st)
        app_list_count = len(app_list)
        log.info("==== 查询到『{}』笔数据，sql查询耗时：{}秒。".format(app_list_count, query_cost_time))

        clean_st = datetime.datetime.now()
        for app_dict in app_list:
            # log.info(">>>> app_dict = {}".format(json.dumps(app_dict, cls=DateEncoder, sort_keys=True, indent=4, ensure_ascii=False)))
            pipeline_id = app_dict['pipeline_id']
            app_name = app_dict['appName']
            job_name = "{}_{}".format(pipeline_id, app_name)

            jenkins_url = app_dict.get('jenkins_url')
            if jenkins_url:
                jenkins_job_url = jenkins_url + "job/" + job_name
                log.info(">>>> jenkins_req_url = {}".format(jenkins_job_url))
                
                # 获取并打印job信息
                try:
                    job_info = get_jenkins_job_info(jenkins_url, job_name)
                    if job_info:
                        log.info(">>>> job_name: {}".format(job_name))
                        log.info(">>>> job_url: {}".format(job_info.get('url', 'N/A')))
                        log.info(">>>> job_description: {}".format(job_info.get('description', 'N/A')))
                        log.info(">>>> job_buildable: {}".format(job_info.get('buildable', 'N/A')))
                        log.info(">>>> job_color: {}".format(job_info.get('color', 'N/A')))
                        log.info(">>>> job_displayName: {}".format(job_info.get('displayName', 'N/A')))
                        log.info(">>>> job_fullDisplayName: {}".format(job_info.get('fullDisplayName', 'N/A')))
                        log.info(">>>> job_fullName: {}".format(job_info.get('fullName', 'N/A')))
                        
                        # 打印最后构建信息
                        last_build = job_info.get('lastBuild')
                        if last_build:
                            log.info(">>>> lastBuild_number: {}".format(last_build.get('number', 'N/A')))
                            log.info(">>>> lastBuild_url: {}".format(last_build.get('url', 'N/A')))
                        
                        # 打印下次构建编号
                        log.info(">>>> nextBuildNumber: {}".format(job_info.get('nextBuildNumber', 'N/A')))
                        log.info(">>>> ========================================")

                        # 已存在的job，进行重命名：
                        if not job_name.endswith("-bak20250627"):
                            rename_jenkins_job_with_library(jenkins_url, job_name, job_name+"-bak20250627")
                    else:
                        log.warning(">>>> 无法获取job信息: {}".format(job_name))
                        # 如果找不到job，就使用模板「h5_pipeline_template」复制一个。
                        template_name = "h5_pipeline_template"
                        log.info(">>>> 开始从模板 '{}' 创建新job: {}".format(template_name, job_name))
                        create_success = create_job_from_template(jenkins_url, template_name, job_name)
                        if create_success:
                            log.info(">>>> 成功从模板创建job: {}".format(job_name))
                        else:
                            log.error(">>>> 从模板创建job失败: {}".format(job_name))

                except Exception as e:
                    log.error(">>>> 获取job信息出错: {} - {}".format(job_name, str(e)))
            else:
                log.warning(">>>> jenkins_url为空，跳过job: {}".format(job_name))

        clean_cost_time = get_cost_time(clean_st)
        log.info("==== 重建所有的ramdisk耗时：{}秒。".format(clean_cost_time))

    except Exception as e:
        exec_err_msg = ">>>> 重建job出错：{}".format(e)
        log.error(exec_err_msg)
        raise e
    finally:
        conn_close(scm_conn)


if __name__ == '__main__':
    """主入口，先判断参数"""
    req_start_time = datetime.datetime.now()
    # 请求参数处理
    req_jenkins_recreate_enum = JenkinsRecreateEnum.JENKINS_RECREATE_ALL
    req_jenkins_job_name = "ALL"
    if len(sys.argv) >= 1:
        if len(sys.argv) > 1:
            req_jenkins_job_name = sys.argv[1]
        if req_jenkins_job_name.upper() == 'ALL':
            req_jenkins_recreate_enum = JenkinsRecreateEnum.JENKINS_RECREATE_ALL
        else:
            req_jenkins_recreate_enum = JenkinsRecreateEnum.JENKINS_RECREATE_ONE
    if len(sys.argv) > 2:
        log.error("==== ！！！！参数过多！！！！ ====")
        err_msg = ">>>> 一次只能传入一个「应用」！！！"
        log.error(err_msg)
        exit(1)
    try:
        main(req_jenkins_recreate_enum, req_jenkins_job_name)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        traceback_str = traceback.format_exc()
        err_msg = ">>>> 执行异常(Exception) {}".format(traceback_str)
        log.error(err_msg)
        exit(1)
    finally:
        req_cost_time = get_cost_time(req_start_time)
        log.info("== 执行最终耗时（秒）：{}".format(req_cost_time))
        log.info("====================")
        log.info("====================\n\n")
