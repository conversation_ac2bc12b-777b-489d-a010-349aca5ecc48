import sys
import logging
import requests
import os
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from utils.call_external_interface.call_http_api import SpiderHttpCaller
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd
from common.common_tool.common_tool import clean_dir
from settings import JENKINS_INFO
import jenkins

class CheckServiceHealth:
    def __init__(self):
        self.spider_login_interface = "http://appdeploy-test.howbuy.pa/spider/user/login"
        self.mantis_login_interface = "http://mantis.howbuy.pa/mantis/user/login"
        self.git_url = "*************************:scm/spider.git"
        self.code_path = "/data/scm_check/checkgit"
        self.ngnix_path = "/data/scm_check/checkngnix"
        self.nexus_path = "/data/scm_check/checknexus"
        self.jenkins_url = ['http://jkp-master.howbuy.pa/jenkins/','http://jkp-s1.howbuy.pa/jenkins/','http://jkp-s2.howbuy.pa/jenkins/']
        self.spider_interface = "http://appdeploy-test.howbuy.pa/spider/iter_mgt/pipeline"
        self.mantis_interface = "http://mantis.howbuy.pa/mantis/code_quality/p3c_entrance_guard"

    def call_spider_interface(self ):
        logging.info('gggggggggggggggggggggggggggg')
        logging.info(self.spider_login_interface)
        login_res = requests.get(self.spider_login_interface, params={"userName": JENKINS_INFO["USER"], "password": JENKINS_INFO["PASSWORD"]})
        res = requests.get(self.spider_interface,params={"pipeline_id": "AMS_ams-20230412-ysgl"})
        logging.info(login_res.status_code)
        logging.info(res.status_code)
        if login_res.status_code == 200 and res.status_code == 200:
            logging.info("spider服务正常")
        else:
            raise Exception("spider服务不正常")


    def call_mantis_interface(self):
        logging.info('gggggggggggggggggggggggggggg')
        logging.info(self.mantis_login_interface)
        login_res = requests.get(self.mantis_login_interface, params={"userName": JENKINS_INFO["USER"], "passWord": JENKINS_INFO["PASSWORD"]})
        logging.info(login_res)
        res = requests.get(self.mantis_interface, params={"app_name": "howbuy-cachemanagement-tester-entrance",
                                                                "iteration_id": "cache_3.6.0",
                                                                "lib_type":"p3c",
                                                                "lib_md5":"685e8c9b384eae4db1ec57a079037e30",
                               "lib_url":"http://nginx-lib.howbuy.pa/report-lib/test_report/p3c/howbuy-cachemanagement-tester-entrance/cache_3.6.0/p3c_cache_3.6.0_howbuy-cachemanagement-tester-entrance_20230627141556.csv",
                               "last_arc_iteration_id":"cache_3.5.1",
                                                                "appName" : "howbuy-cachemanagement-tester-entrance"})
        logging.info(res)
        if login_res.status_code == 200 and res.status_code == 200:
            logging.info("mantis服务正常")
        else:
            raise Exception("mantis服务不正常")

    def check_git(self):
        if os.path.isdir(self.code_path):
            if len(self.code_path) > 3 and os.path.exists(self.code_path):
                exec_local_cmd('rm -rf {}'.format(self.code_path))
            else:
                logging.error('删除的目录长度小于3,或目录不存在，请人工check{}'.format(self.code_path))
        else:
            cmd = 'mkdir -p {}'.format(self.code_path)
            exec_local_cmd(cmd)
        completed_process_obj = exec_local_cmd('git clone {} {}'.format(self.git_url, self.code_path))
        logging.info(completed_process_obj.returncode)
        db_dir_list = [f.path for f in os.scandir(self.code_path) if f.is_dir()]
        if len(db_dir_list) > 0 and completed_process_obj.returncode == 0:
            logging.info('git检测成功')
        else:
            raise Exception("git检测失败")


    def check_ngnix(self):
        if os.path.isdir(self.ngnix_path):
            if len(self.code_path) > 3 and os.path.exists(self.ngnix_path):
                exec_local_cmd('rm -rf {}'.format(self.ngnix_path))
            else:
                logging.error('删除的目录长度小于3,或目录不存在，请人工check{}'.format(self.ngnix_path))

        cmd = 'mkdir -p {}'.format(self.ngnix_path)
        exec_local_cmd(cmd)
        cmd = "cd {} && wget http://nginx-lib.howbuy.pa/h5_pkg/fundzip/h5-project-230303-tax-delay_it06/h5-project-230303-tax-delay.zip".format(self.ngnix_path)
        completed_process_obj = exec_local_cmd(cmd)
        db_dir_list = [f.path for f in os.scandir(self.ngnix_path)]
        logging.info(completed_process_obj.returncode)
        logging.info(db_dir_list)
        if len(db_dir_list) > 0 and completed_process_obj.returncode == 0:
            logging.info('git检测成功')
        else:
            raise Exception("git检测失败")

    def check_nexus(self):
        if os.path.isdir(self.nexus_path):
            if len(self.code_path) > 3 and os.path.exists(self.nexus_path):
                exec_local_cmd('rm -rf {}'.format(self.nexus_path))
            else:
                logging.error('删除的目录长度小于3,或目录不存在，请人工check{}'.format(self.nexus_path))
        cmd = 'mkdir -p {}'.format(self.nexus_path)
        exec_local_cmd(cmd)
        cmd = "cd {} && wget http://mvn.intelnal.howbuy.com/nexus/content/groups/public/org/codehaus/plexus/plexus-classworlds/2.2.3/plexus-classworlds-2.2.3.jar".format(self.nexus_path)
        completed_process_obj = exec_local_cmd(cmd)
        db_dir_list = [f.path for f in os.scandir(self.nexus_path)]
        logging.info(completed_process_obj.returncode)
        logging.info(db_dir_list)
        if len(db_dir_list) > 0 and completed_process_obj.returncode == 0:
            logging.info('git检测成功')
        else:
            raise Exception("git检测失败")

    def check_jenkins(self):
        self.jenkins_url = self.jenkins_url
        for item in self.jenkins_url:
            server = jenkins.Jenkins(item, username=JENKINS_INFO["USER"], password=JENKINS_INFO["PASSWORD"])
            logging.info(server)
        # self.jenkins_info_id = 0


    def call(self, params):
        acceptor = {
            "call_spider_interface": self.call_spider_interface,
            "call_mantis_interface": self.call_mantis_interface,
            "check_git": self.check_git,
            "check_ngnix": self.check_ngnix,
            "check_jenkins": self.check_jenkins,
            "check_nexus": self.check_nexus
        }

        acceptor[params[0]]()

if __name__ == "__main__":
    checkServiceHealth = CheckServiceHealth()

    checkServiceHealth.call(sys.argv[1:])
    logging.info(sys.argv[1:])
    # spider_param = {"userName": "wei.liu", "password": "lw202032_"}JENKINS_INFO["USER"] JENKINS_INFO["PASSWORD"]
    # mantis_param = {"userName": "wei.liu", "passWord": "lw202032_"}
    # checkServiceHealth.call(['call_interface','http://appdeploy-test.howbuy.pa/spider/user/login',{"userName": "wei.liu", "password": "lw202032_"},'liuwei'])
    # checkServiceHealth.call(['call_interface', 'http://mantis.howbuy.pa/mantis/user/login',
    #                          {"userName": "wei.liu", "passWord": "lw202032_"}, 'liuwei'])
    # checkServiceHealth.call(['call_interface', 'http://mantis.howbuy.pa/mantis/code_quality/p3c_entrance_guard',
    #                          {"app_name": "howbuy-cachemanagement-tester-entrance", "iteration_id": "cache_3.6.0","lib_type":"p3c","lib_md5":"685e8c9b384eae4db1ec57a079037e30",
    #                           "lib_url":"http://nginx-lib.howbuy.pa/report-lib/test_report/p3c/howbuy-cachemanagement-tester-entrance/cache_3.6.0/p3c_cache_3.6.0_howbuy-cachemanagement-tester-entrance_20230627141556.csv",
    #                           "last_arc_iteration_id":"cache_3.5.1","appName" : "howbuy-cachemanagement-tester-entrance"}, 'liuwei'])
