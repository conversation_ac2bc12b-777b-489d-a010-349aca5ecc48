import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from urllib3.exceptions import InsecureRequestWarning
from settings import logger as log
import requests
import json
from job.jenkins.info_warning.zepplin_reporter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er
from job.jenkins.info_warning.abstract_warner import AWarner

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class WechatRobotWarning(AWarner):
    def __init__(self):
        self.base_wechat_robot_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key='
        self.template_text = '''
        {
            "msgtype": "text", 
            "text": 
                {
                    "content": "$ccc", 
                    "mentioned_list":[$mmm]
                }
        }
        '''
        return

    def process(self, notes_str):
        e = json.loads(notes_str)
        model = WechatRobotWarningModel(e.get('key'), e.get('note_id'), e.get('note_column'),
                                        e.get('content_note_column'), e.get('list_str'), e.get('content'))

        if model.who_care_note_id:
            zr = ZepplinReporter()
            zr.runjob(model.who_care_note_id)
            table_data_rows = zr.get_table_data_rows_from_server(model.who_care_note_id)

        if model.who_care_note_column:
            names = zr.get_val_list_from_column(table_data_rows, model.who_care_note_column)
            if names is None or len(names) == 0 or (len(names) == 1 and names[0] == ''):
                log.info("指定了动态获取发送者但没找到人所以不发了！")
                return
            names = list(set(names))
            for name in names:
                if name != "":
                    model.who_care_list_str = "\"{}\",{}".format(name, model.who_care_list_str)

        if model.content_note_column:
            contents = zr.get_val_list_from_column(table_data_rows, model.content_note_column)
            if contents and contents[0] != '':
                model.content = "{}***{}".format(model.content, contents[0])

        self._warn(model.wechat_robot_key, model.who_care_list_str, model.content)

    def _warn(self, key, who_care_list, content):
        url = "{}{}".format(self.base_wechat_robot_url, key)
        json_content = self.template_text.replace("$ccc", content).replace("$mmm", who_care_list)
        log.info("本次发送参数{}".format(json_content))
        try:
            a = requests.post(url=url, json=json.loads(json_content))
            log.info(a)
        except Exception as e:
            log.error(str(e))

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "process": self.process
        }

        acceptor[params[0]](params[1])


class WechatRobotWarningModel:

    def __init__(self, wechat_robot_key, who_care_note_id, who_care_note_column, content_note_column, who_care_list_str,
                 content):
        self.wechat_robot_key = wechat_robot_key
        self.who_care_note_id = who_care_note_id
        self.who_care_note_column = who_care_note_column
        self.content_note_column = content_note_column
        self.who_care_list_str = who_care_list_str if who_care_list_str else "\"yixiang.zhang000\""
        self.content = content


def local_testing():
    mw = WechatRobotWarning()
    # mw.process('{"key": "c9a0b649-a5e0-4950-8c12-9bc1eef3df43", "note_id": "2HQWQQSRN", "note_column": "分支负责人", "list_str": "\\"yixiang.zhang\\",\\"shuai.liu\\"", "content": "你对我刚刚满意吗？我对自己的表现挺满意的！！！"}')
    mw.process(
        '{"key": "20730d78-9fb6-4658-8bbb-cd9327da96c0", "note_id": "2HTKWK252", "note_column": "负责人", "content_note_column": "内容", "content": "宙斯归档问题"}')


if __name__ == "__main__":
    # local_testing('''
    # {
    # "subject": "【DevOps提醒】构建耗时分位统计-近一周",
    # "note_id": "2H5FU5CAU",
    # "mail_to": "<EMAIL>",
    # "description": "【中位数预警值】是判定执行耗时是否满足要求的指标。<br/>处理机制：连续两周同一项超标(爆红)，或单周超过指标≥30%，平台介入调查处理。否则会再观测一周。<br/>如果50分位值在范围内，但体验仍觉得慢，可以向平台提出调整【中位数预警值】。"
    # }
    # ''')
    mw = WechatRobotWarning()
    mw.call(sys.argv[1:])
    # local_testing()

"""
pipeline {
    agent any
    stages{
        stage('测试用例-R') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/wechat_robot_warning.py 'process' 
                '{"key": "20730d78-9fb6-4658-8bbb-cd9327da96c0", "note_id": "2GU4YXR1H", "list_str": "\\"yixiang.zhang\\",\\"shuai.liu\\"", "content": "你对我刚刚满意吗？我对自己的表现挺满意的！！！"}' '''
            }
        }
        stage('分支提醒-R') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/wechat_robot_warning.py 'process' 
                '{"key": "20730d78-9fb6-4658-8bbb-cd9327da96c0", "note_id": "2HQWQQSRN", "note_column": "分支负责人", "content": "你对我刚刚满意吗？我对自己的表现挺满意的！！！"}' '''
            }
        }
        stage('归档问题-R') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/wechat_robot_warning.py 'process' 
                '{"key": "20730d78-9fb6-4658-8bbb-cd9327da96c0", "note_id": "2HTKWK252", "note_column": "负责人", "content_note_column": "内容", "content": "宙斯归档问题"}' '''
            }
        }
    }
}
"""
