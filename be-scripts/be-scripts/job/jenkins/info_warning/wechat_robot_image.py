import base64
import hashlib
import sys
import os
import time

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

import traceback
from urllib3.exceptions import InsecureRequestWarning
from settings import logger as log
import requests
import json
from job.jenkins.info_warning.zepplin_reporter import <PERSON><PERSON><PERSON>lin<PERSON><PERSON><PERSON>er
from selenium import webdriver
from selenium.common.exceptions import WebDriverException
from PIL import Image

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class WechatRobotImage():
    def __init__(self):
        self.zr = ZepplinReporter()
        self.base_wechat_robot_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key='
        self.base_note_url = 'http://paas.hongkou.howbuy.com/report/#/notebook/{}/paragraph/{}?asIframe'
        self.template_text = '''
        {
           "msgtype": "image",
          "image": {
              "base64": "$ccc",
                "md5": "$mmm"
               }
        }
        '''
        return

    def process(self, notes_str, workspace):
        e = json.loads(notes_str)
        model = WechatRobotImageModel(e.get('key'), e.get('note_id'), e.get('paragraph'), workspace, e.get('upper'),
                                      e.get('lower'))
        self.zr.runjob(model.note_id)
        local_filename = os.path.join(workspace, '{}.png'.format(model.note_id))
        self.do_screenshot(model.note_id, model.paragraph, local_filename)
        self.crop_image(local_filename, model.upper, model.lower)
        data, md5 = self.convert_image_2_base64(local_filename)
        self._warn(model.wechat_robot_key, md5, data)

    def _warn(self, key, md5, data):
        url = "{}{}".format(self.base_wechat_robot_url, key)
        json_content = self.template_text.replace("$ccc", data).replace("$mmm", md5)
        try:
            a = requests.post(url=url, json=json.loads(json_content))
            log.info(a)
        except Exception as e:
            log.error(str(e))

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "process": self.process
        }

        acceptor[params[0]](params[1], params[2])

    def do_screenshot(self, note_id, paragraph, local_file):
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')  # Chrome最新驱动无效，可以使用firefox或phantomjs
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('window-size=1920x1080')

        full_path = self.base_note_url.format(note_id, paragraph)

        try:
            driver = webdriver.Chrome(options=options)
            driver.maximize_window()
            driver.set_window_size(1280, 960)
            driver.get(full_path)
            print("截图路径{}".format(full_path))
            time.sleep(20)
            # os.remove(os.path.join(workspace, ''.format()))
            driver.get_screenshot_as_file(local_file)
            driver.save_screenshot(local_file)
            print("存储路径{}".format(local_file))
            driver.quit()
            print("截图成功")

        except WebDriverException as e:
            print("截图失败")
            print(e)
            traceback.print_exc()

    def crop_image(self, filename, upper, lower):
        img = Image.open(filename)
        cropped = img.crop((0, upper, 1280, lower))  # (left, upper, right, lower)
        cropped.save(filename)

    def convert_image_2_base64(self, filename):
        with open(filename, 'rb') as f:
            image = f.read()
        md5 = hashlib.md5(image).hexdigest()
        data = base64.b64encode(image)
        return data.decode('utf-8'), md5

    def testing(self):
        self.do_screenshot()
        self.crop_image('D:\\test-canvas.png')
        data, md5 = self.convert_image_2_base64('D:\\test-canvas.png')
        # self._warn('c9a0b649-a5e0-4950-8c12-9bc1eef3df43', md5, data)


class WechatRobotImageModel:

    def __init__(self, wechat_robot_key, note_id, paragraph, workspace, upper=0, lower=600):
        self.wechat_robot_key = wechat_robot_key
        self.note_id = note_id
        self.paragraph = paragraph
        self.workspace = workspace
        self.upper = int(upper) if upper else 0
        self.lower = int(lower) if lower else 600


def local_testing():
    mw = WechatRobotImage()
    mw.process(
        '{"key": "c9a0b649-a5e0-4950-8c12-9bc1eef3df43", "note_id": "2HRQB25Z1", "paragraph": "20230117-181405_367728624"}',
        'D:\\')
    # mw.process('{"key": "c9a0b649-a5e0-4950-8c12-9bc1eef3df43", "note_id": "2HQWQQSRN", "note_column": "分支负责人", "list_str": "\\"yixiang.zhang\\",\\"shuai.liu\\"", "content": "你对我刚刚满意吗？我对自己的表现挺满意的！！！"}')


if __name__ == "__main__":
    # local_testing('''
    # {
    # "subject": "【DevOps提醒】构建耗时分位统计-近一周",
    # "note_id": "2H5FU5CAU",
    # "mail_to": "<EMAIL>",
    # "description": "【中位数预警值】是判定执行耗时是否满足要求的指标。<br/>处理机制：连续两周同一项超标(爆红)，或单周超过指标≥30%，平台介入调查处理。否则会再观测一周。<br/>如果50分位值在范围内，但体验仍觉得慢，可以向平台提出调整【中位数预警值】。"
    # }
    # ''')
    mw = WechatRobotImage()
    mw.call(sys.argv[1:])
    # local_testing()

"""
pipeline {
    agent any
    stages{
        stage('测试-IMG') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/wechat_robot_image.py 'process' 
                '{"key": "c9a0b649-a5e0-4950-8c12-9bc1eef3df43", "note_id": "2HRQB25Z1", "paragraph": "20230117-181405_367728624"}' ${WORKSPACE} '''
            }
        }
    }
}
"""
