import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from urllib3.exceptions import InsecureRequestWarning
from settings import logger as log
import requests
import json
from job.jenkins.info_warning.zepplin_reporter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>orter
from job.jenkins.info_warning.abstract_warner import AWarner

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class WechatRobotMarkdown(AWarner):
    def __init__(self):
        self.base_wechat_robot_url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key='
        self.template_text = '''
        {
            "msgtype": "markdown",
            "markdown": {
                "content": "# ccc\\nlll"
            }
        }
        '''
        self.template_line = "\n>## ttt：<font color=\\\"sss\\\">vvv</font>"
        self.zr = ZepplinReporter()
        return

    def process(self, notes_str):
        e = json.loads(notes_str)
        model = WechatRobotMarkdownModel(e.get('key'), e.get('note_id'), e.get('content'))

        if model.note_id and model.title_note_column and model.value_note_column:
            lines_str = self._get_lines_str(model)
            if lines_str == '':
                return
            json_content = self.template_text.replace("ccc", model.content).replace("lll", lines_str)
            self._warn(model.wechat_robot_key, json_content)
        else:
            return

    def real_sending_content(self, header, lines):
        lines_str = ""
        if lines:
            for i in range(0, len(lines)):
                lines_str = lines_str + lines[i]

        return self.template_text.replace("ccc", header).replace("lll", lines_str)

    def real_line_str(self, title, value, style='comment'):
        line_str = self.template_line.replace("ttt", title).replace("sss", style).replace(
            "vvv", value)
        return line_str

    def _get_lines_str(self, model):
        self.zr.runjob(model.note_id)
        table_data_rows = self.zr.get_table_data_rows_from_server(model.note_id)
        titles = self.zr.get_val_list_from_column(table_data_rows, model.title_note_column)
        values = self.zr.get_val_list_from_column(table_data_rows, model.value_note_column)
        styles = self.zr.get_val_list_from_column(table_data_rows, model.style_note_column)

        lines_str = "";
        if titles and values:

            for i in range(0, len(titles)):
                if titles[i] == '':
                    continue
                style = 'comment'
                if styles and len(styles) > i:
                    style = style if styles[i] is None or styles[i] == '' else styles[i]
                lines_str = lines_str + self.template_line.replace("ttt", titles[i]).replace("sss", style).replace(
                    "vvv", values[i])

        return lines_str

    def _warn(self, key, json_content):
        url = "{}{}".format(self.base_wechat_robot_url, key)
        log.info("本次发送参数{}".format(json_content))
        try:
            a = requests.post(url=url, data=json_content.encode('utf-8'))
            log.info(a)
        except Exception as e:
            log.error(str(e))

    def warn(self, key, content):
        self._warn(key, content)

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "process": self.process
        }

        acceptor[params[0]](params[1])


class WechatRobotMarkdownModel:

    def __init__(self, wechat_robot_key, note_id, content, title_note_column="title", value_note_column="value",
                 style_note_column="style"):
        self.wechat_robot_key = wechat_robot_key
        self.note_id = note_id
        self.content = content
        self.title_note_column = title_note_column
        self.value_note_column = value_note_column
        self.style_note_column = style_note_column


def local_testing():
    md = WechatRobotMarkdown()
    # md.process('{"key": "c9a0b649-a5e0-4950-8c12-9bc1eef3df43", "note_id": "2HQWQQSRN", "note_column": "分支负责人", "list_str": "\\"yixiang.zhang\\",\\"shuai.liu\\"", "content": "你对我刚刚满意吗？我对自己的表现挺满意的！！！"}')
    md.process(
        '{"key": "20730d78-9fb6-4658-8bbb-cd9327da96c0", "note_id": "2HSA5C7JQ", "content": "最火热的MOCK接口排行。"}')


if __name__ == "__main__":
    # local_testing('''
    # {
    # "subject": "【DevOps提醒】构建耗时分位统计-近一周",
    # "note_id": "2H5FU5CAU",
    # "mail_to": "<EMAIL>",
    # "description": "【中位数预警值】是判定执行耗时是否满足要求的指标。<br/>处理机制：连续两周同一项超标(爆红)，或单周超过指标≥30%，平台介入调查处理。否则会再观测一周。<br/>如果50分位值在范围内，但体验仍觉得慢，可以向平台提出调整【中位数预警值】。"
    # }
    # ''')
    md = WechatRobotMarkdown()
    md.call(sys.argv[1:])
    # local_testing()

"""
pipeline {
    agent any
    stages{
        stage('测试用例-R') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/wechat_robot_markdown.py 'process' 
                '{"key": "20730d78-9fb6-4658-8bbb-cd9327da96c0", "note_id": "2HSDEDQPH", "content": "上周研发过程质量监测。"}' '''
            }
        }
    }
}
"""
