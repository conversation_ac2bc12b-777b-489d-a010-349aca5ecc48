import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from urllib3.exceptions import InsecureRequestWarning
from settings import logger as log
import requests
import json
from common.email.send_email import Send<PERSON><PERSON>
from zepplin_reporter import <PERSON><PERSON><PERSON>linReporter
from job.jenkins.info_warning.abstract_warner import AWarner

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class MailWarning(AWarner):

    def fragment_test(self):
        content = self.zr.get_content_from_server("2JCBMU2UK")
        warning_content = self._get_table_data_from_content("", content)

    def __init__(self):
        self.zr = ZepplinReporter()

    def process(self, notes_str):
        e = json.loads(notes_str)
        model = MailWarningModel(e.get('note_id'), e.get('description'), e.get('mail_to'), e.get('subject'))
        self.zr.runjob(model.note_id)
        content = self.zr.get_content_from_server(model.note_id)
        warning_content = self._get_table_data_from_content(model.description, content)
        if warning_content is None:
            log.info("没有任何内容，不发送！")
            return
        warning_content = self.__escape(warning_content)
        self._warn(model.mail_to, model.subject, warning_content)

    def __escape(self, content):
        return content.replace("{", "{{").replace("}", "}}")

    def _warn(self, mail_to, subject, content):
        request = {}
        try:
            request['mail_to'] = mail_to
            request['subject'] = subject
            sd = SendMail()
            sd.send_mail(request, content)
        except Exception as e:
            log.error(str(e))
            raise e

    def _get_table_data_from_content(self, description, content):
        table_string = '<font size="4">{}</font><br/><br/><table border="1" width="80%">'.format(description)
        table_data_rows = self.zr.get_table_data_rows_from_content(content)
        if len(table_data_rows) < 3:
            return None
        level = 0
        for row in table_data_rows:
            table_string = table_string + '<tr>'
            table_data_columns = row.split('\t')
            for val in table_data_columns:
                if level == 0:
                    table_string = table_string + '<td><b>' + val + '</b></td>'
                else:
                    table_string = table_string + '<td>' + val + '</td>'
            table_string = table_string + '</tr>'
            level = level + 1
        table_string = table_string + '</table>'
        return table_string

    def _get_colomns(self, content):
        column_names = []
        columns = json.loads(content.get('body')).get('paragraphs')
        for column in content.get('columns').json():
            columns.append(column.get('name'))
        return columns

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "process": self.process
        }

        acceptor[params[0]](params[1])


class MailWarningModel:
    def __init__(self, note_id, description, mail_to, subject):
        self.note_id = note_id
        self.description = description
        self.mail_to = mail_to
        self.subject = subject


def local_testing(notes):
    mw = MailWarning()
    mw.process(notes)


if __name__ == "__main__":
    # local_testing('''
    # {
    # "subject": "【DevOps提醒】构建耗时分位统计-近一周",
    # "note_id": "2H5FU5CAU",
    # "mail_to": "<EMAIL>",
    # "description": "【中位数预警值】是判定执行耗时是否满足要求的指标。<br/>处理机制：连续两周同一项超标(爆红)，或单周超过指标≥30%，平台介入调查处理。否则会再观测一周。<br/>如果50分位值在范围内，但体验仍觉得慢，可以向平台提出调整【中位数预警值】。"
    # }
    # ''')
    mw = MailWarning()
    #mw.fragment_test()
    mw.call(sys.argv[1:])

"""
pipeline {
    agent any
    stages{
        stage('归档提醒') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/mail_warning.py 'process' 
                '{"subject": "【DevOps提醒】服务上线完成归档(首次申请已过一周)", "note_id": "2GU4YXR1H", "mail_to": "<EMAIL>", "description": "test"}' '''
            }
        }
        stage('无团队应用') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/info_warning/mail_warning.py 'process' 
                '{"subject": "【DevOps提醒】还没有所属团队的应用", "note_id": "2GRTSQ8UN", "mail_to": "<EMAIL>", "description": "test"}' '''
            }
        }
    }
}
"""
