import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from urllib3.exceptions import InsecureRequestWarning
import requests
import json

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)


class ZepplinReporter:
    def __init__(self):
        self.base_zepplin_url = 'http://paas.hongkou.howbuy.com/report/api/notebook/'
        self.func_url_export = 'export/{}'
        self.func_url_runjob = 'job/{}'
        return

    def runjob(self, note_id):
        for i in range(2):
            url = self.base_zepplin_url + self.func_url_runjob.format(note_id)
            requests.post(url=url)

    def get_content_from_server(self, note_id):
        url = self.base_zepplin_url + self.func_url_export.format(note_id)
        result = requests.get(url=url)
        if result or result.status_code == 200:
            return result.json()
        return None

    def get_table_data_rows_from_server(self, note_id):
        content = self.get_content_from_server(note_id)
        return self.get_table_data_rows_from_content(content)

    def get_table_data_rows_from_content(self, content):
        table_data = self.__get_table_data_from_content(content)
        return table_data.split('\n')

    def get_val_list_from_column(self, table_data_rows, column_name):
        result = []
        level = 0
        column_at = -1

        for row in table_data_rows:
            table_data_columns = row.split('\t')
            index = 0
            for val in table_data_columns:
                if level == 0:
                    if val == column_name:
                        column_at = index
                else:
                    if index == column_at:
                        result.append(val)
                index = index + 1
            level = level + 1

        return result

    def __get_table_data_from_content(self, content):
        msgs = (json.loads(content.get('body')).get('paragraphs')[0]).get('results').get('msg')
        for msg in msgs:
            if msg.get('type') == 'TABLE':
                return msg.get('data')

    def get_table_coloums_list_from_content(self, content):
        table_coloums = (json.loads(content.get('body')).get('paragraphs')[0]).get('config').get('results').get(
            '0').get('graph').get('setting').get('table').get('tableGridState').get('columns')
        column_names = []
        for column in table_coloums:
            column_names.append(column.get('name'))
        return column_names

if __name__ == '__main__':
    report = ZepplinReporter()
    a = report.get_table_data_rows_from_server('2J46YEPDR')
    print(a)