from dao.base_model import SpiderBaseModels
from peewee import *
from dao.base_model import BaseModel


class IterMgtIterRepoInfo(BaseModel):
    pipeline_id = Char<PERSON>ield(verbose_name='迭代ID', max_length=100)
    module_name = Char<PERSON>ield(verbose_name='应用名', max_length=100)
    repo_virtual_id = CharField(verbose_name='制品虚拟id', max_length=300)
    git_last_version = CharField(verbose_name='代码最后提交记录', max_length=50)
    git_repo_version = CharField(verbose_name='制品最新提交记录', max_length=50)
    create_time = DateTimeField(verbose_name='创建时间')
    update_time = DateTimeField(verbose_name='更新时间')

    class Meta:
        db_table = 'iter_mgt_iter_repo_info'
        verbose_name = '迭代应用制品信息表'
