import logging
import os
import sys



class BisIterInfoParam:
    cdc_batch_no = None
    restore_his_id = None
    bis_pipeline_id = None
    bis_code = None
    biz_base_db_code = None
    biz_app_list = None
    bis_branch_name = None
    start_datetime = None
    workspace = None
    build_id = None
    binlog_name = None
    log_pos = None
    db_info_id = None
    db_srv_id = None
    db_info_suffix_name = None
    db_group_name = None
    db_info_username = None
    db_info_password = None
    db_srv_type = None
    db_srv_url = None
    db_srv_name = None
    db_srv_hosts = None
    db_srv_port = None
    db_srv_username = None
    db_srv_password = None
    suite_db_name = None
    cdc_position = None
    suite_code = None
    operate_user = None
    local_diff_sql_code_repo_path = None
    bis_iter_diff_sql_root_path = None
    diff_sql_logger = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    @classmethod
    def build_bis_iter_info_param(cls, diff_info, start_datetime, workspace, build_id):
        bis_iter_info_param = BisIterInfoParam(bis_pipeline_id=diff_info.get("biz_test_iter_id"),
                                               bis_code=diff_info.get("biz_test_iter_id").split('_')[0],
                                               bis_branch_name=diff_info.get("biz_test_iter_id").split('_')[1],
                                               start_datetime=start_datetime,
                                               local_diff_sql_code_repo_path=diff_info.get("diff_sql_root_path"))
        bis_iter_info_param.build_id = build_id
        bis_iter_info_param.biz_base_db_code = diff_info.get("biz_base_db_code")
        bis_iter_info_param.binlog_name = diff_info.get("binlog_name")
        bis_iter_info_param.log_pos = diff_info.get("log_pos")
        bis_iter_info_param.db_info_id = diff_info.get("db_info_id")
        bis_iter_info_param.suite_db_name = diff_info.get("suite_db_name")
        bis_iter_info_param.db_info_suffix_name = diff_info.get("db_info_suffix_name")
        bis_iter_info_param.db_group_name = diff_info.get("db_group_name")
        bis_iter_info_param.db_info_username = diff_info.get("db_info_username")
        bis_iter_info_param.db_info_password = diff_info.get("db_info_password")
        bis_iter_info_param.db_srv_id = diff_info.get("db_srv_id")
        bis_iter_info_param.db_srv_type = diff_info.get("db_srv_type")
        bis_iter_info_param.db_srv_url = diff_info.get("db_srv_url")
        bis_iter_info_param.db_srv_name = diff_info.get("db_srv_name")
        bis_iter_info_param.db_srv_hosts = diff_info.get("db_srv_hosts")
        bis_iter_info_param.db_srv_port = diff_info.get("db_srv_port")
        bis_iter_info_param.db_srv_username = diff_info.get("db_srv_username")
        bis_iter_info_param.db_srv_password = diff_info.get("db_srv_password")
        bis_iter_info_param.cdc_position = diff_info.get("cdc_position")
        bis_iter_info_param.suite_code = diff_info.get("suite_code")
        bis_iter_info_param.workspace = workspace
        return bis_iter_info_param

    def diff_sql_log_logger(self, diff_log_file_dir_path, diff_log_file_path):
        if not os.path.exists(diff_log_file_dir_path):
            os.makedirs(diff_log_file_dir_path)
        handler = logging.FileHandler(diff_log_file_path)  # 创建一个FileHandler实例，用于写入日志文件
        diff_sql_logger = logging.getLogger("diff_sql_log")
        # 遍历logger的所有handlers，如果发现是StreamHandler并且指向的是标准错误流，则移除
        for handler in diff_sql_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stderr:
                diff_sql_logger.removeHandler(handler)
        diff_sql_logger.setLevel(logging.DEBUG)  # 设置日志级别
        diff_sql_logger.addHandler(handler)  # 添加处理器
        return diff_sql_logger
