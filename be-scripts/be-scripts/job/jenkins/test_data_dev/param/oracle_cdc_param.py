from app_mgt.app_apidoc.util.obj_ext import ObjExt


# Pa 数据库cdc 参数
class DbCdcParam(ObjExt):
    host = None
    port = None
    url = None
    user = None
    password = None
    db = None
    db_name_suffix_name = None
    service_name = None
    srv_id = None
    ignore_tables = None
    pipeline_id = None
    cdc_position = None
    diff_dml_sql_path = None
    diff_sql_name = None
    diff_dml_sql_file_parent_path = None
    next_cdc_position_path = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def __eq__(self, other: object) -> bool:
        if isinstance(other, DbCdcParam):
            return self.host == other.host and self.port == other.port and self.db == other.db and self.service_name == other.service_name
        return False

    def __hash__(self) -> int:
        return hash((self.host, self.port, self.db, self.service_name))
