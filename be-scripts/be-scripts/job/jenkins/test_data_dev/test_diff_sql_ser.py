import logging
from typing import List
from sqlalchemy import text
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.connect.oracle_sqlalchemy import DBConnectionManagerForSqlalchemyOracle
from dao.get.mysql import db_mgt_bind_view
from job.jenkins.test_data_dev.param.bis_iter_info_param import BisIterInfoParam
from job.jenkins.test_data_dev.param.oracle_cdc_param import DbCdcParam
from job.jenkins.test_data_dev.bo.db_restore_his_bo import DbRestoreHisBo
from job.jenkins.test_data_dev.bo.db_info_bo import TestingDbInfoBo
from job.jenkins.test_data_dev.bo.db_sequence_bo import DbSequenceBO
from settings import logger
from dao.connect.mysql import DBConnectionManager
from dao.connect.oracle import OracleConnectionManager


def get_db_tables(db_name_suffix_name) -> List[str]:
    """
        因增加domain，需优化 已完成优化 20231113
        因重构，二次优化 20240606
    """
    sql = '''
            SELECT DISTINCT table_info.table_name, li.logic_db_name AS db_info_suffix_name
            FROM db_mgt_table_info table_info
            INNER JOIN db_mgt_app_bind b ON table_info.module_name = b.app_module_name
            INNER JOIN db_mgt_logic_info li ON li.db_domain_id = b.db_domain_id
            WHERE  li.logic_db_name = '{}';
          '''.format(db_name_suffix_name)
    logger.info(sql)
    db_tables = list()
    with DBConnectionManagerForSqlalchemy() as db:
        values = db.session.execute(text(sql)).fetchall()
        for db_info in values:
            if "flyway_schema_history" != db_info[0]:
                db_tables.append(db_info[0])
    return db_tables


def add_supplemental_log_to_table(table_names, db_cdc_param):
    error_tables = []
    for table_name in table_names:
        logging.basicConfig()
        logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
        try:
            sql = '''ALTER TABLE {} ADD SUPPLEMENTAL LOG DATA (ALL) COLUMNS'''.format(table_name)
            logger.info(sql)
            logger.info("supplemental db_info :{}".format(db_cdc_param))
            with DBConnectionManagerForSqlalchemyOracle(db_cdc_param.host, db_cdc_param.port, db_cdc_param.user,
                                                        db_cdc_param.password, db_cdc_param.service_name) as db:
                db.session.execute(text(sql))
                db.session.commit()
        except Exception as ex:
            error_tables.append(table_name)
            logger.error(ex)
    return error_tables


# 更新cdc_position
def update_oracle_cdc_position(cdc_position, bis_iter_info_param: BisIterInfoParam):
    with DBConnectionManager() as db:
        sql = '''
                    UPDATE db_mgt_cdc_info set cdc_position={} 
                    WHERE restore_his_id = '{}' and cdc_flag=1;
                  '''.format(cdc_position, bis_iter_info_param.restore_his_id)
        logger.info("db_mgt_cdc_info:{}".format(sql))
        db.cur.execute(sql)


def validate_db_cdc_info(bis_pipeline_id):
    sql = '''
            DELETE cdc_info
            FROM db_mgt_cdc_info AS cdc_info
            JOIN db_mgt_db_restore_his AS restore_his ON cdc_info.restore_his_id = restore_his.id
            WHERE restore_his.opt_pipeline_id = '{}' AND cdc_info.cdc_flag = 1;
          '''.format(bis_pipeline_id)
    logger.info("validate_db_cdc_info:{}".format(sql))
    with DBConnectionManagerForSqlalchemy() as db:
        db.session.execute(text(sql))
        db.session.commit()


def update_mysql_cdc_position(binlog_filename, log_pos, bis_iter_info_param: BisIterInfoParam):
    with DBConnectionManager() as db:
        sql = '''
                    UPDATE db_mgt_cdc_info set binlog_name='{}' , log_pos='{}'
                    WHERE restore_his_id = '{}' and cdc_flag=1;
                  '''.format(binlog_filename, log_pos, bis_iter_info_param.restore_his_id)
        logger.info("update_db_mysql_cdc_position:{}".format(sql))
        logger.info(sql)
        db.cur.execute(sql)


def get_current_scn(db_cdc_param: DbCdcParam):
    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
    sql = f'''select CURRENT_SCN from V$DATABASE'''
    logger.info(sql)
    with DBConnectionManagerForSqlalchemyOracle(db_cdc_param.host, db_cdc_param.port, db_cdc_param.user,
                                                db_cdc_param.password, db_cdc_param.service_name) as db:
        value = db.session.execute(text(sql)).fetchone()[0]
    return value


def get_all_sequence(db_cdc_param: DbCdcParam):
    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
    sql = 'select sequence_name,min_value, max_value,increment_by,cycle_flag,order_flag,cache_size,last_number from user_sequences'
    logger.info(sql)
    dict_result = {}
    res_str = ""
    with OracleConnectionManager(db_cdc_param.user, db_cdc_param.password, db_cdc_param.host,
                                 db_cdc_param.service_name) as db:
        res = db.cur.execute(sql)
        for row in res:
            bo = DbSequenceBO()
            bo.sequence_name = row[0]
            bo.min_value = row[1]
            bo.max_value = row[2]
            bo.increment_by = row[3]
            bo.cycle_flag = row[4]
            bo.order_flag = row[5]
            bo.cache_size = row[6]
            bo.last_number = row[7]
            dict_result[bo.sequence_name] = bo.get_create_sequence_sql()
            res_str = res_str + bo.get_create_sequence_sql() + "\n"
    return res_str


#
# 查询测试数据开发的库信息
def query_test_data_dev_db_info(db_srv_type=None, biz_test_iter_id=None):
    pass
#     sql = """
#     SELECT DISTINCT
# 	      iter.biz_test_iter_id,
# 	      db_srv.id AS db_srv_id,
# 	      db_info.id AS db_info_id,
#           restore_his.db_name AS suite_db_name,
#           db_group.db_group_name,
#           li.logic_db_name AS db_info_suffix_name,
#           db_srv.db_srv_username,
# 	      db_srv.db_srv_password,
#           biz_base.biz_base_db_code,
# 	      iter.biz_code,
# 	      db_srv.db_srv_hosts,
# 	      db_srv.db_srv_port,
# 	      restore_his.suite_code,
# 	      restore_his.restore_datetime,
# 	      cdc_info.binlog_name,
# 	      cdc_info.log_pos,
# 	      db_srv.db_srv_type,
# 	      db_srv.db_srv_url,
# 	      db_srv.db_srv_name,
# 	      cdc_info.cdc_position ,
# 	      restore_his.id AS restore_his_id
#     FROM
# 	    db_mgt_db_restore_his restore_his
# 	    JOIN db_mgt_cdc_info cdc_info ON restore_his.id = cdc_info.restore_his_id
# 	    JOIN biz_test_iter iter ON restore_his.opt_pipeline_id = iter.biz_test_iter_id
# 	    AND iter.br_status = 'open'
# 	    JOIN db_mgt_info db_info ON restore_his.db_info_id = db_info.id
# 	    JOIN db_mgt_suite_bind sb ON sb.db_info_id = db_info.id
# 	    JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id
# 	    JOIN db_mgt_domain dmd ON li.db_domain_id = dmd.id
# 	    JOIN db_mgt_group db_group ON dmd.db_group_id= db_group.id
# 	    JOIN db_mgt_srv db_srv ON db_info.db_srv_id = db_srv.id
# 	    JOIN biz_base_db_bind biz_base ON iter.biz_code = biz_base.biz_code
# 	    WHERE db_srv.db_srv_type = '{db_srv_type}'
# 	    AND cdc_info.cdc_flag=1
# 	    AND iter.biz_test_iter_id = '{biz_test_iter_id}'
#         """.format(db_srv_type=db_srv_type, biz_test_iter_id=biz_test_iter_id)
#     with DBConnectionManager() as db:
#         db.cur.execute(sql)
#     return db.cur.fetchall()


def get_user_connect_info(db_cdc_param: DbCdcParam, username):
    logger.info(db_cdc_param)
    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
    sql = """SELECT MACHINE, PROGRAM FROM v$session WHERE username = '{}'""".format(username)
    logger.info(sql)
    with DBConnectionManagerForSqlalchemyOracle(db_cdc_param.host, db_cdc_param.port, db_cdc_param.user,
                                                db_cdc_param.password, db_cdc_param.service_name) as db:
        value = db.session.execute(text(sql)).fetchall()
        for i in value:
            logger.info("user_connect_info:{},{}".format(i[0], i[1]))
    return value


def get_unlock_user(db_cdc_param: DbCdcParam, username):
    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
    sql = """alter user {} account  unlock""".format(username)
    logger.info(sql)
    with DBConnectionManagerForSqlalchemyOracle(db_cdc_param.host, db_cdc_param.port, db_cdc_param.user,
                                                db_cdc_param.password, db_cdc_param.service_name) as db:
        db.session.execute(text(sql))
        db.session.commit()


def get_all_table(db_cdc_param: DbCdcParam, username):
    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)
    sql = """SELECT table_name FROM all_tables WHERE owner = '{}'""".format(username)
    logger.info(sql)
    with DBConnectionManagerForSqlalchemyOracle(db_cdc_param.host, db_cdc_param.port, db_cdc_param.user,
                                                db_cdc_param.password, db_cdc_param.service_name) as db:
        value = db.session.execute(text(sql)).fetchall()
    logger.info("all_table:{}".format(value))
    if value:
        value = [i[0] for i in value]
    return value
