import gc
import inspect
import traceback
import json
import logging
import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from common.mysql.models import DbMgtBisTestSql, DbMgtCdcInfo
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from job.jenkins.test_data_dev.models import BizBaseDbBind
from test_publish_aio.test_publish_aio_models.test_publish_ser import query_diff_info
from common.mysql.mysql_binlog import MySQLDiffSqlCollect
from dao.get.mysql import db_mgt_bind_view
from job.jenkins.test_data_dev.service.oracle_cdc_service import OracleCdcService
from job.jenkins.test_data_dev.service.push_sql_service import PushSqlService
from job.jenkins.test_data_dev.util.cdc_file_util import get_diff_dml_sql_path, \
    get_diff_sql_file_parent_path, get_make_diff_dml_sql_path, get_bis_iter_diff_sql_root_path, \
    get_bis_iter_test_dml_root_path
from job.jenkins.test_data_dev.cdc_common.cdc_constants import DbCdcEnum
from job.jenkins.test_data_dev.param.oracle_cdc_param import DbCdcParam
from job.jenkins.test_data_dev.param.bis_iter_info_param import BisIterInfoParam
from utils.test_env.test_env_lib import step_desc
from settings import logger as log, TEST_DATA_INIT, GITLAB_LIB_SSH, logger
from job.jenkins.test_data_dev.test_diff_sql_ser import query_test_data_dev_db_info, get_all_sequence
from datetime import datetime



def prepare_ignore_tables(item: BisIterInfoParam):
    ignore_tables = []
    test_dml_cdc_db_ignore_table = json.loads(TEST_DATA_INIT["test_dml_cdc_db_ignore_table"])
    if test_dml_cdc_db_ignore_table.get(item.db_info_suffix_name):
        for table in test_dml_cdc_db_ignore_table.get(item.db_info_suffix_name):
            ignore_tables.append("{}.{}".format(item.suite_db_name, table))
    return ",".join(ignore_tables)


class TestDiffSqlParent:
    diff_infos = []
    diff_db_type = None
    workspace = None
    build_id = None
    cdc_batch_no = None

    def __init__(self, workspace, build_id, cdc_batch_no):
        self.workspace = workspace
        self.build_id = build_id
        self.cdc_batch_no = cdc_batch_no
        if self.diff_db_type:
            self.query_diff_info()

    def produce_diff_sql(self):
        raise NotImplementedError

    def make_and_log_diff_sql(self, bis_iter_info_param: BisIterInfoParam):
        if not os.path.exists(get_diff_dml_sql_path(bis_iter_info_param)) or not DbMgtCdcInfo.select().where(
                DbMgtCdcInfo.restore_his_id == bis_iter_info_param.restore_his_id,
                DbMgtCdcInfo.cdc_flag == 1).get_or_none():
            return
        # 获取源目录中的所有dml文件
        dml_files = [f for f in os.listdir(get_diff_dml_sql_path(bis_iter_info_param)) if f.endswith(".sql")]
        count = 1
        curr_time = datetime.now()
        db_ver_prefix = curr_time.strftime('%Y.%m.%d.%H.%M.%S')
        logger.info("dml_files:{}".format(dml_files))
        # 遍历每个dml文件，读取并拆分
        for dml_file in dml_files:
            with open(os.path.join(get_diff_dml_sql_path(bis_iter_info_param), dml_file), "r", encoding='utf-8') as f:
                lines = f.readlines()
            if not lines:
                print("ingore:" + dml_file[:-4])
                continue
            new_file_name = dml_file[:-4] + "_diff_dml.sql"
            sql_ver_name = 'V{}.900{}__{}'.format(db_ver_prefix,
                                                  '%03d' % (count),
                                                  new_file_name)
            new_file_path = os.path.join(get_make_diff_dml_sql_path(bis_iter_info_param), sql_ver_name)
            # 将500行dml语句写入新文件中
            with open(new_file_path, "w", encoding='utf-8') as f:
                f.writelines(lines)
            if dml_file.strip() and sql_ver_name and dml_file:
                DbMgtBisTestSql.get_or_create(
                    cdc_batch_no=bis_iter_info_param.cdc_batch_no,
                    suite_code=bis_iter_info_param.suite_code,
                    db_info_id=bis_iter_info_param.db_info_id,
                    bis_pipeline_id=bis_iter_info_param.bis_pipeline_id,
                                       sql_ver_group=bis_iter_info_param.db_group_name,
                                       sql_ver_db=bis_iter_info_param.db_info_suffix_name,
                                       sql_file_name=dml_file,
                                       create_user="howbuyscm",
                                       create_time=curr_time,
                                       update_user="howbuyscm",
                                       update_time=curr_time,
                                       stamp=0,
                                       bis_branch_name=bis_iter_info_param.bis_branch_name,
                                       sql_file_path=get_diff_sql_file_parent_path(bis_iter_info_param),
                                       sql_ver_name=sql_ver_name,
                                       sql_ver_upload_status=0)
            logger.info("table:{},编译完成:{}".format(dml_file[:-4], new_file_path))
            count = count + 1

    def generate_biz_param(self, diff_info: dict, workspace: str):
        # 格式化当前时间
        current_datetime = datetime.now()
        # Format the datetime to a string
        start_datetime = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
        # 构建业务迭代参数
        bis_iter_info_param = BisIterInfoParam.build_bis_iter_info_param(diff_info, start_datetime, self.workspace,
                                                                         self.build_id)
        bis_iter_info_param.cdc_batch_no = self.cdc_batch_no
        bis_iter_info_param.bis_iter_diff_sql_root_path = get_bis_iter_diff_sql_root_path(
            bis_iter_info_param.biz_base_db_code, bis_iter_info_param.bis_pipeline_id)
        bis_iter_info_param.restore_his_id = diff_info.get("restore_his_id")
        # 清理local_diff_sql_code_repo_path 下所有文件
        if not os.path.exists(get_make_diff_dml_sql_path(bis_iter_info_param)):
            os.makedirs(get_make_diff_dml_sql_path(bis_iter_info_param))
        if os.path.exists(bis_iter_info_param.local_diff_sql_code_repo_path):
            os.system('rm -rf {}'.format(bis_iter_info_param.local_diff_sql_code_repo_path))
        return bis_iter_info_param

    def query_diff_info(self):
        # 找到所有需要采集的oracle数据库信息
        self.diff_infos = query_diff_info(db_srv_type=self.diff_db_type, workspace=self.workspace)
        if not self.diff_infos:
            logger.info("没有需要采集的数据")
        logger.info("diff_infos:{}".format(self.diff_infos))





class TestDiffOracleSql(TestDiffSqlParent):
    diff_db_type = DbCdcEnum.ORACLE.value

    def __init__(self, workspace, build_id, cdc_batch_no):
        super().__init__(workspace, build_id, cdc_batch_no)

    def produce_diff_sql(self):
        for diff_info in self.diff_infos:
            bis_iter_info_param: BisIterInfoParam = self.generate_biz_param(diff_info, self.workspace)
            cdc_param: DbCdcParam = self.build_cdc_param(bis_iter_info_param)
            logger.info("开始采集:{}".format(json.dumps(bis_iter_info_param.__dict__, indent=4)))
            OracleCdcService.generate_diff_sql(cdc_param, bis_iter_info_param)
            self.make_and_log_diff_sql(bis_iter_info_param)

    @staticmethod
    def create_sequence_sql(biz_test_iter_id: str):
        sequence_sql = {}
        for db_info in query_test_data_dev_db_info(TestDiffOracleSql.diff_db_type, biz_test_iter_id):
            cdc_param = DbCdcParam(
            host=db_info['db_srv_hosts'],
            port=db_info['db_srv_port'],
            user=db_info['suite_db_name'],
            password=db_info['db_srv_password'],
            service_name=db_info['db_srv_name'],
            db_name_suffix_name=db_info['db_info_suffix_name'])
            sequence_sql[db_info['db_group_name'], db_info['db_info_suffix_name']] = get_all_sequence(cdc_param)

        return sequence_sql

    def build_cdc_param(self, bis_iter_info_param: BisIterInfoParam):
        cdc_param = DbCdcParam(
            srv_id=bis_iter_info_param.db_srv_id,
            host=bis_iter_info_param.db_srv_hosts,
            port=bis_iter_info_param.db_srv_port,
            user=DbCdcEnum.ORACLE_CDC_USER.value,
            password=DbCdcEnum.ORACLE_CDC_PWD.value,
            cdc_position=bis_iter_info_param.cdc_position,
            db=bis_iter_info_param.suite_db_name,
            db_name_suffix_name=bis_iter_info_param.db_info_suffix_name,
            pipeline_id=bis_iter_info_param.bis_pipeline_id,
            service_name=bis_iter_info_param.db_srv_name,
            diff_dml_sql_path=get_diff_dml_sql_path(bis_iter_info_param),
            diff_dml_sql_file_parent_path=get_diff_sql_file_parent_path(bis_iter_info_param),
            ignore_tables=prepare_ignore_tables(bis_iter_info_param),
        )
        return cdc_param


class TestDiffMysqlSql(TestDiffSqlParent):
    diff_db_type = DbCdcEnum.MYSQL.value

    def __init__(self, workspace, build_id, cdc_batch_no):
        super().__init__(workspace, build_id, cdc_batch_no)

    def produce_diff_sql(self):
        for diff_info in self.diff_infos:
            env_db_infos = db_mgt_bind_view.get(suite_code=diff_info.get("suite_code"),
                                                db_info_id=diff_info.get("db_info_id"))
            diff_info['db_info_username'] = env_db_infos[0].get("username")
            diff_info['db_info_password'] = env_db_infos[0].get("password")
            bis_iter_info_param: BisIterInfoParam = self.generate_biz_param(diff_info,
                                                                            self.workspace)
            logger.info("开始采集:{}".format(json.dumps(bis_iter_info_param.__dict__, indent=4)))
            mySQLDiffSqlCollect = MySQLDiffSqlCollect(bis_iter_info_param=bis_iter_info_param)
            result, msg = mySQLDiffSqlCollect.mysql_diff_sql_collect()
            if not result:
                logging.error(msg)
                exit(1)
            else:
                logging.info(msg)
                self.make_and_log_diff_sql(bis_iter_info_param)


class DiffExecutorFactory:
    @staticmethod
    def create_executor(diff_db_type: str, workspace: str, build_id: str,cdc_batch_no:str):
        # 获取当前所有的对象
        all_objects = gc.get_objects()
        for obj in all_objects:
            # 检查对象是否是一个类
            isclass = False
            try:
                isclass = inspect.isclass(obj)
            except Exception as e:
                pass
            if isclass:
                # 检查类是否是BaseExecutor的子类
                if issubclass(obj,
                              TestDiffSqlParent) and obj is not TestDiffSqlParent and obj.diff_db_type == diff_db_type:
                    return obj(workspace, build_id,cdc_batch_no)
        logger.error("执行器未找到实现:{}".format(diff_db_type))
        return None


class TestDiffSqlEntrance:
    @step_desc("生成diff-sql")
    def _produce_diff_sql(self, diff_type, workspace, build_id,cdc_batch_no):
        diff_executor = DiffExecutorFactory.create_executor(diff_type, workspace, build_id,cdc_batch_no)
        if not diff_executor:
            raise Exception("diff_executor not found")
        diff_executor.produce_diff_sql()
        return 'success'

    # @step_desc("提交test-dml")
    # def _commit_test_dml(self, job_name, workspace, bis_pipeline_id, start_datetime, suite_code, operate_user):
    #     CommitTestDmlService.commit_test_dml(bis_pipeline_id, operate_user)
    #     return 'success'
    def _add_sequence(self, bis_pipeline_id,  biz_base_db_code, bis_branch_name):
        sequence_file_name = "recreate_sequence"
        sequence_sql_dict = TestDiffOracleSql.create_sequence_sql(bis_pipeline_id)
        count = 1
        new_file_name = sequence_file_name + "_diff_dml.sql"
        curr_time = datetime.now()
        db_ver_prefix = curr_time.strftime('%Y.%m.%d.%H.%M.%S')
        sql_ver_name = 'V{}.900{}__{}'.format(db_ver_prefix,
                                              '%03d' % (count),
                                              new_file_name)
        for key, sequence_sql in sequence_sql_dict.items():
            # 如果存在query_set记录则生成sequence文件
            query_set = DbMgtBisTestSql.select().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id,
                                                       DbMgtBisTestSql.sql_ver_group == key[0],
                                                       DbMgtBisTestSql.sql_ver_db == key[1])
            query_res = [i for i in query_set]
            # 判断query_set大小是否为0
            if len(query_res) > 0:
                # 获取query_set中的sql_ver_id
                sql_ver_path = os.path.join(get_bis_iter_test_dml_root_path(biz_base_db_code, bis_branch_name), query_res[0].sql_file_path)
                if not os.path.exists(sql_ver_path):
                    cmd = 'mkdir -p {}'.format(sql_ver_path)
                    os.system(cmd)
                sequence_file_path = os.path.join(sql_ver_path, sql_ver_name)
                for file_name in os.listdir(sql_ver_path):
                    if (new_file_name in file_name):
                        old_file_path = os.path.join(sql_ver_path, file_name)
                        logging.info("sequence文件已存在,删除:{}".format(old_file_path))
                        os.remove(old_file_path)
                DbMgtBisTestSql.delete().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id,
                                               DbMgtBisTestSql.db_info_id == query_res[0].db_info_id,
                                               DbMgtBisTestSql.sql_file_name == new_file_name).execute()
                with open(sequence_file_path, 'w') as f:
                    f.write(sequence_sql)
                    logging.info("生成sequence文件:{}".format(sequence_file_path))
                DbMgtBisTestSql.get_or_create(
                    cdc_batch_no=query_res[0].cdc_batch_no,
                    suite_code=query_res[0].suite_code,
                    db_info_id=query_res[0].db_info_id,
                    bis_pipeline_id=bis_pipeline_id,
                    sql_ver_group=query_res[0].sql_ver_group,
                    sql_ver_db=query_res[0].sql_ver_db,
                    sql_file_name=new_file_name,
                    create_user="howbuyscm",
                    create_time=curr_time,
                    update_user="howbuyscm",
                    update_time=curr_time,
                    stamp=0,
                    bis_branch_name=query_res[0].bis_branch_name,
                    sql_file_path=query_res[0].sql_file_path,
                    sql_ver_name=sql_ver_name,
                    sql_ver_upload_status=1)
                logging.info("生成sequence文件写入数据库成功:{}".format(DbMgtBisTestSql))



    @step_desc("提交test-dml-repo")
    def _commit_test_dml_repo(self, bis_pipeline_id, operate_user):
        logging.info("operate_user:{}".format(operate_user))
        bis_code = bis_pipeline_id.split('_')[0]
        bis_branch_name = bis_pipeline_id.split('_')[1]
        biz_base_db_code = None
        with DBConnectionManagerForSqlalchemy() as db:
            obj = db.session.query(BizBaseDbBind).filter(
                BizBaseDbBind.biz_code == bis_code).first()
            biz_base_db_code = obj.biz_base_db_code
        if not biz_base_db_code:
            raise Exception("基础库信息:{}".format(biz_base_db_code))

        test_dml_repo_group = TEST_DATA_INIT.get("test_dml_repo_group")
        bis_git_repo_url = '{}:{}/{}.git'.format(GITLAB_LIB_SSH, test_dml_repo_group, biz_base_db_code)
        bis_src_root_path = get_bis_iter_diff_sql_root_path(biz_base_db_code, bis_pipeline_id)

        bis_root_path = get_bis_iter_test_dml_root_path(biz_base_db_code, None)
        logging.info("克隆或更新制品库:{}".format(bis_root_path))
        # 克隆或更新制品库
        self.__ck_gitlab(bis_git_repo_url, bis_branch_name, bis_root_path)
        logging.info("更新sql并推送制品库")
        # add sequence
        self._add_sequence(bis_pipeline_id, biz_base_db_code, bis_branch_name)
        # 更新sql并推送制品库
        logging.info(
            "更新sql并推送制品库 参数：{},{},{},{}".format(bis_pipeline_id, bis_code, bis_branch_name, operate_user))
        self.__push_sql(bis_pipeline_id, bis_code, bis_branch_name, operate_user, biz_base_db_code, bis_src_root_path)

    @staticmethod
    def __ck_gitlab(bis_git_repo_url, bis_branch_name, bis_root_path):
        if not os.path.isdir(bis_root_path):
            cmd = 'mkdir -p {}'.format(bis_root_path)
            log.info(cmd)
            os.system(cmd)

        os.chdir(bis_root_path)
        if not os.path.isdir(bis_branch_name):
            cmd = 'git clone -b {} {} {}'.format(bis_branch_name, bis_git_repo_url, bis_branch_name)
            log.info(cmd)
            os.system(cmd)
            if not os.path.isdir(bis_branch_name):
                cmd = 'git clone -b master {} {}'.format(bis_git_repo_url, bis_branch_name)
                log.info(cmd)
                os.system(cmd)
                os.chdir(os.path.join(bis_root_path, bis_branch_name))
                cmd = 'git checkout -b {}'.format(bis_branch_name)
                os.system(cmd)
                cmd = 'git add -A'
                os.system(cmd)
                cmd = 'git commit -m "Create {}"'.format(bis_branch_name)
                os.system(cmd)
                cmd = 'git push --set-upstream origin {}'.format(bis_branch_name)
                log.info(cmd)
                os.system(cmd)

        os.chdir(os.path.join(bis_root_path, bis_branch_name))
        os.system('git checkout .')
        log.info('git checkout .')
        os.system('git checkout {}'.format(bis_branch_name))
        log.info('git checkout {}'.format(bis_branch_name))
        os.system('git pull origin {}'.format(bis_branch_name))
        log.info('git pull origin {}'.format(bis_branch_name))

    @staticmethod
    def __push_sql(bis_pipeline_id, bis_code, bis_branch_name, operate_user, biz_base_db_code, bis_root_path):
        PushSqlService.push_sql(bis_pipeline_id, bis_code, bis_branch_name, operate_user, biz_base_db_code,
                                bis_root_path)

    def call(self, params):
        try:
            call_func = getattr(self, "_" + params[0])
            return call_func(params[1], params[2])
        except Exception as e:
            traceback.print_exc()
        return self._produce_diff_sql(params[1], params[0], params[2],params[3])


if __name__ == "__main__":
    log.info("调用 {}".format(sys.argv[1:]))
    test_diff_sql = TestDiffSqlEntrance()
    test_diff_sql.call(sys.argv[1:])
    # bis_pipeline_id = 'FIN-CONSOLE_20231220-master'
    # bis_code = bis_pipeline_id.split('_')[0]
    # test_diff_sql._add_sequence(bis_pipeline_id, "it100", bis_code)