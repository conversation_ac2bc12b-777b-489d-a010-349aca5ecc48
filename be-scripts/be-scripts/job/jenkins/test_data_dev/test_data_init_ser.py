from dao.connect.mysql import DBConnectionManager
from dao.get.mysql.app_info import get_db_in_transit_br_names_group_name
from settings import logger


def get_bis_iter_info(bis_pipeline_id):
    sql = "SELECT * FROM `db_mgt_bis_iter_info` WHERE bis_pipeline_id = '{}'".format(bis_pipeline_id)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return db.cur.fetchone()


def get_archive_biz_br_by_gt_biz_iter_id(biz_test_iter_id):
    sql = '''
       SELECT o.biz_test_iter_id,o.biz_test_iter_br,o.br_end_time,i.br_end_time FROM biz_test_iter o JOIN 
                      (SELECT biz_code,br_end_time FROM biz_test_iter WHERE biz_test_iter_id = "{}") 
                      i ON o.biz_code = i.biz_code
                      WHERE o.br_status= "close" AND o.br_end_time > i.br_end_time'''.format(biz_test_iter_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    return [item['biz_test_iter_br'] for item in db.cur.fetchall()]


def handler_db_info_list(db_info_list):
    db_group_in_transit_br = {}
    db_group_dict = {}

    # 按db_group收集在途分支号
    for db_info in db_info_list:
        db_group_name = db_info.get("db_group_name")
        if db_group_name not in db_group_in_transit_br:
            db_group_in_transit_br[db_group_name] = get_db_in_transit_br_names_group_name(db_group_name)

    for db_info in db_info_list:
        db_group_name = db_info.get("db_group_name")
        branch_name = db_info.get("branch_name")

        # 按db_group选择分支
        if db_group_name not in db_group_dict:
            # 如果分支是在途分支则保留在途分支号
            if branch_name in db_group_in_transit_br.get(db_group_name):
                db_group_dict.update({db_info.get("db_group_name"): db_info})
            # 否则用master替换
            else:
                db_info["branch_name"] = 'master'
                db_group_dict.update({db_group_name: db_info})
        else:
            # db_group已经存在分支信息，则优先判断有没有被指定为master，
            # 如果不是master，说明之前已经选了在途分支，跳过处理
            if db_group_dict.get(db_group_name).get("branch_name") == 'master':
                if branch_name in db_group_in_transit_br.get(db_group_name):
                    db_group_dict.update({db_group_name: db_info})

    return db_group_dict.values()


if __name__ == '__main__':
    dict1 = {}
    dict2 = {"a": "b", "A": 1}
    dict3 = {"a": "c", "A": 1}
    dict1.update(dict2)
    print(dict1)
    dict1.update(dict3)
    print(dict1)
