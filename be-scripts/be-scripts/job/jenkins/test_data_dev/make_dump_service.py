import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from datetime import datetime
from sqlalchemy import text
from utils.form.models import DbMgtDumpFile, DbMgtDumpFileLog
from job.jenkins.scm_prod_check import SSHConnectionManager
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from job.jenkins.test_data_dev.models import JenkinsMgtTestDataDevJobInfo
from settings import logger, NGINX_LIB_REPO, DUMP
from dao.get.mysql import db_mgt_bind_view
from test_publish_aio.test_publish_aio_models.test_publish_ser import db_info_reset, get_last_db_archive_branch_list


def trans_date(date_str):
    if date_str and date_str != '':
        date_obj = datetime.strptime(str(date_str), '%Y-%m-%d %H:%M:%S')

        # 转换为日期二的格式
        date2 = date_obj.strftime('%Y年%m月%d日 %H时%M分')
        return date2
    return None


class MakeDumpService:

    def setStepParam(self, biz_code, biz_branch_name, suite_code, business_id):
        self.biz_code = biz_code
        self.biz_branch_name = biz_branch_name
        self.suite_code = suite_code
        self.business_id = business_id

    def get_app_by_bis_code(self, biz_code):
        sql = '''
            SELECT DISTINCT
                app.app_module_name
            FROM
                biz_app_bind app
                where app.biz_code='{}'
            '''.format(biz_code)
        logger.info(sql)
        results = list()
        with DBConnectionManagerForSqlalchemy() as db:
            db_result_list = db.session.execute(text(sql)).fetchall()
            for result_item in db_result_list:
                results.append(result_item[0])

        return results

    def get_dev_pipeline_id(self, biz_test_iter_id):
        sql = '''
            SELECT DISTINCT
                b.git_url,
                a.pipeline_id 
            FROM
                iter_mgt_iter_info a
                LEFT JOIN app_mgt_app_info b ON a.project_group = b.git_url
                LEFT JOIN app_mgt_app_module c ON b.id = c.app_id
                LEFT JOIN biz_test_iter_app app ON c.module_name = app.app_module_name
                AND app.archive_br_name = a.br_name 
            WHERE
                app.biz_test_iter_id = '{}'
                AND a.br_status = 'open'
                AND b.git_url IS NOT NULL;
            '''.format(biz_test_iter_id)
        logger.info(sql)
        results = list()
        with DBConnectionManagerForSqlalchemy() as db:
            pipeline_result = db.session.execute(text(sql)).fetchall()
            for item in pipeline_result:
                results.append({"project_group": item[0], "pipeline_id": item[1]})
        return results

    def get_app_project_group(self, biz_test_iter_id):
        sql = '''
            SELECT DISTINCT
                app.app_module_name,
                c.git_url
            FROM
                biz_test_iter_app app
                LEFT JOIN app_mgt_app_module b ON b.module_name = app.app_module_name
                LEFT JOIN app_mgt_app_info c ON b.app_id = c.id 
            WHERE
                app.biz_test_iter_id = '{}'
                AND c.git_url IS NOT NULL;
            '''.format(biz_test_iter_id)
        logger.info(sql)
        results = list()
        with DBConnectionManagerForSqlalchemy() as db:
            pipeline_result = db.session.execute(text(sql)).fetchall()
            for item in pipeline_result:
                results.append({"module_name": item[0], "project_group": item[1]})
        return results


    def get_db_name_max_limit(self, db_name):
        sql = '''
                    SELECT DISTINCT
                        dl.max_limit
                    FROM
                        db_mgt_logic_info li
                        INNER JOIN db_mgt_dump_limit dl ON dl.db_logic_id = li.id
                    WHERE
                        li.logic_db_name = '{}'
                    '''.format(db_name)
        with DBConnectionManagerForSqlalchemy() as db:
            result = db.session.execute(text(sql)).fetchone()
            if result:
                return result[0]
        return None

    def insert_or_update_dump_file(self, biz_iter_id, db_logic_id, db_info_id, dump_file_name, cur_time, pipeline_id):
        try:
            dump_file = DbMgtDumpFile.select().where(DbMgtDumpFile.biz_test_iter_id == biz_iter_id,
                                                     DbMgtDumpFile.db_logic_id == db_logic_id).first()
            if dump_file:
                DbMgtDumpFile.update({DbMgtDumpFile.update_time: cur_time,
                                      DbMgtDumpFile.pipeline_id: pipeline_id,
                                      DbMgtDumpFile.db_info_id: db_info_id,
                                      DbMgtDumpFile.dump_file_name: dump_file_name,
                                      DbMgtDumpFile.dump_source: 1,
                                      DbMgtDumpFile.dump_file_is_active: 1
                                      }).where(DbMgtDumpFile.biz_test_iter_id == biz_iter_id,
                                               DbMgtDumpFile.db_logic_id == db_logic_id).execute()
            else:
                DbMgtDumpFile.insert(biz_test_iter_id=biz_iter_id,
                                     db_logic_id=db_logic_id,
                                     dump_file_name=dump_file_name,
                                     pipeline_id=pipeline_id,
                                     db_info_id=db_info_id,
                                     dump_source=1,
                                     dump_file_is_active=1,
                                     create_user='howbuyscm',
                                     update_time=cur_time,
                                     update_user='howbuyscm',
                                     create_time=cur_time).execute()
            logger.info(
                'dump_file写入成功，biz_iter_id:{},db_info_id: {},dump_file_name:{}'.format(biz_iter_id, db_info_id,
                                                                                           dump_file_name))
        except Exception as e:
            logger.error(
                'dump_file写入失败，biz_iter_id:{},db_info_id: {},dump_file_name:{}'.format(biz_iter_id, db_info_id,
                                                                                           dump_file_name))
            logger.error(e)
            raise Exception(e)

    def record_dump_log(self, biz_test_iter_id, pipeline_id, db_logic_id, db_info_id, dump_file_batch, dump_file_name,
                        cur_time):
        try:
            DbMgtDumpFileLog.insert(pipeline_id=pipeline_id,
                                    biz_test_iter_id=biz_test_iter_id,
                                    db_logic_id=db_logic_id,
                                    db_info_id=db_info_id,
                                    dump_source=1,
                                    dump_file_batch=dump_file_batch,
                                    dump_file_name=dump_file_name,
                                    dump_file_is_active=1,
                                    create_user="howbuyscm",
                                    create_time=cur_time,
                                    update_user="howbuyscm",
                                    update_time=cur_time
                                    ).execute()
        except Exception as e:
            logger.error('记录详情失败：{}'.format(e))

    def make_dump_and_check_or_upload(self, db_info, dump_cmd, local_file, dump_file_name, remote_path=None):
        with SSHConnectionManager(db_info['db_srv_hosts'], db_info['db_srv_username'],
                                  db_info['db_srv_password']) as ssh:
            tdin, stdout, stderr = ssh.SSH.exec_command(dump_cmd, bufsize=-1)
            logger.info(stdout.read().decode("utf-8"))
            logger.info(stderr.read().decode("utf-8"))
            if remote_path:
                # 上传NG的dump有文件大小限制,默认200M
                db_name = db_info.get('db_name')
                try:
                    db_max_limit = self.get_db_name_max_limit(db_name)
                    max_limit = int(db_max_limit) if db_max_limit else int(DUMP['dump_max_limit'])
                    sftp = ssh.SSH.open_sftp()
                    file_stat = sftp.stat(local_file)
                    # 计算文件大小（单位：MB）
                    file_size_mb = file_stat.st_size / (1024 * 1024)
                    logger.info("文件大小：{},限制文件大小：{}".format(file_size_mb, max_limit))
                    # 限制文件大小
                    if file_size_mb == 0:
                        raise Exception("dump文件不存在或者文件为空")
                    if file_size_mb > max_limit:
                        raise Exception("dump文件大小超过限制")
                except Exception as e:
                    logger.error(e)
                    raise Exception(e)
                scp_cmd = "source {} ;sshpass -p '{}' scp -o StrictHostKeyChecking=no -r {} {}@{}:{}".format(
                    db_info['db_srv_bash_profile'],
                    NGINX_LIB_REPO["password"],
                    local_file,
                    NGINX_LIB_REPO["username"],
                    NGINX_LIB_REPO["ip"],
                    remote_path)
                logger.info(scp_cmd)
                tdin, stdout, stderr = ssh.SSH.exec_command(scp_cmd, bufsize=-1)
                logger.info(stdout.read().decode("utf-8"))
                logger.info(stderr.read().decode("utf-8"))
                remote_file = os.path.join(remote_path, dump_file_name)
                with SSHConnectionManager(NGINX_LIB_REPO["ip"], NGINX_LIB_REPO["username"],
                                          NGINX_LIB_REPO["password"]) as ng_ssh:
                    try:
                        sftp = ng_ssh.SSH.open_sftp()
                        size = sftp.stat(remote_file).st_size
                        if size == 0:
                            raise Exception("NG文件不存在或者文件为空")
                    except Exception as e:
                        logger.error("检查NG文件大小异常")
                        logger.error(e)
                        raise Exception(e)

    def get_dump_cmd(self, db_info, env_db_name, dump_file_name):
        if db_info['db_srv_type'] == "oracle":
            dump_cmd = 'source {} ;expdp \\\'/ as sysdba\\\' schemas={}  directory=DATA_PUMP_DIR dumpfile={} ' \
                       'exclude=statistics'.format(db_info['db_srv_bash_profile'],
                                                   env_db_name,
                                                   dump_file_name)
            logger.info("oracle_make_dump: {}命令".format(dump_cmd))
        elif db_info['db_srv_type'] == "mysql":
            dump_cmd = "cd {}&&source {} ;mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob --socket={} -u{} -p'{}' {}  > {}".format(
                db_info['data_dump_dir'],
                db_info['db_srv_bash_profile'],
                db_info['db_srv_socket_path'],
                db_info['username'],
                db_info['password'],
                env_db_name,
                dump_file_name)
            logger.info("mysql_make_dump: {}命令".format(dump_cmd))
        else:
            raise Exception("不支持的数据库类型")
        return dump_cmd

    def make_dump(self):
        with DBConnectionManagerForSqlalchemy() as db:
            jmtd = db.session.query(JenkinsMgtTestDataDevJobInfo).filter(
                JenkinsMgtTestDataDevJobInfo.id == self.business_id).one()
        app_dict_list = jmtd.job_param.get("app_list")
        app_list = []
        for app_dict in app_dict_list:
            app_list.append(app_dict.get("module_name"))
        logger.info("app_names size:{}".format(len(app_list)))
        biz_iter_id = self.biz_code + '_' + self.biz_branch_name
        app_group_map = None
        group_pipeline_map = None
        if 'master' != self.biz_branch_name:
            project_group_pipeline = self.get_dev_pipeline_id(biz_iter_id)
            if project_group_pipeline:
                group_pipeline_map = {d["project_group"]: d["pipeline_id"] for d in project_group_pipeline}
            app_group_list = self.get_app_project_group(biz_iter_id)
            if app_group_list:
                app_group_map = {d["module_name"]: d["project_group"] for d in app_group_list}
        cur_time = datetime.now()
        dump_file_batch = cur_time.strftime("%Y%m%d%H%M%S")
        union_list = set()
        db_info_result = db_mgt_bind_view.get(module_name_list=app_list, suite_code=self.suite_code)
        db_info_list = db_info_reset(db_info_result)
        logger.info("db_info_list:{}".format(len(db_info_list)))
        for db_info in db_info_list:
            db_info_id = db_info['db_info_id']
            if db_info_id in union_list:
                continue
            else:
                union_list.add(db_info_id)
            env_db_name = db_info.get('suite_db_name').upper()
            dump_file_suffix = "{}_db_BACKUP".format(env_db_name)
            remote_path = "{}/{}".format(NGINX_LIB_REPO['remote_path'], db_info['db_srv_type'])
            local_path = db_info['data_dump_dir']
            if db_info['db_srv_type'] == "oracle":
                dump_file_name = "{}_{}.dmp".format(dump_file_suffix, dump_file_batch)
            elif db_info['db_srv_type'] == "mysql":
                dump_file_name = "{}_{}.sql".format(dump_file_suffix, dump_file_batch)
            else:
                raise Exception("不支持的数据库类型")
            dump_cmd = self.get_dump_cmd(db_info, env_db_name, dump_file_name)
            logger.info("NG目录，remote_path: {}, local_path: {}".format(remote_path, local_path))
            logger.info('db_srv_hosts:{}, db_srv_username:{}'.format(db_info['db_srv_hosts'],
                                                                     db_info['db_srv_username']))
            local_file = os.path.join(local_path, dump_file_name)
            self.make_dump_and_check_or_upload(db_info, dump_cmd, local_file, dump_file_name, remote_path)
            # 1、dev_dump取pipeline_id
            pipeline_id = None
            if 'master' != self.biz_branch_name:
                if app_group_map:
                    project_group = app_group_map.get(db_info.get('module_name'))
                    if project_group and group_pipeline_map:
                        pipeline_id = group_pipeline_map.get(project_group)
            if pipeline_id is None:
                pipeline_id = get_last_db_archive_branch_list(db_info.get('db_group_name'))
            db_logic_id = db_info.get('db_logic_id')
            self.record_dump_log(biz_iter_id, pipeline_id, db_logic_id, db_info_id, dump_file_batch, dump_file_name,
                                 cur_time)
            self.insert_or_update_dump_file(biz_iter_id, db_logic_id, db_info_id, dump_file_name, cur_time, pipeline_id)
        return 'success'

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))
        acceptor = {
            "make_dump": self.make_dump,
        }
        if params[0] in acceptor:
            acceptor[params[0]]()

        return 'success'


if __name__ == '__main__':
    params = sys.argv[1:]
    logger.info("调用 {}".format(params))
    biz_code = params[1]
    biz_branch_name = params[2]
    suit_code = params[3]
    business_id = int(params[4])
    make_dump_ser = MakeDumpService()
    make_dump_ser.setStepParam(biz_code, biz_branch_name, suit_code, business_id)
    make_dump_ser.call(params)
    sys.exit(0)
