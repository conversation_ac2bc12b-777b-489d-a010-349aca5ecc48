import datetime
import os

from common.mysql.models import DbMgtBisTestSql
from job.jenkins.test_data_dev.cdc_common.cdc_constants import DbCdcEnum
from job.jenkins.test_data_dev.param.bis_iter_info_param import BisIterInfoParam
from job.jenkins.test_data_dev.param.oracle_cdc_param import DbCdcParam
from settings import TEST_DATA_INIT


# 准备diff_sql的路径
def get_diff_dml_sql_path(bis_iter_info_param: BisIterInfoParam):
    return os.path.sep.join(
        [bis_iter_info_param.local_diff_sql_code_repo_path, get_diff_sql_file_parent_path(bis_iter_info_param)])


def get_make_diff_dml_sql_path(bis_iter_info_param: BisIterInfoParam):
    return os.path.sep.join(
        [bis_iter_info_param.bis_iter_diff_sql_root_path, get_diff_sql_file_parent_path(bis_iter_info_param)])


def get_bis_iter_diff_sql_root_path(biz_base_db_code, bis_pipeline_id):
    return os.path.sep.join(
        [TEST_DATA_INIT["test_dml_diff_root_path"], biz_base_db_code, bis_pipeline_id])


def get_bis_iter_test_dml_root_path(biz_base_db_code, bis_branch_name):
    if bis_branch_name:
        return os.path.sep.join(
            ['/data', TEST_DATA_INIT.get("test_dml_repo_group"), biz_base_db_code, bis_branch_name])
    return os.path.sep.join(
        ['/data', TEST_DATA_INIT.get("test_dml_repo_group"), biz_base_db_code])


def get_diff_sql_file_parent_path(bis_iter_info_param: BisIterInfoParam):
    return os.path.sep.join(
        [bis_iter_info_param.db_group_name, bis_iter_info_param.db_info_suffix_name, DbCdcEnum.DIFF_DML_DIR.value[0]])


def log_diff_sql(db_info: DbCdcParam, bis_iter_info_param: BisIterInfoParam):
    curr_time = datetime.datetime.now()
    p, created = DbMgtBisTestSql.get_or_create(bis_pipeline_id=bis_iter_info_param.bis_pipeline_id,
                                               sql_file_name=db_info.diff_sql_name,
                                               defaults={'create_user': bis_iter_info_param.operate_user,
                                                         'create_time': curr_time,
                                                         'update_user': bis_iter_info_param.operate_user,
                                                         'update_time': curr_time,
                                                         'stamp': 0,
                                                         'bis_branch_name': bis_iter_info_param.bis_branch_name,
                                                         'sql_file_path': db_info.diff_dml_sql_file_parent_path,
                                                         'sql_ver_upload_status': 0})
    if not created:
        DbMgtBisTestSql.update({DbMgtBisTestSql.update_user: bis_iter_info_param.operate_user,
                                DbMgtBisTestSql.update_time: curr_time}). \
            where(DbMgtBisTestSql.id == p.id).execute()


def get_diff_log_file_path():
    # 格式化当前时间
    current_datetime = datetime.datetime.now()
    # Format the datetime to a string
    curr_day = current_datetime.strftime('%Y-%m-%d')
    return os.path.sep.join(
        [TEST_DATA_INIT["test_dml_diff_log_root_path"], curr_day]), os.path.sep.join(
        [TEST_DATA_INIT["test_dml_diff_log_root_path"], curr_day, "diff_sql.log"])
