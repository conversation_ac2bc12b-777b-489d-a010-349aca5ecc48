# coding: utf-8
from sqlalchemy import Column, String, TIMESTAMP, JSO<PERSON>, DateTime
from sqlalchemy.dialects.mysql import BIGINT, INTEGER
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class JenkinsMgtTestDataDevJobInfo(Base):
    __tablename__ = 'jenkins_mgt_test_data_dev_job'
    __table_args__ = {'comment': '业务执行流水线日志表'}
    id = Column(BIGINT(11), primary_key=True)
    job_build_id = Column(BIGINT(11), comment='jenkins构建编号')
    job_param = Column(JSON, comment='http请求参数dict')
    job_url = Column(String(512), comment='job请求地址')
    job_type = Column(String(100), comment='job类型')
    suite_code = Column(String(100), comment='job请求地址')
    status = Column(String(20), comment='流水线状态')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizBaseDbBind(Base):
    __tablename__ = 'biz_base_db_bind'
    id = Column(BIGINT(11), primary_key=True)
    biz_code = Column(String(512), comment='业务')
    biz_base_db_code = Column(String(512), comment='基础库集')
    biz_base_db_bind_is_active = Column(INTEGER(1), comment='是否有效')
    biz_base_db_bind_desc = Column(String(512), comment='描述')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizBaseInfo(Base):
    __tablename__ = 'biz_base_info'
    id = Column(BIGINT(20), primary_key=True)
    biz_type = Column(BIGINT(20), comment='业务类型')
    biz_code = Column(String(100), comment='业务')
    biz_identity = Column(String(200), comment='业务标识')
    biz_name = Column(String(100), comment='业务名称')
    biz_scenario_identity = Column(String(100), comment='业务「场景」标识')
    biz_scenario_name = Column(String(100), comment='业务「场景」中文名')
    biz_category = Column(INTEGER(1), comment='1:标准业务;2:衍生业务')
    biz_parent_id = Column(BIGINT(20), comment='父业务id')
    biz_is_active = Column(INTEGER(1), comment='业务是否可用')
    biz_desc = Column(String(255), comment='业务说明')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizBaseType(Base):
    __tablename__ = 'biz_base_type'
    id = Column(BIGINT(11), primary_key=True)
    biz_type_transaction = Column(String(512), comment='业务')
    biz_type_department = Column(String(512), comment='基础库集')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizAppBind(Base):
    __tablename__ = 'biz_app_bind'
    id = Column(BIGINT(11), primary_key=True)
    biz_code = Column(String(512), comment='业务')
    app_module_name = Column(String(512), comment='应用名')
    biz_app_bind_type = Column(INTEGER(2), comment='应用类型')
    biz_app_bind_is_active = Column(INTEGER(1), comment='是否有效')
    biz_app_bind_desc = Column(String(512), comment='描述')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizTestFlowExecHistory(Base):
    __tablename__ = 'biz_test_flow_exec_history'

    id = Column(BIGINT(11), primary_key=True)
    biz_type = Column(String(255), comment='业务类型')
    exec_suite_code = Column(String(100), comment='执行环境')
    exec_action_type = Column(String(100), comment='执行环境')
    exec_detail_param = Column(JSON, comment='查询详情参数')
    create_time = Column(DateTime, comment='创建时间')
    create_user = Column(String(50), comment='创建人')
    update_time = Column(DateTime, comment='修改时间')
    update_user = Column(String(50), comment='修改人')


class BizTestIter(Base):
    __tablename__ = 'biz_test_iter'

    id = Column(BIGINT(11), primary_key=True)
    biz_test_iter_id = Column(String(100), comment='业务测试迭代ID')
    biz_code = Column(String(100), comment='业务编码')
    biz_test_iter_br = Column(String(100), comment='业务测试迭代分支')
    br_status = Column(String(100), comment='分支状态')
    br_start_time = Column(DateTime, comment='分支开始时间')
    br_end_time = Column(DateTime, comment='分支结束时间')
    br_from = Column(String(100), comment='从哪个分支拉出')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')


class BizTestFlowInfo(Base):
    __tablename__ = 'biz_test_flow_info'

    id = Column(BIGINT(11), primary_key=True)
    biz_code = Column(String(255), comment='业务编码')
    biz_flow_name = Column(String(100), comment='编码/命名')
    biz_pipeline_name = Column(String(100), comment='编码/命名')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')


class BizTestFlowTestsetDetail(Base):
    __tablename__ = 'biz_test_flow_testset_detail'

    id = Column(BIGINT(11), primary_key=True)
    flow_id = Column(BIGINT(11), comment='biz_test_flow_info.id')
    testset_id = Column(INTEGER(11), comment='测试集id')
    app_name = Column(String(100), comment='测试集中的应用')
    script_branch = Column(String(100), comment='脚本版本')
    version_type = Column(String(10), comment='版本类型')
    create_user = Column(String(50), comment='创建人')
    create_time = Column(DateTime, comment='创建时间')
    update_user = Column(String(50), comment='修改人')
    update_time = Column(DateTime, comment='修改时间')
    stamp = Column(BIGINT(11), comment='版本')
