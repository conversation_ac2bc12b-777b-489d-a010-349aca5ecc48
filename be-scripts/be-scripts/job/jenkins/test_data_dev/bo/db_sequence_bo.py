

class DbSequenceBO:
    sequence_name = ""
    min_value = ""
    max_value = ""
    increment_by = ""
    cycle_flag = ""
    order_flag = ""
    cache_size = 0
    last_number = 0
    noorder = ""
    nocycle = ""

    def get_create_sequence_sql(self):
        if self.cycle_flag == "N":
            self.nocycle = "nocycle"
        else:
            self.nocycle = ""
        if self.order_flag == "N":
            self.noorder = "noorder"
        else:
            self.noorder = ""
        self.last_number = self.last_number + 1
        drop_sql = "drop sequence {sequence_name};\n".format(**self.__dict__)
        create_sql = "create sequence {sequence_name} minvalue {min_value} maxvalue {max_value} {nocycle} {noorder} increment by {increment_by} start with {last_number};".format(**self.__dict__)
        return drop_sql + create_sql
