import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.util.obj_ext import ObjExt


class TestDmlInfoBo(ObjExt):
    # 业务代码
    bis_code = None
    # 基础库集
    bis_base_db_code = None
    # 业务分支
    bis_branch_name = None
    # 应用名
    module_name = None
    # db组
    db_group_name = None
    # 数据库名
    db_name = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)
