

from app_mgt.app_apidoc.util.obj_ext import ObjExt


class TestingDbInfoBo(ObjExt):
    # 业务代码
    bis_code = None
    # 业务分支
    bis_branch_name = None
    # 应用名
    module_name = None
    # 归档分支
    archive_branch_name = None

    # 数据库信息
    db_info_id = None
    db_group_name = None
    db_info_suffix_name = None
    db_info_username = None
    db_info_password = None

    # 数据库实例信息
    db_srv_type = None
    db_srv_hosts = None
    db_srv_port = None
    db_srv_name = None
    db_srv_username = None
    db_srv_password = None
    db_srv_id= None

    # 恢复历史
    restore_datetime = None

    cdc_position = None
    db_name = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

    def __repr__(self):
        return "{}:{}:{}".format(self.db_srv_hosts, self.db_srv_port, self.cdc_position)

