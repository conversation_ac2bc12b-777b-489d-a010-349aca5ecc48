import sys
import os

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.util.obj_ext import ObjExt


# mysql binlog  解析工具 参数类
class MysqlSettingsBo(ObjExt):
    host = None
    port = None
    user = None
    passwd = None

    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)
