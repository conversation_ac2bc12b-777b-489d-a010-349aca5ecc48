import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from dao.connect.mysql import DBConnectionManager
from settings import logger

class RunHandleData:

    def setExecuteParam(self, job_name, business_id, biz_code, status=None, build_id=None):
        self.job_name = job_name
        self.business_id = business_id
        self.biz_code = biz_code
        self.status = status
        self.build_id = build_id
        logger.info("设置执行参数 {},{},{},{},{}".format(job_name, business_id, biz_code, status, build_id))


    def record_build_id(self):
        logger.info('record_build_id')
        with DBConnectionManager() as db:
            sql = '''
            update jenkins_mgt_test_data_dev_job set job_build_id = '{}',update_time = now() 
            where id = {}
            '''.format(self.build_id, self.business_id)
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()

        return 'success'

    def record_execute_result(self):
        logger.info('record_execute_result')
        with DBConnectionManager() as db:
            sql = '''
            update jenkins_mgt_test_data_dev_job set status = '{}' 
            where id = {}
            '''.format(self.status, self.business_id)
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()
        return 'success'


    def call(self, execute_method):
        acceptor = {
            "record_build_id": self.record_build_id,
            "record_execute_result": self.record_execute_result,
        }
        if execute_method in acceptor:
            acceptor[execute_method]()

        return 'success'


if __name__ == '__main__':
    params = sys.argv[1:]
    logger.info("调用 {}".format(params))
    execute_method = params[0]
    job_name = params[1]
    business_id = params[2]
    biz_code = params[3]
    build_id = params[4]
    status = params[5]

    make_dump_ser = RunHandleData()
    make_dump_ser.setExecuteParam(job_name, business_id, biz_code, status, build_id)
    make_dump_ser.call(execute_method)
    sys.exit(0)
