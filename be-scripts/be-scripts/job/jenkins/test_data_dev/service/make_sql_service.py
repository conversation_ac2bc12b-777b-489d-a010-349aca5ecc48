import datetime
import logging
import os
import re
import sys

PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from common.common_tool.common_tool import exec_local_cmd
from common.mysql.models import DbMgtBisTestSql
from settings import logger


class MakeSqlService:
    @staticmethod
    def make_sql(bis_pipeline_id):
        query_set = DbMgtBisTestSql.select().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id)
        curr_time = datetime.datetime.now()
        db_ver_prefix = curr_time.strftime('%Y.%m.%d.%H.%M.%S')
        for test_sql in query_set:
            sql_ver_name = test_sql.sql_ver_name
            sql_ver_upload_status = test_sql.sql_ver_upload_status
            sql_file_name = test_sql.sql_file_name
            sql_file_path = test_sql.sql_file_path

            logging.info("sql文件： {} 的上传状态为 {}".format(sql_file_name, sql_ver_upload_status))
            logging.info("sql版本化的名字为：{}".format(sql_ver_name))
            if not sql_ver_upload_status:
                if not sql_ver_name:
                    sql_ver_name = 'V{}.{}__{}'.format(db_ver_prefix, '001', sql_file_name)

                DbMgtBisTestSql.update({DbMgtBisTestSql.sql_ver_name: sql_ver_name,
                                        DbMgtBisTestSql.sql_ver_db: sql_file_path.split('/')[1],
                                        DbMgtBisTestSql.sql_ver_group: sql_file_path.split('/')[0]}). \
                    where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id,
                          DbMgtBisTestSql.sql_file_name == sql_file_name).execute()


def handle_dml():
    # 源目录和目标目录
    src_dir = '/Users/<USER>/Desktop/trade/biz-sql-init/pre-DML'
    dst_dir = '/Users/<USER>/Desktop/trade/biz-sql-init/DML'
    # 遍历目录中的所有dml文件
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)

    # 获取源目录中的所有dml文件
    dml_files = [f for f in os.listdir(src_dir) if f.endswith(".sql")]
    curr_time = datetime.datetime.now()
    db_ver_prefix = curr_time.strftime('%Y.%m.%d.%H.%M.%S')
    count = 0
    exist_version = []
    # 遍历每个dml文件，读取并拆分
    for dml_file in dml_files:
        with open(os.path.join(src_dir, dml_file), "r") as f:
            lines = f.readlines()
        # 将dml语句拆分成多个文件
        num_files = (len(lines) + 1999) // 2000  # 计算需要拆分成多少个文件
        if dml_file[:-4] == 'flyway_schema_history':
            continue
        for i in range(num_files):
            start = i * 2000
            end = min((i + 1) * 2000, len(lines))
            # 构造新文件名：在原文件名后加上序号
            new_file_name = dml_file[:-4] + "_dml.sql"
            if count in exist_version:
                count = count + 1
            new_file_path = os.path.join(dst_dir,
                                         'V{}.900{}__{}'.format(db_ver_prefix,
                                                                '%03d' % (count),
                                                                new_file_name))
            logging.info("count:{},start:{},end:{},table:{}".format(count, start, end, dml_file[:-4]))
            # 将500行dml语句写入新文件中
            with open(new_file_path, "w") as f:
                f.writelines(lines[start:end])
            exist_version.append(count)


def handle_ddl():
    type_code = 200
    type_name = "tables"
    # 源目录和目标目录
    src_dir = '/Users/<USER>/Desktop/param/biz-sql-init/pre-DDL-tables'
    dst_dir = '/Users/<USER>/Desktop/param/biz-sql-init/DDL'
    # 遍历目录中的所有dml文件
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)
    # 获取源目录中的所有dml文件
    dml_files = [f for f in os.listdir(src_dir) if f.endswith(".sql")]
    count = 0
    exist_version = []
    # 遍历每个dml文件，读取并拆分
    for dml_file in dml_files:
        if dml_file[:-4] == 'flyway_schema_history':
            continue

        with open(os.path.join(src_dir, dml_file), "r") as f:
            lines = f.readlines()

        new_file_name = dml_file[:-4]
        if count in exist_version:
            count = count + 1
        new_file_path = os.path.join(dst_dir, 'V2023.08.08.13.00.00.{}{}__{}-{}-{}.sql'.format(type_code,
                                                                                               '%03d' % count,
                                                                                               type_code,
                                                                                               new_file_name,
                                                                                               type_name))

        # 将500行dml语句写入新文件中
        with open(new_file_path, "w") as f:
            f.writelines(lines)

        exist_version.append(count)


def underscore_to_camelcase(string):
    # 使用正则表达式将下划线后的字母转换为大写
    return re.sub(r'_([a-zA-Z])', lambda match: match.group(1).upper(), string)


def make_dml(src_dir="/data/biz-sql-split/PRE-DML", dst_dir="/data/biz-sql-split/DML"):
    # ingore_tables = [
    #     "app_feedback_log_files"
    # ]
    ingore_tables = []
    ingore_tables = [element.upper() for element in ingore_tables]
    # 遍历目录中的所有dml文件
    if not os.path.exists(dst_dir):
        os.makedirs(dst_dir)
    else:
        exec_local_cmd("rm -rf {}/*".format(dst_dir))
    # 获取源目录中的所有dml文件
    dml_files = [f for f in os.listdir(src_dir) if f.endswith(".sql")]
    count = 0
    db_ver_prefix = "2023.08.24.14.49.50"
    exist_version = []
    # 遍历每个dml文件，读取并拆分
    for dml_file in dml_files:
        if dml_file[:-4] == 'flyway_schema_history' or 'BAK' in dml_file[:-4].upper() or underscore_to_camelcase(
                dml_file[:-4]) in ingore_tables:
            print("ingore:" + dml_file[:-4])
            continue

        logger.info("待打开的文件:{}".format(dml_file))
        old_file_path = os.path.join(src_dir, dml_file)
        file_size = os.path.getsize(old_file_path)
        # 打印文件大小
        logger.info("文件大小为：{} 字节".format(file_size))
        if file_size == 0:
            continue
        # with open(os.path.join(src_dir, dml_file), "r", encoding='utf-8') as f:
        #     lines = f.readlines()
        # if not lines:
        #     print("ingore:" + dml_file[:-4])
        #     continue
        new_file_name = dml_file[:-4] + "_dump_dml.sql"
        if count in exist_version:
            count = count + 1
        new_file_path = os.path.join(dst_dir,
                                     'V{}.900{}__{}'.format(db_ver_prefix,
                                                            '%03d' % (count),
                                                            new_file_name))
        # 将500行dml语句写入新文件中
        # with open(new_file_path, "w", encoding='utf-8') as f:
        #     f.writelines(lines)
        # logger.info("table:{},编译完成:{}".format(dml_file[:-4], new_file_path))
        logger.info("cp {} {}".format(old_file_path, new_file_path))
        os.system("cp {} {}".format(old_file_path, new_file_path))
        exist_version.append(count)


if __name__ == '__main__':
    make_dml()