import sys
import os

PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)

from job.jenkins.test_data_dev.service.biz_data_split_service import *

import logging

if __name__ == '__main__':
    logging.info('数据库为： {}'.format(sys.argv[1]))
    logging.info('it100业务为： {}'.format(sys.argv[2]))
    logging.info('it300业务为： {}'.format(sys.argv[3]))
    db = sys.argv[1]
    # 计算biz_it100_01 feature_it100
    biz_db_it100 = sys.argv[2]
    biz_db_it300 = sys.argv[3]

    it100 = "it100"
    it300 = "it300"
    biz_it100 = "docker_{biz}_{db_name}_biz".format(db_name=db, biz=it100)
    biz_it300 = "docker_{biz}_{db_name}_biz".format(db_name=db, biz=it300)
    feature = "docker_multi_{db_name}_feature".format(db_name=db)

    # 业务库特有的数据
    mysql_db_create_single(biz_it100)
    # 业务库特有的数据
    mysql_db_create_single(biz_it300)
    mysql_db_create_single(feature)
    # 业务库特有it300的数据
    mysql_diff_data(biz_db_it300, biz_db_it100, biz_it300)
    # 公共数据
    mysql_diff_data(biz_db_it300, biz_it300, feature)
    # 业务库特有it100的数据
    mysql_diff_data(biz_db_it100, feature, biz_it100)

    create_root_dir("mkdir -p /data/files/feature_sql/{db_name}".format(db_name=db))
    create_root_dir("mkdir -p /data/files/biz_sql/{biz}/{db_name}".format(db_name=db, biz=it100))
    create_root_dir("mkdir -p /data/files/biz_sql/{biz}/{db_name}".format(db_name=db, biz=it300))

    clear_mysql_empty_table(biz_it100, db, it100)
    clear_mysql_empty_table(biz_it300, db, it300)
    clear_mysql_empty_table(feature, db, None)

    sys.exit(0)
