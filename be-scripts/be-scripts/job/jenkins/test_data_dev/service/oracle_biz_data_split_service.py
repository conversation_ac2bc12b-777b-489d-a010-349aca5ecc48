import datetime
import os
import sys
import traceback

from common.oracle.models import DbMgtOptScript
from common.oracle.oracle import OracleHandler

PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from typing import List
import json
import logging
from sqlalchemy import text
from settings import DATABASES
from job.jenkins.test_data_dev.service.commit_biz_repo import commit_biz_sql
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from dao.connect.oracle_sqlalchemy import DBConnectionManagerForSqlalchemyOracle
from dao.connect.mysql import DBConnectionManager
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd, http_request_post


def oracle_export_table_data(db_name, table, db, biz):
    #
    if db_name.endswith("feature"):
        type = "feature_sql"
    else:
        type = "biz_sql/{}".format(biz)
    cmd = '''mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob -h ************** -P 3306 -uscm -p123456  "{db_name}" "{table}" --result-file="/data/files/{type}/{db}/{table}.dml.sql" --no-create-info --skip-add-drop-table --skip-disable-keys --skip-create-options --skip-lock-tables --skip-add-locks --skip-extended-insert --skip-comments'''.format(
        type=type, db_name=db_name, table=table, db=db)
    """远程执行启动命令"""
    sshpass_cmd = "sshpass -p {} ssh root@{} {}".format('howbuy1!', "**************", cmd)
    return exec_local_cmd(sshpass_cmd)


def oracle_biz_data(biz_db, diff_data_db, basic_db):
    with DBConnectionManagerForSqlalchemyOracle("***************", 1521,
                                                diff_data_db, "howbuy2015", "hbqa11g") as db:
        query_tables_sql = '''SELECT table_name FROM all_tables
                        WHERE owner = '{biz_db}' and table_name not in 
                             (SELECT table_name FROM all_tables
                                      WHERE owner = '{basic_db}')'''.format(
            biz_db=biz_db.upper(), basic_db=basic_db.upper())
        table_tuples = db.session.execute(text(query_tables_sql)).fetchall()
        for table_tuple in table_tuples:
            table = table_tuple[0]
            if table != 'flyway_schema_history' and table not in get_ora_err_value():
                insert_diff_data_sql = '''INSERT INTO {table} select * from {biz_db}.{table}'''.format(
                    table=table, biz_db=biz_db)
                db.session.execute(text(insert_diff_data_sql))
                db.session.commit()
        logging.info("{} 独有表供 {} 迁移进 {}处理完毕 ".format(biz_db, len(table_tuples), diff_data_db))


def oracle_diff_biz_data(biz_db, diff_data_db, basic_db):
    with DBConnectionManagerForSqlalchemyOracle("***************", 1521,
                                                diff_data_db, "howbuy2015", "hbqa11g") as db:
        query_tables_sql = '''SELECT table_name FROM all_tables
                        WHERE owner = '{biz_db}' and table_name in 
                             (SELECT table_name FROM all_tables
                                      WHERE owner = '{basic_db}')'''.format(
            biz_db=biz_db.upper(), basic_db=basic_db.upper())
        lines = []
        table_tuples = db.session.execute(text(query_tables_sql)).fetchall()
        for table_tuple in table_tuples:
            table = table_tuple[0]
            if table != 'flyway_schema_history' and table not in get_ora_err_value():
                query_unique_sql = '''SELECT LISTAGG(cols.column_name, ',') WITHIN GROUP (ORDER BY cols.column_name)
                                      FROM all_constraints cons, all_cons_columns cols
                                    WHERE cons.constraint_type = 'P'
                                    AND cons.owner = '{biz_db}'
                                    AND cons.table_name = '{table}'
                                    AND cols.table_name = cons.table_name
                                    AND cols.owner = cons.owner
                                    AND cols.constraint_name = cons.constraint_name
                                    group by cons.constraint_type
                                    '''.format(
                    table=table, biz_db=biz_db.upper())
                join_condition = ''
                unique_cols = db.session.execute(text(query_unique_sql)).fetchone()
                if unique_cols:
                    for col in unique_cols[0].split(","):
                        if join_condition:
                            join_condition = join_condition + f' AND {biz_db}.{table}.{col}={basic_db}.{table}.{col}'
                        else:
                            join_condition = join_condition + f'{biz_db}.{table}.{col}={basic_db}.{table}.{col}'
                if not join_condition:
                    set_warn_value(
                        "biz_db={},diff_data_db={},basic_db={} table={}没有唯一键，使用全字段匹配".format(biz_db,
                                                                                                         diff_data_db,
                                                                                                         basic_db,
                                                                                                         table))
                    query_unique_sql = '''
                         SELECT column_name
                          FROM all_tab_columns
                           WHERE table_name = '{table}'
                           AND owner = '{biz_db}'
                    '''.format(table=table, biz_db=biz_db.upper())
                    all_cols = db.session.execute(text(query_unique_sql)).fetchall()
                    for col in all_cols:
                        col = col[0]
                        if join_condition:
                            join_condition = join_condition + f' AND {biz_db}.{table}.{col}={basic_db}.{table}.{col}'
                        else:
                            join_condition = join_condition + f'{biz_db}.{table}.{col}={basic_db}.{table}.{col}'
                if not join_condition:
                    set_warn_value(
                        "biz_db={},diff_data_db={},basic_db={} table={}没有唯一键".format(biz_db, diff_data_db,
                                                                                          basic_db, table))
                    continue

                # ch   查询diff sql
                query_diff_data_sql = '''select count(*) as count_num from {biz_db}.{table}  where ( select count(1) from {basic_db}.{table}  where {join_condition} )=0
                '''.format(table=table, biz_db=biz_db, join_condition=join_condition, basic_db=basic_db,
                           diff_data_db=diff_data_db)

                insert_diff_data_sql = '''INSERT INTO {table} select * from {biz_db}.{table}  where ( select count(1) from {basic_db}.{table}  where {join_condition} )=0'''.format(
                    table=table, biz_db=biz_db, join_condition=join_condition, basic_db=basic_db,
                    diff_data_db=diff_data_db)
                try:
                    diff_datas = db.session.execute(text(query_diff_data_sql)).fetchall()
                except Exception as ex:
                    if "ORA-00904" in str(ex):
                        set_columns_inconsistent_value(biz_db, basic_db, str(ex))
                    logging.info(ex)
                    continue

                if diff_datas and diff_datas[0][0] > 0:
                    logging.info("diff_data_num :{}".format(diff_datas[0][0]))
                    # 清空数据
                    clear_data_sql = '''delete from {diff_data_db}.{table}'''.format(table=table,
                                                                                     diff_data_db=diff_data_db)
                    # db.session.execute(text(clear_data_sql))
                    # db.session.commit()
                    lines.append(clear_data_sql)
                    lines.append("\r\n")
                    lines.append(insert_diff_data_sql)
                    db.session.execute(text(insert_diff_data_sql))
                    db.session.commit()
                    lines.append("\r\n")
        if lines:
            with open(
                    "./{}.sql".format(diff_data_db),
                    'w',
                    encoding='utf-8', ) as f:
                f.writelines(lines)
            set_result_value(
                "diff_sql生成语句在{};人工检查，如有异常,请使用用户:{}执行此文件".format("./{}.sql".format(diff_data_db),
                                                                                        diff_data_db))
        else:
            set_warn_value(
                "biz_db={},diff_data_db={},basic_db={} 没有diff_sql生成".format(biz_db, diff_data_db, basic_db))


def drop_empty_table(biz_diff, feature_table):
    if not biz_diff.endswith("biz") or not feature_table.endswith("feature"):
        raise Exception("biz_diff:{},feature_table:{} 错误！！！".format(biz_diff, feature_table))
    drop_table(biz_diff)
    drop_table(feature_table)


def drop_table(db_name):
    with DBConnectionManagerForSqlalchemyOracle("***************", 1521,
                                                db_name, "howbuy2015", "hbqa11g") as db:
        query_tables_sql = """SELECT table_name FROM all_tables
                                  WHERE owner = '{db_name}' """.format(
            db_name=db_name.upper())
        table_tuples: List = db.session.execute(text(query_tables_sql)).fetchall()
        for table_tuple in table_tuples:
            table = table_tuple[0]
            if table != 'flyway_schema_history':
                try:
                    sql_template = '''select count(*) as count_num from {table}'''.format(table=table)
                    diff_datas = db.session.execute(text(sql_template)).fetchall()
                    if diff_datas and diff_datas[0][0] == 0:
                        sql_template = "drop table {} cascade constraints".format(table)
                        db.session.execute(text(sql_template))
                except Exception as e:
                    logging.warning(e)


def oracle_truncate_data(db_info):
    try:
        db_srv_hosts = db_info.get("db_srv_hosts")
        username = db_info.get("username")
        password = db_info.get("password")
        db_srv_name = db_info.get("db_srv_name")
        suite_db_name = db_info.get("suite_db_name")
        db_srv_username = db_info.get("db_srv_username")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        logging.info("开始执行数据库->begin {},{},{},{}".format(db_srv_hosts, username, password, db_srv_name))
        opt_type = 17
        dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)
        oh = OracleHandler("spider", "it29", [], "PA-TEST_master")
        oh._execute_sql_cmd(dump_file_path=db_info.get("data_dump_dir"),
                            sql_script=dos.script.format(username=username),
                            username=username, db_srv_hosts=db_srv_hosts,
                            db_srv_username=db_srv_username,
                            db_srv_password=db_srv_password,
                            db_srv_bash_profile=db_srv_bash_profile,
                            opt_type=opt_type, is_not_sys=True, db_srv_name=db_srv_name,
                            password=password)
        # with DBConnectionManager(host=db_srv_hosts, port=1521, user=username,
        #                          password=password, db=db_srv_name, charset=DATABASES["CHARSET"]) as db:
        #     sql = '''
        #         SELECT DISTINCT table_name FROM all_tables WHERE owner = '{}'
        #     '''.format(suite_db_name)
        #     sql_result = db.session.execute(text(sql))
        #     logging.info('sql_result--->{}'.format(sql_result))
        #     for row in sql_result:
        #         table_name = row[0]
        #         if table_name != 'flyway_schema_history':
        #             row_sql = """SELECT COUNT(*) FROM {table_name} """.format(table_name=table_name)
        #             logging.info('row_sql:{}'.format(row_sql))
        #             count = db.session.execute(text(row_sql)).fetchone()
        #             if count[0] == 0:
        #                 continue
        #             else:
        #                 truncate_data_sql = '''truncate table {table_name}'''.format(table_name=table_name)
        #                 db.session.execute(text(truncate_data_sql))
        #                 db.session.commit()
        #         logging.info('oracle_truncate_data:{} truncate table {} success'.format(db_srv_name, table_name))
    except Exception as e:
        traceback.print_exc()
        raise Exception(e)


def mysql_truncate_data(db_info):
    try:
        db_srv_hosts = db_info.get("db_srv_hosts")
        username = db_info.get("username")
        password = db_info.get("password")
        suite_db_name = db_info.get("suite_db_name")

        with DBConnectionManagerForSqlalchemy(host=db_srv_hosts, port=3306, user=username,
                                              password=password,
                                              db=suite_db_name, charset='utf8') as db:
            sql = '''
                    SELECT table_name FROM information_schema.tables WHERE table_schema = '{}';
                  '''.format(suite_db_name)
            sql_result = db.session.execute(text(sql)).fetchall()
            close_foreign_key_sql = '''SET FOREIGN_KEY_CHECKS=0; '''
            db.session.execute(text(close_foreign_key_sql))
            db.session.commit()

            for row in sql_result:
                table_name = row[0]
                if 'flyway_schema_history' != table_name:
                    truncate_data_sql = '''truncate table {table_name}'''.format(table_name=table_name)
                    db.session.execute(text(truncate_data_sql))
                    db.session.commit()
                    logging.info('mysql_truncate_data->truncate table {} success'.format(table_name))
            open_foreign_key_sql = '''SET FOREIGN_KEY_CHECKS=1; '''
            db.session.execute(text(open_foreign_key_sql))
            db.session.commit()
    except Exception as e:
        traceback.print_exc()
        raise Exception(e)


# 授予权限当前用户访问业务库和基准库所有表；
def oracle_grant(biz_db, diff_data_db, basic_db):
    with DBConnectionManagerForSqlalchemyOracle("***************", 1521,
                                                "ORACDC", "ORACDC", "hbqa11g") as db:
        if not diff_data_db.endswith("biz") and not diff_data_db.endswith("feature"):
            raise Exception("diff_data_db:{} 错误！！！".format(diff_data_db))
        else:
            try:
                # 删除用户
                create_user = '''DROP USER {} cascade'''.format(diff_data_db)
                db.session.execute(text(create_user))
            except Exception as e:
                logging.error(str(e))
            # 创建用户
            create_user = '''CREATE USER {} identified by howbuy2015'''.format(diff_data_db)
            db.session.execute(text(create_user))

            grant_user_connect_resource = '''grant connect, resource to {}'''.format(diff_data_db)
            db.session.execute(text(grant_user_connect_resource))

            grant_user_seq = ['grant alter any table to {}',
                              'grant select any table to {}',
                              'grant delete any table to {}',
                              'grant insert any table to {}',
                              'grant update any table to {}',
                              'grant drop any table to {}',
                              'grant SELECT ANY TRANSACTION to {}']

            for grant_user in grant_user_seq:
                grant_user = grant_user.format(diff_data_db.upper())
                db.session.execute(text(grant_user))

            query_tables_sql = """SELECT table_name FROM all_tables
                              WHERE owner = '{biz_db}' """.format(
                biz_db=biz_db.upper(), basic_db=basic_db.upper())
            table_tuples: List = db.session.execute(text(query_tables_sql)).fetchall()

            query_tables_sql = """SELECT table_name FROM all_tables
                                                      WHERE owner = '{basic_db}'""".format(basic_db=basic_db.upper())
            table_tuples_basic: List = db.session.execute(text(query_tables_sql)).fetchall()

            table_tuples.extend(table_tuples_basic)

            for table_tuple in table_tuples:
                table = table_tuple[0]
                if table != 'flyway_schema_history':
                    try:
                        sql_template = '''GRANT SELECT ON {0}.{1} TO {2}'''.format(basic_db, table, diff_data_db)
                        db.session.execute(text(sql_template))
                    except Exception as e:
                        logging.warning(e)
                    try:
                        sql_template = '''GRANT SELECT ON {0}.{1} TO {2}'''.format(biz_db, table, diff_data_db)
                        db.session.execute(text(sql_template))
                    except Exception as e:
                        logging.warning(e)
            create_table(table_tuples, biz_db, basic_db, diff_data_db)
            logging.info("grant over !!!")


def create_table(table_tuples, biz_db, basic_db, diff_data_db):
    tables = []
    with DBConnectionManagerForSqlalchemyOracle("***************", 1521,
                                                diff_data_db, "howbuy2015", "hbqa11g") as db:
        for table_tuple in table_tuples:
            table = table_tuple[0]
            if table != 'flyway_schema_history' and table not in tables:
                tables.append(table)
                try:
                    table_str = "CREATE TABLE {} AS SELECT * FROM {}.{} WHERE 1=2".format(table, biz_db, table)
                    db.session.execute(text(table_str))
                except Exception as e:
                    if "ORA-06564" in str(e):
                        set_ora_err_value(table)
                    logging.warning(e)
                    try:
                        table_str = "CREATE TABLE {} AS SELECT * FROM {}.{} WHERE 1=2".format(table, basic_db, table)
                        db.session.execute(text(table_str))
                    except Exception as e:
                        logging.warning(e)
                        if "ORA-06564" in str(e):
                            set_ora_err_value(table)


def execute(payload):
    logging.info("{} 执行中。。。".format(payload))
    url = "http://127.0.0.1:8080/api/dump/download/lctzt"
    try:
        res = http_request_post(url, payload, headers={'Content-Type': 'application/json'})
        result = res.content
        logging.info(result)
        return 0
    except Exception as ex:
        logging.error(ex)
        return "导出数据异常：{}".format(payload)


def init():  # 初始化
    global warn_result
    global results
    global ORA_ERR
    global columns_inconsistent
    warn_result = []
    results = []
    ORA_ERR = []
    columns_inconsistent = []


def set_warn_value(value):
    """ 定义一个全局变量 """
    warn_result.append(value)


def set_columns_inconsistent_value(biz, basic, column):
    """ 定义一个全局变量 """
    columns_inconsistent.append({"biz": biz, "basic": basic, "column": column})


def set_ora_err_value(value):
    """ 定义一个全局变量 """
    ORA_ERR.append(value)


def set_result_value(value):
    """ 定义一个全局变量 """
    results.append(value)


def get_result_value():
    """ 定义一个全局变量 """
    return results


def get_warn_value():
    """ 定义一个全局变量 """
    return warn_result


def get_ora_err_value():
    """ 定义一个全局变量 """
    return ORA_ERR


def get_columns_inconsistent_value():
    """ 定义一个全局变量 """
    return columns_inconsistent


if __name__ == '__main__':
    logging.info('数据库为： {}'.format(sys.argv[1]))
    logging.info('业务库： {}'.format(sys.argv[2]))
    logging.info('基准库： {}'.format(sys.argv[3]))
    logging.info('业务为： {}'.format(sys.argv[4]))
    logging.info('业务分支为： {}'.format(sys.argv[5]))
    logging.info('sql_file_path为： {}'.format(sys.argv[6]))
    db_name = sys.argv[1]
    biz = sys.argv[4]
    biz_db = sys.argv[2]
    basic_db = sys.argv[3]
    bis_branch_name = sys.argv[5]
    sql_file_path = sys.argv[6]
    biz_diff_data_db = "docker_{biz}_{db_name}_biz".format(db_name=db_name, biz=biz)
    feature_data_db = "docker_{biz}_{db_name}_feature".format(db_name=db_name, biz=biz)
    init()
    # 业务库特有的数据
    oracle_grant(biz_db, biz_diff_data_db, basic_db)
    oracle_diff_biz_data(biz_db, biz_diff_data_db, basic_db)
    oracle_biz_data(biz_db, biz_diff_data_db, basic_db)
    # 业务库-业务库特有的数据=feature_data
    oracle_grant(biz_db, feature_data_db, biz_diff_data_db)
    oracle_diff_biz_data(biz_db, feature_data_db, biz_diff_data_db)
    drop_empty_table(biz_diff_data_db, feature_data_db)

    for result in results:
        if biz_diff_data_db in str(result):
            ver_prefix = 'V{}.'.format(datetime.datetime.now().strftime('%Y.%m.%d.%H.%M.%S'))
            dml_root_path = "/data/files/biz_sql/{}/{}".format(biz, db_name)
            payload = {
                "host": "***************",
                "port": "1521",
                "sid": "hbqa11g",
                "username": biz_diff_data_db,
                "password": "howbuy2015",
                "dmlRootPath": dml_root_path,
                "ignoreTables": "flyway_schema_history",
                "dmlNamePrefix": ver_prefix
            }
            execute_result = execute(payload)
            if execute_result == 0:
                commit_biz_sql(biz, bis_branch_name, sql_file_path, dml_root_path)
        elif feature_data_db in str(result):
            payload = {
                "host": "***************",
                "port": "1521",
                "sid": "hbqa11g",
                "username": feature_data_db,
                "password": "howbuy2015",
                "dmlRootPath": "/data/files/feature_sql/{}".format(db_name),
                "ignoreTables": "flyway_schema_history"
            }
            execute(payload)

    print("结果文件:")
    print(json.dumps(get_result_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))
    print("外部表 object does not exist 需要找用户确认:")
    print(json.dumps(get_ora_err_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))
    print("没有唯一键的表 需要找用户确认:")
    print(json.dumps(get_warn_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))

    print("对比的库的字段不一致的表 需要找用户确认:")
    print(json.dumps(get_columns_inconsistent_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))
