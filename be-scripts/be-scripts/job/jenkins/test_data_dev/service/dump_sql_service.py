import json
import sys
import os

PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from common.common_tool.common_tool import exec_local_cmd
from job.jenkins.test_data_dev.cdc_common.cdc_constants import DbCdcEnum
from settings import TEST_DATA_INIT, logger
from dao.connect.mysql import DBConnectionManager

if __name__ == '__main__':
    env = sys.argv[1]
    db_name = sys.argv[2]
    db_user = sys.argv[3]
    db_pwd = sys.argv[4]
    db_srv_ip = sys.argv[5]
    env_db_name = "docker_{}_{}".format(env, db_name)
    # 拆分dml文件存放目录
    if not os.path.exists(DbCdcEnum.SPLIT_PRE_DML_DIR.value):
        os.makedirs(DbCdcEnum.SPLIT_PRE_DML_DIR.value)
    else:
        exec_local_cmd("rm -rf {}/*".format(DbCdcEnum.SPLIT_PRE_DML_DIR.value))
    ignore_tables = json.loads(TEST_DATA_INIT["test_dml_cdc_db_ignore_table"]).get(db_name,[])
    logger.info("ignore_tables:{}".format(ignore_tables))
    # 查询所有可拆分的表
    with DBConnectionManager(host=db_srv_ip, port=3306, user=db_user, password=db_pwd, db=env_db_name) as db:
        cursor = db.cur
        db.cur.execute("""SHOW TABLES;""")
        # 获取所有表
        tables = db.cur.fetchall()
        # 获取所有表名
        for table in tables:
            table_name = table['Tables_in_{}'.format(env_db_name)]
            if table_name in ignore_tables:
                continue
            # 查询该表的记录数
            cursor.execute("""SELECT COUNT(*) FROM {};""".format(table_name))
            count = cursor.fetchone()
            if count['COUNT(*)'] == 0:
                continue
            mysql_dump_cmd = "mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob -h{db_srv_ip} -u{db_user} -p'{db_pwd}' --no-create-info --complete-insert --hex-blob --skip-comments --set-gtid-purged=OFF {env_db_name} {table_name} > {pre_dml_dir}/{table_name}.sql".format(
                    db_user=db_user, db_pwd=db_pwd, db_srv_ip=db_srv_ip, env_db_name=env_db_name, table_name=table_name,
                    pre_dml_dir=DbCdcEnum.SPLIT_PRE_DML_DIR.value)
            # mysql_dump_cmd = "mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob -h{db_srv_ip} -u{db_user} -p{db_pwd} --no-create-info  --set-gtid-purged=OFF " \
            #                  "{env_db_name} {table_name} > {pre_dml_dir}/{table_name}.sql".format(
            #     db_user=db_user, db_pwd=db_pwd, db_srv_ip=db_srv_ip, env_db_name=env_db_name, table_name=table_name,
            #     pre_dml_dir=DbCdcEnum.SPLIT_PRE_DML_DIR.value)
            logger.info("dump表{}命令".format(mysql_dump_cmd))
            logger.info("开始dump表{}".format(table_name))
            exec_local_cmd(mysql_dump_cmd, timeout=600)
            logger.info("dump表{}成功".format(table_name))
    sys.exit(0)
