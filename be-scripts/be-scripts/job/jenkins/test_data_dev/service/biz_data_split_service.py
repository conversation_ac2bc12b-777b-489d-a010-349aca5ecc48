import datetime
import sys
import os

PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from job.jenkins.test_data_dev.service.commit_biz_repo import commit_biz_sql, commit_feature_sql

import logging
from sqlalchemy import text

from dao.connect.mysql import DBConnectionManager
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd, get_tomcat_password, \
    exec_local_cmd_by_sshpass_tomcat


def clear_mysql_empty_table(biz_db, db_name, biz):
    with DBConnectionManager() as db:
        cursor = db.cur
        db.cur.execute("""SELECT table_name FROM information_schema.tables WHERE table_schema = '{biz_db}'""".format(
            biz_db=biz_db))
        # 获取所有表
        tables = db.cur.fetchall()
        count = 1
        for current_table in tables:
            current_table = current_table.get('table_name')
            check_sql = f'SELECT COUNT(*)>0 as count_num FROM {biz_db}.{current_table} '
            cursor.execute(check_sql)
            count_num = cursor.fetchone()
            drop_sql = f'DROP TABLE IF EXISTS {biz_db}.{current_table}'
            if count_num is not None:
                if count_num.get('count_num') == 0:
                    cursor.execute(drop_sql)
                else:
                    if current_table != "flyway_schema_history":
                        export_table_data(biz_db, current_table, db_name, biz, count)
                        count = count + 1
            else:
                cursor.execute(drop_sql)
    return count


def export_table_data(db_name, table, db, biz, count=0):
    #
    if db_name.endswith("feature"):
        type = "feature_sql"
    else:
        type = "biz_sql/{}".format(biz)
    db_ver_prefix = datetime.datetime.now().strftime('%Y.%m.%d.%H.%M.%S')
    sql_file_idx = '%03d' % count
    sql_ver_str = 'V{}.{}__{}'.format(db_ver_prefix, sql_file_idx, table + ".dml.sql")
    cmd = '''mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob -h ************** -P 3306 -uscm -p123456  "{db_name}" "{table}" --result-file="/data/files/{type}/{db}/DML/{sql_name}" --no-create-info --skip-add-drop-table --skip-disable-keys --skip-create-options --skip-lock-tables --skip-add-locks --skip-extended-insert --skip-comments'''.format(
        type=type, db_name=db_name, table=table, db=db, sql_name=sql_ver_str)
    """远程执行启动命令"""
    sshpass_cmd = "sshpass -p {} ssh root@{} {}".format('howbuy1!', "**************", cmd)
    return exec_local_cmd(sshpass_cmd)


def mysql_diff_data(biz_db, diff_data_db, biz_basic_db):
    with DBConnectionManager() as db:
        cursor = db.cur
        db.cur.execute("""SELECT table_name FROM information_schema.tables WHERE table_schema = '{biz_db}'""".format(
            biz_db=biz_db))
        # 获取所有表
        tables = db.cur.fetchall()
        for current_table in tables:
            current_table = current_table.get('table_name')
            # 查出表的唯一键 组装join条件
            join_unique_sql = '''SELECT GROUP_CONCAT(COLUMN_NAME) COLUMN_NAME FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = '{biz_db}' 
                    AND TABLE_NAME = '{current_table}' AND NON_UNIQUE = 0 AND COLUMN_NAME <> 'id' GROUP BY INDEX_NAME '''.format(
                current_table=current_table, biz_db=biz_db)
            logging.info(join_unique_sql)
            # 查询当前表的主键字段
            cursor.execute(join_unique_sql)
            uniq_cols = cursor.fetchall()
            no_uniq_cols_tables = []
            if uniq_cols:
                join_condition = ''
                logging.info(uniq_cols)
                for col in uniq_cols[0]['COLUMN_NAME'].split(","):
                    if join_condition:
                        join_condition = join_condition + f' AND {biz_db}.{current_table}.{col}={diff_data_db}.{current_table}.{col}'
                    else:
                        join_condition = join_condition + f'{biz_db}.{current_table}.{col}={diff_data_db}.{current_table}.{col}'
                # 删除表
                drop_sql = f'DROP TABLE IF EXISTS {biz_basic_db}.{current_table}'
                cursor.execute(drop_sql)
                # 创建表
                create_sql = f'CREATE TABLE IF NOT EXISTS {biz_basic_db}.{current_table} ' \
                             f'LIKE {biz_db}.{current_table}'
                cursor.execute(create_sql)

                create_sql = f'CREATE TABLE IF NOT EXISTS {diff_data_db}.{current_table} ' \
                             f'LIKE {biz_db}.{current_table}'
                cursor.execute(create_sql)

                query_diff_data_sql = '''select * from {biz_db}.{table}  where ( select count(1) from {diff_data_db}.{table}  where {join_condition} )=0'''.format(
                    table=current_table, biz_db=biz_db, join_condition=join_condition,
                    diff_data_db=diff_data_db)
                cursor.execute(query_diff_data_sql)
                diff_datas = db.cur.fetchall()
                if diff_datas:
                    # 插入DIFF数据
                    insert_sql = f'INSERT INTO {biz_basic_db}.{current_table} {query_diff_data_sql} '
                    cursor.execute(insert_sql)
                else:
                    logging.info("table:{} 没有diff_data".format(current_table))
            else:
                no_uniq_cols_tables.append(current_table)
        cursor.close()
        logging.info(f"{biz_db} 数据库 diff data 生成完毕;")
        logging.warning("no_uniq_cols_tables:{};这些表需要人工确认唯一键！！！！！！".format(no_uniq_cols_tables))


def mysql_db_create(db_name, biz):
    with DBConnectionManager() as db:
        cursor = db.cur
        # 创建feture库
        cursor.execute(
            """DROP DATABASE  IF EXISTS docker_{biz}_{db_name}_feature """.format(
                db_name=db_name, biz=biz))
        cursor.execute(
            """CREATE DATABASE docker_{biz}_{db_name}_feature DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci""".format(
                db_name=db_name, biz=biz))
        # 创建业务库
        cursor.execute(
            """DROP DATABASE  IF EXISTS docker_{biz}_{db_name}_biz """.format(
                db_name=db_name, biz=biz))
        cursor.execute(
            """CREATE DATABASE docker_{biz}_{db_name}_biz DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci""".format(
                db_name=db_name, biz=biz))


def mysql_db_create_single(db_name):
    with DBConnectionManager() as db:
        cursor = db.cur
        # 创建库
        cursor.execute(
            """DROP DATABASE  IF EXISTS {db_name} """.format(
                db_name=db_name))
        cursor.execute(
            """CREATE DATABASE {db_name} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci""".format(
                db_name=db_name))


def create_root_dir(cmd):
    try:
        sshpass_cmd = "sshpass -p {} ssh root@{} {}".format('howbuy1!', "**************", cmd)
        exec_local_cmd(sshpass_cmd)
    except Exception as ex:
        logging.warning(ex)

 # it101 master OTCMOCK/otcmock/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
 # it304 master PAY/pay/DML /archive/0.0.1/ /Users/<USER>/Desktop/sql/pay/biz-sql-init/DML biz None
 # it304 master TRADE/trade/DML /archive/0.0.1/ /Users/<USER>/Desktop/sql/trade/biz-sql-init/DML biz None
if __name__ == '__main__':
    logging.info('业务基础库集为： {}'.format(sys.argv[1]))
    logging.info('业务分支为： {}'.format(sys.argv[2]))
    basic_db_code = sys.argv[1]
    biz_branch = sys.argv[2]
    sql_file_path = sys.argv[3]
    archive_path = sys.argv[4]
    src_sql_root_path = sys.argv[5]
    type = sys.argv[6]
    db_group = sys.argv[7]
    commit_biz_sql(basic_db_code, biz_branch, sql_file_path, src_sql_root_path, archive_path)
    sys.exit(0)
    # if type == 'feature':
    #     # None master None /archive/tradenew-init-sql/trade/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature TRADE
    #     # None master None /archive/pa-sql-0530/param/DDL /Users/<USER>/Desktop/param/biz-sql-init/DDL feature PARAM
    #     # None master None /archive/init-sql/zhzx_hk/DDL /Users/<USER>/Desktop/zhzx_hk/dev_init/DDL feature ZHZX_HK
    #     # None master None /archive/init-sql/otcmock/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature OTCMOCK
    #     # None master None /archive/fin-init-sql/fin/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature FIN
    #     # None master None /archive/fin-init-sql/his/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature HIS
    #     # None master None /archive/settle-init-sql/settle/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature SETTLE
    #     # None master None /archive/pay-init-sql/pay/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature PAY
    #     # None master None /archive/esign-init-sql/pay/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature ESIGN
    #     # None master None /archive/deal-init-sql/pay/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature DEAL
    #     # None master None /archive/lctzt-init-sql/pay/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature LCTZT
    #     # None master None /archive/zhzx-init-sql/zhzx_0/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature ZHZX
    #     # None master None /archive/zhzx-init-sql/zhzx_1/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature ZHZX
    #     # None master None /archive/zhzx-init-sql/zhzx_index/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature ZHZX
    #     # None master None /archive/zhzx-hk-init-sql/zhzx_hk/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature ZHZX_HK
    #     # None master None /archive/otcmock-init-sql/zhzx_hk/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature OTCMOCK
    #     # None master None /archive/fpc-init-sql/reptile/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature FPC
    #     # None master None /archive/fpc-init-sql/fof/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature FPC
    #     # None master None /archive/support-init-sql/support/DDL /Users/<USER>/Desktop/trade/biz-sql-init/DDL feature SUPPORT
    #     commit_feature_sql(db_group, biz_branch,src_sql_root_path, archive_path)
    # elif type == 'biz':
        # it100 master TRADE/trade/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it100 master PARAM/param/DML /archive/0.0.1/ /Users/<USER>/Desktop/param/biz-sql-init/DML biz None
        # it100 master ZHZX_HK/zhzx_hk/DML /archive/0.0.1/ /Users/<USER>/Desktop/zhzx_hk/biz_init/DML biz None
        # basic master OTC/otc/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master TRADE/trade/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master FIN/fin/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master SETTLE/settle/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master PAY/pay/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master ESIGN/esign/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master DEAL/deal/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master LCTZT/lctzt/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master ZHZX_HK/zhzx_hk/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # basic master ZHZX/zhzx_index/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it100 master OTC/otc/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it101 master OTC/otc/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it100 master OTCMOCK/otcmock/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it101 master OTCMOCK/otcmock/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # fpc master FPC/reptile/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # fpc master FPC/fof/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # fpc master FPC/support/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it102 master FIN/fin/DML /archive/0.0.1/ /Users/<USER>/Desktop/trade/biz-sql-init/DML biz None
        # it200 master FPS\\st_high\\DML \\archive\\0.0.1\\ D:\\data\\st_high\\dml biz None
        # it200 master SUPPORT\\support\\DML \\archive\\0.0.1\\ D:\\data\\support\\dml biz None
        # it200 master FPS\\fpc_report\\DML \\archive\\0.0.1\\ D:\\data\\fpc_report\\dml biz None
        # it200 master FPS\\fpc_manage\\DML \\archive\\0.0.1\\ D:\\data\\fpc_manage\\dml biz None
        # it200 master FPS\\st_fund\\DML \\archive\\0.0.1\\ D:\\data\\st_fund\\dml biz None
        # it200 master FPS\\st_hedge\\DML \\archive\\0.0.1\\ D:\\data\\st_hedge\\dml biz None
        # it200 master FPS\\st_main\\DML \\archive\\0.0.1\\ D:\\data\\st_main\\dml biz None
        # it200 master FPS\\st_market\\DML \\archive\\0.0.1\\ D:\\data\\st_market\\dml biz None
        # it200 master FPS\\st_portfolio\\DML \\archive\\0.0.1\\ D:\\data\\st_portfolio\\dml biz None
        # it200 master FPS\\st_fof\\DML \\archive\\0.0.1\\ D:\\data\\st_fof\\dml biz None
        # it200 master FPS\\st_insurance\\DML \\archive\\0.0.1\\ D:\\data\\st_insurance\\dml biz None
        # it200 master FPS\\st_ashare\\DML \\archive\\0.0.1\\ D:\\data\\st_ashare\\dml biz None
        # it200 master FPS\\st_fixed\\DML \\archive\\0.0.1\\ D:\\data\\st_fixed\\dml biz None
        # it200 master FPS\\st_market\\DML \\archive\\0.0.1\\ D:\\data\\st_market\\dml biz None
        # it200 master FPS\\st_pe\\DML \\archive\\0.0.1\\ D:\\data\\st_pe\\dml biz None
        # it200 master FPS\\fpc_sync\\DML \\archive\\0.0.1\\ D:\\data\\fpc_sync\\dml biz None
        # it200 master FPS\\va_fund\\DML \\archive\\0.0.1\\ D:\\data\\va_fund\\dml biz None
        # it200 master FPS\\st_fund\\DML \\archive\\0.0.1\\ D:\\data\\st_fund\\dml biz None

