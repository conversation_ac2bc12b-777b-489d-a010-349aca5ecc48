import datetime
import logging
import os

from common.mysql.models import DbMgtBisTestSql, DbMgtBisTestSqlCommitHis
from job.jenkins.test_data_dev.util.cdc_file_util import get_bis_iter_test_dml_root_path
from common.ext_cmd import shell_cmd as common_shell_cmd


class PushSqlService:
    @staticmethod
    def push_sql(bis_pipeline_id, bis_code, bis_branch_name, operate_user, biz_base_db_code, bis_src_root_path):
        query_set = DbMgtBisTestSql.select().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id)
        curr_time = datetime.datetime.now()
        for test_sql in query_set:
            if not test_sql.sql_ver_name:
                continue
            sql_ver_name = test_sql.sql_ver_name
            sql_ver_upload_status = test_sql.sql_ver_upload_status
            sql_src_path = os.path.join(bis_src_root_path, test_sql.sql_file_path,
                                        sql_ver_name)
            sql_ver_path = os.path.sep.join(
                [get_bis_iter_test_dml_root_path(biz_base_db_code, bis_branch_name), test_sql.sql_file_path])
            if not os.path.exists(sql_ver_path):
                cmd = 'mkdir -p {}'.format(sql_ver_path)
                os.system(cmd)

            if sql_ver_name and not sql_ver_upload_status:
                sql_ver_path = os.path.join(sql_ver_path, sql_ver_name)
                cmd = 'pwd && cp {} {}'.format(sql_src_path, sql_ver_path)
                logging.info(cmd)
                os.system(cmd)
        os.chdir(get_bis_iter_test_dml_root_path(biz_base_db_code, bis_branch_name))
        cmd = 'git pull origin {}'.format(bis_branch_name)
        os.system(cmd)
        cmd = 'git add -A'
        os.system(cmd)
        cmd = 'git commit -m "{}"'.format('提交diff_sql')
        return_code = os.system(cmd)
        logging.info('执行的cmd:{},返回的结果：{}'.format(cmd, return_code))
        cmd = 'git push origin {}'.format(bis_branch_name)
        return_code = os.system(cmd)
        logging.info('执行的cmd:{},返回的结果：{}'.format(cmd, return_code))

        get_commit_id_command = 'git rev-parse HEAD'
        stdcode, latest_commit_id = common_shell_cmd.shell_cmd(get_commit_id_command)
        logging.info("获取最后一次提交记录结果为{}，最后一次提交记录为{}".format(stdcode, latest_commit_id))
        if stdcode == 0:
            # 去除回车符
            latest_commit_id = latest_commit_id.replace('\n', '').replace('\r', '')
            for test_sql in query_set:
                sql_ver_name = test_sql.sql_ver_name
                sql_ver_upload_status = test_sql.sql_ver_upload_status
                if sql_ver_name and not sql_ver_upload_status:
                    DbMgtBisTestSql.update({DbMgtBisTestSql.update_user: operate_user,
                                            DbMgtBisTestSql.update_time: curr_time,
                                            DbMgtBisTestSql.gitlab_lib_version: latest_commit_id,
                                            DbMgtBisTestSql.sql_ver_upload_status: 1,
                                            DbMgtBisTestSql.sql_ver_upload_time: curr_time}). \
                        where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id,
                              DbMgtBisTestSql.sql_ver_name == sql_ver_name).execute()

                    DbMgtBisTestSqlCommitHis.get_or_create(bis_pipeline_id=bis_pipeline_id,
                                                           db_info_id=test_sql.db_info_id,
                                                           sql_ver_name=test_sql.sql_ver_name,
                                                           git_commit_time=curr_time,
                                                           defaults={'gitlab_repo_version': latest_commit_id,
                                                                     'operate_user': operate_user})
