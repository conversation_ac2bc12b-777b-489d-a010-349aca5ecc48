import datetime
import logging
import os

from common.mysql.models import DbMgtBisTestSql, DbMgtBisTestSqlCommitHis
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from job.jenkins.test_data_dev.models import BizBaseDbBind
from settings import TEST_DATA_INIT
from common.ext_cmd.shell_cmd import shell_cmd

class CommitTestDmlService:
    @staticmethod
    def commit_test_dml(bis_pipeline_id, operate_user):
        bis_code = bis_pipeline_id.split('_')[0]
        bis_branch_name = bis_pipeline_id.split('_')[1]

        biz_base_db_code = None
        with DBConnectionManagerForSqlalchemy() as db:
            obj = db.session.query(BizBaseDbBind).filter(
                BizBaseDbBind.biz_code == bis_code).first()
            biz_base_db_code = obj.biz_base_db_code
        if not biz_base_db_code:
            raise Exception("基础库信息:{}".format(biz_base_db_code))
        test_dml_group = TEST_DATA_INIT.get("test_dml_group")
        diff_sql_path = os.path.join('/data', test_dml_group, biz_base_db_code, bis_branch_name)
        if not os.path.exists(diff_sql_path):
            logging.info("diff_sql_path:{}不存在;本次没有采集任何数据,停止数据提交".format(diff_sql_path))
        os.chdir(diff_sql_path)
        commit_msg = '业务迭代{}提交diff.sql'.format(bis_pipeline_id)
        cmd = 'git add -A'
        os.system(cmd)
        cmd = 'git commit -m "{}"'.format(commit_msg)
        os.system(cmd)
        cmd = 'git push origin {}'.format(bis_branch_name)
        os.system(cmd)

        get_commit_id_command = 'git rev-parse HEAD'
        stdcode, latest_commit_id = shell_cmd(get_commit_id_command)
        logging.info("获取最后一次提交记录结果为{}，最后一次提交记录为{}".format(stdcode, latest_commit_id))
        if stdcode == 0:
            # 去除回车符
            latest_commit_id = latest_commit_id.replace('\n', '').replace('\r', '')
            curr_time = datetime.datetime.now()
            for row in DbMgtBisTestSql.select().where(DbMgtBisTestSql.bis_pipeline_id == bis_pipeline_id):
                DbMgtBisTestSql.update({DbMgtBisTestSql.update_user: operate_user,
                                        DbMgtBisTestSql.update_time: curr_time,
                                        DbMgtBisTestSql.gitlab_code_version: latest_commit_id,
                                        DbMgtBisTestSql.sql_ver_upload_status: 0}). \
                    where(DbMgtBisTestSql.id == row.id).execute()
                DbMgtBisTestSqlCommitHis.get_or_create(bis_pipeline_id=bis_pipeline_id,
                                                       sql_file_name=row.sql_file_name, git_commit_time=curr_time,
                                                       defaults={'gitlab_code_version': latest_commit_id,
                                                                 'operate_user': operate_user})

        return 'success'


if __name__ == '__main__':
    CommitTestDmlService.commit_test_dml("it29_test-cdc-mix", "admin")
