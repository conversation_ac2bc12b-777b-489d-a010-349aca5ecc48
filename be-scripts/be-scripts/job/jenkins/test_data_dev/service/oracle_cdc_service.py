import datetime
import os

from common.mysql.models import DbMgtCdcRecordInfo
from job.jenkins.test_data_dev.param.bis_iter_info_param import BisIterInfoParam
from job.jenkins.test_data_dev.test_diff_sql_ser import get_current_scn, update_oracle_cdc_position
from job.jenkins.test_data_dev.util.cdc_file_util import log_diff_sql, get_diff_log_file_path
from settings import TEST_DATA_INIT
import logging
import re
import time

from typing import List, Set

from job.jenkins.test_data_dev.param.oracle_cdc_param import DbCdcParam


# oracle 数据捕获服务
class OracleCdcService:

    @classmethod
    def generate_diff_sql(cls, cdc_param: DbCdcParam, bis_iter_info_param: BisIterInfoParam):
        # 查询当前cdc_position
        end_cdc_position = cls.get_current_cdc_position(cdc_param)
        # 准备cdc环境
        cls.prepare_cdc_work_dir(bis_iter_info_param)
        # 准备cdc 程序配置文件
        cls.prepare_conf(end_cdc_position, cdc_param, bis_iter_info_param)
        # 准备cdc 日志配置文件
        cls.prepare_log_conf(bis_iter_info_param)
        # 启动cdc程序
        cls.start_oracle_cdc(bis_iter_info_param)
        # 更新采集点位并记录日志
        cls.update_cdc_position(end_cdc_position, cdc_param, bis_iter_info_param)

    @classmethod
    def get_current_cdc_position(cls, cdc_param: DbCdcParam):
        return get_current_scn(cdc_param)

    @classmethod
    def update_cdc_position(cls, end_cdc_position: int, cdc_param: DbCdcParam,
                            bis_iter_info_param: BisIterInfoParam):
        update_oracle_cdc_position(end_cdc_position, bis_iter_info_param)
        DbMgtCdcRecordInfo.create(cdc_batch_no=bis_iter_info_param.cdc_batch_no,
                                  restore_his_id=bis_iter_info_param.restore_his_id,
                                  suite_code=bis_iter_info_param.suite_code,
                                  db_info_id=bis_iter_info_param.db_info_id,
                                  start_cdc_position=cdc_param.cdc_position, end_cdc_position=end_cdc_position,
                                  create_time=datetime.datetime.now(),
                                  cdc_pipeline_url=TEST_DATA_INIT['test_dml_diff_cdc_pipeline_url'].format(
                                      bis_iter_info_param.build_id))
        if not os.path.exists(os.path.dirname(cdc_param.diff_dml_sql_path)):
            logging.info("{} {} 无diff_sql 生成".format(os.path.dirname(cdc_param.diff_dml_sql_path), cdc_param.db))

    @classmethod
    def prepare_conf(cls, end_cdc_position: int, cdc_param: DbCdcParam, bis_iter_info_param: BisIterInfoParam):

        conf_list = list()
        # conf template start
        conf_driver = "oracle.jdbc.driver = oracle.jdbc.OracleDriver"
        conf_storage = "offset.storage.file.filename = {}/hcdc"
        conf_url = "oracle.jdbc.url = jdbc:oracle:thin:@//{}:{}/{}"
        conf_srv_id = "oracle.srv.id = {}"
        conf_biz_iter_id = "biz.iter.id = {}"
        conf_database = "oracle.jdbc.database = {}"
        conf_host = "oracle.jdbc.host = {}"
        conf_port = "oracle.jdbc.port = {}"
        conf_user = "oracle.jdbc.user= {} "
        conf_password = "oracle.jdbc.password = {}"
        conf_schema = "oracle.jdbc.schema[{}].schema = {}"
        conf_sql_path = "oracle.jdbc.schema[{}].dml = {}"
        conf_sql_log_path = "diff.sql.log.path = {}"
        conf_tables = "oracle.cdc.ignore.tables = {}"
        conf_cdc_position = "oracle.cdc.position = {}"
        conf_end_cdc_position = "oracle.cdc.end.position = {}"
        # conf template start
        conf_list.append(conf_srv_id.format(cdc_param.srv_id))
        conf_list.append(conf_sql_log_path.format(get_diff_log_file_path()[1]))
        conf_list.append(conf_biz_iter_id.format(bis_iter_info_param.bis_pipeline_id))
        conf_list.append(conf_user.format(cdc_param.user))
        conf_list.append(conf_password.format(cdc_param.password))
        conf_list.append(conf_tables.format(cdc_param.ignore_tables))
        conf_list.append(conf_cdc_position.format(cdc_param.cdc_position))
        conf_list.append(conf_end_cdc_position.format(end_cdc_position))
        conf_list.append(conf_driver)
        conf_list.append(conf_storage.format(cls.get_cdc_conf_dir(bis_iter_info_param)))
        conf_list.append(conf_url.format(cdc_param.host, cdc_param.port, cdc_param.service_name))
        conf_list.append(conf_database.format(cdc_param.service_name))
        conf_list.append(conf_host.format(cdc_param.host))
        conf_list.append(conf_port.format(cdc_param.port))
        conf_list.append(conf_schema.format(0, cdc_param.db))
        conf_list.append(conf_sql_path.format(0, cdc_param.diff_dml_sql_path))
        cdc_conf_file = open(cls.prepare_cdc_conf_file(bis_iter_info_param), "w")
        for conf_line in conf_list:
            cdc_conf_file.write(conf_line + os.linesep)
        return True

    @classmethod
    def prepare_cdc_work_dir(cls, bis_iter_info_param: BisIterInfoParam):
        work_dir = cls.get_cdc_work_dir(bis_iter_info_param)
        os.system("mkdir -p {}".format(work_dir))
        os.system("rm -rf {}/**".format(work_dir))
        os.system("cp -r {}/** {}".format(TEST_DATA_INIT['test_dml_oracle_cdc_source_dir'], work_dir))

    @classmethod
    def get_cdc_work_dir(cls, bis_iter_info_param: BisIterInfoParam):
        return bis_iter_info_param.workspace + os.path.sep + bis_iter_info_param.bis_pipeline_id + os.path.sep + "oracdc" + os.path.sep + \
            bis_iter_info_param.start_datetime.split(" ")[0]

    @classmethod
    def get_cdc_log_dir(cls, bis_iter_info_param: BisIterInfoParam):
        return cls.get_cdc_work_dir(bis_iter_info_param) + os.path.sep + "logs"

    @classmethod
    def get_cdc_bin_dir(cls, bis_iter_info_param: BisIterInfoParam):
        return cls.get_cdc_work_dir(bis_iter_info_param) + os.path.sep + "bin"

    @classmethod
    def get_cdc_conf_dir(cls, bis_iter_info_param: BisIterInfoParam):
        return cls.get_cdc_work_dir(bis_iter_info_param) + os.path.sep + "conf"

    @classmethod
    def prepare_cdc_log_conf_file(cls, bis_iter_info_param: BisIterInfoParam):
        return cls.get_cdc_conf_dir(bis_iter_info_param) + os.path.sep + \
            TEST_DATA_INIT['test_dml_oracle_cdc_log_conf_file']

    @classmethod
    def prepare_cdc_conf_file(cls, bis_iter_info_param: BisIterInfoParam):
        return cls.get_cdc_conf_dir(bis_iter_info_param) + os.path.sep + \
            TEST_DATA_INIT['test_dml_oracle_cdc_conf_file']

    @classmethod
    def prepare_log_conf(cls, bis_iter_info_param: BisIterInfoParam):
        logback_xml = cls.prepare_cdc_log_conf_file(bis_iter_info_param)
        # 读
        f = open(logback_xml, 'r')
        all_lines = f.readlines()
        f.close()
        # 替换
        logging.info("rewrite logback.xml :{}".format(logback_xml))
        log_conf_file = open(logback_xml, "w")
        for each_line in all_lines:
            a = re.sub('/data/logs', cls.get_cdc_log_dir(bis_iter_info_param), each_line)
            log_conf_file.writelines(a)
        f.close()
        return True

    @classmethod
    def start_oracle_cdc(cls, bis_iter_info_param: BisIterInfoParam):
        logging.info("启动oracle cdc")
        res = os.system("cd {} && sh start.sh".format(cls.get_cdc_bin_dir(bis_iter_info_param)))
        logging.info("oracle cdc 结束")
        logging.info("oracle cdc res:{}".format(res))
        if res != 0:
            logging.info("oracle cdc 出错！！！")
            exit(1)
