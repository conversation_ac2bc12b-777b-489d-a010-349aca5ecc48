import sys
import os

PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)
from job.jenkins.test_data_dev.service.oracle_biz_data_split_service import *

import logging

if __name__ == '__main__':
    init()
    logging.info('数据库为： {}'.format(sys.argv[1]))
    logging.info('it100业务为： {}'.format(sys.argv[2]))
    logging.info('it300业务为： {}'.format(sys.argv[3]))
    logging.info('业务分支为： {}'.format(sys.argv[4]))
    logging.info('sql_file_path为： {}'.format(sys.argv[5]))
    bis_branch_name = sys.argv[4]
    sql_file_path = sys.argv[5]

    db = sys.argv[1]
    db_name = sys.argv[1]
    # 计算biz_it100_01 feature_it100
    biz_db_it100 = sys.argv[2]
    biz_db_it300 = sys.argv[3]

    it100 = "it100"
    it300 = "it300"
    biz_it100 = "docker_{biz}_{db_name}_biz".format(db_name=db, biz=it100)
    biz_it300 = "docker_{biz}_{db_name}_biz".format(db_name=db, biz=it300)
    feature = "docker_multi_{db_name}_feature".format(db_name=db)
    # it300业务库特有的数据
    oracle_grant(biz_db_it300, biz_it300, biz_db_it100)
    oracle_diff_biz_data(biz_db_it300, biz_it300, biz_db_it100)
    oracle_biz_data(biz_db_it300, biz_it300, biz_db_it100)

    # 公共数据
    oracle_grant(biz_db_it300, feature, biz_it300)
    oracle_diff_biz_data(biz_db_it300, feature, biz_it300)
    oracle_biz_data(biz_db_it300, feature, biz_it300)

    # it100业务库特有的数据
    oracle_grant(biz_db_it100, biz_it100, biz_db_it300)
    oracle_diff_biz_data(biz_db_it100, biz_it100, biz_db_it300)
    oracle_biz_data(biz_db_it100, biz_it100, biz_db_it300)

    drop_empty_table(biz_it100, feature)
    drop_empty_table(biz_it300, feature)

    for result in get_result_value():
        if biz_it100 in str(result):
            ver_prefix = 'V{}.'.format(datetime.datetime.now().strftime('%Y.%m.%d.%H.%M.%S'))
            dml_root_path = "/data/files/biz_sql/{}/{}".format("it100", db_name)
            payload = {
                "host": "***************",
                "port": "1521",
                "sid": "hbqa11g",
                "username": biz_it100,
                "password": "howbuy2015",
                "dmlRootPath": dml_root_path,
                "ignoreTables": "flyway_schema_history",
                "dmlNamePrefix": ver_prefix
            }
            execute_result=execute(payload)
            if execute_result == 0:
                commit_biz_sql("it100", bis_branch_name, sql_file_path, dml_root_path)
        elif biz_it300 in str(result):
            ver_prefix = 'V{}.'.format(datetime.datetime.now().strftime('%Y.%m.%d.%H.%M.%S'))
            dml_root_path = "/data/files/biz_sql/{}/{}".format("it300", db_name)
            payload = {
                "host": "***************",
                "port": "1521",
                "sid": "hbqa11g",
                "username": biz_it300,
                "password": "howbuy2015",
                "dmlRootPath": dml_root_path,
                "ignoreTables": "flyway_schema_history",
                "dmlNamePrefix": ver_prefix
            }
            execute_result = execute(payload)
            if execute_result == 0:
                commit_biz_sql("it300", bis_branch_name, sql_file_path, dml_root_path)
        elif feature in str(result):
            payload = {
                "host": "***************",
                "port": "1521",
                "sid": "hbqa11g",
                "username": feature,
                "password": "howbuy2015",
                "dmlRootPath": "/data/files/feature_sql/{}".format(db_name),
                "ignoreTables": "flyway_schema_history"
            }
            execute(payload)
    print("结果文件:")
    print(json.dumps(get_result_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))
    print("外部表 object does not exist 需要找用户确认:")
    print(json.dumps(get_ora_err_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))
    print("没有唯一键的表 需要找用户确认:")
    print(json.dumps(get_warn_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))

    print("对比的库的字段不一致的表 需要找用户确认:")
    print(json.dumps(get_columns_inconsistent_value(), sort_keys=True,
                     indent=4,
                     ensure_ascii=False))
