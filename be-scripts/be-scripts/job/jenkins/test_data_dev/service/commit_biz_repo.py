import sys
import os
import logging


PROJECT_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(PROJECT_DIR)
from settings import TEST_DATA_INIT, GITLAB_LIB_SSH
from common.ext_cmd import shell_cmd as common_shell_cmd


def commit_biz_sql(bis_code, bis_branch_name, sql_file_path, dml_root_path, archive_path=None):
    test_dml_repo_group = TEST_DATA_INIT.get("test_dml_repo_group")
    bis_git_repo_url = '{}:{}/{}.git'.format(GITLAB_LIB_SSH, test_dml_repo_group, bis_code)
    if sys.platform == 'win32':
        data_path = 'D:\\data'
    else:
        data_path = '/data'
    bis_root_path = os.path.join(data_path, test_dml_repo_group+os.sep+ "biz-sql-split", bis_code)
    logging.info("克隆或更新制品库")
    # 克隆或更新制品库
    clone_gitlab(bis_git_repo_url, bis_branch_name, bis_root_path)
    # 更新sql并推送制品库
    push_sql(bis_code, bis_branch_name, sql_file_path, dml_root_path, archive_path)


def commit_feature_sql(db_group, branch_name, ddl_root_path, archive_path):
    devops_sql_repo_group = "devops-sql"
    bis_git_repo_url = '{}:{}/{}.git'.format(GITLAB_LIB_SSH, devops_sql_repo_group, db_group)
    if sys.platform == 'win32':
        data_path = 'D:\\data'
    else:
        data_path = '/data'
    bis_root_path = os.path.join(data_path, devops_sql_repo_group, db_group)
    logging.info("克隆或更新制品库")
    # 克隆或更新制品库
    clone_gitlab(bis_git_repo_url, branch_name, bis_root_path)
    # 更新sql并推送制品库
    push_feature_sql(db_group, branch_name, archive_path, ddl_root_path)


def push_sql(bis_code, bis_branch_name, sql_file_path, dml_root_path, archive_path=None):
    logging.info(bis_code)
    logging.info(bis_branch_name)
    logging.info(sql_file_path)
    logging.info(dml_root_path)
    if not archive_path:
        archive_path = ""
    test_dml_repo_group = TEST_DATA_INIT.get("test_dml_repo_group")+os.sep+ "biz-sql-split"
    file_names = os.listdir(dml_root_path)
    logging.info(file_names)
    if sys.platform == 'win32':
        data_path = 'D:\\data'
    else:
        data_path = '/data'
    sql_ver_path = os.path.join(data_path, test_dml_repo_group, bis_code, bis_branch_name) + archive_path + sql_file_path
    if not os.path.exists(sql_ver_path):
        if sys.platform == 'win32':
            cmd = 'mkdir {}'.format(sql_ver_path)
        else:
            cmd = 'mkdir -p {}'.format(sql_ver_path)

        os.system(cmd)
    else:
        cmd = 'rm -rf {}/*'.format(sql_ver_path)
        os.system(cmd)

    if not os.path.exists(sql_ver_path):
        if sys.platform == 'win32':
            cmd = 'mkdir {}'.format(sql_ver_path)
        else:
            cmd = 'mkdir -p {}'.format(sql_ver_path)
        os.system(cmd)

    for sql_file_name in file_names:
        sql_src_path = os.path.join(dml_root_path,
                                    sql_file_name)
        det_file_path = os.path.join(sql_ver_path, sql_file_name)
        if sys.platform == 'win32':
            cmd = 'cd && copy "{}" "{}"'.format(sql_src_path, det_file_path)
        else:
            cmd = 'pwd && cp {} {}'.format(sql_src_path, det_file_path)
        logging.info(cmd)
        os.system(cmd)

    if sys.platform == 'win32':
        data_path = 'D:\\data'
    else:
        data_path = '/data'
    os.chdir(os.path.join(data_path, test_dml_repo_group, bis_code, bis_branch_name))
    cmd = 'git pull origin {}'.format(bis_branch_name)
    os.system(cmd)
    cmd = 'git add -A'
    os.system(cmd)
    cmd = 'git commit -m "{}"'.format('提交diff_sql')
    os.system(cmd)
    cmd = 'git push origin {}'.format(bis_branch_name)
    os.system(cmd)

    get_commit_id_command = 'git rev-parse HEAD'
    stdcode, latest_commit_id = common_shell_cmd.shell_cmd(get_commit_id_command)
    logging.info("获取最后一次提交记录结果为{}，最后一次提交记录为{}".format(stdcode, latest_commit_id))


def push_feature_sql(db_group, branch_name, archive_path, ddl_root_path):
    if not archive_path:
        archive_path = ""
    devops_sql_repo_group = "devops-sql"
    file_names = os.listdir(ddl_root_path)
    logging.info(file_names)
    if sys.platform == 'win32':
        data_path = 'D:\\data'
    else:
        data_path = '/data'
    sql_ver_path = os.path.join(data_path, devops_sql_repo_group, db_group, branch_name) + archive_path
    if not os.path.exists(sql_ver_path):
        if sys.platform == 'win32':
            cmd = 'mkdir {}'.format(sql_ver_path)
        else:
            cmd = 'mkdir -p {}'.format(sql_ver_path)
        os.system(cmd)
    else:
        cmd = 'rm -rf {}/*'.format(sql_ver_path)
        os.system(cmd)

    if not os.path.exists(sql_ver_path):
        if sys.platform == 'win32':
            cmd = 'mkdir {}'.format(sql_ver_path)
        else:
            cmd = 'mkdir -p {}'.format(sql_ver_path)
        os.system(cmd)

    for sql_file_name in file_names:
        sql_src_path = os.path.join(ddl_root_path,
                                    sql_file_name)
        det_file_path = os.path.join(sql_ver_path, sql_file_name)
        if sys.platform == 'win32':
            cmd = 'cd && copy "{}" "{}"'.format(sql_src_path, det_file_path)
        else:
            cmd = 'pwd && cp {} {}'.format(sql_src_path, det_file_path)
        logging.info(cmd)
        os.system(cmd)
    if sys.platform == 'win32':
        data_path = 'D:\\data\\'
    else:
        data_path = '/data/'
    path = data_path + devops_sql_repo_group + os.sep + db_group + os.sep + branch_name
    logging.info(path)
    cmd = 'cd {} && git pull origin {}'.format(path, branch_name)
    os.system(cmd)
    cmd = 'cd {} && git add -A'.format(path)
    os.system(cmd)
    cmd = 'cd {} && git commit -m "{}"'.format(path, '提交diff_sql')
    os.system(cmd)
    cmd = 'cd {} && git push origin {}'.format(path, branch_name)
    os.system(cmd)

    get_commit_id_command = 'cd {} && git rev-parse HEAD'.format(path)

    stdcode, latest_commit_id = common_shell_cmd.shell_cmd(get_commit_id_command)
    logging.info("获取最后一次提交记录结果为{}，最后一次提交记录为{}".format(stdcode, latest_commit_id))


def clone_gitlab(bis_git_repo_url, bis_branch_name, bis_root_path):
    if not os.path.exists(bis_root_path):
        if sys.platform == 'win32':
            cmd = 'mkdir {}'.format(bis_root_path)
        else:
            cmd = 'mkdir -p {}'.format(bis_root_path)
        logging.info(cmd)
        os.system(cmd)

    os.chdir(bis_root_path)
    if not os.path.isdir(bis_branch_name):
        cmd = 'git clone -b {} {} {}'.format(bis_branch_name, bis_git_repo_url, bis_branch_name)
        logging.info(cmd)
        os.system(cmd)
        if not os.path.isdir(bis_branch_name):
            cmd = 'git clone -b master {} {}'.format(bis_git_repo_url, bis_branch_name)
            logging.info(cmd)
            os.system(cmd)
            os.chdir(os.path.join(bis_root_path, bis_branch_name))
            cmd = 'git checkout -b {}'.format(bis_branch_name)
            os.system(cmd)
            cmd = 'git add -A'
            os.system(cmd)
            cmd = 'git commit -m "Create {}"'.format(bis_branch_name)
            os.system(cmd)
            cmd = 'git push --set-upstream origin {}'.format(bis_branch_name)
            logging.info(cmd)
            os.system(cmd)

    os.chdir(os.path.join(bis_root_path, bis_branch_name))
    os.system('git checkout .')
    logging.info('git checkout .')
    os.system('git checkout {}'.format(bis_branch_name))
    logging.info('git checkout {}'.format(bis_branch_name))
    os.system('git pull origin {}'.format(bis_branch_name))
    logging.info('git pull origin {}'.format(bis_branch_name))
