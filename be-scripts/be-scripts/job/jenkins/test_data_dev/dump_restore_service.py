import os
import sys
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
print(PROJECT_DIR)
from datetime import datetime
from settings import logger
from utils.form.models import DbMgtDumpFile, DbMgtDumpRestore
from common.mysql.mysql import MysqlHandler
from common.oracle.oracle import OracleHandler
from dao.get.mysql import db_mgt_bind_view
from dao.get.mysql.db_mgt_dump_file import get_db_dump_info_by_dump_file_id
from test_publish_aio.test_publish_aio_models.test_publish_ser import db_info_reset


class DumpRestoreService:

    def set_dump_restore_param(self, db_file_id, suite_code, execute_result):
        self.db_file_id = db_file_id
        self.suite_code = suite_code
        self.execute_result = execute_result

    def restore_oracle(self, db_info_list, dump_file_list):
        oh = OracleHandler(db_info_list=db_info_list)
        oh.set_param(dump_file_list)
        oh._check_dump_exist('oracle')
        oh._db_unlock()
        oh._drop_ddl()
        oh._db_restore()
        oh._db_set_user_and_password()
        oh._db_update_synonym()


    def restore_mysql(self, db_info_list, dump_file_list):
        mh = MysqlHandler(db_info_list=db_info_list)
        mh.set_param(dump_file_list)
        mh._check_dump_exist('mysql')
        mh._db_drop()
        mh._db_create()
        mh._db_restore()

    def dump_restore(self):
        logger.info('start dump_restore')
        cur_time = datetime.now()
        dump_file = DbMgtDumpFile.select().where(DbMgtDumpFile.id == self.db_file_id).first()
        if not dump_file:
            raise Exception('没有找到dump文件:{}'.format(self.db_file_id))
        DbMgtDumpRestore.update({DbMgtDumpRestore.update_time: cur_time,
                                 DbMgtDumpRestore.lock_status: 'unlock'
                                 }).where(DbMgtDumpRestore.db_logic_id == dump_file.db_logic_id).execute()
        execute_result = 'success'
        try:
            logger.info('执行 dump_restore')
            dump_file_list = get_db_dump_info_by_dump_file_id(self.db_file_id)
            db_list = list()
            for dump_info in dump_file_list:
                db_info_result = db_mgt_bind_view.get(suite_code=self.suite_code, db_name=dump_info['db_name'])
                db_info_list = db_info_reset(db_info_result)
                for db_info in db_info_list:
                    if db_info['db_name'] == dump_info['db_name']:
                        if db_info['suite_db_name'] not in db_list:
                            db_list.append(db_info['suite_db_name'])
                            if db_info['db_srv_type'] == 'oracle':
                                logger.info('执行 oracle dump_restore')
                                self.restore_oracle([db_info], dump_file_list)
                            elif db_info['db_srv_type'] == 'mysql':
                                self.restore_mysql([db_info], dump_file_list)



        except Exception as e:
            execute_result = 'failure'
            logger.error(e)
            traceback.print_exc()
        finally:
            logger.info('end dump_restore')
            DbMgtDumpRestore.update({DbMgtDumpRestore.update_time: cur_time,
                                     DbMgtDumpRestore.status: execute_result})\
                .where(DbMgtDumpRestore.db_logic_id == dump_file.db_logic_id).execute()

    def record_execute_result(self):
        cur_time = datetime.now()
        dump_file = DbMgtDumpFile.select().where(DbMgtDumpFile.id == self.db_file_id).first()
        if not dump_file:
            raise Exception('没有找到dump文件:{}'.format(self.db_file_id))
        DbMgtDumpRestore.update({DbMgtDumpRestore.update_time: cur_time,
                                 DbMgtDumpRestore.lock_status: 'unlock',
                                 DbMgtDumpRestore.status: self.execute_result}) \
            .where(DbMgtDumpRestore.db_logic_id == dump_file.db_logic_id).execute()

    def call(self, params):
        acceptor = {
            "dump_restore": self.dump_restore,
            "record_execute_result": self.record_execute_result
        }
        if params[0] in acceptor:
            acceptor[params[0]]()

        return 'success'


if __name__ == '__main__':
    params = sys.argv[1:]
    logger.info("调用 {}".format(params))
    logger.info("参数总数 {}".format(len(params)))
    db_file_id = int(params[1])
    suite_code = params[2]
    param_length = len(params)
    execute_result = 'success'
    if param_length > 3:
        execute_result = params[3]
    dump_restore_ser = DumpRestoreService()
    dump_restore_ser.set_dump_restore_param(db_file_id, suite_code, execute_result)
    dump_restore_ser.call(params)
    sys.exit(0)
