import sys
import os
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from utils.form.branch_apply import BranchApplyForm, FeatureApplyForm, NonMvnBranchApplyForm, NonMvnFeatureApplyForm
from settings import logger


def branch_apply(branch_name, branch_type, gitlab_group, deadline, repos_str, tapd_id, desc, workspace, proposer):

    repos_list = repos_str.split(",")

    link = {"feature": (FeatureApplyForm, NonMvnFeatureApplyForm),
            "release": (BranchApplyForm, NonMvnBranchApplyForm),
            "bugfix": (BranchApplyForm, NonMvnBranchApplyForm)}
    my_node_list = []
    # 实例化所有node
    for node in link[branch_type]:
        my_node_list.append(node(branch_name, branch_type, gitlab_group,
                                                         deadline, repos_list, desc, workspace, proposer, tapd_id))
    # 给每个node设置下一个调用的node
    for node_index in range(len(my_node_list)-1):
        my_node_list[node_index].set_successor(my_node_list[node_index+1])
    # 调用第一个的commit方法，开启责任链调用模式
    my_node_list[0].commit()
    logger.info("repos_str")
    logger.info(repos_str)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    branch_apply(sys.argv[1], sys.argv[2], sys.argv[3].split("_")[0], sys.argv[4], sys.argv[5], sys.argv[6], sys.argv[7], sys.argv[8], sys.argv[9])

