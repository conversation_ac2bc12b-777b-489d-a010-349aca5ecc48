import json
import os
import sys
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from dao.connect.mysql import DBConnectionManager
from settings import logger, TAPD
from common.ext_cmd.shell_cmd import shell_cmd


def get_iter_app_list(timeline, team_id):
    sql = """
        SELECT DISTINCT CONCAT(t.pipeline_id, '_', t.appName) AS pipeline_app FROM iter_mgt_iter_app_info t
        LEFT JOIN iter_mgt_iter_info i ON t.pipeline_id = i.pipeline_id
        LEFT JOIN app_mgt_app_module m ON t.appName = m.module_name 
	    LEFT JOIN team_mgt_app_bind amat ON amat.app_id = m.app_id
	    LEFT JOIN tool_mgt_team_info ti ON ti.id = amat.team_id
        WHERE m.need_online = '1' AND i.br_status = 'open' AND CAST(i.br_start_date AS DATETIME) > '{}' AND ti.id IN ("{}")
        ORDER BY t.pipeline_id;
    """.format(timeline, '", "'.join(team_id.split(",")))

    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    iter_app_list = []
    for row in db.cur.fetchall():
        iter_app_list.append(row.get("pipeline_app"))
    return iter_app_list


def sync_iteration_app_to_tapd(days, params_dict):
    timeline = datetime.datetime.now() - datetime.timedelta(days=int(days))
    api_user = TAPD["api_user"]
    api_password = TAPD["api_password"]
    api_url = TAPD["update_bug_select_field_options_api_url"]
    for workspace in params_dict:
        for k, v in workspace.items():
            workspace_id = k
            for item in v:
                team_id = item.get("team_id")
                custom_config_id = item.get("custom_config_id")
                iter_app_list = get_iter_app_list(timeline, team_id)
                options = "|".join(iter_app_list)
                logger.info("options的个数为：{}".format(len(iter_app_list)))
                logger.info(options)

                cmd = '''curl -u '{api_user}:{api_password}' -d 'options={options}&id={custom_config_id}&workspace_id={workspace_id}' '{api_url}'
                    '''.format(api_user=api_user, api_password=api_password, options=options,
                               custom_config_id=custom_config_id, workspace_id=workspace_id,
                               api_url=api_url)
                logger.info(cmd)
                return_code, return_message = shell_cmd(cmd)
                logger.info(return_code)
                logger.info(return_message)


if __name__ == '__main__':
    days = sys.argv[1]
    params = sys.argv[2]
    # days = 30
    # params = '[{"55014084":[{"team_id":"2","custom_config_id":"1155014084001000137"},{"team_id":"3","custom_config_id":"1155014084001000138"},{"team_id":"7","custom_config_id":"1155014084001000139"},{"team_id":"1","custom_config_id":"1155014084001000140"},{"team_id":"4,5,6","custom_config_id":"1155014084001000141"}]}]'
    params_dict = json.loads(params)

    sync_iteration_app_to_tapd(days, params_dict)
