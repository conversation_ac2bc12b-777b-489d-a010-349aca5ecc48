#
import pymysql
import logging
import psycopg2
import psycopg2.extras
from pymysql.cursors import DictCursor

logging.basicConfig(level=logging.INFO, format='%(asctime)s [line:%(lineno)d] - %(levelname)s: %(message)s')

"""
spider: 检查spider节点与cmdb resource_server节点差异
"""


def connect_mysql():
    conn = pymysql.connect(
        host='**************', port=3306, database='spider', charset='utf8', user='scm', password='howbuyscm')
    return conn


def connect_postgre():
    conn = psycopg2.connect(
        # host="**************", port=5432, database="witcher_cmdb", user="witcher_cmdb",
        # password="LziV5E01t6E3dhWBzgQwfQ==")
        host="**************", port=5432, database="witcher_cmdb_sit", user="postgres",
        password="postgres")
    return conn


def conn_close(conn):
    conn.close()


def get_spider_node_info(conn):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''SELECT id, node_ip FROM env_mgt_node WHERE node_status=0''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['node_ip'] not in node_ip_info:
            node_ip_info[item['node_ip']] = {'id': item['id']}
        else:
            logging.info(item)
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


def get_cmdb_server_info(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT id, ip, server_cpu, server_memory FROM "public"."resources_server"
                          WHERE status=0 and
                               (sync_result<>1 or sync_result IS NULL) and
                               (server_type <> 'PHY' or server_type IS NULL) ''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['ip'] not in node_ip_info:
            node_ip_info[item['ip']] = {'id': item['id'], 'cpu': item['server_cpu'], 'memory': item['server_memory']}
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


if __name__ == '__main__':
    pg_conn = connect_postgre()
    my_conn = connect_mysql()
    cmdb_server_info = get_cmdb_server_info(pg_conn)
    spider_node_info = get_spider_node_info(my_conn)

    cmdb_server_ip_set = set(cmdb_server_info)
    spider_node_ip_set = set(spider_node_info)
    only_cmdb_server_have_ip = cmdb_server_ip_set - spider_node_ip_set
    only_spider_node_have_ip = spider_node_ip_set - cmdb_server_ip_set

    logging.info('****only cmdb server hava****')
    logging.info(only_cmdb_server_have_ip)
    logging.info(len(only_cmdb_server_have_ip))
    logging.info('***********************')
    logging.info('****only spider node hava****')
    logging.info(only_spider_node_have_ip)
    logging.info(len(only_spider_node_have_ip))
    logging.info('***********************')

    conn_close(pg_conn)
    conn_close(my_conn)
