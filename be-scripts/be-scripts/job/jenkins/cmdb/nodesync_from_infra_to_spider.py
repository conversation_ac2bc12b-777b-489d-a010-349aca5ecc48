import os
import sys
import pymysql
import json
import requests
import datetime
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import DATABASES, EMAIL_BASES


class DBConnectionManager:
    """
    通过上下文管理器的方式管理数据库连接
    """

    def __init__(self, host=DATABASES['IP'], port=DATABASES['PORT'], user=DATABASES['USER'],
                 password=DATABASES['PASSWORD'], db=DATABASES['DB'], charset=DATABASES["CHARSET"]):
        """
        host: 数据库地址
        port: 端口
        user: 用户名
        password: 密码
        db: 数据库名称
        charset: 字符集
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db = db
        self.charset = charset

    def __enter__(self):
        self.connection = pymysql.connect(host=self.host, port=self.port, user=self.user,
                                          passwd=self.password, db=self.db,
                                          charset=self.charset)
        self.cur = self.connection.cursor(cursor=pymysql.cursors.DictCursor)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()

class DealWithDataTool():
    def _filter_abnormal_data(self, data):
        '''
        处理IP为空
        一个IP对应多个name的
        一个name对应多个IP的
        name为空的
        IP乱码的
        '''
        ret_info_dict = {}
        abnormal_ip_none = []
        abnormal_name_none = []
        temporary_repeat_name = []
        temporary_repeat_ip = []
        repeat_name = []
        repeat_ip = []
        error_ip = []
        for item in data:
            if 'IpAddress' in item: #这里因为基础运维数据字段不统一
                if item['IpAddress'] == None:
                    abnormal_ip_none.append(item)
                    continue
                if item['Name'] == None or item['Name'] == '未命名':
                    abnormal_name_none.append(item)
                    continue
                if ':' in item['IpAddress']:
                    error_ip.append(item)
                    continue
                if item['Name'] not in temporary_repeat_name:
                    temporary_repeat_name.append(item['Name'])
                else:
                    repeat_name.append(item)
                    continue
                if item['IpAddress'] not in temporary_repeat_ip:
                    temporary_repeat_ip.append(item['IpAddress'])
                else:
                    repeat_ip.append(item)
                    continue
                ret_info_dict[item['IpAddress']] = item
            else:
                if item['Ip'] == None:  # 这里是处理异常数据的
                    abnormal_ip_none.append(item)
                    continue
                if item['Name'] == None or item['Name'] == '未命名': #这里处理没有名字的数据
                    abnormal_name_none.append(item)
                    continue
                if ':' in item['Ip']:#这里处理乱码的数据
                    error_ip.append(item)
                    continue
                if item['Name'] not in temporary_repeat_name:#这里处理name重复的数据
                    temporary_repeat_name.append(item['Name'])
                else:
                    repeat_name.append(item)
                    continue
                if item['Ip'] not in temporary_repeat_ip:#这里处理IP重复的数据
                    temporary_repeat_ip.append(item['Ip'])

                else:
                    repeat_ip.append(item)
                    continue
                ret_info_dict[item['Ip']] = {'NumCpu': item['CPU'], 'MemorySize': item['Memory'],
                                                    'Name': item['Name'],
                                                    'IpAddress': item['Ip'], 'Prz': item['Prz']}

        return ret_info_dict, abnormal_ip_none, abnormal_name_none, repeat_ip, repeat_name

    def _deal_with_prz(self,  filter_info):
        '''
        处理prz为空，并返回通过网段查询也查询不到的数据
        zone_code_is_none为通过网络查询也查询不到的数据
        网段接口返回的数据中查不到的数据
        zone_code_is_none能查询到网段信息，查不到zone_code信息
        net_work_is_none网段信息都查不到的
        '''
        ret_net_work_info_dict = {}
        ret_resource_domain_info_dict = {}
        ret_net_work_info = json.loads(requests.get(url=self.net_works_url).text)['results']
        ret_resource_domain_info = json.loads(requests.get(url=self.resource_domainsurl).text)['results']
        zone_code_is_none = []
        net_work_is_none = []
        insert_info_dict = {}
        for work in ret_net_work_info:
            ret_net_work_info_dict[work['network']] = work
        for domain in ret_resource_domain_info:
            ret_resource_domain_info_dict[domain['id']] = domain
        for ip in filter_info:
            item = filter_info[ip]
            if not item['Prz']:
                ipl = ip.split('.')
                ipl[3] = '0'
                ipw = '.'.join(ipl)
                if ipw in ret_net_work_info_dict:
                    resource_domain_id = ret_net_work_info_dict[ipw]['resource_domain']
                    if resource_domain_id in ret_resource_domain_info_dict:
                        zone_code = ret_resource_domain_info_dict[resource_domain_id]['zone_code']
                        item['Prz'] = zone_code
                    else:
                        zone_code_is_none.append(filter_info[ip])
                        continue
                else:
                    net_work_is_none.append(filter_info[ip])
                    continue
            insert_info_dict[ip] = filter_info[ip]

        return zone_code_is_none, insert_info_dict, net_work_is_none

    @staticmethod
    def _package_data(param_data):
        '''
        组装数据集合
        '''
        param_data_set = set()
        for item in param_data:
            param_data_set.add(item)
        return param_data_set

    def supple_insert_data(self, diff_data, insert_info):
        '''
        补充完整插入数据，因为通过调节点接口，有些数据是不完整的，可能缺少Prz的value
        返回值regin_is_none为在spider查询不到region信息的节点列表
        '''
        regin_is_none = []
        insert_data_info = {}
        for ip in diff_data:
            zone_code = insert_info[ip]['Prz']
            try:
                with DBConnectionManager(host=DATABASES['IP'], port=DATABASES['PORT'], db=DATABASES['DB'],
                                         user=DATABASES['USER'], password=DATABASES['PASSWORD']) as db:
                    sql = "select r.id from env_mgt_region r inner join env_mgt_zone z on " \
                          "z.relative_region_code = r.region_name where z.zone_code = '{}'".format(zone_code)
                    db.cur.execute(sql)
                    region = db.cur.fetchone()
                    insert_info[ip]['region_id'] = region['id']
            except Exception as e:
                logging.info(e)
                regin_is_none.append(insert_info[ip])
                continue
            insert_data_info[ip] = insert_info[ip]
        return insert_data_info, regin_is_none

    def package_insert_data(self, insert_info):
        '''
        组装sql
        返回值insert_sql为组装后的sql语句列表
        '''
        insert_sql = []
        for ip in insert_info:
            node_ip = insert_info[ip]['IpAddress']
            node_name = insert_info[ip]['Name']
            region_id = insert_info[ip]['region_id']
            node_status = 0
            node_desc = '每日从基础运维同步过来的'
            create_user = 'sync_scm'
            update_user = 'sync_scm'
            create_time = datetime.datetime.now()
            update_time = datetime.datetime.now()
            sql = "INSERT INTO env_mgt_node(node_ip, node_name, region_id," \
                  " node_status, create_user, update_user, create_time, " \
                  "update_time,node_desc) " \
                  "value({},{},{},{},{},{},{},{},{})".format(node_ip,
                                                             node_name, region_id, node_status,
                                                             create_user, update_user,
                                                             create_time, update_time, node_desc)
            insert_sql.append(sql)
        return insert_sql

    def global_abnormal_data(self, ret_ucloud_info_dict,):
        '''
        保证数据绝对准确，组装数据的时候这里做全局校验，以防漏数据，
        因为调用的是三个不同接口获取的数据，
        他们之间的name或IP可能会重复
        本来原则上是不用做这一层校验的
        '''
        temporary_repeat_ip = []
        repeat_ip = []

        for item in ret_ucloud_info_dict:
            if item not in temporary_repeat_ip:
                temporary_repeat_ip.append(item)
            else:
                repeat_ip.append(item)

    def package_mail_msg(self, insert_info, v_abnormal_ip_none, v_abnormal_name_none, v_repeat_ip, v_repeat_name, regin_is_none):
        '''
        组装邮件模板
        '''
        content = '<br>'
        content += '本次需要处理的数据: <br>'
        if insert_info:
            content += '本次同步的数据: <br>'
            for item in insert_info:
                content += str(insert_info[item]) + '<br>'
        if v_abnormal_ip_none:
            content += 'ip为none的数据(异常数据): <br>'
            for item in v_abnormal_ip_none:
                content += str(item) + '<br>'
        if v_abnormal_name_none:
            content += 'name为none的数据(异常数据): <br>'
            for item in v_abnormal_name_none:
                content += str(item) + '<br>'
        if v_repeat_ip:
            content += 'ip为重复的数据(异常数据): <br>'
            for item in v_repeat_ip:
                content += str(item) + '<br>'
        if v_repeat_name:
            content += 'name为重复的数据(异常数据): <br>'
            for item in v_repeat_name:
                content += str(item) + '<br>'
        if regin_is_none:
            content += 'region在devops查不到的数据(异常数据): <br>'
            for item in regin_is_none:
                content += str(item) + '<br>'
        content += '<br>'
        mail_msg = """
                        <p>基础运维与devops节点对账</p>
                  <p></p>
                  <table border="1" cellpadding="0" cellspacing="0" width="500" style="border-collapse: collapse;margin-top: 1em">
                   <tbody>
                   <tr>    
                            本邮件由系统自动发出，无需回复！<br/>            
                            各位同事，大家好，以下基础运维与devops节点信息对账</br> 
                            备注：</br>
                            [ip为空]是指基础运维返回的接口信息中ip为none</br>
                            [name为空]是指基础运维返回的接口信息中name为none</br>
                            [ip重复]是指基础运维返回的接口信息中ip重复</br>
                            [name重复]是指基础运维返回的接口信息中name重复</br>
                            [region为空]是指通过基础运维返回的接口信息在devops中查询不到region</br>

                            处理方式：</br>
                            1》正常差异自动化同步到devops</br>
                            2》异常的数据除region为空，其他的需要运维同事关注处理一下</br>

                    {content}                   
                    </tr>
                  </tbody>
                    """
        mail_msg = mail_msg.format(
            content=content,
        )
        subject = '基础运维与devops节点对账'
        return mail_msg, subject

class NodeSync(DealWithDataTool):

    def __init__(self):
        self.fromaddr = EMAIL_BASES['SENDER']
        self.password = EMAIL_BASES['PASSWORD']
        self.toaddrs = ['<EMAIL>']
        self.net_works_url = 'http://**************/mianbao/api/v1/networks/'
        self.resource_domainsurl = 'http://**************/mianbao/api/v1/resource_domains/'
        self.ucloud_url = 'http://**************/mianbao/api/v1/ucloud/uhost/'
        self.qcloud_url = 'http://**************/mianbao/api/v1/qqcloud/instance/'
        self.vmware_url = 'http://**************:80/mianbao/api/v1/vsphere/vms/?limit=3000'

    def get_infra_node_info(self):
        try:
            ret_ucloud_info = json.loads(requests.get(url=self.ucloud_url).text)['results']
            ret_qcloud_info = json.loads(requests.get(url=self.qcloud_url).text)['results']
            ret_vmware_info = json.loads(requests.get(url=self.vmware_url).text)['results']

            ret_ucloud_info_dict, u_abnormal_ip_none, u_abnormal_name_none, u_repeat_ip, u_repeat_name\
                = self._filter_abnormal_data(ret_ucloud_info)
            ret_qcloud_info_dict, q_abnormal_ip_none, q_abnormal_name_none, q_repeat_ip, q_repeat_name\
                = self._filter_abnormal_data(ret_qcloud_info)
            ret_vmware_info_dict, v_abnormal_ip_none, v_abnormal_name_none, v_repeat_ip, v_repeat_name\
                = self._filter_abnormal_data(ret_vmware_info)
            ret_vmware_info_dict.update(ret_ucloud_info_dict)
            ret_vmware_info_dict.update(ret_qcloud_info_dict)
            v_abnormal_ip_none.extend(u_abnormal_ip_none)
            v_abnormal_ip_none.extend(q_abnormal_ip_none)
            v_abnormal_name_none.extend(u_abnormal_name_none)
            v_abnormal_name_none.extend(q_abnormal_name_none)
            v_repeat_name.extend(u_repeat_name)
            v_repeat_name.extend(q_repeat_name)
            v_repeat_ip.extend(u_repeat_ip)
            v_repeat_ip.extend(q_repeat_ip)
        except Exception as e:
            raise Exception(e)
        zone_code_is_none, ret_vmware_info_dict, net_work_is_none = self._deal_with_prz(ret_vmware_info_dict)
        return ret_vmware_info_dict, v_abnormal_ip_none, v_abnormal_name_none, v_repeat_ip, v_repeat_name

    @staticmethod
    def get_spider_node_info():
        '''
        获取devops中的正在使用的节点
        '''
        spider_node_info_dict = {}
        with DBConnectionManager(host=DATABASES['IP'], port=DATABASES['PORT'], db=DATABASES['DB'],
                                 user=DATABASES['USER'], password=DATABASES['PASSWORD']) as db:
            sql="SELECT node_name,node_ip,minion_id,region_id from env_mgt_node WHERE node_status = '0'"
            db.cur.execute(sql)
            spider_node_info = db.cur.fetchall()
        for item in spider_node_info:
            spider_node_info_dict[item['node_ip']] = {'node_name': item['node_name'],
                                                      'node_ip': item['node_ip'], 'minion_id': item['minion_id'],
                                                      'region_id': item['region_id']}
        return spider_node_info_dict

    def compare_node(self, infra_info, spider_info):
        '''
        取两集合差集，包含在infra中，不包含在spider_info中的
        diff_data是要返回的IP
        '''
        infra_info_set = self._package_data(infra_info)
        spider_info_set = self._package_data(spider_info)
        infra_diff_data = infra_info_set.difference(spider_info_set)
        spider_diff_data = spider_info_set.difference(infra_info_set)
        return infra_diff_data, spider_diff_data

    def insert_node_to_spider(self, insert_info):
        '''
        这里不用考虑事务，因为有提交失败，数据不会漏
        '''
        for sql in insert_info:
            with DBConnectionManager(host=DATABASES['IP'], port=DATABASES['PORT'], db=DATABASES['DB'],
                                     user=DATABASES['USER'], password=DATABASES['PASSWORD']) as db:
                db.cur.execute(sql)
        db.connection.commit()

    def send_mail(self, mail_msg, subject):
        textApart = MIMEText(mail_msg, 'html', 'utf-8')
        m = MIMEMultipart()
        m.attach(textApart)
        m['Subject'] = subject
        try:
            server = smtplib.SMTP('smtp.howbuy.com', )
            server.login(self.fromaddr, self.password)
            server.sendmail(self.fromaddr, self.toaddrs, m.as_string())
            logging.info('success')
            server.quit()
        except smtplib.SMTPException:
            logging.error('Error: 发送邮件失败')

if __name__ == '__main__':
    node_sync = NodeSync()
    ret_vmware_info, v_abnormal_ip_none, v_abnormal_name_none, v_repeat_ip, v_repeat_name = node_sync.get_infra_node_info()
    spider_node = node_sync.get_spider_node_info()
    infra_diff_data,spider_diff_data = node_sync.compare_node(ret_vmware_info, spider_node)
    insert_info, regin_is_none = node_sync.supple_insert_data(infra_diff_data, ret_vmware_info)
    node_sync.package_insert_data(insert_info)
    mail_msg, subject = node_sync.package_mail_msg(insert_info, v_abnormal_ip_none, v_abnormal_name_none,
                                         v_repeat_ip, v_repeat_name, regin_is_none)
    node_sync.send_mail(mail_msg, subject)
