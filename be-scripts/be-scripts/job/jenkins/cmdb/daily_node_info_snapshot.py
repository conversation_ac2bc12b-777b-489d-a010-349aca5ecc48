#
import os
import sys
import datetime
import pymysql
import logging
import psycopg2
import psycopg2.extras
from pymysql.cursors import DictCursor
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from common.email.send_email import SendMail
from settings import R<PERSON><PERSON>
from settings import DATAB<PERSON><PERSON>
from settings import CMDBDATABASES

logging.basicConfig(level=logging.INFO, format='%(asctime)s [line:%(lineno)d] - %(levelname)s: %(message)s')

"""
根据绑定关系录入应用节点信息汇总表node_info_snapshot
"""


def connect_mysql():
    conn = pymysql.connect(
        host=DATABASES['IP'],port=DATABASES['PORT'],database=DATABASES['DB'],charset=DATABASES['CHARSET'],user=DATABASES['USER'],password=DATABASES['PASSWORD'])
        # host='**************', port=3306, database='spider', charset='utf8', user='ops', password='123456')
        # host='**************', port=3306, database='spider', charset='utf8', user='scm', password='howbuyscm')
    return conn


def connect_postgre():
    conn = psycopg2.connect(
        host=CMDBDATABASES['IP'], port=CMDBDATABASES['PORT'], database=CMDBDATABASES['DB'],user=CMDBDATABASES['USER'], password=CMDBDATABASES['PASSWORD'])
        # host="**************", port=5432, database="witcher_cmdb_sit", user="postgres",
        # password="postgres")
        # host="**************", port=5432, database="witcher_cmdb", user="witcher_cmdb",
        # password="LziV5E01t6E3dhWBzgQwfQ==")
    return conn


def conn_close(conn):
    conn.close()


def get_cmdb_server_info(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT id, ip, server_cpu, server_memory FROM "public"."resources_server"
                          WHERE status=0 ''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['ip'] not in node_ip_info:
            node_ip_info[item['ip']] = {'id': item['id'], 'cpu': item['server_cpu'], 'memory': item['server_memory']}
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info

def get_cmdb_group_info(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''select 
                        gp."id" as "id",
                        rs."name" as "module_name",
                        rs.code as "module_code",
                        gp."name" as "deploy_group_name",
                        gp.code as "deploy_group_code",
                        gp."desc" as "deploy_group_desc"
                    from 
                        resources_resourcegroup gp 
                    left join resources_resource rs on gp.resource_code = rs.code''')
        result = cursor.fetchall()

    node_group_info = {}
    for item in result:
        if item['id'] not in node_group_info:
            node_group_info[item['id']] = {'module_name': item['module_name'], 'module_code': item['module_code'], 'deploy_group_name': item['deploy_group_name']
                                           , 'deploy_group_code': item['deploy_group_code'], 'deploy_group_desc': item['deploy_group_desc']}
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_group_info

def sync_group_to_spider(conn, group_info):
    for item in group_info:
        print("   "+str(item))
        module_name = group_info[item]["module_name"]
        module_code = group_info[item]["module_code"]
        deploy_group_name = group_info[item]["deploy_group_name"]
        deploy_group_code = group_info[item]["deploy_group_code"]
        deploy_group_desc = group_info[item]["deploy_group_desc"]
        print("        " + str(module_name)+"/"+str(module_code))
        print("        " + str(deploy_group_name)+"/"+str(deploy_group_code)+"/"+str(deploy_group_desc))

        with conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''
                DELETE from env_mgt_deploy_group where module_code='{}' and deploy_group_code = '{}'
            '''.format(module_code,deploy_group_code))
            conn.commit()

            cur_time = datetime.datetime.now()
            create_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                            INSERT INTO `spider`.`env_mgt_deploy_group` (
                                `id`,
                                `module_name`,
                                `module_code`,
                                `deploy_group_name`,
                                `deploy_group_code`,
                                `deploy_group_desc`,
                                `create_user`,
                                `create_time`,
                                `update_user`,
                                `update_time`,
                                `stamp`
                            )VALUES(
                                    {},
                                    '{}',
                                    '{}',
                                    '{}',
                                    '{}',
                                    '{}',
                                    'be-script',
                                    '{}',
                                    'be-script',
                                    '{}',
                                    '0'
                            )
                            """.format(item, module_name, module_code, deploy_group_name, deploy_group_code,
                                       deploy_group_desc,
                                       create_time, create_time)
                           )
            conn.commit()


def test0(conn_ops):
    with conn_ops.cursor(cursor=DictCursor) as cursor2:
        cursor2.execute('''
                select bak.ip,bak.app_name,bak.dubbo_port,bak.health_check_url from django_scm.common_service_cmdbdata bak 
                ''')
        result2 = cursor2.fetchall()
        bind_info = {}
        for item in result2:
            bind_info[item['ip']+"-"+item['app_name']] = {"dubbo_port": item['dubbo_port'], "health_check_url": item['health_check_url']}
            print("-----------" + item['ip'] + item['app_name'] + "-" + item['dubbo_port'] + "/" + item['health_check_url'])
    return bind_info


def test1(conn_spider, bind_info):
    with conn_spider.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
                        update env_mgt_node_bind set node_port = 0,health_check_url=''
                    ''')
        conn_spider.commit()

        cursor.execute('''
                    select b.id,n.node_ip,b.module_name,b.node_port,b.health_check_url from env_mgt_node_bind b,env_mgt_node n where n.id = b.node_id 
                ''')
        result1 = cursor.fetchall()

    for item in result1:
        with conn_spider.cursor(cursor=DictCursor) as cursor:
            print("      "+str(item["id"])+"/"+item["node_ip"])
            if item["node_ip"] + "-"+item["module_name"] in bind_info and bind_info[item["node_ip"]+"-"+item["module_name"]]:
                port = bind_info[item["node_ip"]+"-"+item["module_name"]]["dubbo_port"]
                url = bind_info[item["node_ip"]+"-"+item["module_name"]]["health_check_url"]
                print("                   " + port + "/" + url)
                if port == '':
                    port = '0'

                cursor.execute('''
                        update env_mgt_node_bind set node_port = '{}',health_check_url='{}' where id = {}
                        '''.format(port, url, item["id"]))
                conn_spider.commit()


def get_spider_node_info(conn):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''SELECT id, node_ip FROM env_mgt_node WHERE node_status=0''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['node_ip'] not in node_ip_info:
            node_ip_info[item['node_ip']] = {'id': item['id']}
        else:
            logging.info(item)
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


def generate_node_diff_content(cmdb_ip_set, spider_ip_set, last_snapshot, new_snapshot,nodeip,insert_error_list,update_ip_status,update_ip_status_error):
    content = '<br>'
    if cmdb_ip_set:
        content += '仅cmdb存在的节点: <br>'
        content += ', '.join(cmdb_ip_set)
        content += '<br><br>'
    if spider_ip_set:
        content += '仅spider存在的节点: <br>'
        content += ', '.join(spider_ip_set)
        content += '<br><br>'
    if last_snapshot:
        content += '之前节点快照差异：<br>'
        for item in last_snapshot:
            ip = item[0]
            app_name = item[1] if item[1] else ''
            cpu = item[2] if item[2] else ''
            memory = item[3] if item[3] else ''
            content += ip + ', ' + app_name + ', ' + str(cpu) + ', ' + str(memory) + '<br>'
        content += '<br>'
    if new_snapshot:
        content += '新节点快照差异：<br>'
        for item in new_snapshot:
            ip = item[0]
            app_name = item[1] if item[1] else ''
            cpu = item[2] if item[2] else ''
            memory = item[3] if item[3] else ''
            content += ip + ', ' + app_name + ', ' + str(cpu) + ', ' + str(memory) + '<br>'
        content += '<br>'
    if nodeip:
        content += '本次自动处理过的节点: <br>'
        for item in nodeip:
            node_ip = item[0]
            node_name = item[1]
            node_os = item[2] if item[2] else 'None'
            zone_code = item[3] if item[3] else 'None'
            minion_id = item[4] if item[4] else 'None'
            content += node_name + ', ' + node_ip + ', ' + str(node_os) + ', ' + str(zone_code)  +',' + str(minion_id) +'<br>'
        content += '<br>'
    if insert_error_list:
        content += '本次自动处理失败，需要人工干预的节点: <br>'
        for item in insert_error_list:
            node_ip = item[0]
            node_name = item[1]
            node_os = item[2] if item[2] else 'None'
            zone_code = item[3] if item[3] else 'None'
            minion_id = item[4] if item[4] else 'None'
            content += node_name + ', ' + node_ip + ', ' + str(node_os) + ', ' + str(zone_code)  +',' + str(minion_id) +'<br>'
        content += '<br>'
    if update_ip_status:
        content += '本次更新过状态的节点: <br>'
        for item in update_ip_status:
            node_ip = item[0]
            node_status = item[1]
            content += 'IP:' + node_ip + ', ' + 'status:' + str(node_status) + '<br>'
        content += '<br>'
    if update_ip_status_error:
        content += '本次自动更新节点状态失败的详情，需要人工干预: <br>'
        for item in update_ip_status_error:
            node_ip = item[0]
            node_status = item[1]
            content += 'IP:' + node_ip + ', ' + 'status:' + str(node_status) +'<br>'
        content += '<br>'
    return content


def send_node_info_mail(cmdb_ip_set, spider_ip_set, last_snapshot, new_snapshot,nodeip,insert_error_list,update_ip_status,update_ip_status_error):
    content = generate_node_diff_content(cmdb_ip_set, spider_ip_set, last_snapshot, new_snapshot,nodeip,insert_error_list,update_ip_status,update_ip_status_error)
    mail_to = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    mail_sender = SendMail()
    mail_sender.set_subject('spider每日节点差异校验')
    mail_sender.set_content(content)
    mail_sender.set_to(','.join(mail_to))
    mail_sender.send()


def get_app_ip_bind_list(cursor, ip):
    cursor.execute('''SELECT b.module_name, b.node_id, n.node_ip
                      FROM env_mgt_node_bind b
                      LEFT JOIN env_mgt_node n
                      ON b.node_id=n.id
                      WHERE n.node_ip="{}" '''.format(ip))
    result = cursor.fetchall()

    app_list = []
    for item in result:
        app_list.append(item['module_name'])

    return app_list


def get_ip_region(conn, ip):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''SELECT n.id, n.node_ip, n.region_id, b.suite_id, r.region_name, r2.region_name AS node_region_name
                          FROM env_mgt_node n
                          LEFT JOIN env_mgt_node_bind b ON n.id=b.node_id
                          LEFT JOIN env_mgt_suite s ON b.suite_id=s.id AND s.suite_is_active=1
                          LEFT JOIN env_mgt_region r ON s.region_id=r.id
                          LEFT JOIN env_mgt_region r2 ON n.region_id=r2.id
                          WHERE n.node_ip="{}" AND n.node_status=0
                       '''.format(ip))
        result = cursor.fetchall()

    if result:
        if result[0]['region_name']:
            return result[0]['region_name']
        else:
            return result[0]['node_region_name']
    else:
        return None


def insert_into_snapshot(conn, today, data):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            DELETE FROM node_info_snapshot WHERE day={}
        '''.format(today))
        conn.commit()

        sql = """
            INSERT INTO node_info_snapshot (`day`, `ip`, `app_name`, `cpu`, `memory`, `region_name`)
            VALUES ({}, %s, %s, %s, %s, %s)""".format(today)

        cursor.executemany(sql, data)
        conn.commit()


def get_last_snapshot(conn, today):
    last_day = get_last_record_day(conn, today)
    last_snapshot = set()
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT ip, app_name, cpu, memory, region_name FROM node_info_snapshot WHERE day="{}" '''.format(last_day))
        result = cursor.fetchall()

        for row in result:
            last_snapshot.add((row['ip'], row['app_name'], row['cpu'], row['memory'], row['region_name']))

    return last_snapshot


def generate_snapshot(conn, today, spider_node_data, cmdb_node_data):
    last_snapshot = get_last_snapshot(conn, today)

    new_snapshot = set()
    with conn.cursor(cursor=DictCursor) as cursor:
        for ip in spider_node_data:
            if ip in cmdb_node_data:
                cpu = cmdb_node_data[ip]['cpu']
                memory = cmdb_node_data[ip]['memory']
                app_list = get_app_ip_bind_list(cursor, ip)
                region_name = get_ip_region(conn, ip)
                if app_list:
                    for app_name in app_list:
                        new_snapshot.add((ip, app_name, cpu, memory, region_name))
                else:
                    new_snapshot.add((ip, None, cpu, memory, region_name))

    return last_snapshot, new_snapshot


def get_last_record_day(conn, today):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT `day` FROM node_info_snapshot WHERE day<"{}" GROUP BY `day` ORDER BY `day` DESC LIMIT 1;
        '''.format(today))

        result = cursor.fetchone()
        if result:
            return result['day']
        else:
            return None

def dist_cmdb_to_spider(conn, only_spider_have_ip):
    '''提取cmdb同步到部署平台的信息'''
    # new_snapshot = set()

    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT id, name,ip, server_cpu,minion_id, os,server_memory,zone_code,region_code FROM "public"."resources_server"
                          WHERE ip in ('{}') and status=0 '''.format(only_spider_have_ip))
        new_snapshot = cursor.fetchall()
    node_ip_info = {}
    for item in new_snapshot:
        if item['ip'] not in node_ip_info:
            node_ip_info[item['ip']] = {'id': item['id'],'name': item['name'],'os': item['os'],'minion_id': item['minion_id'], 'cpu': item['server_cpu'], 'memory': item['server_memory'],'zone_code':item['zone_code'],'region_code':item['region_code']}
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info

def update_spider_node_status(conn,pg_conn,only_spider_have_ip):
    update_ip_status =[]
    update_ip_status_error=[]
    cur_time = datetime.datetime.now()
    create_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    update_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")

    update_user = 'sync_scm'
    for ip in only_spider_have_ip:
        with pg_conn.cursor(cursor=DictCursor) as cursor:
            cursor.execute('''select node_name from  env_mgt_node
                              WHERE node_ip ='{}' and node_status=0 '''.format(ip))
            new_snapshot = cursor.fetchall()

        for item in new_snapshot:
            node_name =item['node_name']
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute('''SELECT id, ip,status FROM "public"."resources_server"
                                  WHERE ip ='{}' and name = '{}' '''.format(ip,node_name))
                cmdb_new_snapshot = cursor.fetchall()
            for it in cmdb_new_snapshot:
                node_status =it['status']
                try:
                    with pg_conn.cursor(cursor=DictCursor) as cursor:
                        cursor.execute('''update env_mgt_node
                                          set node_status={},update_time='{}',update_user='{}'
                                          WHERE node_ip ='{}' and node_name='{}' '''.format(node_status,update_time,update_user,ip,node_name))
                        new_snapshot = cursor.fetchall()
                        pg_conn.commit()
                    print('''update env_mgt_node
                                          set node_status={},update_time='{}',update_user='{}'
                                          WHERE node_ip ='{}' and node_name='{}' '''.format(node_status,update_time,update_user,ip,node_name))
                    update_ip_status.append((ip,node_status))
                except Exception as e:
                    update_ip_status_error.append((ip,node_status))
    return update_ip_status,update_ip_status_error

def sync_cmdb_to_spider(conn,insert_data):
    '''同步cmdb数据到部署平台'''
    nodeip = []
    insert_error_list=[]
    cur_time = datetime.datetime.now()
    create_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    update_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    # region={'howbuy-sh-hk-a':1,'howbuy-sh-bs-db':12,'howbuy-sh-wgq-hb':16,'howbuy-sh-bs-a':9,'howbuy-sh-bs-ehb':12,'howbuy-sh-bs-hb':12,'howbuy-sh-bs-pa':12,'howbuy-sh-wgq-ds':16,}
    print(insert_data)
    for ip in insert_data:
        node_ip= ip
        node_name=insert_data[ip]['name']
        node_os=insert_data[ip]['os'] if insert_data[ip]['os'] != 'None' else None
        # node_id = insert_data[ip]['']
        zone_code = insert_data[ip]['zone_code'] if insert_data[ip]['zone_code'] != 'None' else None
        region_id = REGION[zone_code]
        minion_id = insert_data[ip]['minion_id'] if insert_data[ip]['minion_id'] != 'None' else None
        create_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
        update_time = cur_time.strftime("%Y-%m-%d %H:%M:%S")
        node_status = 0
        region_code = insert_data[ip]['region_code'] if insert_data[ip]['zone_code'] != 'None' else None
        create_user = 'sync_scm'
        create_time = create_time
        update_time = update_time
        update_user = 'sync_scm'

        try:
            with conn.cursor(cursor=DictCursor) as cursor:
                cursor.execute('''
                            INSERT INTO env_mgt_node (node_ip ,node_name ,node_os,minion_id,region_id,node_status,create_user,create_time,update_time,update_user )
                            VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);
                            ''' ,(node_ip,node_name,node_os,minion_id,region_id,node_status,create_user,create_time,update_time,update_user))
            conn.commit()
            nodeip.append((node_name, node_ip, node_os, zone_code, minion_id))
        except Exception as e:
            logging.error(e)
            insert_error_list.append((node_name,node_ip,node_os,zone_code,minion_id))
            conn.rollback()
    return nodeip,insert_error_list
    # with conn.cursor(cursor=DictCursor) as cursor:
    #     cursor.execute('''update env_mgt_node set node_ip=%s ,node_name=%s ,node_os=%s ,node_status=%s'''%(node_ip,node_name,node_os,node_status))
    #     result = cursor.fetchall()

if __name__ == '__main__':
    my_conn = connect_mysql()
    pg_conn = connect_postgre()

    '''
        从ops同步监控检查状态和端口至spider
    '''
    # group_info = test0(my_conn)
    # test1(my_conn, group_info)

    group_info = get_cmdb_group_info(pg_conn)
    sync_group_to_spider(my_conn,group_info)

    cmdb_server_info = get_cmdb_server_info(pg_conn)
    spider_node_info = get_spider_node_info(my_conn)
    cmdb_server_ip_set = set(cmdb_server_info)
    spider_node_ip_set = set(spider_node_info)
    only_cmdb_server_have_ip = cmdb_server_ip_set - spider_node_ip_set
    only_spider_have_ip = spider_node_ip_set - cmdb_server_ip_set
    logging.info('****only cmdb hava****')
    logging.info(only_cmdb_server_have_ip)
    logging.info(len(only_cmdb_server_have_ip))
    logging.info('***********************')
    logging.info('****only spider hava****')
    logging.info(only_spider_have_ip)
    logging.info(len(only_spider_have_ip))
    update_ip_status,update_ip_status_error = update_spider_node_status(pg_conn,my_conn,only_spider_have_ip)
    logging.info('***********************')
    # dist_data = tuple(only_cmdb_server_have_ip)
    dist_data = "','".join(only_cmdb_server_have_ip)
    now = datetime.datetime.now()
    day = now.year * 10000 + now.month * 100 + now.day

    spider_last_snapshot, spider_new_snapshot = generate_snapshot(my_conn, day, spider_node_info, cmdb_server_info)

    only_last_have = []
    only_new_have = []
    for item in spider_new_snapshot:
        if item not in spider_last_snapshot:
            only_new_have.append(item)
    for item in spider_last_snapshot:
        if item not in spider_new_snapshot:
            only_last_have.append(item)

    if only_new_have or only_last_have:
        insert_into_snapshot(my_conn, day, spider_new_snapshot)
    else:
        logging.info('No different with last snapshot.')
    if dist_data:
        new = dist_cmdb_to_spider(pg_conn, dist_data)
        nodeip, insert_error_list = sync_cmdb_to_spider(my_conn, new)
    else:
        nodeip = []
        insert_error_list = []

    if only_cmdb_server_have_ip or only_spider_have_ip or only_last_have or only_new_have or nodeip or insert_error_list or update_ip_status or update_ip_status_error:
        send_node_info_mail(only_cmdb_server_have_ip, only_spider_have_ip, only_last_have, only_new_have,nodeip,insert_error_list,update_ip_status,update_ip_status_error)

    conn_close(pg_conn)
    conn_close(my_conn)
