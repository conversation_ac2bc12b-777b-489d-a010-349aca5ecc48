#
import pymysql
import logging
from pymysql.cursors import DictCursor

logging.basicConfig(level=logging.INFO, format='%(asctime)s [line:%(lineno)d] - %(levelname)s: %(message)s')

"""
spider: 绑定关系的节点与spider所有可用节点关系比对
"""


def connect_mysql():
    conn = pymysql.connect(
        host='**************', port=3306, database='spider', charset='utf8', user='scm', password='howbuyscm')
    return conn


def conn_close(conn):
    conn.close()


def get_spider_app_bind_node_id(conn):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''SELECT module_name, node_id FROM env_mgt_node_bind''')
        result = cursor.fetchall()

    node_id_set = set()
    for item in result:
        node_id_set.add(item['node_id'])

    return node_id_set


def get_spider_node_ip(conn, node_id_set):
    with conn.cursor(cursor=DictCursor) as cursor:
        node_id_str = ','.join(list(map(lambda x: str(x), node_id_set)))
        cursor.execute(
            '''SELECT id, node_ip, node_status FROM env_mgt_node WHERE id in (%s) and node_status != 3 ''' % node_id_str)
        result = cursor.fetchall()

    node_info = {}
    for node in result:
        if node['node_ip'] not in node_info:
            node_info[node['node_ip']] = node
        else:
            logging.info('xxxxxxxxxxxxxxxxxxxxxxxxx')

    return node_info


def get_spider_node_info(conn):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''SELECT id, node_ip FROM env_mgt_node WHERE node_status=0''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['node_ip'] not in node_ip_info:
            node_ip_info[item['node_ip']] = {'id': item['id']}
        else:
            logging.info(item)
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


if __name__ == '__main__':
    my_conn = connect_mysql()
    bind_node_id_set = set(get_spider_app_bind_node_id(my_conn))
    bind_node_info = get_spider_node_ip(my_conn, bind_node_id_set)
    spider_node_info = get_spider_node_info(my_conn)

    bind_node_ip_set = set(bind_node_info)
    spider_node_ip_set = set(spider_node_info)

    only_bind_node_ip = bind_node_ip_set - spider_node_ip_set
    only_spider_node_ip = spider_node_ip_set - bind_node_ip_set
    logging.info('****only bind node ip****')
    logging.info(only_bind_node_ip)
    logging.info(len(only_bind_node_ip))
    logging.info('***********************')
    logging.info('****only spider node ip****')
    logging.info(only_spider_node_ip)
    logging.info(len(only_spider_node_ip))
    logging.info('***********************')

    conn_close(my_conn)
