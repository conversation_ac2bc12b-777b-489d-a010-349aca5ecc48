#
import logging
import requests
import psycopg2
import psycopg2.extras

logging.basicConfig(level=logging.INFO, format='%(asctime)s [line:%(lineno)d] - %(levelname)s: %(message)s')

"""
mianbao节点的cpu、memory信息写入 cmdb同步节点表 cmdb_base_compare_info
"""


def connect_postgre():
    conn = psycopg2.connect(
        # host="**************", port=5432, database="witcher_cmdb_sit", user="postgres",
        # password="postgres")
        host="**************", port=5432, database="witcher_cmdb", user="witcher_cmdb",
        password="LziV5E01t6E3dhWBzgQwfQ==")
    return conn


def conn_close(conn):
    conn.close()


def get_mianbao_node_data():
    ucloud_url = "http://**************/mianbao/api/v1/ucloud/uhost/"
    vm_url = "http://**************:6060/mianbao/api/v1/vsphere/vms/?limit=3000"
    ucloud_result = requests.get(ucloud_url).json()['results']
    vm_result = requests.get(vm_url).json()['results']

    nodes_info = {}
    nodes_ip_list = []

    logging.info('****mianbao异常数据*****')
    for item in ucloud_result:
        if item['Ip'] and item['Ip'] not in nodes_ip_list:
            nodes_ip_list.append(item['Ip'])
            nodes_info[item['Ip']] = {'cpu': item['CPU'], 'memory': item['Memory'], 'name': item['Name']}
        else:
            logging.info(item)

    for item in vm_result:
        if item['IpAddress'] and item['IpAddress'] not in nodes_ip_list:
            nodes_ip_list.append(item['IpAddress'])
            nodes_info[item['IpAddress']] = {'cpu': item['NumCpu'], 'memory': item['MemorySize'], 'name': item['Name']}
        else:
            logging.info(item)
    logging.info('****************')

    return nodes_info, nodes_ip_list


def get_cmdb_base_compare_info(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT id, ip FROM "public"."cmdb_base_compare_info"
                          WHERE cmdb_have='TRUE' AND base_have='TRUE' ''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['ip'] not in node_ip_info:
            node_ip_info[item['ip']] = item['id']
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


def update_cmdb_node_info(conn, node_info, cmdb_info, ip_set):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        for ip in ip_set:
            cursor.execute('''
                UPDATE "public"."resources_server" SET server_cpu={}, server_memory={} WHERE ip = INET '{}'
            '''.format(node_info[ip]['cpu'], node_info[ip]['memory'], ip))
        conn.commit()


if __name__ == '__main__':
    mianbao_nodes_info, mianbao_nodes_ip_list = get_mianbao_node_data()
    pg_conn = connect_postgre()
    cmdb_nodes_ip_info = get_cmdb_base_compare_info(pg_conn)

    cmdb_ip_set = set(cmdb_nodes_ip_info)
    mianbao_ip_set = set(mianbao_nodes_info)
    same_ip_set = mianbao_ip_set & cmdb_ip_set

    only_mianbao_ip_set = mianbao_ip_set - cmdb_ip_set
    only_cmdb_ip_set = cmdb_ip_set - mianbao_ip_set

    logging.info('****only mianbao hava****')
    logging.info(only_mianbao_ip_set)
    logging.info('***********************')
    logging.info('****only cmdb hava****')
    logging.info(only_cmdb_ip_set)
    logging.info('***********************')

    update_cmdb_node_info(pg_conn, mianbao_nodes_info, cmdb_nodes_ip_info, same_ip_set)
    conn_close(pg_conn)
