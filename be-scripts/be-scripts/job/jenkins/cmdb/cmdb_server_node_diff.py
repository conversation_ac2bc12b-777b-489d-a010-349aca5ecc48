#
import logging
import psycopg2
import psycopg2.extras

logging.basicConfig(level=logging.INFO, format='%(asctime)s [line:%(lineno)d] - %(levelname)s: %(message)s')

"""
CMDB: 检查从mianbao同步的节点与自身的resource_server节点差异
"""


def connect_postgre():
    conn = psycopg2.connect(
        # host="**************", port=5432, database="witcher_cmdb", user="witcher_cmdb",
        # password="LziV5E01t6E3dhWBzgQwfQ==")
        host="**************", port=5432, database="witcher_cmdb_sit", user="postgres",
        password="postgres")
    return conn


def conn_close(conn):
    conn.close()


def get_cmdb_base_compare_info(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT id, ip, server_cpu, server_memory FROM "public"."cmdb_base_compare_info"
                          WHERE cmdb_have='TRUE' AND base_have='TRUE' ''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['ip'] not in node_ip_info:
            node_ip_info[item['ip']] = {'id': item['id'], 'cpu': item['server_cpu'], 'memory': item['server_memory']}
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


def get_cmdb_server_info(conn):
    with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
        cursor.execute('''SELECT id, ip, server_cpu, server_memory FROM "public"."resources_server"
                          WHERE status=0 and
                               (sync_result<>1 or sync_result IS NULL) and
                               (server_type <> 'PHY' or server_type IS NULL) ''')
        result = cursor.fetchall()

    node_ip_info = {}
    for item in result:
        if item['ip'] not in node_ip_info:
            node_ip_info[item['ip']] = {'id': item['id'], 'cpu': item['server_cpu'], 'memory': item['server_memory']}
        else:
            logging.info('xxxxxxxxxxxxxx')

    return node_ip_info


if __name__ == '__main__':
    pg_conn = connect_postgre()
    cmdb_compare_info = get_cmdb_base_compare_info(pg_conn)
    cmdb_server_info = get_cmdb_server_info(pg_conn)
    cmdb_compare_ip_set = set(cmdb_compare_info)
    cmdb_server_ip_set = set(cmdb_server_info)

    only_cmdb_compare_have_ip = cmdb_compare_ip_set - cmdb_server_ip_set
    only_cmdb_server_have_ip = cmdb_server_ip_set - cmdb_compare_ip_set

    logging.info('****only cmdb compare hava****')
    logging.info(only_cmdb_compare_have_ip)
    logging.info(len(only_cmdb_compare_have_ip))
    logging.info('***********************')
    logging.info('****only cmdb server hava****')
    logging.info(only_cmdb_server_have_ip)
    logging.info(len(only_cmdb_server_have_ip))
    logging.info('***********************')

    conn_close(pg_conn)
