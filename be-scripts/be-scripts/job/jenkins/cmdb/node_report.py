#
import logging
import datetime
import pymysql
from pymysql.cursors import DictCursor

logging.basicConfig(level=logging.INFO, format='%(message)s')


def connect_mysql():
    conn = pymysql.connect(
        # host='**************', port=3306, database='spider', charset='utf8', user='ops', password='ops')
        host='**************', port=3306, database='spider', charset='utf8', user='scm', password='howbuyscm')
    return conn


def conn_close(conn):
    conn.close()


def get_last_record_day(conn, today):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT `day` FROM node_info_snapshot WHERE day<"{}" GROUP BY `day` ORDER BY `day` DESC LIMIT 1;
        '''.format(today))

        result = cursor.fetchone()
        if result:
            return result['day']
        else:
            return None


def all_node_info(conn, day):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT COUNT(id) AS `total`, SUM(`cpu`) AS `cpu`, SUM(`memory`) DIV 1024 AS `memory`
            FROM node_info_snapshot WHERE `day`={}
        '''.format(day))

        data = cursor.fetchone()
        logging.info(str(day // 10000) + '年' + str(day // 100 % 100) + '月:')
        logging.info(str(data['total']) + '节点, ' + str(data['cpu']) + '核, ' + str(data['memory']) + 'GB')
        logging.info('')


def app_node_info(conn, day):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT app_name, COUNT(id) AS `total` , SUM(`cpu`) AS `cpu`,SUM(`memory`) DIV 1024 AS `memory`
            FROM node_info_snapshot
            WHERE `day`=20200728 GROUP BY `app_name`
        '''.format(day))

        data = cursor.fetchall()
        for item in data:
            if item['app_name']:
                logging.info(
                    item['app_name'] + ', ' + str(item['total']) + ',' + ' ' + str(item['cpu']) + ', ' + str(
                        item['memory']) + 'GB')
            else:
                logging.info('' + ', ' + str(item['total']) + ',' + ' ' + str(item['cpu']) + ', ' + str(
                    item['memory']) + 'GB')


def app_node_info_detail(conn, day):
    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute('''
            SELECT app_name, ip, memory DIV 1024 AS memory, cpu
            FROM node_info_snapshot
            WHERE `day`=20200728 ORDER BY `app_name`
        '''.format(day))

        data = cursor.fetchall()
        for item in data:
            if item['app_name']:
                logging.info(
                    item['app_name'] + ', ' + str(item['ip']) + ', ' + str(item['cpu']) + ', ' + str(item['memory']) + 'GB')
            else:
                logging.info(
                    'None, ' + str(item['ip']) + ', ' + str(item['cpu']) + ', ' + str(item['memory']) + 'GB')


if __name__ == '__main__':
    my_conn = connect_mysql()
    now = datetime.datetime.now()
    today = now.year * 10000 + (now.month + 1) * 100
    last_day = get_last_record_day(my_conn, today)
    all_node_info(my_conn, last_day)
    app_node_info(my_conn, last_day)
    app_node_info_detail(my_conn, last_day)
    conn_close(my_conn)
