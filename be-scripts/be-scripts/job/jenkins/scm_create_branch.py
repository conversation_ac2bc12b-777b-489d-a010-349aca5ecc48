import gitlab
import sys
import sys
import datetime
#from dao.connect.mysql import DBConnectionManager
url = 'http://dev-gitlab.intelnal.howbuy.com/'
token = '********************'
def creat_branch(keyword,br_name):
    # projects={'be-script':'117','spider':'190','website_web':'36'}
    gl = gitlab.Gitlab(url, token)
    print(gl)
    # print(id)
    id = gl.projects.list(search=keyword)[0].id
    print(id)
    project = gl.projects.get(id)
    print(project.name)
    print('==========')
    project.branches.create({'branch': br_name,'ref': 'master'})
    # project.branches.delete('liuweitest')
    print('==========')
    # for p in gl.projects.list(all=True, as_list=False):
    #     print(p.name, p.id)

# def insert(
#         pipeline_id,
#         br_name,
#         project_group,
#         br_style,
#         br_start_date,
#         br_end_date,
#         duedate,
#         br_status,
#         samulation_mail_status,
#         description,
#         releaseNotic=None,
#         schedule,
# file_ccms_config,
#          tapd_id,
#     ):
#     """创建应用发布申请记录"""
#     meta = dict(
#         team                 = team,
#         appName              = appName,
#         br_name              = br_name,
#         applicant            = applicant,
#         apply_at             = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
#         pipeline_description = pipeline_description,
#         sql_address          = sql_address,
#         description          = description,
#         pipeline_id          = pipeline_id,
#         env                  = env,
#         git_last_version     = git_last_version,
#         git_repo_version     = git_repo_version,
#     )
#
#     for k in list(meta.keys()):
#         if not meta[k] or meta[k] is None:
#             del meta[k]
#
#     sql = '''
#         INSERT INTO iterative_pipeline_branches ({fields})
#         VALUES ({values})
#     '''.format(
#             fields=','.join(meta.keys()),
#             values=','.join([ "'{}'".format(i) for i in meta.values()])
#         )
#
#     with DBConnectionManager() as db:
#         db.cur.execute(sql)
#         db.connection.commit()

if __name__ == '__main__':
    keyword = 'be-scripts'
    br_name = 'liuweitest'
    # keyword = sys.argv[1]
    # br_name = sys.argv[2]
    creat_branch(keyword,br_name)
