import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from ci_pipeline.ci_pipeline_utils.node_docker_ip_record import NodeDockerIPRecorder
from dao.connect.mysql import DBConnectionManager


def get_app_container_name(suite_code, app_name):
    with DBConnectionManager() as db:
        sql = '''select 
                IF ( t.container_name IS NULL, pdi.container_name, t.container_name) AS container_name
                from env_mgt_node_bind t 
                left join env_mgt_suite ems on t.suite_id = ems.id
                left join publish_deploy_info pdi on t.module_name = pdi.module_name
                where ems.suite_code = '{}' and t.module_name = '{}'
            '''.format(suite_code, app_name)
        db.cur.execute(sql)
        result = db.cur.fetchone()
        if result:
            return result['container_name']
        else:
            return app_name


if __name__ == '__main__':
    suite_code =sys.argv[1]
    app_list = sys.argv[2]

    for app in app_list.split(","):
        container_name = get_app_container_name(suite_code, app)
        record = NodeDockerIPRecorder(app, suite_code, container_name)
        record.record_info()