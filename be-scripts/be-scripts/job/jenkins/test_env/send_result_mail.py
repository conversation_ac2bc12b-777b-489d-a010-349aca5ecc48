import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from common.email.send_email import SendMail
from settings import logger

if __name__ == '__main__':
    params = sys.argv[1:]
    logger.info('send_result_mail params: {}'.format(params))
    try:
        opt_type = sys.argv[1]
        execute_result = sys.argv[2]
        build_id = sys.argv[3]
        content = ''
        subject = ''
        if opt_type == 'archive_ddl_to_basic':
            if execute_result == 'failure':
                content_url = 'http://jkp-master.howbuy.pa/jenkins/job/sync_feature_sql_to_basic/' + build_id + '/'
                content = content + '<br><a href="{}">点击查看</a>'.format(content_url)
                subject = '每日归档DDL执行到标准库'

        mail_to = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']

        if content and subject:
            mail_builder = SendMail()
            mail_builder.set_subject(subject)
            mail_builder.set_content(content)
            mail_builder.set_to(','.join(mail_to))
            mail_builder.send()
    except Exception as e:
        logger.error(e)
