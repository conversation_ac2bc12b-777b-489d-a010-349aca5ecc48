#
import os
import re
import xlrd
import xlwt
import subprocess
from common.ext_cmd.svn.svn_cmd import SvnCmd
from common.ext_cmd.shell_cmd import shell_cmd
from dao.get.mysql.ccms import get_backdb_ip_by_src, get_backdb_ip_by_target

file_list = ['分组', '系列', '节点']


def ip_replace(ip):
    """
    IP地址的替换
    eg:
        '***********'
        ==>
        '1_1', '-1-1'

    :param ip:
    :return:
    """
    return ip.replace("192.168.", "").replace(".", "_"), ip.replace("192.168", "").replace(".", "-")


def replace_by_rule(text, rule):
    """根据规则替换字符串内容

    :params text: 替换前的文本
    :params rule: dict. 替换规则. eg: {'原值': '替换值'}
    :return: 替换后的文本
    """
    rule = dict((re.escape(k), v) for k, v in rule.items())
    pattern = re.compile("|".join(rule.keys()))
    result = pattern.sub(lambda m: rule[re.escape(m.group(0))], text)
    return result


def excel_write(file_path_pro, file_path_rep, tmp_file_path):
    write_dict = resolve_excel(file_path_pro, file_path_rep)
    del write_dict['节点']
    # list_keys: 节点
    list_keys = list(write_dict)
    # list_class_values: [['组名','数据'],]
    list_class_values = list(write_dict.values())
    for i in range(0, len(list_keys)):
        # list_class_values: [['组名', '数据', '节点']]
        list_class_values[i].append(list_keys[i])

    datefile = xlrd.open_workbook(file_path_pro)
    data_list = [datefile.sheet_by_name(name) for name in file_list]

    # 删除临时文件
    if os.path.exists(tmp_file_path):
        os.remove(tmp_file_path)
    # 新建临时文件
    datafile_temp = xlwt.Workbook()
    sheet_list = [datafile_temp.add_sheet(name, cell_overwrite_ok=True) for name in file_list]
    # 数据写入临时文件
    # 向sheet1插入数据
    for row in range(data_list[0].nrows):
        sheet_list[0].write(row, 0, data_list[0].row_values(row))
    # 向sheet_list[1]插入数据
    sheet_list[1].write(0, 0, '组名')
    sheet_list[1].write(0, 1, '系列')
    sheet_list[1].write(0, 2, '节点')
    sheet_list[1].write(0, 3, '数据')

    # 向sheet_list[2]插入数据

    excel_list = []
    for i in range(0, len(write_dict)):
        excel_dict = dict()
        excel_dict.update({
            'num': i,
            'class': list_class_values[i][0],
            'keys': list_class_values[i][2],
            'values': list_class_values[i][1]
        })
        excel_list.append(excel_dict)
    for j in excel_list:
        sheet_list[2].write(j['num'], 0, j['class'])
        sheet_list[2].write(j['num'], 1, j['keys'])
        sheet_list[2].write(j['num'], 2, j['values'])
    datafile_temp.save(tmp_file_path)


def excel_replace(file_path, src_ip, new_ip, tmp_file_path, back_ip):
    """
    替换excel文件
    :param file_path: ccms_temp_60.xls文件路径
    :param src_ip: **************
    :param new_ip: tms的k8s访问地址
    :param tmp_file_path: ccms_new.xls的地址
    :param back_ip: tp的ip地址
    :return:
    """
    target_result = get_backdb_ip_by_target(new_ip, back_ip)
    src_result = get_backdb_ip_by_src(src_ip)

    # excel文件的操作部分
    # 读取源文件
    file_read = xlrd.open_workbook(file_path)
    data_list = [file_read.sheet_by_name(name) for name in file_list]
    # 删除临时文件
    if os.path.exists(tmp_file_path):
        os.remove(tmp_file_path)
    # 新建临时文件
    file_tmp = xlwt.Workbook()
    sheet_list = [file_tmp.add_sheet(name, cell_overwrite_ok=True) for name in file_list]
    # 数据写入临时文件
    # 向 分组sheet 插入数据
    for row in range(data_list[0].nrows):
        sheet_list[0].write(row, 0, data_list[0].row_values(row))
    # 向 系列sheet 插入数据
    sheet_list[1].write(0, 0, '组名')
    sheet_list[1].write(0, 1, '系列')
    sheet_list[1].write(0, 2, '节点')
    sheet_list[1].write(0, 3, '数据')
    # 向 节点sheet 插入数据
    for row in range(data_list[2].nrows):
        # 插入第1列数据
        for column in range(data_list[2].ncols):
            # 替换前台ip
            new_value = str(data_list[2].cell_value(row, column)).replace(ip_replace(src_ip)[0], ip_replace(new_ip)[0]). \
                replace(ip_replace(src_ip)[1], ip_replace(new_ip)[1]).replace(src_ip, new_ip)

            # 替换后台ip
            new_value = new_value.replace(ip_replace(src_result['ip'])[0], ip_replace(target_result['ip'])[0]). \
                replace(ip_replace(src_result['ip'])[1], ip_replace(target_result['ip'])[1]). \
                replace(src_result['ip'], target_result['ip'])

            # 替换数据库实例
            new_value = new_value.replace(src_result['name'], target_result['name']).replace(
                str.lower(src_result['name']), target_result['name'])

            # 替换数据库IP
            new_value = new_value.replace(src_result['backdb_ip'], target_result['backdb_ip'])
            new_value = new_value.replace(src_result['frontdb_ip'], target_result['frontdb_ip'])

            # 替换产线IP
            new_value = new_value.replace('10.1', 'xx.x').replace('10.7', 'xx.x').replace('10.40', 'xx.xx')

            # common  order.plan.center.base.ur  http://plan.tmsxx.k8s.howbuy.com
            new_value = new_value.replace('http://order-plan.inner.howbuy.com', 'http://plan.{}'.format(new_ip))

            # 特殊处理. 如果TP部署在k8s的情况下
            if target_result['ip'].startswith('it'):
                # targe_result['ip'] 就是 back_ip
                new_back_ip = target_result['ip']
                rule = {
                    '{}:2182'.format(new_back_ip): 'zookeeper:2181',
                    '{}.k8s.howbuy.com:20381'.format(new_back_ip): 'o32-pre-web.{}.k8s.howbuy.com'.format(new_back_ip),
                    '{}.37070'.format(new_back_ip): 'pay-console.{}.k8s.howbuy.com'.format(new_back_ip),
                    '{}.k8s.howbuy.com:8002'.format(new_back_ip): 'es-web-console.{}.k8s.howbuy.com'.format(
                        new_back_ip),
                    'http://{}/acc-console/'.format(
                        new_back_ip): 'http://acc-console.{}.k8s.howbuy.com/acc-console/'.format(new_back_ip),
                    'http://{}.k8s.howbuy.com/acc-console/'.format(
                        new_back_ip): 'http://acc-console.{}.k8s.howbuy.com/acc-console/'.format(new_back_ip),
                }
                new_value = replace_by_rule(new_value, rule)

            # 写入sheet
            sheet_list[2].write(row, column, new_value)

    file_tmp.save(tmp_file_path)


def resolve_excel(file_path_pro, file_path_rep):
    """合并excel中数据, 后者会覆盖前者'节点'的值

    :params file_path_pro: 源文件. 生产ccms配置文件
    :params file_path_rep: 替换文件. 包含需要替换的ccms内容
    :return: 返回一个合并的字典对象.
        {
            '节点': ['组名', '数据']
        }
    """
    tmp_dict = dict()
    # 读取excel
    workbook_pro = xlrd.open_workbook(file_path_pro)
    workbook_rep = xlrd.open_workbook(file_path_rep)
    # 获取sheet. sheet_all_values_pro: ccms源配置. sheet_all_values_rep: 配置内容
    sheet_all_values_pro = workbook_pro.sheets()[2]
    sheet_all_values_rep = workbook_rep.sheets()[0]
    # 获取sheet行数
    row_length_pro = sheet_all_values_pro.nrows
    row_length_rep = sheet_all_values_rep.nrows

    # 组装数据: 会去重同名'节点'数据
    for i in range(row_length_pro):
        rows_pro = sheet_all_values_pro.row_values(i)
        tmp_dict[rows_pro[1]] = [rows_pro[0], rows_pro[2]]

    for i in range(row_length_rep):
        rows_rep = sheet_all_values_rep.row_values(i)
        tmp_dict[rows_rep[1]] = [rows_rep[0], rows_rep[2]]

    return tmp_dict
