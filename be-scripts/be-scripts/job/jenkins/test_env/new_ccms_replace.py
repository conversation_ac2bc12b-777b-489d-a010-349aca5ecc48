#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql.test_env import get_env_info_by_tms
from job.jenkins.test_env.new_ccms import excel_replace, excel_write


def former_ccms_replace(env):
    base_path = '/tmp/ccms/'
    file_path_pro = base_path + "ccms_online.xls"
    file_path_rep = base_path + "ccms_match.xlsx"
    file_path = base_path + "_temp.xls"
    new_file_path = base_path + 'tmp/ccms_new.xls'

    new_ip = "{}.k8s.howbuy.com".format(env)
    src_ip = "**************"
    back_ip = "127.0.0.1"
    excel_write(file_path_pro, file_path_rep, file_path)
    os.chdir(base_path)
    excel_replace(file_path, src_ip, new_ip, new_file_path, back_ip)


if __name__ == "__main__":
    tms_env = 'tms08'
    former_ccms_replace(tms_env)
