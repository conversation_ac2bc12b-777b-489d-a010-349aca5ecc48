#
import os
import sys
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger
from utils.test_env.test_env_lib import step_desc, request, gen_signature, md5_factory, scp_file


@step_desc('复制替换后的ccms文件到测试ccms服务器')
def send_ccms_file(env):
    # local_ccms_upload_file = WORKSPACE + 'tmp/ccms_new.xls'
    local_ccms_upload_file = WORKSPACE + '{}.xls'.format(env)
    remote_file_path = '/data/app/import/' + 'ccms_new.xls'
    scp_file_result = scp_file("**************", "root", local_ccms_upload_file, remote_file_path, 'put')
    if not scp_file_result:
        raise Exception("CCMS文件导入失败")
    else:
        logger.info("CCMS文件导入成功")


@step_desc('调用ccms导入接口')
def import_ccms_api(env):
    import_env = 'docker-k8s-{}'.format(env)
    import_srv_url = 'http://**************:9500'
    operate_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    import_security_code = gen_signature(md5_factory(operate_time, '654321', import_env))
    import_data = {
        'envFlag': import_env,
        'fileName': 'ccms_new.xls',
        'needCall': 'no',
        'operator_ip': '',
        'operator_name': '',
        'operator_time': operate_time,
        'security_code': import_security_code
    }
    res = request(import_srv_url, '/ccms/apiFordevops/import.htm', import_data)
    res_json = res.json()
    if res_json['code'] == 200:
        logger.info("请求CCMS导入接口成功")
    else:
        raise Exception('请求CCMS导入接口失败: ' + str(res_json))


if __name__ == '__main__':
    tms_env = sys.argv[1]
    # WORKSPACE = '/data/env_init/ccms/'
    WORKSPACE = '/data/env_init/config/ccms/'
    send_ccms_file(tms_env)
    import_ccms_api(tms_env)
