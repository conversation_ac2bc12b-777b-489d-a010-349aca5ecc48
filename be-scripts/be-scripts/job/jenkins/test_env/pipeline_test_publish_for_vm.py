# 流水线的测试环境发布（vm实现）
# 第1版 zt@2020-08-28
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from settings import PIPELINE_TEST_PUBLISH as PTP
from job.jenkins.test_env.pipeline_test_publish_for_base import PipelineTestPublishHandler
from job.jenkins.test_env.pipeline_test_publish_common import step_decorator
from job.jenkins.test_env.pipeline_test_publish_common import exec_remote_cmd
from job.jenkins.test_env.pipeline_test_publish_common import exec_local_rsync_to_remote


class PipelineTestPublishVmHandler(PipelineTestPublishHandler):
    """
    流水线测试环境环境发布，虚机 zt@2020-08-25(七夕)
    """

    def __init__(self, ptp_id, ptp_type, po, node):
        super().__init__(ptp_id, ptp_type, po)
        self.__init_node_info(node)

    @step_decorator(">>>> 应用推送至目标虚拟机 >> ")
    def vm_app_push(self, **kwargs):
        """虚机应用推送"""
        log.info(">>>> vm_app_push >> node = {}".format(self.node))
        lib_deploy_path = self.node['deploy_path']
        if not lib_deploy_path:
            if self.po.package_type and self.po.package_type == 'jar':
                lib_deploy_path = '{}{}/lib'.format(PTP['def_vm_jar_lib_path'], self.po.app_name)
            else:
                container_name = self.po['container_name']
                if not container_name:
                    container_name = self.po['app_name']

                lib_deploy_path = '{}{}/webapps/{}'.format(PTP['def_vm_tomcat_lib_path'],
                                                           container_name,
                                                           self.po.app_name)
        cmd = "mkdir -p {}".format(lib_deploy_path)
        exec_remote_cmd(self.node['node_code'], cmd)
        # 带*号时delete无法生效问题 zt@2020-09-21
        # pkg_cache_path = os.path.join(self.get_pkg_cache_path(), '*')
        pkg_cache_path = self.get_pkg_cache_path() + '/'

        exec_local_rsync_to_remote(self.node['node_code'],
                                   pkg_cache_path,
                                   lib_deploy_path,
                                   is_delete=True)

    @step_decorator(">>>> 配置推送至目标虚拟机 >> ")
    def vm_conf_push(self, **kwargs):
        """虚机配置推送"""
        if self.app_test_conf_path:
            log.info(">>>> vm_conf_push >> app_test_conf_path = {}".format(self.app_test_conf_path))

            conf_deploy_path = self.node['config_path']
            if not conf_deploy_path:
                if self.po.package_type and self.po.package_type == 'jar':
                    conf_deploy_path = os.path.join(PTP['def_vm_jar_conf_path'], self.po.app_name)
                else:
                    container_name = self.po['container_name']
                    if not container_name:
                        container_name = self.po['app_name']
                    conf_deploy_path = os.path.join(PTP['def_vm_tomcat_conf_path'], container_name, self.po.app_name)

            cmd = "mkdir -p {}".format(conf_deploy_path)
            exec_remote_cmd(self.node['node_code'], cmd)
            # 带*号时delete无法生效问题 zt@2020-09-21
            # conf_cache_path = os.path.join(self.app_test_conf_path, '*')
            conf_cache_path = self.app_test_conf_path + '/'

            exec_local_rsync_to_remote(self.node['node_code'],
                                       conf_cache_path,
                                       conf_deploy_path,
                                       is_delete=False)
        else:
            log.warning(">>>> app_resource中没有找到关于「{}」的配置，跳过『虚机配置推送』。".format(self.po.app_name))

    @step_decorator(">>>> 目标虚拟机应用重启 >> ")
    def vm_restart(self, **kwargs):
        """虚机应用重启"""
        script_path = self.node['script_path']
        if not script_path:
            if self.po.package_type and self.po.package_type == 'jar':
                script_path = self.node['deploy_path']
                if not script_path:
                    script_path = '{}{}/bin/'.format(PTP['def_vm_jar_lib_path'], self.po.app_name)
            else:
                script_path = self.node['tomcat_path']
                if not script_path:
                    container_name = self.po.container_name
                    if not container_name:
                        container_name = self.po.app_name
                    script_path = '{}{}/bin/'.format(PTP['def_vm_tomcat_lib_path'], container_name)

        script_name = self.node['script_name'] if self.node['script_name'] else PTP['def_script_name']

        cmd = "pkill -9 -f {}".format(self.po.app_name + "/")
        exec_remote_cmd(self.node['node_code'], cmd)

        cmd = "find {} -name {} | xargs sh".format(script_path, script_name)
        exec_remote_cmd(self.node['node_code'], cmd)

    def __init_node_info(self, node):
        if not node:
            raise ValueError("「{}」应用无节点信息".format(self.po.app_name))
        if not node['node_code']:
            raise ValueError("「{}」应用绑定节点无IP信息".format(self.po.app_name))
        self.node = node

    def get_handlers(self):
        handlers = super().get_handlers()
        handlers.extend([
            self.vm_app_push,
            self.vm_conf_push,
            self.vm_restart,
        ])
        return handlers
