#
import os
import sys
import json
import requests

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from dao.get.mysql.test_env import get_env_cache_info
from utils.test_env.test_env_lib import step_desc
from settings import logger


@step_desc('清理缓存')
def clean_cache(env):
    url = 'http://192.168.222.94:9094/restful/cacheService/flushDB'

    headers = {'Content-Type': 'application/json; charset=UTF-8'}
    cache_node = '/' + get_env_cache_info(env)['CACHE_NODE']
    data = {
        "identity": 'default',
        "zkNode": cache_node,
        "connectKey": cache_node + 'default',
    }

    res = requests.post(url, data=json.dumps(data), headers=headers)
    result = res.json()
    if "isSuc" in result and result["isSuc"]:
        logger.info('清理{}'.format(cache_node))
    else:
        raise Exception(result['msg'])


if __name__ == '__main__':
    tms_env = sys.argv[1]
    clean_cache(tms_env)
