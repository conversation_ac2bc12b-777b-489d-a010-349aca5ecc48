#
import os
import sys


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, MIRROR_FACTORY, INTERFACE_URL
from dao.get.mysql.test_env import get_env_info_by_tms, get_product_store
from utils.test_env.test_env_lib import step_desc, TMS_REMOTE_LIST, TMS_TOMCAT_LIST, TMS_APP_LIST, request
from utils.test_env.tms_config_update import TmsConfigUpdate
from utils.test_env.tms_config_rule import TmsConfigRule
from utils.test_env.config_init import TmsConfigInit
from job.jenkins.test_env.tms_init import init_tms_list, adapt_app_name


@step_desc('配置拉取')
def update_config():
    logger.info('更新产线配置')
    if os.path.isdir(BASE_CONFIG_PATH):
        os.system("cd {} && git fetch --all && git reset --hard origin/master && git pull".format(BASE_CONFIG_PATH))
    else:
        os.system("mkdir -p {}".format(os.path.dirname(BASE_CONFIG_PATH)))
        os.chdir(os.path.dirname(BASE_CONFIG_PATH))
        os.system("git clone --depth 1 {}".format("******************************:app-ops/app_resource.git"))


@step_desc('更新svn配置')
def update_svn_config(env):
    tcu = TmsConfigUpdate(env)
    tcu.tms_config_update(CONFIG_DIR)


@step_desc('配置替换')
def former_config_replace(env, back_ip):
    logger.info('配置复制到工作空间')
    src_dir = os.path.join(BASE_CONFIG_PATH, 'ec')

    for app in TMS_REMOTE_LIST:
        app_name = app
        app_info = get_product_store(app_name)
        if 'gitRepo' in app_info and app_info['gitRepo']:
            # 两个tms应用的配置标准化，去掉原来的特殊处理 zt@2020-10-12
            # if app == "order-center-search-remote":
            #     app_src_config = os.path.join(src_dir, app_info["gitRepo"], special_CONFIG_DIR[app])
            # elif app == "high-order-search-remote":
            #     app_src_config = os.path.join(src_dir, app_info["gitRepo"], special_CONFIG_DIR[app])
            # else:
            #     app_src_config = os.path.join(src_dir, app_info["gitRepo"], "pro-w")
            app_src_config = os.path.join(src_dir, app_info["gitRepo"], "pro-w")
        else:
            app_src_config = os.path.join(MOCK_CONFIG_PATH, "remote", app)

        app_target_config = os.path.join(CONFIG_DIR, "remote", app_name)
        os.system("mkdir -p {}".format(app_target_config))
        os.system("cp -r {}/* {}".format(app_src_config, app_target_config))

    for app in TMS_TOMCAT_LIST:
        if app == "cgi":
            app_name = "cgi-ehowbuy"
        elif app == "trade":
            app_name = "ehowbuy-web"
        else:
            app_name = app

        app_info = get_product_store(app_name)
        if 'gitRepo' in app_info and app_info['gitRepo']:
            # cgi应用的配置标准化，去掉原来的特殊处理 zt@2020-10-12
            # if app == "cgi":
            #     app_src_config = os.path.join(src_dir, app_info['gitRepo'], special_CONFIG_DIR[app])
            # else:
            #     app_src_config = os.path.join(src_dir, app_info['gitRepo'], "pro-w")
            app_src_config = os.path.join(src_dir, app_info['gitRepo'], "pro-w")
        else:
            app_src_config = os.path.join(MOCK_CONFIG_PATH, "tomcat", app)
        app_target_config = os.path.join(CONFIG_DIR, "tomcat", app, "WEB-INF/classes")
        os.system("mkdir -p {}".format(app_target_config))
        os.system("cp -r {}/* {}".format(app_src_config, app_target_config))

    front_ip = env + ".k8s.howbuy.com"
    tms_config_rule = TmsConfigRule(env, front_ip, back_ip, CONFIG_DIR, CONTAINER_PATH, TMS_APP_LIST)
    logger.info("开始替换配置文件")
    config_init = TmsConfigInit()
    config_init.config_init(tms_config_rule.file_path_replace_rule)


@step_desc('配置替换')
def config_replace(env):
    url = "{}/zeus-service/apiForDeploy/envinit".format(INTERFACE_URL['zeus'])
    data = {
        "apps": ','.join(remote_list + tomcat_list),
        "env": env,
    }
    res = request(url, '', data)
    result = res.text
    if result == 'success':
        return True
    else:
        raise Exception(result)


@step_desc('推送配置到镜像工厂')
def config_to_img_factory(env):
    target_path = os.path.join(MIRROR_FACTORY["MIRROR_PATH"], env)
    for app in remote_list:
        cmd = "rsync -Lav --exclude='*.git' {}/* tomcat@{}:{}".format(
            os.path.join(CONFIG_WORKSPACE, env, 'remote', app), MIRROR_FACTORY["IP"],
            os.path.join(target_path, app, app, 'conf'))
        logger.info(cmd)
        os.system(cmd)

    for app in tomcat_list:
        cmd = "rsync -Lav --exclude='*.git' {}/* tomcat@{}:{}".format(
            os.path.join(CONFIG_WORKSPACE, env, 'tomcat', app), MIRROR_FACTORY["IP"],
            os.path.join(target_path, app, app))
        logger.info(cmd)
        os.system(cmd)


@step_desc('推送配置到config_map')
def config_to_config_map(env):
    target_path = os.path.join(KUBECTL_CONFIG_PATH, env)
    for app in remote_list:
        cmd = "rsync -Lav --exclude='*.git' {}/* {}:{}".format(
            os.path.join(CONFIG_WORKSPACE, env, 'remote', app), KUBECTL_SERVER,
            os.path.join(target_path, 'remote', app, 'conf'))
        logger.info(cmd)
        os.system(cmd)

    for app in tomcat_list:
        cmd = "rsync -Lav --exclude='*.git' {}/* {}:{}".format(
            os.path.join(CONFIG_WORKSPACE, env, 'tomcat', app), KUBECTL_SERVER,
            os.path.join(target_path, 'tomcat', app))
        logger.info(cmd)
        os.system(cmd)


@step_desc('创建config_map')
def create_config_map(env):
    for app in remote_list:
        conf_dir = '/home/<USER>/config/{}/{}/{}/conf/'.format(env, 'remote', app)
        cmd = "ssh {} 'kubectl delete configmap {} -n {}'".format(KUBECTL_SERVER, app, env)
        logger.info(cmd)
        os.system(cmd)

        cmd = "ssh {} 'kubectl create configmap {} --from-file={} -n {}'".format(KUBECTL_SERVER, app, conf_dir, env)
        logger.info(cmd)
        os.system(cmd)

    tomcat_dir_map = {
        'cgi': {
            'cgi-web-inf-classes': 'WEB-INF/classes',
            'cgi-web-inf-classes-properties': 'WEB-INF/classes/properties',
        },
        'cgi-simu': {
            'simu-web-inf-classes': 'WEB-INF/classes',
            'simu-web-inf-classes-properties': 'WEB-INF/classes/properties',
        },
        'trade': {
            'trade-classes': 'WEB-INF/classes',
            'trade-classes-properties': 'WEB-INF/classes/properties',
        },
        'tomcat-quartz': {
            'tomcat-quartz-web-inf-classes': 'WEB-INF/classes',
            'tomcat-quartz-web-inf-properties': 'WEB-INF/classes/properties',
        },
        'howbuy-trade-wap': {
            'howbuy-trade-wap-account': 'WEB-INF/view/ftl/account/main',
            'howbuy-trade-wap-classes': 'WEB-INF/classes',
            'howbuy-trade-wap-common': 'WEB-INF/classes/properties',
            'howbuy-trade-wap-properties': 'WEB-INF/classes/properties',
            'howbuy-trade-wap-publicjs': 'html/fund/common',
        },
        'howbuy-trade-wap-piggy': {
            'howbuy-trade-wap-piggy-classes': 'WEB-INF/classes',
            'howbuy-trade-wap-piggy-ftl': 'WEB-INF/view/ftl/common',
            'howbuy-trade-wap-piggy-publicjs': 'html/piggy/common',
        },
        'howbuy-interlayer-console': {
            'howbuy-interlayer-console': 'WEB-INF/classes',
        },
        'tms-counter-console': {
            'tms-counter-console-baseconfigjs': 'tmscounter/js',
            'tms-counter-console-classes': 'WEB-INF/classes',
        }
    }
    for app in tomcat_list:
        if app in tomcat_dir_map:
            for key in tomcat_dir_map[app]:
                conf_dir = '/home/<USER>/config/{}/{}/{}/{}'.format(env, 'tomcat', app, tomcat_dir_map[app][key])
                cmd = "ssh {} 'kubectl delete configmap {} -n {}'".format(KUBECTL_SERVER, key, env)
                logger.info(cmd)
                os.system(cmd)

                cmd = "ssh {} 'kubectl create configmap {} --from-file={} -n {}'".format(
                    KUBECTL_SERVER, key, conf_dir, env)
                logger.info(cmd)
                os.system(cmd)


if __name__ == '__main__':
    BASE_CONFIG_PATH = "/data/publish/git/uat/TMS_conf/app_resource"
    CONFIG_WORKSPACE = "/data/env_init/config"
    MOCK_CONFIG_PATH = "/data/env_init/tms_mock_config"
    CONTAINER_PATH = "/data/env_init/docker"
    KUBECTL_SERVER = 'tomcat@**************'
    KUBECTL_CONFIG_PATH = "/home/<USER>/config/"
    special_CONFIG_DIR = {
        "high-order-search-remote": "w-high-order-center-search-10-12-101-16",
        "order-center-search-remote": "w-order-center-search-10-12-101-23",
        "cgi": "w-ehowbuy-cgi-10-12-101-13"
    }

    tms_env = sys.argv[1]
    CONFIG_DIR = os.path.join(CONFIG_WORKSPACE, tms_env)
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']

    # 自定义TMS_LIST zt@2020-07-29
    TMS_NAME_STR = sys.argv[2]
    if TMS_NAME_STR != '':
        TMS_NAME_LIST = TMS_NAME_STR.split(",")
        remote_list, tomcat_list, static_list = init_tms_list(tms_env, TMS_NAME_LIST)
        logger.info(">>>>「tms配置初始化」>> remote_list = {}".format(remote_list))
        logger.info(">>>>「tms配置初始化」>> tomcat_list = {}".format(tomcat_list))
        logger.info(">>>>「tms配置初始化」>> static_list = {}".format(static_list))

    # 伟敏应用名的临时匹配功能 zt@2020-09-16
    # 伟敏应用名的临时匹配功能优化 zt@2020-09-24
    tomcat_list = adapt_app_name(tomcat_list)
    logger.info(">>>> 特殊匹配：tomcat_list = {}".format(tomcat_list))

    update_config()
    # former_config_replace(tms_env, tp_ip)
    config_replace(tms_env)
    update_svn_config(tms_env)
    config_to_img_factory(tms_env)
    config_to_config_map(tms_env)
    create_config_map(tms_env)
