#
import os
import sys


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from utils.test_env.test_env_lib import step_desc, TMS_APP_LIST
from utils.release.docker.rebuild_docker import ImageBuild

from job.jenkins.test_env.tms_init import init_tms_list, adapt_app_name
from settings import logger


@step_desc('镜像制作')
def make_img(env):
    for app_name in remote_list + tomcat_list:
        work = ImageBuild()
        work.make_image(env, app_name)


if __name__ == '__main__':
    tms_env = sys.argv[1]

    # 自定义TMS_LIST zt@2020-07-29
    TMS_NAME_STR = sys.argv[2]
    if TMS_NAME_STR != '':
        TMS_NAME_LIST = TMS_NAME_STR.split(",")
        remote_list, tomcat_list, static_list = init_tms_list(tms_env, TMS_NAME_LIST)
        logger.info(">>>>「tms镜像构建」>> remote_list = {}".format(remote_list))
        logger.info(">>>>「tms镜像构建」>> tomcat_list = {}".format(tomcat_list))
        logger.info(">>>>「tms镜像构建」>> static_list = {}".format(static_list))

    # 伟敏应用名的临时匹配功能 zt@2020-09-16
    # 伟敏应用名的临时匹配功能优化 zt@2020-09-24
    tomcat_list = adapt_app_name(tomcat_list)
    logger.info(">>>> 特殊匹配：tomcat_list = {}".format(tomcat_list))

    make_img(tms_env)
