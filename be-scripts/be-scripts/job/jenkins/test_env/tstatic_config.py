#
import os
import re
import logging


def file_content_replace(pattern, replace_obj, file_path):
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            content = f.read()
            new_content = re.sub(pattern, replace_obj, content)
        with open(file_path, 'w') as f:
            f.write(new_content)
    else:
        logging.debug(file_path + '不存在')


def tstatic_config_replace(env):
    env_domain = env + '.k8s.howbuy.com'
    tstatic_path = '/data/env_init/docker/tstatic'

    hbenv_paths = os.popen('find {} -name hbenv.js'.format(tstatic_path)).read().split()
    dist_paths = os.popen('find {} -name dist.js'.format(tstatic_path)).read().split()

    hbenv_replace_rules = {
        '_hbEnvFlag = "1"': '_hbEnvFlag = "3"',
        'apppre.ehowbuy.com': 'tstatic.{}'.format(env_domain),
        'data.howbuy.qa': 'tstatic.{}'.format(env_domain),
        'URL_HEADER_SIMU =.*': 'URL_HEADER_SIMU = "http://simu.{}/cgi/";'.format(env_domain),
        'URL_HEADER_GONGMU_CGI =.*': 'URL_HEADER_GONGMU_CGI = "http://cgi.{}/cgi/";'.format(env_domain),
        'URL_HEADER_GONGMU_PIGGY_DEPOSIT =.*': 'URL_HEADER_GONGMU_PIGGY_DEPOSIT = "http://tstatic.{}/";'.format(
            env_domain),
        'URL_HEADER_GONGMU_RISK =.*': 'URL_HEADER_GONGMU_RISK = "http://tstatic.{}/newwap/index.html#/riskevaluation?examType=1"'.format(
            env_domain),
        'URL_HEADER_FILE =.*': 'URL_HEADER_FILE = "http://tstatic.{}/";'.format(env_domain),
        'URL_HEADER =.*': 'URL_HEADER = "http://cgi.{}/cgi/";'.format(env_domain),
        'URL_HOST =.*': 'URL_HOST = "http://web.{}/trade/";'.format(env_domain),
        'URL_LOGININDEX =.*': 'URL_LOGININDEX = "http://web.{}/trade/";'.format(env_domain),
        'URL_SIMU_PLATFORM =.*': 'URL_SIMU_PLATFORM = "http://tstatic.{}/pcsmfund/index.html";'.format(env_domain),
        'URL_HEADER_CXG =.*': 'URL_HEADER_CXG = "http://cgi.{}/cgi/";'.format(env_domain),
        'URL_HEADER_DATA =.*': 'URL_HEADER_DATA = "http://cgi.{}/cgi/";'.format(env_domain),
        'URL_FUND_HEADER =.*': 'URL_FUND_HEADER = "http://simu.{}";'.format(env_domain),
        'URL_WAP =.*': 'URL_WAP = "http://wap.{}/wap/";'.format(env_domain),
        'JOIN_HEADER =.*': 'JOIN_HEADER = "http://tstatic.{}/wappig/";'.format(env_domain),
        'JOIN_HEADER_HAODOU =.*': 'JOIN_HEADER_HAODOU = "https://trade.ehowbuy.test/tstatic/";',
        'URL_GONGMU_INDEX =.*': 'URL_GONGMU_INDEX = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/index.html";'.format(
            env_domain),
        'URL_GONGMU_PIGGY_PAGE =.*': 'URL_GONGMU_PIGGY_PAGE = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/piggyDeposit.html";'.format(
            env_domain),
        'URL_ASSET_CERTIFICATE =.*': 'URL_ASSET_CERTIFICATE = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/index.html";'.format(
            env_domain),
        'URL_HEADER_GONGMU_REGIST_RISK =.*': 'URL_HEADER_GONGMU_REGIST_RISK = "http://tstatic.{}/newwap/index.html#/riskevaluation?examType=1'.format(
            env_domain),
        'HB_URL =.*': 'HB_URL = "http://cgi.{}/cgi/"'.format(env_domain),
        'URL_PIG =.*': 'URL_PIG = "http://tstatic.{}/newpig/"'.format(env_domain),
    }

    hbenv_special_rules = {
        'pcsmfund': {
            'URL_HEADER_GONGMU =.*': 'URL_HEADER_GONGMU = "http://web.{}/trade/";'.format(env_domain)
        },
        'smasset': {
            'URL_HEADER_GONGMU =.*': 'URL_HEADER_GONGMU = "http://wap.{}/wap/";'.format(env_domain)
        }
    }

    dist_replace_rules = {
        '_hbEnvFlag = "1"': '_hbEnvFlag = "3"',
        'simu.ehowbuy.com': 'URL_HEADER_FILE = "https://tstatic.{}/";'.format(env_domain),
        'URL_HEADER =.*': 'URL_HEADER = "http://cgi.{}/cgi/";'.format(env_domain),
        'URL_HOST =.*': 'URL_HOST = "http://web.{}/trade/";'.format(env_domain),
        'URL_LOGININDEX =.*': 'URL_LOGININDEX = "http://web.{}/trade/";'.format(env_domain),
        'URL_SIMU_PLATFORM =.*': 'URL_SIMU_PLATFORM = "http://tstatic.{}/pcsmfund/index.html";'.format(env_domain),
        'URL_HEADER_SIMU =.*': 'URL_HEADER_SIMU = "http://simu.{}/cgi/";'.format(env_domain),
        'URL_HEADER_GONGMU_CGI =.*': 'URL_HEADER_GONGMU_CGI = "http://cgi.{}/cgi/";'.format(env_domain),
        'URL_HEADER_GONGMU =.*': 'URL_HEADER_GONGMU = "http://web.{}/trade/";'.format(env_domain),
        'URL_HEADER_GONGMU_RISK =.*': 'URL_HEADER_GONGMU_RISK = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/riskevaluation.html?examType=1";'.format(
            env_domain),
        'URL_HEADER_GONGMU_REGIST_RISK =.*': 'URL_HEADER_GONGMU_REGIST_RISK = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/registerRiskevaluation.html?examType=1";'.format(
            env_domain),
        'URL_HEADER_GONGMU_PIGGY_DEPOSIT =.*': 'URL_HEADER_GONGMU_PIGGY_DEPOSIT = "http://tstatic.{}/";'.format(
            env_domain),
        'URL_GONGMU_INDEX =.*': 'URL_GONGMU_INDEX = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/index.html";'.format(
            env_domain),
        'URL_GONGMU_PIGGY_PAGE =.*': 'URL_GONGMU_PIGGY_PAGE = "http://tstatic.{}/newpc/pcfund/module/pcfund/view/piggyDeposit.html";'.format(
            env_domain),
        'URL_HEADER_FILE =.*': 'URL_HEADER_FILE = "http://tstatic.{}/";'.format(env_domain),
        'URL_FUND_HEADER =.*': 'URL_FUND_HEADER = "http://simu.{}/";'.format(env_domain),
        'URL_ASSET_CERTIFICATE =.*': 'URL_ASSET_CERTIFICATE = "http://tstatic.{}/";'.format(env_domain),
        'URL_BUYFUND_CONTRACT_PRE =.*': 'URL_BUYFUND_CONTRACT_PRE = "http://tstatic.{}/doc/funddoc/";'.format(
            env_domain),
        'URL_HEADER_HELF_CENTER =.*': 'URL_HEADER_HELF_CENTER = "http://tstatic.{}/";'.format(env_domain),
    }

    for fl in hbenv_paths:
        for rule in hbenv_replace_rules:
            file_content_replace(rule, hbenv_replace_rules[rule], fl)
            if 'pcsmfund' in fl:
                for s_rule in hbenv_special_rules['pcsmfund']:
                    file_content_replace(s_rule, hbenv_special_rules['pcsmfund'][s_rule], fl)
            if 'smasset' in fl:
                for s_rule in hbenv_special_rules['smasset']:
                    file_content_replace(s_rule, hbenv_special_rules['smasset'][s_rule], fl)

    for fl in dist_paths:
        for rule in dist_replace_rules:
            file_content_replace(rule, dist_replace_rules[rule], fl)
