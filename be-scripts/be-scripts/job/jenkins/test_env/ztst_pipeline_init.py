# 流水线初始化，用于测试环境发布
# 第1版 zt@2020-07-28
import os
import subprocess
import sys
from datetime import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql.branch_info import get_iteration_id
from dao.insert.mysql.pipeline_log import insert_pipeline_log_main
from job.jenkins.env_test_publish import publish_env

from dao.get.mysql.env_info import get_br_name_list, get_whether_app_suite_bind, get_node_info_for_test

from settings import logger, PIPELINE_TEST_PUBLISH
from utils.test_env.test_env_lib import step_desc


@step_desc("流水线初始化")
def init_pipeline():
    logger.info(">>>>「流水线初始化」>> init_pipeline() >> ENV_NAME = {}".format(ENV_NAME))
    logger.info(">>>>「流水线初始化」>> init_pipeline() >> APP_NAME_STR = {}".format(APP_NAME_STR))
    app_name_list = APP_NAME_STR.split(",")
    result = get_br_name_list(app_name_list)
    logger.info(">>>>「流水线初始化」>> init_pipeline() >> result = {}".format(result))
    if result and len(result) > 0:
        for obj in result:
            br_name = obj['br_name']
            app_name = obj['appName']

            iteration_id = get_iteration_id(app_name, br_name)
            sid = insert_pipeline_log_main(exec_jenkins="platform", exec_parameter="",
                                           start_at=datetime.now(), iteration_id=iteration_id,
                                           app_name=app_name, status="running", suite_name=ENV_NAME)
            if get_whether_app_suite_bind(app_name, ENV_NAME):
                node_list = get_node_info_for_test(app_name, ENV_NAME)
                if node_list and len(node_list) > 0:
                    node_obj = node_list[0]
                    node_code = node_obj['node_code']
                    worker = publish_env(app_name, br_name, node_code, sid)

            else:
                logger.error("{} 未绑定".format(app_name))


if __name__ == '__main__':
    ENV_NAME = sys.argv[1]
    APP_NAME_STR = sys.argv[2]
    init_pipeline()
