# tms初始化中公共的方法
# 第1版 zt@2020-07-29
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger
from utils.test_env.test_env_lib import step_desc
from dao.get.mysql.test_env import get_tms_info_list


@step_desc("tms列表拆分")
def init_tms_list(env_name, tms_list):
    """
    根据环境套名称和选择的tms应用列表，获取tms信息并切分为remote、tomcat、static三个列表
    :param env_name:
    :param tms_list:
    :return:
    """
    remote_name_list = []
    tomcat_name_list = []
    static_name_list = []

    if env_name and len(tms_list) > 0:
        tms_info_list = get_tms_info_list(env_name, tms_list)

    if tms_info_list and len(tms_info_list) > 0:
        for obj in tms_info_list:
            module_name = obj['module_name']
            package_type = obj['package_type']
            if package_type == 'jar':
                remote_name_list.append(module_name)
            else:
                if module_name == 'tstatic':
                    static_name_list = ['tstatic']
                    logger.info(">>>>包含tstatic")
                else:
                    tomcat_name_list.append(module_name)

    return remote_name_list, tomcat_name_list, static_name_list


def adapt_app_name(tms_list):
    """tms应用改名适配 zt@2020-09-24"""
    if tms_list:
        special_remote_dict = {
            "cgi-ehowbuy": "cgi",
            "ehowbuy-web": "trade",
        }
        tms_list = [special_remote_dict[app_name] if app_name in special_remote_dict else app_name
                    for app_name in tms_list]
    return tms_list


def adapt_app_name_str(tms_list_str):
    """tms应用改名适配（字符串） zt@2020-09-24"""
    if tms_list_str:
        special_remote_dict = {
            "cgi-ehowbuy": "cgi",
            "ehowbuy-web": "trade",
        }
        tms_list_str = ','.join([special_remote_dict[app_name.strip()]
                                 if app_name.strip() in special_remote_dict else app_name.strip()
                                 for app_name in tms_list_str.split(',')])
    return tms_list_str


if __name__ == '__main__':
    test_list = ['acc-center', "cgi-ehowbuy", "ehowbuy-web"]
    logger.info(">>>> before：test_list = {}".format(test_list))
    new_test_list = adapt_app_name(test_list)
    logger.info(">>>> after：test_list = {}".format(test_list))
    logger.info(">>>> after：new_test_list = {}".format(new_test_list))

    test_list_str = 'acc-center, cgi-ehowbuy, ehowbuy-web'
    logger.info(">>>> before：test_list_str = {}".format(test_list_str))
    new_test_list_str = adapt_app_name_str(test_list_str)
    logger.info(">>>> after：test_list_str = {}".format(test_list_str))
    logger.info(">>>> after：new_test_list_str = {}".format(new_test_list_str))

