# 流水线的测试环境发布（docker实现）
# 第1版 zt@2020-08-28
import os
import sys
from time import sleep

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from settings import PIPELINE_TEST_PUBLISH as PTP
from job.jenkins.test_env.pipeline_test_publish_for_base import PipelineTestPublishHandler
from job.jenkins.test_env.pipeline_test_publish_common import step_decorator
from job.jenkins.test_env.pipeline_test_publish_common import exec_local_cmd
from job.jenkins.test_env.pipeline_test_publish_common import exec_remote_cmd
from job.jenkins.test_env.pipeline_test_publish_common import exec_local_popen
from job.jenkins.test_env.pipeline_test_publish_common import exec_local_rsync_to_remote


class PipelineTestPublishDockerHandler(PipelineTestPublishHandler):
    """
    流水线测试环境环境发布，容器 zt@2020-08-25(七夕)
    """
    def __init__(self, ptp_id, ptp_type, po, node):
        super().__init__(ptp_id, ptp_type, po)
        self.__init_node_info(node)

    @step_decorator(">>>> 应用推送至镜像工厂 >> ")
    def docker_app_push(self, **kwargs):
        """容器应用推送"""
        log.info(">>>> docker_app_push >> node = {}".format(self.node))
        lib_deploy_path = self.node['deploy_path']
        if not lib_deploy_path:
            container_name = self.po.container_name if self.po.container_name else self.po.app_name
            if self.po.package_type and self.po.package_type == 'jar':
                lib_deploy_path = '{}{}/{}/{}/lib'.format(PTP['mirror_factory_root_path'],
                                                          self.node['node_code'],
                                                          container_name,
                                                          container_name)
            else:
                lib_deploy_path = '{}{}/{}/{}'.format(PTP['mirror_factory_root_path'],
                                                      self.node['node_code'],
                                                      container_name,
                                                      container_name,)
        mkdir_cmd = "mkdir -p {}".format(lib_deploy_path)
        exec_remote_cmd(PTP['mirror_factory_ip'], mkdir_cmd)
        rm_cmd = "rm -rf {}/*".format(lib_deploy_path)
        exec_remote_cmd(PTP['mirror_factory_ip'], rm_cmd)
        # 带*号时delete无法生效问题 zt@2020-09-21
        # pkg_cache_path = os.path.join(self.get_pkg_cache_path(), '*')
        pkg_cache_path = self.get_pkg_cache_path() + '/'

        exec_local_rsync_to_remote(PTP['mirror_factory_ip'],
                                   pkg_cache_path,
                                   lib_deploy_path,
                                   is_delete=True)

    @step_decorator(">>>> 配置推送至ConfigMap >> ")
    def docker_conf_push(self, **kwargs):
        """容器配置推送"""
        if self.app_test_conf_path:
            log.info(">>>> docker_conf_push >> app_test_conf_path = {}".format(self.app_test_conf_path))

            conf_deploy_path = self.node['config_path']
            if not conf_deploy_path:
                container_name = self.po.container_name if self.po.container_name else self.po.app_name
                if self.po.package_type and self.po.package_type == 'jar':
                    conf_deploy_path = os.path.join(PTP['config_map_conf_path'],
                                                    self.node['node_code'],
                                                    'remote',
                                                    container_name)
                else:
                    tomcat_container_path = 'tomcat/{}/WEB-INF/classes/'.format(container_name)
                    conf_deploy_path = os.path.join(PTP['config_map_conf_path'],
                                                    self.node['node_code'],
                                                    tomcat_container_path)
            # 带*号时delete无法生效问题 zt@2020-09-21
            # conf_cache_path = os.path.join(self.app_test_conf_path, '*')
            conf_cache_path = self.app_test_conf_path + '/'

            exec_local_rsync_to_remote(PTP['config_map_ip'],
                                       conf_cache_path,
                                       conf_deploy_path,
                                       is_delete=False)
        else:
            log.warning(">>>> app_resource中没有找到关于「{}」的配置，跳过『配置推送至ConfigMap』。".format(self.po.app_name))

    @step_decorator(">>>> KubeCtl:构建image >> ")
    def docker_image_build(self, **kwargs):
        """构建image"""
        # 优化使用帅维的脚本来统一处理 zt@2020-09-11
        container_name = self.po.container_name if self.po.container_name else self.po.app_name
        img_build_cmd = 'python3.x {} {} {}'.format(PTP['docker_img_build_cmd'],
                                                  self.node['node_code'],
                                                  container_name)
        exec_local_popen(img_build_cmd)

    @step_decorator(">>>> KubeCtl:清理ConfigMap >> ")
    def docker_image_push(self, **kwargs):
        """清理ConfigMap"""
        # 优化使用帅维的脚本来统一处理 zt@2020-09-11
        container_name = self.po.container_name if self.po.container_name else self.po.app_name
        cm_del_cmd = 'python3.x {} {} {} {}'.format(PTP['docker_cm_build_cmd'],
                                                  self.node['node_code'],
                                                  container_name,
                                                  'delete')
        # completed_process_obj = exec_local_cmd(cm_del_cmd)
        stdout = exec_local_popen(cm_del_cmd)
        # log.info("清理ConfigMap成功：{}".format(bytes.decode(stdout)))

    @step_decorator(">>>> KubeCtl:创建ConfigMap >> ")
    def docker_image_clear(self, **kwargs):
        """创建ConfigMap"""
        # 优化使用帅维的脚本来统一处理 zt@2020-09-11
        container_name = self.po.container_name if self.po.container_name else self.po.app_name
        cm_create_cmd = 'python3.x {} {} {} {}'.format(PTP['docker_cm_build_cmd'],
                                                     self.node['node_code'],
                                                     container_name,
                                                     'create')
        # completed_process_obj = exec_local_cmd(cm_create_cmd)
        stdout = exec_local_popen(cm_create_cmd)
        # log.info("创建ConfigMap成功：{}".format(bytes.decode(stdout)))

    @step_decorator(">>>> 镜像重启(1/2):pods构建 >> ")
    def docker_pods_build(self, **kwargs):
        """pods构建"""
        # 优化使用帅维的脚本来统一处理 zt@2020-09-11
        container_name = self.po.container_name if self.po.container_name else self.po.app_name
        pods_build_cmd = 'python3.x {} {} {}'.format(PTP['docker_pods_build_cmd'],
                                                   self.node['node_code'],
                                                   container_name)
        stdout = exec_local_popen(pods_build_cmd)
        # log.info("pods构建成功：{}".format(bytes.decode(stdout)))
        sleep(10)

    @step_decorator(">>>> 镜像重启(2/2):pods重启 >> ")
    def docker_pods_restart(self, **kwargs):
        """pods重启"""
        # 优化使用帅维的脚本来统一处理 zt@2020-09-11
        container_name = self.po.container_name if self.po.container_name else self.po.app_name
        pods_restart_cmd = 'python3.x {} {} {}'.format(PTP['docker_pods_restart_cmd'],
                                                     self.node['node_code'],
                                                     container_name)
        stdout = exec_local_popen(pods_restart_cmd)
        # log.info("pods重启成功：{}".format(bytes.decode(stdout)))

    def __init_node_info(self, node):
        if not node:
            raise ValueError("「{}」应用无docker节点信息".format(self.po.app_name))
        if not node['node_code']:
            raise ValueError("「{}」应用绑定docker节点无code信息".format(self.po.app_name))
        self.node = node

    def get_handlers(self):
        handlers = super().get_handlers()
        handlers.extend([
            self.docker_app_push,
            self.docker_conf_push,
            self.docker_image_build,
            self.docker_image_push,
            self.docker_image_clear,
            self.docker_pods_build,
            self.docker_pods_restart,
        ])
        return handlers
