# 流水线的测试环境发布（工具实现）
# 第1版 zt@2020-08-31
import os
import re
import subprocess
import sys
import functools
import paramiko

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)


from settings import logger as log
from settings import PIPELINE_TEST_PUBLISH as PTP
from dao.get.mysql.env_info import get_tomcat_password_by_node_ip


class CmdException(Exception):
    pass


def get_tomcat_password(node_ip, cmd=None, password=None):
    if not password:
        password = get_tomcat_password_by_node_ip(node_ip)
        if password and password.strip() != '':
            msg = "使用「配置密码」连接服务器：{}，执行命令：{}".format(node_ip, cmd)
        else:
            password = PTP['def_tomcat_password']
            msg = "使用「默认密码」连接服务器：{}，执行命令：{}".format(node_ip, cmd)
    else:
        msg = "使用「传入密码」连接服务器：{}，执行命令：{}".format(node_ip, cmd)

    return password, msg


def exec_local_cmd(cmd):
    """本地执行命令 zt@2020-09-08"""
    log.info("本地执行命令：{}".format(re.sub(r'^sshpass -p \S*', "sshpass -p ******", cmd)))
    try:
        completed_process_obj = subprocess.run(cmd, shell=True,
                                               timeout=int(PTP['exec_cmd_timeout']),
                                               capture_output=True, check=True)
        log.info("本地执行结果：{}".format(bytes.decode(completed_process_obj.stdout)))
    except subprocess.CalledProcessError as err:
        msg = "本地执行命令出错：{}".format(bytes.decode(err.stderr))
        log.error(msg)
        raise CmdException(msg)
    return completed_process_obj


def exec_local_popen(cmd):
    """本地Popen执行命令 zt@2020-09-11"""
    log.info("本地Popen执行命令：{}".format(re.sub(r'^sshpass -p \S*', "sshpass -p ******", cmd)))
    try:
        p = subprocess.Popen(cmd, shell=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE,)

        stdout, stderr = p.communicate(timeout=int(PTP['exec_cmd_timeout']))
        code = p.returncode

        if code == 0:
            log.info("本地Popen执行结果：{}".format(bytes.decode(stdout)))
        else:
            msg = "本地Popen执行命令出错：{}".format(bytes.decode(stderr))
            log.error(msg)
            raise CmdException(msg)
    except subprocess.CalledProcessError as err:
        msg = "本地Popen执行命令CalledProcessError：{}".format(bytes.decode(err.stderr))
        log.error(msg)
        raise CmdException(msg)
    return stdout


def exec_local_cmd_by_sshpass(node_ip, cmd, password=None):
    """利用sshpass执行命令 zt@2020-09-09"""
    password, msg = get_tomcat_password(node_ip, cmd, password)
    log.info(msg)
    sshpass_cmd = "sshpass -p '{}' {}".format(password, cmd)
    return exec_local_cmd(sshpass_cmd)


def exec_local_scp_from_remote(source_ip, source_path, target_path, password=None):
    """利用sshpass执行scp命令 zt@2020-09-09"""
    scp_cmd = "scp -r {}@{}:{} {}".format(PTP['def_ssh_user'], source_ip, source_path, target_path)
    exec_local_cmd_by_sshpass(source_ip, scp_cmd, password)


def exec_local_scp_to_remote(target_ip, source_path, target_path, password=None):
    """利用sshpass执行scp命令 zt@2020-09-09"""
    scp_cmd = "scp -r {} {}@{}:{}".format(source_path, PTP['def_ssh_user'], target_ip, target_path)
    exec_local_cmd_by_sshpass(target_ip, scp_cmd, password)


def exec_local_rsync_to_remote(target_ip, source_path, target_path, is_delete=None, password=None):
    """利用sshpass执行rsync命令 zt@2020-09-09"""
    rsync_cmd = "rsync{} " \
                "-LPa " \
                "--timeout={} " \
                "--exclude='*.git' " \
                "{} {}@{}:{}".format(' --delete' if is_delete else '',
                                     int(PTP['exec_cmd_timeout']),
                                     source_path,
                                     PTP['def_ssh_user'],
                                     target_ip,
                                     target_path)

    try:
        completed_process_obj1 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第1次rsync成功：{}".format(bytes.decode(completed_process_obj1.stdout)))
    except Exception as e1:
        log.error(">>>> 第1次rsync失败 >> msg：{}".format(e1))

    try:
        completed_process_obj2 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第2次rsync成功：{}".format(bytes.decode(completed_process_obj2.stdout)))
    except Exception as e2:
        log.error(">>>> 第2次rsync失败 >> msg：{}".format(e2))

    try:
        completed_process_obj3 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第3次rsync成功：{}".format(bytes.decode(completed_process_obj3.stdout)))
    except Exception as e3:
        log.error(">>>> 第3次rsync失败 >> msg：{}".format(e3))
        raise CmdException(">>>> 3次rsync全部失败: {}".format(e3))


def exec_remote_cmd(node_ip, cmd, password=None):
    """利用paramiko远程执行命令 zt@2020-09-09"""
    password, msg = get_tomcat_password(node_ip, cmd, password)
    log.info("『远程』{}".format(msg))

    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        client.connect(node_ip, username=PTP['def_ssh_user'], password=password, timeout=int(PTP['exec_cmd_timeout']))

        stdin, stdout, stderr = client.exec_command(cmd)
        stderr_str = stderr.read()
        if stderr_str:
            msg = "连接服务器：{}，paramiko远程执行命令出错：{}".format(node_ip, stderr_str)
            raise ValueError(msg)
    except paramiko.SSHException as e:
        msg = "连接服务器：{}，paramiko远程执行命令发生SSHException：{}".format(node_ip, e)
        raise ValueError(msg)
    finally:
        client.close()


def exec_remote_cmd_by_sshpass(agent_ip, target_ip, cmd, agent_password=None, target_password=None):
    """远程执行sshpass命令 zt@2020-09-09"""
    target_password, target_msg = get_tomcat_password(target_ip, cmd, target_password)
    log.info("远程目标机，{}".format(target_msg))
    sshpass_cmd = "sshpass -p '{}' ssh -o StrictHostKeyChecking=no {}".format(target_password, cmd)
    exec_remote_cmd(agent_ip, sshpass_cmd, password=agent_password)


def exec_remote_scp(agent_ip, target_ip, cmd, agent_password=None, target_password=None):
    """远程执行scp命令 zt@2020-09-09"""
    exec_remote_cmd_by_sshpass(agent_ip, target_ip, cmd, agent_password, target_password)


def exec_remote_rsync(agent_ip, target_ip, cmd, agent_password=None, target_password=None):
    """远程执行rsync命令 zt@2020-09-09"""
    exec_remote_cmd_by_sshpass(agent_ip, target_ip, cmd, agent_password, target_password)


def step_decorator(name):
    def decorator(func):
        @functools.wraps(func)
        def des(*args, **kwargs):
            try:
                func_name = func.__name__
                func_doc = func.__doc__
                kwargs['func_name'] = func_name
                kwargs['func_doc'] = func_doc
                log.info("====================================")
                log.info("Step({}): {}start".format(func_name, name))
                func(*args, **kwargs)
                log.info("Step({}): {}end".format(func_name, name))
                log.info("====================================")
            except Exception as ex:
                log.error("{}fail: {}".format(name, str(ex)))
                raise ValueError("{}fail: {}".format(name, str(ex)))

        return des

    return decorator

