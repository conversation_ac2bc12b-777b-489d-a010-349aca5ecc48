#
import os
import sys
import datetime

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger
from dao.get.mysql.test_env import get_env_info_by_tms
from utils.test_env.test_env_lib import shell_cmd, step_desc, request, gen_signature, md5_factory


@step_desc('调用ccms导出接口')
def export_ccms_api():
    export_srv_url = 'http://***********:8080'
    operate_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    export_security_code = gen_signature(md5_factory(operate_time, '123456', 'online', ''))
    export_data = {
        "envFlag": 'online',
        "groupNames": '',  # 不传groupNames为所有组, 待万成开发, 十月后上线
        "operator_ip": '',
        "operator_name": '',
        "operator_time": operate_time,
        "security_code": export_security_code
    }
    res = request(export_srv_url, '/ccms/apiFordevops/export.htm', export_data)
    res_json = res.json()
    if res_json['code'] == 200:
        remote_file = res_json['file_path']
    else:
        raise Exception('请求CCMS导出接口失败')

    return remote_file


@step_desc('复制产线ccms文件到本地')
def receive_ccms_file(remote_file):
    local_ccms_download_path = WORKSPACE + 'ccms_online.xls'
    cmd = "rsync -av --password-file=/home/<USER>/rsync.pass rsync@***********::ccms-export/{} {}".format(
        remote_file.split('/')[-1], local_ccms_download_path)
    logger.info('Exec: {}'.format(cmd))
    code, out, err = shell_cmd(cmd)
    if code != 0:
        raise Exception(err)
    else:
        logger.info(out)


if __name__ == '__main__':
    tms_env = sys.argv[1]
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']
    WORKSPACE = '/data/env_init/ccms/'
    remote_ccms_file = export_ccms_api()
    receive_ccms_file(remote_ccms_file)
