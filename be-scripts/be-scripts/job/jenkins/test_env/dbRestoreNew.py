#!/usr/bin python3
# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File    : dbRestoreNew.py
# @Des     ：环境改造之后的数据库还原脚本
# <AUTHOR> yuanxiang.li
# @E-mail  : <EMAIL>
# @Time    : 九月 18, 2021
# --


import paramiko, logging, pymysql, threading, time, sys, os

# 加入日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s [line:%(lineno)d]  %(message)s')


class SSHExec():
    '''
    说明：
    1、若oldEnvName=None，则处理dbBakFileList,否则不处理dbBakFileList；
    2、若oldEnvName=tp_basic,则还原文件为固定的TP_BASIC的备份文件；
    '''

    def __init__(self, oldEnvName, newEnvName, dbBakFileList, jobId):
        self.oldEnvName = oldEnvName  # 环境前缀 如：tms10
        self.newEnvName = newEnvName  # 环境前缀 如：tms15
        self.dbBakFileList = dbBakFileList  # 要还原的文件
        # self.isnotLock = isnotLock  # 是否锁定数据库
        # self.isnotSeq = isnotSeq  # 是否删除sequence
        # self.isnotOGG = isnotOGG  # 是否执行OGG操作
        # self.isnotexistFile = isnotexistFile  # 是否执行判断文件是否存在的动作
        self.jobId = jobId  # 备份还原ID

    def mysqlConnect(self, sql):
        '''
        mysql操作
        :return:
        '''
        # 数据库连接
        db = pymysql.connect(host='***************', user='adminuser', password='adminuser', db='testcenter',
                             charset='utf8')
        # 使用 cursor() 方法创建游标对象
        cursor = db.cursor()
        try:
            cursor.execute(sql)
            # 获取全部的返回结果行
            data = cursor.fetchall()
            db.commit()
            return data
        except:
            db.rollback()
        # 关闭DB连接
        cursor.close()
        db.close()

    def sshConnect(self, host, username, password, cmd):
        '''
        建立ssh连接
        :return:
        '''
        try:
            # 建立一个sshclient对象
            ssh = paramiko.SSHClient()
            # 允许将信任的主机自动加入到host_allow 列表，此方法必须放在connect方法的前面
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # 调用connect方法连接服务器
            ssh.connect(hostname=host, port=22, username=username, password=password)
            stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True, timeout=300)
            res = stdout.readlines()
            # 获取错误信息
            error = stderr.read()
            ssh.close()
            # 如果有错误信息，返回error
            # 否则返回res
            if error.strip():
                return error
            else:
                return res
        except Exception as e:
            logging.error("失败信息" + str(e))
            return False

    @staticmethod
    def printFileLog(filelog):
        '''
        循环打印输出文件执行日志
        :return:
        '''
        # logging.info(filelog)
        if not filelog:
            logging.info('没有输出的日志！！！')
        else:
            for s in filelog:
                s = s.replace('\r', '').replace('\n', '')
                if s == '':
                    continue
                elif s.count('unable to open dump file'):
                    logging.info(s)
                    return exit(1)
                else:
                    logging.info(s)

    def execScript1(self, scriptType, **kwargs):
        '''
        执行sql脚本
        :type 1为同义词；2为sequence处理；3为账户解锁操作;4为账户锁定操作 文件生成需要区分文件名;5数据库还原
        :return:
        '''
        Status = self.dbUserStatus(kwargs['newDbFullNameEn'])
        if scriptType == '1':
            logging.info('开始处理数据库：{}的同义词'.format(kwargs['newDbFullNameEn']))
        if scriptType == '2':
            logging.info('开始处理数据库：{}的sequence'.format(kwargs['newDbFullNameEn']))
            if str(Status) != '1':
                return logging.info('数据库：{}用户不存在无需处理sequence'.format(kwargs['newDbFullNameEn']))
        if scriptType == '3':
            logging.info('开始解锁数据库：{}'.format(kwargs['newDbFullNameEn']))

        if scriptType == '6':
            if kwargs['dbType'].lower() == 'mysql':
                return logging.info('数据库：{}用户无需设置密码'.format(kwargs['newDbFullNameEn']))
            logging.info('开始设置数据库：{}的密码'.format(kwargs['newDbFullNameEn']))
        if scriptType == '7':
            logging.info('开始删除数据库：{}'.format(kwargs['newDbFullNameEn']))
            if str(Status) != '1':
                return logging.info('数据库：{}用户不存在无需删除数据库'.format(kwargs['newDbFullNameEn']))
        if scriptType == '8':
            if str(Status) != '1':
                return logging.info('数据库：{}用户不存在无需断开连接'.format(kwargs['newDbFullNameEn']))
            if kwargs['dbType'].lower() == 'mysql':
                return logging.info('数据库：{}无需断开连接'.format(kwargs['newDbFullNameEn']))
            logging.info('开始断开数据库：{}连接'.format(kwargs['newDbFullNameEn']))

        if scriptType != '5':
            if scriptType == '3' or scriptType == '4' or scriptType == '6' or scriptType == '7' or scriptType == '8':
                kwargs['dbNameEn'] = 'ALL'
            if kwargs['dbType'].lower() == 'mysql' and scriptType == '7':
                logging.info('删除数据库{}'.format(kwargs['newDbFullNameEn']))
                cmd5 = 'source ' + kwargs['bashProFile'] + ';mysql --socket=' + kwargs['sockPath'] + ' -P3306 -u' + \
                       kwargs['dbUser'] + ' -p' + kwargs['dbUserPwd'] + ' -N -se "DROP DATABASE IF EXISTS  ' + kwargs[
                           'newDbFullNameEn'] + '";'

                res5 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd5)
                # logging.info(res5)
                self.printFileLog(res5)
                return

            sqlres = self.getDbScriptFile(scriptType, kwargs['dbNameEn'])
            sqljobfile = sqlres[0][0]
            # logging.info(sqljobfile)
            # 创建sql文件
            logging.info('生成sql文件')
            cmd1 = 'cd ' + kwargs['filepath'] + ';echo "' + sqljobfile + '">' + kwargs['newDbFullNameEn'] + '.sql'
            # logging.info('{}'.format(cmd1))
            res1 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd1)
            self.printFileLog(res1)

            # 执行sql文件
            cmd2 = ''
            if scriptType == '1' or scriptType == '2':
                # 非系统用户执行,同义词需要实际用户执行
                cmd2 = "source " + kwargs['bashProFile'] + ";sqlplus " + kwargs['newDbFullNameEn'] + "/" + kwargs[
                    'dbUserPwd'] + " @" + kwargs['filepath'] + kwargs['newDbFullNameEn'] + ".sql " + kwargs['dbEnvName']
            elif scriptType == '3' or scriptType == '4' or scriptType == '6' or scriptType == '7' or scriptType == '8':
                # 系统用户执行
                cmd2 = "source " + kwargs['bashProFile'] + ";sqlplus 'sys/sys as sysdba' @" + kwargs[
                    'filepath'] + kwargs['newDbFullNameEn'] + ".sql " + kwargs['newDbFullNameEn']
            logging.info(cmd2)
            res2 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd2)
            self.printFileLog(res2)
            # 移除生成的临时文件
            logging.info('移除生成的sql文件')
            # logging.info("cat "+ sqlres[0][0] +"sync.sql ;")
            cmd3 = "mv " + kwargs['filepath'] + kwargs['newDbFullNameEn'] + ".sql /home/<USER>/bak/" + kwargs[
                'newDbFullNameEn'] + ".sql.bak"
            res3 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd3)
            self.printFileLog(res3)
        if scriptType == '5':
            dumpfile = kwargs['bakFile'].split('/')[-1]
            if kwargs['dbType'].lower() == 'oracle':
                logging.info('开始还原数据库：{}'.format(kwargs['newDbFullNameEn']))
                cmd4 = 'source  ' + kwargs[
                    'bashProFile'] + ';impdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile=' + dumpfile + ' remap_schema=' + \
                       kwargs['oldDbFullNameEn'] + ':' + kwargs[
                           'newDbFullNameEn'] + ' table_exists_action=replace transform=oid:n'
                logging.info('cmd4:{}'.format(cmd4))
                res4 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd4)
                self.printFileLog(res4)
            if kwargs['dbType'].lower() == 'mysql':
                # 创建数据库
                # if not res5:
                logging.info('创建数据库{}'.format(kwargs['newDbFullNameEn']))
                cmd6 = 'source  ' + kwargs['bashProFile'] + ';mysql --socket=' + kwargs['sockPath'] + '  -P3306 -u' + \
                       kwargs['dbUser'] + ' -p' + kwargs['dbUserPwd'] + ' -N -se "CREATE DATABASE ' + \
                       kwargs['newDbFullNameEn'] + ' DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci;"'

                res6 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd6)
                logging.info('cmd6-->{}'.format(cmd6))
                self.printFileLog(res6)
                # 还原数据库
                cmd7 = 'source ' + kwargs['bashProFile'] + ';mysql --socket=' + kwargs['sockPath'] + '  -P3306 -u' + \
                       kwargs['dbUser'] + ' -p' + kwargs['dbUserPwd'] + ' -N  ' + kwargs['newDbFullNameEn'] + ' < ' + \
                       kwargs['bakFile']
                logging.info('cmd7-->{}'.format(cmd7))
                res7 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd7)
                self.printFileLog(res7)
            # 修改数据库用户状态
            self.WdbUserStatus(kwargs['newDbFullNameEn'])

    def getDBfile(self):
        '''
        获取还原数据库文件信息
        :return:
        '''
        sql = ''
        if self.jobId:
            sql = "SELECT t.dbNameEn,t.dbFullNameEn,t.dbServerIp,t.bakFile,t.dbType FROM dbbaklog t WHERE t.jobId='%s' ;" % (
                self.jobId)
        if self.oldEnvName:
            sql = "SELECT a.dbNameEn,  b.dbFullNameEn,  b.dbServerIp,  b.bakFile,  b.dbType,  b.jobId FROM (SELECT w.dbNameEn,w.dbType,MAX(id) id FROM dbbaklog w WHERE w.envName='%s' AND w.bakFile NOT LIKE 'ORA-%%' GROUP BY w.dbNameEn,w.dbType) a JOIN dbbaklog b ON a.id=b.id JOIN dbinfonew c ON a.dbNameEn=c.dbNameEn AND a.dbType=c.dbType WHERE  c.envName='%s' AND c.dbStatus='1' ORDER BY b.dbServerIp, b.dbNameEn ;" % (
                self.oldEnvName, self.newEnvName)
        if self.dbBakFileList:
            sql = "SELECT t.dbNameEn,t.dbFullNameEn,t.dbServerIp,t.bakFile,t.dbType FROM dbbaklog t WHERE RTRIM(REPLACE(SUBSTRING_INDEX(t.bakFile,'/',-1),CHAR(13),'')) IN  ('%s');" % (
                self.dbBakFileList)
            sql = sql.replace('[\'', '').replace(']\'', '')
        # logging.info(sql)
        res = self.mysqlConnect(sql)
        return res

    def getNewDbFile(self, dbNameEn, dbType):
        '''
        获取被还原数据库信息
        :param EnvName:
        :return:
        '''
        sql = " SELECT t.dbType,t.dbFullNameEn,t.dbSeq,t.dbSyn,s.dbDumpFilePath,s.dbServerIp,s.dbServerName,s.dbServerPwd,s.bashProfile,t.dbEnvName,s.dbUser,s.dbUserPwd,s.sockPath FROM dbinfonew t JOIN dbextendnew s ON t.dbServerIp=s.dbServerIp WHERE t.dbStatus='1' AND t.dbType=s.dbType  AND  t.envName='%s' AND t.dbNameEn='%s' AND t.dbType='%s';" % (
            self.newEnvName, dbNameEn, dbType)
        res = self.mysqlConnect(sql)
        return res

    def dbUserStatus(self, dbFullNameEn):
        '''
        判断数据库用户是否存在
        :param dbFullNameEn:
        :return:1为存在
        '''
        sql = "SELECT t.dbUserStatus FROM dbinfonew t WHERE t.dbFullNameEn='%s';" % (dbFullNameEn)
        res = self.mysqlConnect(sql)
        return res[0][0]

    def WdbUserStatus(self, dbFullNameEn):
        sql = "UPDATE dbinfonew SET dbUserStatus='1' WHERE dbFullNameEn='%s';" % (dbFullNameEn)
        res = self.mysqlConnect(sql)
        return res

    def getDbScriptFile(self, scriptType, dbNameEn):
        '''
        获取数据库脚本的执行文件
        :param dbNameEn
        :param scriptType
        :return:
        '''
        SQL = "SELECT t.script FROM sqlinfonew t WHERE t.type='%s' AND t.dbNameEn='%s';" % (scriptType, dbNameEn)
        # logging.info('sql={}'.format(sql))
        res = self.mysqlConnect(SQL)
        return res

    @staticmethod
    def infoTip(self, scriptType, DbFullNameEn, dbType):
        '''
        提示信息
        :return:
        '''
        pass

    def execScript2(self, scriptType, res):
        '''
        脚本执行的二次封装
        :param type: 1为同义词；2为sequence处理；3为账户解锁操作;4为账户锁定操作 文件生成需要区分文件名，5还原
        :return:
        '''
        threads = []
        for dbinfo in res:
            # 判断是否执行处理的sequence
            # logging.info('seqres{}'.format(dbinfo))
            seqres = self.getNewDbFile(dbinfo[0], dbinfo[4])
            # logging.info('seqres{}'.format(seqres))
            if not list(seqres):
                logging.info('数据库{}未启用无需操作'.format(dbinfo[0]))
                continue
            # logging.info('seqres{}'.format(seqres))
            # logging.info('数据库名：{}'.format(dbinfo[0]))
            # logging.info('type:{}'.format(scriptType))
            # 查询条件
            selectReq = ''
            # 实际要求条件
            reaReq = ''
            if scriptType == '1':
                selectReq = str(seqres[0][3])
                reaReq = '1'
            if scriptType == '2':
                selectReq = str(seqres[0][2])
                reaReq = '1'
            if scriptType == '3' or scriptType == '4':
                selectReq = str(seqres[0][0]).lower()
                reaReq = 'oracle'
            # logging.info('selectReq:{}'.format(selectReq))
            # logging.info('reaReq:{}'.format(reaReq))
            dbNameEn = dbinfo[0]
            newDbFullNameEn = seqres[0][1]
            dbDumpFilePath = seqres[0][4]
            host = seqres[0][5]
            username = seqres[0][6]
            password = seqres[0][7]
            bashProFile = seqres[0][8]
            dbEnvName = seqres[0][9]
            bakFile = dbinfo[3]
            oldDbFullNameEn = dbinfo[1]
            dbUser = seqres[0][10]
            dbUserPwd = seqres[0][11]
            sockPath = seqres[0][12]
            # logging.info('oldDbFullNameEn：{}'.format(oldDbFullNameEn))
            # logging.info('newDbFullNameEn：{}'.format(newDbFullNameEn))
            # 判断脚本是否要执行 同义词、sequence
            if selectReq == reaReq and scriptType != '5':
                ts1 = threading.Thread(
                    self.execScript1(scriptType=scriptType, host=host, username=username, password=password,
                                     bashProFile=bashProFile, dbNameEn=dbNameEn, newDbFullNameEn=newDbFullNameEn,
                                     filepath=dbDumpFilePath, dbEnvName=dbEnvName, dbType=str(seqres[0][0]).lower(),
                                     dbUser=dbUser, dbUserPwd=dbUserPwd, sockPath=sockPath))
                threads.append(ts1)
            if scriptType == '5':
                ts1 = threading.Thread(
                    self.execScript1(scriptType=scriptType, host=host, username=username, password=password,
                                     bashProFile=bashProFile, dbNameEn=dbNameEn, newDbFullNameEn=newDbFullNameEn,
                                     oldDbFullNameEn=oldDbFullNameEn, filepath=dbDumpFilePath, dbEnvName=dbEnvName,
                                     bakFile=bakFile, dbType=str(seqres[0][0]).lower(), dbUser=dbUser,
                                     dbUserPwd=dbUserPwd, sockPath=sockPath))
                threads.append(ts1)

        for t in threads:
            t.start()
        for t in threads:
            t.join()

    def main(self):
        '''
        主程序
        :return:
        '''
        if self.oldEnvName == 'None' and len(self.dbBakFileList) == 0:
            logging.info('没有要还原的数据库文件信息！')
            sys.exit(0)
        # 获取还原文件
        res = self.getDBfile()
        logging.info('还原数据库文件如下：')
        for s in res:
            logging.info(s[3].split('/')[-1])
        # logging.info(res)
        # logging.info('ss:{}'.format(self.isnotSeq))
        # 判断是否有要处理的sequence,默认执行
        # if self.isnotSeq.lower() == 'true':
        #     self.execScript2('2', res)
        # # 判断是否锁库
        # if self.isnotLock.lower() == 'true':
        #     self.execScript2('4', res)
        # 断开数据库连接
        self.execScript2('8', res)
        # 删除数据库
        self.execScript2('7', res)
        # 数据库还原
        self.execScript2('5', res)

        # 解锁数据库
        self.execScript2('3', res)
        # 设置数据库密码
        self.execScript2('6', res)

        # 执行同义词更新
        self.execScript2('1', res)


oldenv = sys.argv[1]
# logging.info(oldenv)
newenv = sys.argv[2]
# logging.info(newenv)

try:
    jobId = sys.argv[3]
except:
    jobId = None

try:
    dbBakFileList = sys.argv[4:]
except:
    dbBakFileList = None

# oldenv = 'it300'
# newenv = 'it99'
# dbBakFileList = ''
# jobId = ''

obj = SSHExec(oldenv, newenv, dbBakFileList, jobId)

obj.main()
