#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger
from utils.test_env.test_env_lib import step_desc, shell_cmd
from dao.get.mysql.test_env import get_env_info_by_tms


@step_desc('tms时间设置')
def change_env_time(env, new_date):
    logger.info('Recreate config map ...')
    # 删除 config map
    del_config_map_cmd = 'ssh {} "kubectl delete configmap env.info -n {}"'.format(KUBECTL_SERVER, env)
    logger.info('删除{} config map 命令: {}'.format(env, del_config_map_cmd))
    rc, stdout, stderr = shell_cmd(del_config_map_cmd)
    if rc == 0:
        logger.info(stdout)
    else:
        logger.error(stderr)
        logger.error('env.info not exist.')

    # 创建 config map
    # env_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    create_config_map_cmd = '''ssh {} "kubectl create configmap env.info --from-literal=timestamp='@{}' -n {}"
                            '''.format(KUBECTL_SERVER, new_date, env)
    logger.info('创建{} config map 命令: {}'.format(env, create_config_map_cmd))
    rc, stdout, stderr = shell_cmd(create_config_map_cmd)
    if rc == 0:
        logger.info(stdout)
    else:
        logger.error(stderr)
        raise Exception('create env.info failed.')


@step_desc('tp时间设置')
def change_vm_time(vm_ip, new_date, new_time):
    cmd = '''ssh tomcat@{} "date -s '{} {}'"'''.format(vm_ip, new_date, new_time)
    logger.info('设置{}时间命令: {}'.format(vm_ip, cmd))
    rc, stdout, stderr = shell_cmd(cmd)
    if rc == 0:
        logger.info(stdout)
    else:
        logger.error(stderr)
        raise Exception('设置后台时间失败')


if __name__ == '__main__':
    tms_env = sys.argv[1]
    DATE_VALUE = sys.argv[2]
    TIME_VALUE = sys.argv[3]
    KUBECTL_SERVER = 'tomcat@**************'
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']

    change_env_time(tms_env, DATE_VALUE + ' ' + TIME_VALUE)
    change_vm_time(tp_ip, DATE_VALUE, TIME_VALUE)
