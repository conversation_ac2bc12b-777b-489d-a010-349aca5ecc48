# tp初始化，用于测试环境发布
# 第1版 zt@2020-07-28
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger
from utils.test_env.test_env_lib import step_desc


@step_desc("tp初始化")
def init_tp():
    logger.info(">>>>「tp初始化」>> init_tp() >> ENV_NAME = {}".format(ENV_NAME))
    logger.info(">>>>「tp初始化」>> init_tp() >> TP_LIST = {}".format(TP_LIST))


if __name__ == '__main__':
    ENV_NAME = sys.argv[1]
    TP_LIST = sys.argv[2]
    init_tp()
