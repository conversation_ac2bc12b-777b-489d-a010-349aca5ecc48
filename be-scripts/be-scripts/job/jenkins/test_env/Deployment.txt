
# 生产 jenkins 创建job "new_test_publish"

# 免密登录 tomcat用户 -- tomcat用户
192.168.222.118  # tms15 对应虚机节点
192.168.221.195  # 镜像制作节点
192.168.221.45   # k8s
192.168.222.17   # k8s重启脚本节点

# 创建nfs共享
# /etc/exports
/data/publish/git/uat 192.168.223.132(rw,all_squash,anonuid=501,anongid=501,sync)
/data/env_init 192.168.223.132(rw,all_squash,anonuid=501,anongid=501,sync)

# 生产 配置更新节点 挂载nfs共享
# 192.168.223.162
# 192.168.223.105
# /ect/rc.local
mount 192.168.222.164:/data/env_init /data/env_init
mount 192.168.222.164:/data/publish/git/uat /data/publish/git/uat
