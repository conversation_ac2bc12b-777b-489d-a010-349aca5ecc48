#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, INTERFACE_URL
from dao.get.mysql.test_env import get_env_info_by_tms
from utils.test_env.test_env_lib import step_desc, request
from utils.test_env.ccms import excel_replace, excel_write, update_svn


@step_desc('CCMS内容替换')
def former_ccms_replace(env, back_ip):
    file_path_pro = WORKSPACE + "ccms_online.xls"
    file_path_rep = WORKSPACE + "ccms_match.xlsx"
    file_path = WORKSPACE + "tmp/ccms_temp_60.xls"
    new_file_path = WORKSPACE + 'tmp/ccms_new.xls'

    new_ip = "{}.k8s.howbuy.com".format(env)
    src_ip = "**************"

    excel_write(file_path_pro, file_path_rep, file_path)
    os.chdir(WORKSPACE)
    excel_replace(file_path, src_ip, new_ip, new_file_path, back_ip)


@step_desc('CCMS内容替换')
def ccms_replace(env):
    url = "{}/zeus-service/apiForDeploy/envinit".format(INTERFACE_URL['zeus'])
    data = {
        "env": env,
        "apps": "",
    }
    res = request(url, '', data)
    result = res.text
    if result == 'success':
        return True
    else:
        raise Exception(result)


@step_desc('CCMS上传SVN')
def upload_to_svn(env):
    import_env = 'docker-k8s-{}'.format(env)
    path = WORKSPACE + "config/{}".format(import_env)
    svn_path = 'svn://***************/usr/local/subversion-1.4.4/repos_doc/IT/部门级/质量管理部QA/配置管理/config/'
    update_svn(path, svn_path, import_env)


if __name__ == "__main__":
    WORKSPACE = '/data/env_init/ccms/'

    tms_env = sys.argv[1]
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']

    # former_ccms_replace(tms_env, tp_ip)
    ccms_replace(tms_env)
    upload_to_svn(tms_env)
