#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, PRODUCT_STORE_URL
from dao.get.mysql.test_env import get_env_info_by_tms
from utils.test_env.test_env_lib import step_desc, shell_cmd, TP_JAR_APP, TP_TOMCAT_APP, TP_APP


@step_desc('更新制品')
def update_repo(app_list):
    for repos in app_list:
        # 判断是否clone过制品库
        # 优化先清理后拉取 zt@2020-10-20
        lib_cache_path = os.path.join(PRODUCT_PATH, repos)
        if os.path.isdir(lib_cache_path):
            os.system("cd {} && rm -rf {}/* && git fetch --all && git reset --hard origin/master && git pull".
                      format(lib_cache_path, lib_cache_path))
        else:
            os.system("cd {} && git clone --depth 1 {}".format(
                PRODUCT_PATH, os.path.join(PRODUCT_STORE_URL, repos)))


@step_desc('应用拷贝到虚拟机')
def app_to_vm(vm_ip):
    # jar apps
    scp_path = {CONTAINER_PATH + '/jar': '/data/app/online'}
    # special apps:  center-express-service, fds-schedule-new
    scp_path.update({CONTAINER_PATH + '/jar/center-express-service': '/data/app/center-express-service'})
    scp_path.update({CONTAINER_PATH + '/schedule': '/data/app/schedule'})
    # tomcat apps
    for app in TP_TOMCAT_APP:
        src_path = CONTAINER_PATH + '/tomcat/' + TP_TOMCAT_APP[app][0]
        target_path = '/usr/local/' + TP_TOMCAT_APP[app][0]
        scp_path.update({src_path: target_path})

    for src_path in scp_path:
        logger.info("Exec: rsync --timeout=120 --delete --exclude work/ --ignore-errors -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
            src_path, vm_ip, scp_path[src_path]))
        code, stdout, stderr = shell_cmd(
            "rsync --timeout=120 --delete --exclude work/ --ignore-errors -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(src_path, vm_ip, scp_path[src_path]))
        if code != 0:
            raise Exception(stderr)


if __name__ == '__main__':
    # 获取产线版本应用的目录
    PRODUCT_PATH = "/data/publish/git/uat/TP"
    # 将应用和容器框架目录合并的工作区
    CONTAINER_PATH = "/data/env_init/container/tp"

    tms_env = sys.argv[1]
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']
    update_repo(TP_APP)
    app_to_vm(tp_ip)
