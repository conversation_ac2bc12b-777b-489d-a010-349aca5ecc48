#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from settings import logger
from settings import TEST_PUBLISH_AIO as TPA
from utils.test_env.test_env_lib import step_desc, request
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import http_request_post

itest_relation = {
    "tms15": {
        'test_set_id': '408',
        'run_version': 'trunk',
        'run_type': '0'
    }
}


@step_desc('调用itest')
def call_itest(set_id, env, emailUserList):
    if TPA['itest_domain'].startswith('http://'):
        url = TPA['itest_domain'] + TPA['itest_url']
    else:
        url = 'http://' + TPA['itest_domain'] + TPA['itest_url']

    data = {
        "testSetId": set_id,
        "runEnvConfig": env,
        "runType": '0',
        "emailUserList": emailUserList
    }

    res = request(url, '', data)
    result = res.json()
    logger.info(result)
    if 'code' in result and result['code'] != '9999':
        return True
    else:
        raise Exception(result['message'])


@step_desc('调用新魔戒')
def call_itest_post(set_id, env, emailUserList):
    if TPA['itest_domain'].startswith('http://'):
        url = TPA['itest_domain'] + TPA['itest_url']
    else:
        url = 'http://' + TPA['itest_domain'] + TPA['itest_url']
    data = {
        "targetId": int(set_id),
        "runEnv": env,
        "parallel": int(TPA['itest_run_type']),
        "receiveMail": emailUserList
    }
    res = http_request_post(url, data)
    result = res.json()
    logger.info(result)
    if TPA['itest_rst_code_key'] in result and result[TPA['itest_rst_code_key']] != TPA['itest_rst_success_code']:
        return True
    else:
        raise Exception(result['message'])


if __name__ == '__main__':
    test_set_id = sys.argv[1]
    env = sys.argv[2]
    emailUserList = sys.argv[3]
    if TPA['itest_domain'] == 'paas.hongkou.howbuy.com':
        call_itest(test_set_id, env, emailUserList)
    else:
        call_itest_post(test_set_id, env, emailUserList)
