#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger
from dao.get.mysql.test_env import get_env_info_by_tms
from utils.test_env.test_env_lib import step_desc, TP_JAR_APP, TP_TOMCAT_APP, connect_ssh


@step_desc('应用重启')
def tp_restart(vm_ip):
    # 连接ssh
    client = connect_ssh(vm_ip)

    # 重启zookeeper
    try:
        cmd = "cd /usr/local/zookeeper-3.4.6 && ./restart.sh"
        logger.info('Exec: {}'.format(cmd))
        stdin, stdout, stderr = client.exec_command(cmd)
        logger.info(stdout.read().decode('utf-8'))
    except Exception as err:
        logger.error(err)

    for app in TP_JAR_APP:
        if app == 'fds-schedule-new':
            path = '/data/app/online/schedule/fds-schedule-new'
        else:
            path = "{}/online/{}".format(VIRTUAL_JAR_BASE_PATH, app)

        try:
            cmd = "pkill -9 -f {}/".format(app)
            logger.info('Exec: {}'.format(cmd))
            stdin, stdout, stderr = client.exec_command(cmd)
            logger.info(stdout.read().decode('utf-8'))
        except Exception as err:
            logger.error(err)

        try:
            cmd = "find {} -name restart.sh | xargs sh".format(path)
            logger.info('Exec: {}'.format(cmd))
            stdin, stdout, stderr = client.exec_command(cmd)
            logger.info(stdout.read().decode('utf-8'))
        except Exception as err:
            logger.error(err)

    for app in TP_TOMCAT_APP:
        path = "{}/{}".format(VIRTUAL_TOMCAT_BASE_PATH, TP_TOMCAT_APP[app][0])

        try:
            cmd = "pkill -9 -f {}/".format(TP_TOMCAT_APP[app][0])
            logger.info('Exec: {}'.format(cmd))
            stdin, stdout, stderr = client.exec_command(cmd)
            logger.info(stdout.read().decode('utf-8'))
        except Exception as err:
            logger.error(err)

        try:
            cmd = "find {} -name restart.sh | xargs sh".format(path)
            logger.info('Exec: {}'.format(cmd))
            stdin, stdout, stderr = client.exec_command(cmd)
            logger.info(stdout.read().decode('utf-8'))
        except Exception as err:
            logger.error(err)

    # 关闭ssh
    client.close()


if __name__ == '__main__':
    # 配置文件替换的工作区
    CONFIG_WORKSPACE = "/data/env_init/config"
    VIRTUAL_JAR_BASE_PATH = "/data/app"
    VIRTUAL_TOMCAT_BASE_PATH = "/usr/local"

    tms_env = sys.argv[1]
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']
    tp_restart(tp_ip)
