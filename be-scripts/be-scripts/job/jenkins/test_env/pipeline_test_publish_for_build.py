# 流水线的测试环境发布（主线程）
# 第1版 zt@2020-08-28
import json
import os
import sys
import datetime
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from dao.get.mysql.env_info import ins_ptp_log_for_create
from job.jenkins.test_env.pipeline_test_publish_for_parse import ptp_main


if __name__ == '__main__':
    cur_time = datetime.datetime.now()
    cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    log.info(">>>>（全流程构建调用）流水线测试环境发布 {}".format(cur_time_str))

    param_list = sys.argv[1:]
    ptp_batch_num = 0
    ptp_type = 'B'

    err_msg = None

    try:
        if len(param_list) < 4:
            raise ValueError("缺少必要参数(共需要4个参数)：{}".format(param_list))

        app_name = param_list[0]
        br_name = param_list[1]
        suite_code = param_list[2]
        flag_file_dir = param_list[3]
        with open(flag_file_dir, 'r') as f:
            json_dict = json.loads(f.read())

        sid = json_dict.get('sid')
        is_mock_compile = json_dict.get('is_mock_compile')
        if is_mock_compile:
            ptp_type = 'M'
        # 初始化调用日志
        ptp_id = ins_ptp_log_for_create(ptp_batch_num, ptp_type, app_name, suite_code, br_name, err_msg)
        # 调用主脚本
        ptp_main(
            ptp_batch_num=ptp_batch_num,
            ptp_type=ptp_type,
            app_name=app_name,
            suite_code=suite_code,
            br_name=br_name,
            ptp_id=ptp_id
        )
        # 正常返回
        sys.exit()
    except ValueError as e:
        # 打印错误信息
        err_msg = "（全流程构建调用）流水线测试环境发布异常：{}".format(e)
        log.error(err_msg)
        # 异常返回
        sys.exit(-1)
    except Exception as ex:
        # 记录错误信息
        err_msg = "（全流程构建调用）流水线测试环境发布系统异常：{}".format(ex)
        log.error(err_msg)
        # 打印错误信息
        traceback.print_exc()
        # 异常返回
        sys.exit(-1)
