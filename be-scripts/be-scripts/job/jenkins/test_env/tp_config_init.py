#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, INTERFACE_URL
from dao.get.mysql.test_env import get_env_info_by_tms, get_tp_rule
from utils.test_env.test_env_lib import step_desc, shell_cmd, request, TP_APP


def gen_tp_rule(vm_ip):
    tp_rule = get_tp_rule(vm_ip)
    tp_rule["last_ip_"] = "_".join(vm_ip.split(".")[-2:])
    tp_rule["last_ip-"] = "-".join(vm_ip.split(".")[-2:])

    # 获取后台数据库地址，和连接名称
    tp_rule["back_db_ip"] = tp_rule['backdb_ip'].split(":")[-3].replace("@", "")
    tp_rule["back_db_name"] = tp_rule['backdb_ip'].split(":")[-1]

    # 获取前台数据库地址，和连接名称
    tp_rule["front_db_ip"] = tp_rule['frontdb_ip'].split(":")[-3].replace("@", "")
    tp_rule["front_db_name"] = tp_rule['frontdb_ip'].split(":")[-1]

    return tp_rule


def file_search(config_dir):
    for root, dirs, files in os.walk(config_dir, topdown=False):
        for name in files:
            yield os.path.join(root, name)


def do_replace(config_dir, vm_ip):
    base_env_config = {'front_ip': '***************', 'back_ip': '**************', 'dba_name_per': 'SIT_SM25',
                       'baoxian': '***************', 'simu': '***************',
                       'backdb_ip': '*****************************************',
                       'frontdb_ip': '*****************************************',
                       'tp_dba_name': 'SIT_SM25', 'tp_cache_name': 'cache3_tp16', 'last_ip_': '220_25',
                       'last_ip-': '220-25', 'back_db_ip': '***************', 'back_db_name': 'hbqa11g',
                       'front_db_ip': '***************', 'front_db_name': 'hbqa'}

    new_rule = gen_tp_rule(vm_ip)

    logger.info("配置文件目录为{}".format(config_dir))
    logger.info("后台环境IP为{}".format(vm_ip))
    for file_path in file_search(config_dir):
        with open(file_path, "r+", encoding='utf-8', errors="ignore") as f:
            new_content = f.read()
            for i in base_env_config:
                new_content = new_content.replace(base_env_config[i], new_rule[i])
            f.seek(0, 0)
            f.truncate()
            f.write(new_content)


@step_desc('配置替换')
def former_config_replace(vm_ip):
    config_dir = os.path.join(CONFIG_WORKSPACE, vm_ip)
    os.system("rm -rf {}".format(config_dir))
    os.system("cp -a {} {}".format(TP_BASIC_CONFIG, config_dir))
    do_replace(config_dir, vm_ip)


@step_desc('配置替换')
def config_replace(env):
    url = "{}/zeus-service/apiForDeploy/envinit".format(INTERFACE_URL['zeus'])
    data = {
        "apps": ','.join(list(TP_APP)),
        "env": env,
    }
    res = request(url, '', data)
    result = res.text
    if result == 'success':
        return True
    else:
        raise Exception(result)


@step_desc('配置拷贝到虚拟机')
def config_to_vm(vm_ip):
    src_path = os.path.join(CONFIG_WORKSPACE, vm_ip)
    target_path = '/data/app_resource'

    logger.info("Exec: rsync --timeout=120 --delete --exclude work/ --ignore-errors -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
        src_path, vm_ip, target_path))
    code, stdout, stderr = shell_cmd(
        "rsync --timeout=120 --delete --exclude work/ --ignore-errors -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(src_path, vm_ip, target_path))
    if code != 0:
        raise Exception(stderr)


if __name__ == '__main__':
    # 配置文件替换的工作区
    CONFIG_WORKSPACE = "/data/env_init/config"
    # 交易后台自己维护的产线配置文件基线目录
    TP_BASIC_CONFIG = "/data/env_init/resource/app_resource_basic"

    tms_env = sys.argv[1]
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']
    # former_config_replace(tp_ip)
    config_replace(tms_env)
    config_to_vm(tp_ip)
