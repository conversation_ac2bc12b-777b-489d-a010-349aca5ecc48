# 流水线的测试环境发布（基础实现）
# 第1版 zt@2020-08-28
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from settings import PIPELINE_TEST_PUBLISH as PTP
from job.jenkins.test_env.pipeline_test_publish_constants import PtpTypeEnum
from job.jenkins.test_env.pipeline_test_publish_common import step_decorator
from job.jenkins.test_env.pipeline_test_publish_common import exec_local_cmd
from utils.config.nacos_file_rules import ConfigModifier
from utils.zeus.config_publish import ZeusAPI
from dao.get.mysql.env_info import upd_ptp_log_for_run


class PipelineTestPublishHandler:
    """
    流水线测试环境发布的处理都父类 zt@2020-08-25(七夕)
    """

    def __init__(self, ptp_id, ptp_type, po):
        self.ptp_id = ptp_id
        self.ptp_type = ptp_type
        self.po = po
        self.app_test_conf_path = None

    @step_decorator(">>>> 从153拉取「指定分支制品」至本地缓存 >> ")
    def app_pull(self, **kwargs):
        """拉制品"""
        # 切换到应用对应的制品库目录
        app_lib_cache_path = os.path.join(PTP['lib_cache_root_path'], self.po.lib_repo)
        if not os.path.exists(app_lib_cache_path):
            cmd = 'mkdir -p {}'.format(app_lib_cache_path)
            exec_local_cmd(cmd)
        os.chdir(app_lib_cache_path)

        # 拉取分支制品
        if not os.path.exists(self.po.br_name):
            # 组装应用具体分支的制品url
            app_lib_git_url = '{}/{}.git'.format(PTP['lib_git_url'], self.po.lib_repo)
            # 拉分支制品
            cmd = 'git clone -b {} --depth 1 {} {}'.format(self.po.br_name, app_lib_git_url, self.po.br_name)
            exec_local_cmd(cmd)
        else:
            # 切换至分支目录
            os.chdir(self.po.br_name)
            # 更新制品
            cmd = 'git pull origin {}'.format(self.po.br_name)
            exec_local_cmd(cmd)

    @step_decorator(">>>> 从gitlab拉取「最新配置」至本地缓存 >> ")
    def upd_app_resource(self, **kwargs):
        """更新配置"""
        cmd = 'cd {} && git checkout .'.format(PTP['app_resource_gitlab_path'])
        # exec_cmd(cmd)
        exec_local_cmd(cmd)
        cmd = 'cd {} && git pull'.format(PTP['app_resource_gitlab_path'])
        # exec_cmd(cmd)
        exec_local_cmd(cmd)
        # 解析配置文件缓存位置
        app_test_conf_path = PipelineTestPublishHandler.get_app_conf_path_for_test(self.po.app_name)
        if app_test_conf_path:
            self.app_test_conf_path = app_test_conf_path
            log.info(">>>> app_resource中找到「{}」的配置：{}".format(self.po.app_name, self.app_test_conf_path))
        else:
            log.warning(">>>> app_resource中没有找到关于「{}」的配置，跳过配置替换。".format(self.po.app_name))

    @step_decorator(">>>> 宙斯(1/3):配置替换(by 刘帅) >> ")
    def zeus_conf_replace(self, **kwargs):
        """宙斯配置替换"""
        if self.app_test_conf_path:
            # 调用帅的配置替换功能（待研究）
            cm = ConfigModifier([self.po.app_name], version=self.po.br_name, env=self.po.suite_code)
            cm.alter_config_file()
        else:
            log.warning(">>>> app_resource中没有找到关于「{}」的配置，跳过『宙斯配置替换』。".format(self.po.app_name))

    @step_decorator(">>>> 宙斯(2/3):配置同步 >> ")
    def zeus_conf_sync(self, **kwargs):
        """宙斯配置同步"""
        if self.app_test_conf_path:
            zeus_api = ZeusAPI(self.po.app_name, self.po.br_name, self.po.suite_code)
            log.info('调用宙斯同步接口:')
            sync_log = zeus_api.env_sync()
            log.info(sync_log)
        else:
            log.warning(">>>> app_resource中没有找到关于「{}」的配置，跳过『宙斯配置同步』。".format(self.po.app_name))

    @step_decorator(">>>> 宙斯(3/3):配置发布 >> ")
    def zeus_conf_release(self, **kwargs):
        """宙斯配置发布"""
        if self.app_test_conf_path:
            zeus_api = ZeusAPI(self.po.app_name, self.po.br_name, self.po.suite_code)
            log.info('调用宙斯发布接口:')
            publish_lob = zeus_api.release()
            log.info(publish_lob)
        else:
            log.warning(">>>> app_resource中没有找到关于「{}」的配置，跳过『宙斯配置发布』。".format(self.po.app_name))

    @staticmethod
    def get_app_conf_path_for_test(param_app_name):
        test_path = os.path.join(PTP['app_resource_gitlab_path'], 'test')
        cmd = 'find {} -not -path "./.git/*" -not -path "./.git" -maxdepth 2'.format(test_path)
        completed_process_obj = exec_local_cmd(cmd)
        all_conf_path_str = bytes.decode(completed_process_obj.stdout)
        for tmp_path in all_conf_path_str.split():
            if tmp_path.endswith(param_app_name):
                return tmp_path

        return None

    def get_ptp_key(self):
        return '{}_{}_{}_{}'.format(self.po.app_name, self.po.suite_code, self.po.br_name, self.node['node_name'])

    def get_pkg_cache_path(self):
        if self.ptp_type == PtpTypeEnum.MOCK:
            # 修改mock缓存目录 for 伟敏 zt@2020-10-23
            # pkg_cache_path = os.path.join(PTP['mock_cache_root_path'], self.po.lib_repo, self.po.br_name)
            pkg_cache_path = os.path.join(PTP['mock_cache_root_path'], self.po.app_name, self.po.pipeline_id)
            if not os.path.exists(pkg_cache_path):
                raise ValueError("mock包目录「{}」未创建".format(pkg_cache_path))
            if not os.listdir(pkg_cache_path):
                raise ValueError("mock包目录「{}」为空".format(pkg_cache_path))
        else:
            pkg_cache_path = os.path.join(PTP['lib_cache_root_path'], self.po.lib_repo, self.po.br_name)

        return pkg_cache_path

    def get_handlers(self):
        handlers = []

        if self.ptp_type == PtpTypeEnum.MOCK:
            log.info("mock部署，跳过：{}({})".format(self.app_pull.__name__, self.app_pull.__doc__))
        else:
            handlers.append(self.app_pull)

        handlers.append(self.upd_app_resource)

        if self.po.zeus_type:
            handlers.extend([
                self.zeus_conf_replace,
                self.zeus_conf_sync,
                self.zeus_conf_release,
            ])
        return handlers


class PipelineTestPublishManager:
    """
    流水线测试环境发布，责任链管理者 zt@2020-08-26
    """
    # 简略实现一下锁，后期放入数据库中 zt@2020-08-26
    running_map = {}

    def __init__(self, ptp_id, ptp_key):
        self.ptp_id = ptp_id
        self.ptp_key = ptp_key
        self.handlers = []
        PipelineTestPublishManager.running_map[ptp_key] = 'init'

    def add_handler(self, handler):
        self.handlers.append(handler)

    def run(self):
        PipelineTestPublishManager.running_map[self.ptp_key] = 'running'

        for idx in range(len(self.handlers)):
            handler = self.handlers[idx]
            upd_ptp_log_for_run(self.ptp_id, (idx + 1), handler.__name__, handler.__doc__)
            handler()
