# 流水线的测试环境发布（基础实现）
# 第1版 zt@2020-08-28
import datetime
import os
import sys
import time


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from po.env_info.test_publish_models import PipelineTestPublishPo as PtpPo
from job.jenkins.test_env.pipeline_test_publish_constants import PtpTypeEnum
from dao.get.mysql.env_info import upd_ptp_log_for_init
from dao.get.mysql.env_info import upd_ptp_log_for_start
from dao.get.mysql.env_info import ins_ptp_log_for_start
from dao.get.mysql.env_info import upd_ptp_log_for_rs
from dao.get.mysql.env_info import upd_ptp_log_for_success
from dao.get.mysql.env_info import upd_ptp_log_for_sleep
from dao.get.mysql.env_info import upd_ptp_log_for_timeout
from dao.get.mysql.env_info import upd_ptp_log_for_fail

from job.jenkins.test_env.pipeline_test_publish_for_base import PipelineTestPublishManager as PtpManager
from job.jenkins.test_env.pipeline_test_publish_for_vm import PipelineTestPublishVmHandler as PtpVmHandler
from job.jenkins.test_env.pipeline_test_publish_for_docker import PipelineTestPublishDockerHandler as PtpDockerHandler


def ptp_main(*args, **kwargs):
    if kwargs:
        ptp_batch_num = kwargs['ptp_batch_num']
        ptp_type = kwargs['ptp_type']
        app_name = kwargs['app_name']
        suite_code = kwargs['suite_code']
        br_name = kwargs['br_name']
        ptp_id = kwargs['ptp_id']
        # 参数打印
        cur_time = datetime.datetime.now()
        cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")
        log.info("=================================================================")
        log.info("========== 『流水线』测试环境发布 {} ==========".format(cur_time_str))
        log.info(">>>> ptp_batch_num = {}".format(ptp_batch_num))
        log.info(">>>> ptp_type = {}".format(ptp_type))
        log.info(">>>> app_name = {}".format(app_name))
        log.info(">>>> suite_code = {}".format(suite_code))
        log.info(">>>> br_name = {}".format(br_name))
        log.info(">>>> ptp_id = {}".format(ptp_id))
        log.info("=================================================================")

        # 参数验证
        # 0、ptp_id
        if not ptp_id or ptp_id <= 0:
            raise ValueError("参数:「ptp_id：'{}'」无效！".format(ptp_id))
        # 1、ptp_batch_num
        if ptp_batch_num:
            if ptp_batch_num.isnumeric():
                ptp_batch_num = int(ptp_batch_num)
                if ptp_batch_num == 0:
                    ptp_batch_num = '202009309999'
            else:
                raise ValueError("参数:「批次号：'{}'」不是数字！".format(ptp_batch_num))
        else:
            ptp_batch_num = '202009309999'

        # 2、ptp_type_enum
        try:
            ptp_type_enum = PtpTypeEnum(ptp_type)
        except ValueError:
            raise ValueError("参数:「调用方式：'{}'」无效！".format(ptp_type_enum))
        # 3、app_name
        if app_name.isnumeric():
            raise ValueError("参数:「应用名：'{}'」无效！".format(app_name))
        # 4、suite_code
        if suite_code.isnumeric():
            raise ValueError("参数:「环境套：'{}'」无效！".format(suite_code))
        # 5、br_name
        if not br_name or br_name == 'None':
            br_name = 'latest'

        # 参数解析
        # 1、根据3要素初始化model
        upd_ptp_log_for_init(ptp_id)
        po = PtpPo(app_name, suite_code, br_name)

        # 2、获取对应的责任链
        for node_idx in range(len(po.node_list)):

            try:
                node_obj = po.node_list[node_idx]
                node_type = node_obj['node_type']
                node_name = node_obj['node_name']

                ptp_key = '{}_{}_{}_{}'.format(app_name, suite_code, po.br_name, node_name)
                # 日志分裂功能
                if node_idx == 0:
                    # 更新日志至start
                    upd_ptp_log_for_start(ptp_id, po, node_obj)
                else:
                    # 新增日志至start
                    ptp_id = ins_ptp_log_for_start(ptp_id, ptp_batch_num, ptp_type_enum, po, node_obj)

                if node_type == 'VM':
                    ptp_handler = PtpVmHandler(ptp_id, ptp_type_enum, po, node_obj)
                else:
                    ptp_handler = PtpDockerHandler(ptp_id, ptp_type_enum, po, node_obj)

                handlers = ptp_handler.get_handlers()

                log.info("=================================================================")
                log.info("= ({}){}，发布需要{}步: =".format(ptp_type_enum.name, ptp_key, len(handlers)))
                log.info("=================================================================")

                # 3、把责任链委托给manager
                manager = PtpManager(ptp_id, ptp_key)
                for handler_idx in range(len(handlers)):
                    handler = handlers[handler_idx]
                    log.info("第{}步：{}({})".format(handler_idx + 1, handler.__name__, handler.__doc__))
                    manager.add_handler(handler)
                # 4、manager逐个执行责任链中的任务
                sleep_idx = 1
                if upd_ptp_log_for_rs(ptp_id, po, node_obj):
                    manager.run()
                    upd_ptp_log_for_success(ptp_id)
                else:
                    log.info("{}正在运行中，进入首次休眠。".format(ptp_key))
                    upd_ptp_log_for_sleep(ptp_id, sleep_idx)
                    while sleep_idx < 3:
                        time.sleep(3)
                        sleep_idx = sleep_idx + 1
                        if upd_ptp_log_for_rs(ptp_id, po, node_obj):
                            manager.run()
                            upd_ptp_log_for_success(ptp_id)
                            break
                        else:
                            log.info("{}正在运行中，进入第{}次休眠。".format(ptp_key, sleep_idx))
                            upd_ptp_log_for_sleep(ptp_id, sleep_idx)
                    else:
                        log.info("{}休眠{}次后超时。".format(ptp_key, sleep_idx))
                        upd_ptp_log_for_timeout(ptp_id)
            except ValueError as err:
                value_error_msg = "『循环启动失败』：{}".format(err)
                upd_ptp_log_for_fail(ptp_id, value_error_msg)
                raise ValueError(value_error_msg)
    else:
        raise ValueError("参数列表不能为空！")
