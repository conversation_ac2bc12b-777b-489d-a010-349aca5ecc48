#!/usr/bin python3
# -*- coding: utf-8 -*-
# ---
# @Software: PyCharm
# @File    : getNewDB.py
# @Des     ：获取最新dump、sql文件，并还原到数据库；包含中后台所有数据库的操作
# <AUTHOR> yuanxiang.li
# @E-mail  : <EMAIL>
# @Time    : 五月 28, 2020
# --

import paramiko, logging, pymysql, time, sys, os

# 加入日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s [line:%(lineno)d]  %(message)s')


class SSHExec():
    '''
    说明：
    1、若oldEnvName=None，则处理dbBakFileList,否则不处理dbBakFileList；
    2、若oldEnvName=tp_basic,则还原文件为固定的TP_BASIC的备份文件；
    '''

    def __init__(self, oldEnvName, newEnvName, isnotLock='true', isnotSeq='true', isnotOGG='true',
                 isnotexistFile='true', dbBakFileList=''):
        self.oldEnvName = oldEnvName  # 环境前缀 如：tms10
        self.newEnvName = newEnvName  # 环境前缀 如：tms15
        self.dbBakFileList = dbBakFileList  # 要还原的文件
        self.isnotLock = isnotLock  # 是否锁定数据库
        self.isnotSeq = isnotSeq  # 是否删除sequence
        self.isnotOGG = isnotOGG  # 是否执行OGG操作
        self.isnotexistFile = isnotexistFile  # 是否执行判断文件是否存在的动作

    def mysqlConnect(self, sql):
        '''
        mysql操作
        :return:
        '''
        # 数据库连接
        db = pymysql.connect('***************', 'adminuser', 'adminuser', 'testcenter', charset='utf8')
        # 使用 cursor() 方法创建游标对象
        cursor = db.cursor()
        # sql = "SELECT EmId,DevGroup FROM itdev WHERE department='研发部' and  EmId='" + str(id).strip() + "'"
        cursor.execute(sql)
        # 获取全部的返回结果行
        data = cursor.fetchall()
        # 关闭DB连接
        cursor.close()
        return data

    def sshConnect(self, host, username, password, cmd):
        '''
        建立ssh连接
        :return:
        '''
        try:
            # 建立一个sshclient对象
            ssh = paramiko.SSHClient()
            # 允许将信任的主机自动加入到host_allow 列表，此方法必须放在connect方法的前面
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # 调用connect方法连接服务器
            ssh.connect(hostname=host, port=22, username=username, password=password)
            stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True, timeout=300)
            res = stdout.readlines()
            # 获取错误信息
            error = stderr.read()
            ssh.close()
            # 如果有错误信息，返回error
            # 否则返回res
            if error.strip():
                return error
            else:
                return res
        except Exception as e:
            logging.error("失败信息" + str(e))
            return False

    def getDBenvinfo(self, env):
        '''
        根据环境名，获取所有应用数据库信息
        :return:
        '''

        sql = "SELECT t.ENV_NAME,t.DB_PRE_NAME,s.DB_SID,s.DB_TYPE,s.DB_DUMP_FILE_PATH,t.DB_IP,s.USERNAME,s.PWD FROM dbinfodtl t JOIN dbextend s ON t.DB_IP=s.DB_IP WHERE t.ENV_NAME='%s';" % (
            env)
        # logging.info(sql)
        dblist = self.mysqlConnect(sql)

        return dblist

    def getDBInfo(self, env):
        '''
        根据环境名，获取所有备份文件数据库信息
        :return:
        '''
        sql = "SELECT t.ENV_NAME,CONCAT(t.DB_PRE_NAME,'_',m.DB_NAME) DB_FULL_NAME,m.DB_NAME,t.DB_PRE_NAME,s.DB_IP,s.USERNAME,s.PWD,s.DB_DUMP_FILE_PATH,s.DB_SID,s.DB_TYPE,m.DB_LABLE,m.DB_STATUS FROM dbinfodtl t JOIN dbextend s ON t.DB_IP=s.DB_IP JOIN dbinfo m ON  t.DB_IP=m.DB_IP AND m.env_lable=(SELECT env_lable FROM envinfo r WHERE r.env_name=t.ENV_NAME) WHERE m.DB_STATUS IN ('1','3') AND t.ENV_NAME='%s';"
        # 获取老的库文件信息
        # logging.info('sql' + sql % dbname)
        return self.mysqlConnect(sql % env)

    def getoldenv(self, dbProName):
        sql = "SELECT DISTINCT ENV_NAME FROM dbinfodtl t WHERE DB_PRE_NAME='%s';"
        # 获取老的库文件信息
        # logging.info('sql' + sql % dbname)
        return self.mysqlConnect(sql % dbProName)

    def getNewDBfile(self):
        '''
        获取最新的备份数据库文件
        :return:
        '''
        # 初始化
        cmd = ''
        newDmplist = []
        if self.oldEnvName == 'tp_basic':
            newDmplist = ['tp_basic_batch_db.dmp', 'tp_basic_ESIGN_db.dmp', 'tp_basic_message_db.dmp',
                          'tp_basic_OTC_db.dmp',
                          'tp_basic_ORD_db.dmp', 'tp_basic_orders1_db.dmp', 'tp_basic_PRO_db.dmp',
                          'tp_basic_FIN_db.dmp',
                          'tp_basic_ZHZX_db.dmp', 'tp_basic_CXCP_db.dmp', 'tp_basic_DEAL_db.dmp', 'tp_basic_HIS_db.dmp',
                          'tp_basic_LCTZT_db.dmp', 'tp_basic_NOTIFY_db.dmp', 'tp_basic_PAY_db.dmp',
                          'tp_basic_SETTLE_db.dmp',
                          'tp_basic_TRADE_db.dmp']
        elif self.oldEnvName == 'None':
            newDmplist = self.dbBakFileList
        else:
            dbenvlist = self.getDBenvinfo(self.oldEnvName)
            # logging.info(dbenvlist)
            for templist in dbenvlist:
                dumpPath = templist[4]
                dbPreName = templist[1]
                host = templist[5]
                username = templist[6]
                password = templist[7]
                cmd = 'cd ' + dumpPath + '; ls -tl ' + dbPreName + '* | awk \'{print $9}\''
                info = self.sshConnect(host=host, username=username, password=password, cmd=cmd)
                filelist = []
                for s in info:
                    s = s.replace('\r', '').replace('\n', '')
                    # logging.info('执行结果-->' + str(s))
                    # 处理已下线或者不存在的数据库
                    sql = "SELECT DB_NAME FROM dbinfo WHERE DB_STATUS='1' AND DB_IP='%s' AND DB_NAME='%s'  AND ENV_LABLE=(SELECT ENV_LABLE FROM envinfo WHERE ENV_NAME='%s') ;" % (
                        host, '_'.join(s[:-29].split('_')[2:]).upper(), self.oldEnvName)
                    # logging.info('sql-->' + sql)
                    isnotin = self.mysqlConnect(sql)
                    if len(isnotin) == 0:
                        continue
                    filelist.append(s)
                    # 获取最新的备份文件
                    if str(filelist).count(s[:-18]) > 1:
                        # 删除重复的备份文件
                        filelist.pop()
                        break
                newDmplist = newDmplist + filelist
        s = '\n'
        logging.info('所有备份文件如下：')
        logging.info('------------------')
        for li in newDmplist:
            s = s + li + '\n'
            # print(s)
        logging.info(s)
        logging.info('------------------')
        return newDmplist

    def isfile(self, filelist):
        for file in filelist:
            dbname = file.split('_')[2]
            dbprename = '_'.join(file.split('_')[:2])
            sql = "SELECT t.ENV_NAME,t.DB_IP,s.DB_DUMP_FILE_PATH,s.USERNAME,s.PWD,s.BASH_PROFILE FROM dbinfodtl t JOIN dbextend s ON t.DB_IP=s.DB_IP WHERE t.DB_PRE_NAME='%s' AND T.DB_IP=(SELECT DISTINCT DB_IP FROM dbinfo m WHERE m.ENV_LABLE='k8s' and m.db_name=UPPER('%s'));" % (
                dbprename, dbname)
            # logging.info(sql)
            strisfile = self.mysqlConnect(sql)
            try:
                cmd = 'ls -l {}{}'.format(strisfile[0][2], file)
                # logging.info(cmd)
            except:
                logging.info('数据库文件：{}，不存在！'.format(file))
                continue
            # cmd='ls -l {}abc.dmp'.format(strisfile[0][2])
            res = self.sshConnect(strisfile[0][1], strisfile[0][3], strisfile[0][4], cmd)
            for s in res:
                s = s.replace('\r', '').replace('\n', '')
                if s == '':
                    continue
                elif s.find('No such file or directory') != -1 or s.find('没有那个文件或目录') != -1:
                    logging.info(
                        '环境为：{};数据库文件为：{}{}不存在，数据库还原终止,请检查文件是否存在'.format(strisfile[0][1], strisfile[0][2], file))
                    exit(1)

    def donOGG(self, type):
        '''
        OGG的执行
        :param type: 1 start,2 stop
        :return:
        '''
        sql = "SELECT m.BACKSTAGE_IP,t.DB_PRE_NAME,s.DB_IP,s.USERNAME,s.PWD,s.BASH_PROFILE FROM dbinfodtl t JOIN dbextend s ON t.DB_IP=s.DB_IP JOIN envinfo m ON t.ENV_NAME=m.ENV_NAME WHERE s.DB_OTHER_LABLE='1' AND t.env_name='%s'" % (
            self.newEnvName)
        ogginfo = self.mysqlConnect(sql)
        # logging.info('ogginfo-->' + str(ogginfo))
        sql = "SELECT CONCAT(PATH,OGG_NAME) OGG,PATH FROM OGGINFO WHERE OGG_TYPE='%s'" % (type)
        ogg = self.mysqlConnect(sql)
        # logging.info('ogg-->'+str(ogg))
        oggenv = ogginfo[0][1]
        oggname = '_'.join(ogginfo[0][0].split('.')[2:])
        cmd = 'source ' + ogginfo[0][5] + ';cd ' + ogg[0][1] + ';sh ' + ogg[0][
            0] + ' E' + oggname + ' R' + oggname + ' ' + oggenv
        # logging.info('cmd-->' + cmd)
        if type == '1':
            logging.info('开始执行OGG的启动')
        elif type == '2':
            logging.info('开始执行OGG的停止')
        res = self.sshConnect(ogginfo[0][2], ogginfo[0][3], ogginfo[0][4], cmd)
        for s in res:
            s = s.replace('\r', '').replace('\n', '')
            if s == '':
                continue
            else:
                logging.info(s)
        # logging.info('执行结果-->' + str(res))
        # logging.info('cmd--->' + cmd)

    def script(self, type, **kwargs):
        '''
        执行sql脚本
        :type 1为同义词；2为sequence处理；3为账户解锁操作;4为账户锁定操作
        :return:
        '''
        sql = "SELECT SCRIPT FROM sqlinfo WHERE TYPE=%s AND DB_ENV_NAME='%s'" % (type, kwargs['dbEnvName'])
        sqlres = self.mysqlConnect(sql)
        sqljobfile = sqlres[0][0]
        # 创建sql文件
        cmd1 = 'cd ' + kwargs['filepath'] + ';echo "' + sqljobfile + '">sqljob.sql'
        res1 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd1)
        for s in res1:
            s = s.replace('\r', '').replace('\n', '')
            if s == '':
                continue
            else:
                logging.info(s)
        # 执行sql文件
        if type == 2:
            # 非系统用户执行
            cmd2 = "source " + kwargs['envpath'] + ";sqlplus " + kwargs['dbPreName'] + "/howbuy2015 @" + kwargs[
                'filepath'] + "sqljob.sql " + kwargs['dbPreName']
        #  logging.info(cmd2)
        else:
            cmd2 = "source " + kwargs['envpath'] + ";sqlplus 'sys/sys as sysdba' @" + kwargs[
                'filepath'] + "sqljob.sql " + kwargs['dbPreName']
        res2 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd2)
        for s in res2:
            s = s.replace('\r', '').replace('\n', '')
            if s == '':
                continue
            else:
                logging.info(s)
        # 移除生成的临时文件
        logging.info('移除生成的sql文件')
        # logging.info("cat "+ sqlres[0][0] +"sync.sql ;")
        cmd3 = "mv " + kwargs['filepath'] + "sqljob.sql /home/<USER>/bak/sqljob.sql.bak"
        res3 = self.sshConnect(kwargs['host'], kwargs['username'], kwargs['password'], cmd3)
        for s in res3:
            s = s.replace('\r', '').replace('\n', '')
            if s == '':
                continue
            else:
                logging.info(s)

    def dbRestore(self):
        '''
        数据库还原
        :return:
        '''
        # logging.info('是否锁定：{}类型为{}'.format(self.isnotLock, type(self.isnotLock)))
        # logging.info('是否删除：{}类型为{}'.format(self.isnotSeq, type(self.isnotSeq)))
        # logging.info('是否执行OGG：{}类型为{}'.format(self.isnotOGG, type(self.isnotOGG)))
        # logging.info('是否执行文件判断：{}类型为{}'.format(self.isnotexistFile, type(self.isnotexistFile)))
        # 判断还原信息是否为空，否则退出
        if self.oldEnvName == 'None' and len(self.dbBakFileList) == 0:
            logging.info('没有要还原的数据库文件信息！')
            sys.exit(0)

        # 获取数据库文件信息
        dbfile = self.getNewDBfile()
        # 获取完整的新数据库相关信息
        dballnewinfos = self.getDBInfo(self.newEnvName)
        # 获取完整的老数据库相关信息
        # logging.info('dbBakFileList:{}'.format(str(self.dbBakFileList)))
        if self.oldEnvName == 'None' and len(self.dbBakFileList) != 0:
            dbProName = '_'.join(dbfile[0].split('_')[0:2])
            # logging.info('dbProName:{}'.format(dbProName))
            myoldenv = self.getoldenv(dbProName)[0]
        else:
            myoldenv = self.oldEnvName
        dballoldinfos = self.getDBInfo(myoldenv)
        # logging.info(dbfilelist)
        # 判断文件是否存在,若dbBakFileList为空，则不需要判断文件是否为空
        # logging.info(self.dbBakFileList)
        # 判断是否执行，判断文件是否存在的动作,默认执行
        if self.isnotexistFile == 'true':
            if self.dbBakFileList:
                logging.info('判断文件是否存在！')
                self.isfile(dbfile)
                logging.info('文件判断完成')

        # 判断是否处理sequence,默认执行
        if self.isnotSeq == 'true':
            # 处理sequence
            for file in dbfile:
                # 根据文件名获取dbName
                dbName = ''.join(file.split('_')[2:3])
                # logging.info('dbName:{}'.format(dbName))
                for dballnewinfo in dballnewinfos:
                    # logging.info('dballnewinfo:{}'.format(str(dballnewinfo)))
                    # logging.info('dballnewinfo[2]:{}'.format(str(dballnewinfo[2])))
                    # 跳过mock库
                    if dballnewinfo[2].upper() == 'OTCMOCK':
                        continue
                    if dballnewinfo[9] == 'mysql':
                        continue
                    if dballnewinfo[2].upper() == dbName.upper():
                        # 删除sequence
                        IP = dballnewinfo[4]
                        sql = "SELECT BASH_PROFILE FROM dbextend WHERE DB_IP='%s';" % (IP)
                        sqlres = self.mysqlConnect(sql)
                        logging.info('执行{}数据库sequence删除操作'.format(dballnewinfo[1]))
                        self.script(type=2, filepath=dballnewinfo[7], dbPreName=dballnewinfo[1], envpath=sqlres[0][0],
                                    host=dballnewinfo[4], username=dballnewinfo[5], password=dballnewinfo[6],
                                    dbEnvName='ALL')
        # 判断是否锁库,默认执行
        if self.isnotLock == 'true':
            # 锁定全数据库
            for lockdbinfo in dballnewinfos:
                if lockdbinfo[9] == 'mysql':
                    continue
                IP = lockdbinfo[4]
                sql = "SELECT BASH_PROFILE FROM dbextend WHERE DB_IP='%s';" % (IP)
                sqlres = self.mysqlConnect(sql)
                logging.info('锁定账户{}'.format(lockdbinfo[1]))
                self.script(type=4, filepath=lockdbinfo[7], dbPreName=lockdbinfo[1], envpath=sqlres[0][0],
                            host=lockdbinfo[4], username=lockdbinfo[5], password=lockdbinfo[6], dbEnvName='ALL')

        # 根据数据库文件，获取对应文件的库信息

        for file in dbfile:
            # 变量初始化
            newdbRestoreinfo = []
            olddbRestoreinfo = []
            dbName = ''.join(file.split('_')[2:3])
            # logging.info('dbName:{}'.format(dbName))
            for dballnewinfo in dballnewinfos:
                if dballnewinfo[2].upper() == dbName.upper():
                    newdbRestoreinfo = dballnewinfo
                    break
            for dballoldinfo in dballoldinfos:
                if dballoldinfo[2].upper() == dbName.upper():
                    olddbRestoreinfo = dballoldinfo
                    break
            if not newdbRestoreinfo:
                logging.warning('请检查数据库' + file.split('_')[2] + '配置信息是否完整!')
                continue
            if newdbRestoreinfo[11] == '2':
                logging.info('数据库' + newdbRestoreinfo[2] + '未启用，无需还原！')
                continue
            if newdbRestoreinfo[11] == '0':
                logging.info('数据库' + newdbRestoreinfo[2] + '已下线，无需还原！')
                continue
            # 若备份和还原文件不再同一台服务器上，做文件拷贝
            # logging.info('olddbRestoreinfo:{}'.format(str(olddbRestoreinfo)))
            # logging.info('newdbRestoreinfo:{}'.format(str(newdbRestoreinfo)))
            if olddbRestoreinfo[4] != newdbRestoreinfo[4]:
                logging.info('--备份和还原文件不再同一台服务器上，做文件拷贝--')
                cmd = 'sshpass -p ' + newdbRestoreinfo[6] + ' scp -r ' + olddbRestoreinfo[7] + file + ' ' + \
                      newdbRestoreinfo[5] + '@' + newdbRestoreinfo[4] + ':' + newdbRestoreinfo[7]
                # logging.info('cmd--->' + cmd)
                res = self.sshConnect(olddbRestoreinfo[4], olddbRestoreinfo[5], olddbRestoreinfo[6], cmd)
                # logging.info('执行结果-->' + str(res))
            host = newdbRestoreinfo[4]
            username = newdbRestoreinfo[5]
            password = newdbRestoreinfo[6]
            # 判断环境是否要执行OGG操作,默认执行
            if self.isnotOGG == 'true':
                # 判断数据库是否需要执行停止OGG的动作
                if olddbRestoreinfo[2] == 'ZHZX':
                    logging.info('--执行停止OGG的动作--')
                    self.donOGG(2)
            # 开始还原oracle
            # 获取数据库环境变量文件名
            sql = "SELECT BASH_PROFILE FROM dbextend WHERE DB_IP='%s';" % (newdbRestoreinfo[4])
            bashfile = self.mysqlConnect(sql)
            # logging.info('cmd--->' + newdbRestoreinfo[9])
            if newdbRestoreinfo[9] == 'oracle':
                cmd = 'source  ' + bashfile[0][
                    0] + ';impdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile=' + file + ' remap_schema=' + \
                      olddbRestoreinfo[1] + ':' + newdbRestoreinfo[1] + ' table_exists_action=replace transform=oid:n'
                # logging.info('cmd--->' + cmd)
                logging.info('--开始还原数据库' + newdbRestoreinfo[1] + '---')
                res = self.sshConnect(host, username, password, cmd)
                for s in res:
                    s = s.replace('\r', '').replace('\n', '')
                    if s == '':
                        continue
                    else:
                        logging.info(s)
                # logging.info('执行结果-->' + str(res))

            elif newdbRestoreinfo[9] == 'mysql':
                # 查看数据库是否存在
                cmd = 'source  ' + bashfile[0][
                    0] + ';mysql --socket=/data/mysql57/data/mysql.sock -h localhost -P3306 -utms_plan -N -se "show databases like  \'' + \
                      newdbRestoreinfo[1] + '\';"'
                # logging.info('cmd--->' + cmd)
                isnot = self.sshConnect(host, username, password, cmd)
                for s in isnot:
                    s = s.replace('\r', '').replace('\n', '')
                    if s == '':
                        continue
                    else:
                        logging.info(s)
                # logging.info('isnot' + str(isnot))
                # 存在删除数据库
                if isnot:
                    cmd = 'source  ' + bashfile[0][
                        0] + ';mysql --socket=/data/mysql57/data/mysql.sock -h localhost -P3306 -utms_plan -N -se "drop database  ' + \
                          newdbRestoreinfo[1] + ';"'
                    res = self.sshConnect(host, username, password, cmd)
                    # logging.info('执行结果-->' + str(res))
                    for s in res:
                        s = s.replace('\r', '').replace('\n', '')
                        if s == '':
                            continue
                        else:
                            logging.info(s)
                # 创建数据
                cmd = 'source  ' + bashfile[0][
                    0] + ';mysql --socket=/data/mysql57/data/mysql.sock -h localhost -P3306 -utms_plan -N -se "CREATE DATABASE ' + \
                      newdbRestoreinfo[1] + ' DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci;"'
                res = self.sshConnect(host, username, password, cmd)
                logging.info('执行结果-->' + str(res))
                # 开始还原数据库
                logging.info('--开始还原数据库' + newdbRestoreinfo[1] + '---')
                cmd = 'source ~/.bash_profilemysql;mysql --socket=/data/mysql57/data/mysql.sock -h localhost -P3306 -utms_plan -N  ' + \
                      newdbRestoreinfo[1] + ' < ' + newdbRestoreinfo[7] + '/' + file
                res = self.sshConnect(host, username, password, cmd)
                # logging.info('执行结果-->' + str(res))
                for s in res:
                    s = s.replace('\r', '').replace('\n', '')
                    if s == '':
                        continue
                    else:
                        logging.info(s)
            # 判断环境是否要执行OGG操作,默认执行
            if self.isnotOGG == 'true':
                # 判断是否需要执行启动OGG的动作
                if olddbRestoreinfo[2] == 'ZHZX':
                    logging.info('--执行启动OGG的动作--')
                    self.donOGG(1)
        # 判断是否锁定,默认执行
        if self.isnotLock == 'true':
            # 解锁数据库
            for lockdbinfo in dballnewinfos:
                if lockdbinfo[9] == 'mysql':
                    continue
                IP = lockdbinfo[4]
                sql = "SELECT BASH_PROFILE FROM dbextend WHERE DB_IP='%s';" % (IP)
                sqlres = self.mysqlConnect(sql)
                logging.info('解锁账户{}'.format(lockdbinfo[1]))
                self.script(type=3, filepath=lockdbinfo[7], dbPreName=lockdbinfo[1], envpath=sqlres[0][0],
                            host=lockdbinfo[4], username=lockdbinfo[5], password=lockdbinfo[6], dbEnvName='ALL')

        # 执行同义词更新
        for file in dbfile:
            dbName = ''.join(file.split('_')[2:3])
            sql = "SELECT DB_ENV_NAME FROM sqlinfo WHERE TYPE=1"
            sqlres = self.mysqlConnect(sql)
            for db_env_name in sqlres:
                # 判断是否要执行同义词更新
                if db_env_name[0] == dbName:
                    for dballnewinfo in dballnewinfos:
                        if dballnewinfo[2] == dbName:
                            # 删除sequence
                            IP = dballnewinfo[4]
                            sql = "SELECT BASH_PROFILE FROM dbextend WHERE DB_IP='%s';" % (IP)
                            sqlres = self.mysqlConnect(sql)
                            logging.info('执行{}数据库同义词更新操作'.format(dballnewinfo[1]))
                            self.script(type=1, filepath=dballnewinfo[7], dbPreName=dballnewinfo[3],
                                        envpath=sqlres[0][0], host=dballnewinfo[4], username=dballnewinfo[5],
                                        password=dballnewinfo[6], dbEnvName=dbName)
        return True


# oldenv = 'tms10'
# newenv = 'tms15'
# dbBakFileList = ''
oldenv = sys.argv[1]
newenv = sys.argv[2]
try:
    isnotLock = sys.argv[3]
except:
    isnotLock = None
try:
    isnotSeq = sys.argv[4]
except:
    isnotSeq = None

try:
    isnotOGG = sys.argv[5]
except:
    isnotOGG = None

try:
    isnotexistFile = sys.argv[6]
except:
    isnotexistFile = None

try:
    dbBakFileList = sys.argv[7:]
except:
    dbBakFileList = None

obj = SSHExec(oldenv, newenv, isnotLock, isnotSeq, isnotOGG, isnotexistFile, dbBakFileList)
obj.dbRestore()
