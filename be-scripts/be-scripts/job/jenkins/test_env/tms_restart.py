#
import os
import sys
from time import sleep


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from utils.test_env.test_env_lib import step_desc, TMS_APP_LIST, shell_cmd
from utils.release.docker.restart_docker import DockerRestart
from settings import logger
from job.jenkins.test_env.tms_init import adapt_app_name_str


@step_desc('容器重启')
def former_container_restart(env):
    for app in ['redis', 'activemq', 'kafka', 'zookeeper', 'elasticsearch',
                'howbuy-quartz', 'tomcat-quartz', 'dubbo-admin']:
        work = DockerRestart()
        work.restart_extra_pod(env, app)
    for app in TMS_APP_LIST:
        if app in ['howbuy-quartz', 'tomcat-quartz']:
            continue
        work = DockerRestart()
        work.restart_pod(env, app)


@step_desc('容器重启')
def container_restart(env):
    cmd = """
           python3.x /home/<USER>/hb-k8s/k8s_build_script/k8s_app_deploy/k8s_kustomsize_build.py {} {}
    """.format(env, TMS_NAME_STR)
    logger.info("Exec: " + cmd)
    code, stdout, stderr = shell_cmd(cmd)
    if code != 0:
        raise Exception(stderr)
    else:
        logger.info(stdout)

    sleep(10)

    cmd = """
           python3.x /home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_pod_restart.py {} {}
    """.format(env, TMS_NAME_STR)
    logger.info("Exec: " + cmd)
    code, stdout, stderr = shell_cmd(cmd)
    if code != 0:
        msg = "====K8S(容器重启)执行出错====\n{}\n====请联系K8S====".format(stderr)
        raise Exception(msg)
    else:
        logger.info(stdout)


if __name__ == '__main__':
    tms_env = sys.argv[1]
    # 自定义TMS_LIST zt@2020-08-05
    TMS_NAME_STR = sys.argv[2]
    # 伟敏应用名的临时匹配功能优化 zt@2020-09-24
    TMS_NAME_STR = adapt_app_name_str(TMS_NAME_STR)
    logger.info(">>>> 特殊匹配：TMS_NAME_STR = {}".format(TMS_NAME_STR))

    container_restart(tms_env)
