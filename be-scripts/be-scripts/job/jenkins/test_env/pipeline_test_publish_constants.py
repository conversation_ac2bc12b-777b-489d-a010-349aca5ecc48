# 流水线的测试环境发布（常量定义，最底层）
# 第1版 zt@2020-09-09

import os
import sys
import enum

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)


@enum.unique
class PtpTypeEnum(enum.Enum):
    PUBLISH = 'P'
    APPLY = 'A'
    HISTORY = 'H'
    BUILD = 'B'
    MOCK = 'M'
    JENKINS = 'J'
    OTHER = 'O'


@enum.unique
class PtpStepEnum(enum.Enum):
    CREATE = 'Create'
    INIT = 'Init'
    START = 'Start'
    RUN = 'Run'
    R1 = 'R1'
    R2 = 'R2'
    R3 = 'R3'
    R4 = 'R4'
    R5 = 'R5'
    R6 = 'R6'
    R7 = 'R7'
    R8 = 'R8'
    R9 = 'R9'
    R10 = 'R10'
    R11 = 'R11'
    R12 = 'R12'
    R13 = 'R13'
    R14 = 'R14'
    R15 = 'R15'
    R16 = 'R16'
    R17 = 'R17'
    R18 = 'R18'
    R19 = 'R19'
    R20 = 'R20'
    SLEEP = 'Sleep'
    S1 = 'S1'
    S2 = 'S2'
    S3 = 'S3'
    S4 = 'S4'
    S5 = 'S5'
    S6 = 'S6'
    S7 = 'S7'
    S8 = 'S8'
    S9 = 'S9'
    TIMEOUT = 'Timeout'
    SUCCESS = 'Success'
    Fail = 'Fail'
