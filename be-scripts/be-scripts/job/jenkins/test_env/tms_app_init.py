#
import os
import sys


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, MIRROR_FACTORY, PRODUCT_STORE_URL
from dao.get.mysql.test_env import get_env_info_by_tms, get_product_store
from utils.test_env.test_env_lib import step_desc, TMS_REMOTE_LIST, TMS_APP_LIST, TMS_SPECIAL_LIST, TMS_TOMCAT_LIST
from job.jenkins.test_env.tstatic_config import tstatic_config_replace
from job.jenkins.test_env.tms_init import init_tms_list, adapt_app_name


@step_desc('更新制品')
def update_repo(app_list):
    project_info = {}
    for app_name in app_list:
        if app_name == "cgi":
            project_info[app_name] = get_product_store("cgi-ehowbuy")
        elif app_name == "trade":
            project_info[app_name] = get_product_store("ehowbuy-web")
        else:
            project_info[app_name] = get_product_store(app_name)

    product_store_list = [row["gitRepo"] for row in project_info.values() if row and row["gitRepo"]]
    # tstatic 六个仓库
    product_store_list += ['newpc', 'newpig', 'newwap', 'sxy', 'pcsmfund', 'vendor']
    if 'tstatic' not in product_store_list:
        product_store_list += ['tstatic']

    logger.info("拉取仓库列表{}".format(product_store_list))
    for repos in product_store_list:
        if os.path.isdir(os.path.join(PRODUCT_PATH, repos)):
            os.system("cd {} && git fetch --all && git reset --hard origin/master && git pull".
                      format(os.path.join(PRODUCT_PATH, repos)))
        else:
            os.system("cd {} && git clone --depth 1 {}".format(
                PRODUCT_PATH, os.path.join(PRODUCT_STORE_URL, repos)))


def handle_tstatic(env_name):
    # tstatic copy
    os.system('rm -rf /data/env_init/docker/tstatic/*')
    for moudle in ['newpc', 'newpig', 'newwap', 'pcsmfund', 'smasset', 'sxy', 'vendor']:
        if moudle == 'smasset':
            os.system(
                'cp -a  /data/publish/git/uat/TMS/tstatic/{} /data/env_init/docker/tstatic/{}'.format(moudle, moudle))
        else:
            os.system('cp -a  /data/publish/git/uat/TMS/{} /data/env_init/docker/tstatic/{}'.format(moudle, moudle))

    # tstatic config replace
    tstatic_config_replace(env_name)


@step_desc('推送应用到镜像工厂')
def app_to_img_factory(env):
    target_path = os.path.join(MIRROR_FACTORY["MIRROR_PATH"], env)
    logger.info("推送应用")
    for app in remote_list:
        cmd = "rsync --delete -Lav --exclude='*.git' {}/ tomcat@{}:{}".format(
            os.path.join(CONTAINER_PATH, 'remote', app), MIRROR_FACTORY["IP"], os.path.join(target_path, app, app))
        logger.info(cmd)
        os.system(cmd)

    for app in tomcat_list:
        cmd = "rsync --delete -Lav --exclude='*.git' {}/ tomcat@{}:{}".format(
            os.path.join(CONTAINER_PATH, 'tomcat', app), MIRROR_FACTORY["IP"], os.path.join(target_path, app, app))
        logger.info(cmd)
        os.system(cmd)

    # tstatic
    if len(static_list) > 0:
        cmd = "rsync -Lav --exclude='*.git' {}/* tomcat@{}:{}".format(
            CONTAINER_PATH + '/tstatic',
            MIRROR_FACTORY["IP"],
            '/home/<USER>/img-factory-online/{}/tstatic/'.format(env)
        )
        logger.info(cmd)
        os.system(cmd)


if __name__ == '__main__':
    # 获取产线版本应用的目录
    PRODUCT_PATH = "/data/publish/git/uat/TMS"
    CONTAINER_PATH = "/data/env_init/docker"

    tms_env = sys.argv[1]
    env_info = get_env_info_by_tms(tms_env)
    tp_ip = env_info['tp']

    # 自定义TMS_LIST zt@2020-07-28
    TMS_NAME_STR = sys.argv[2]
    if TMS_NAME_STR != '':
        TMS_NAME_LIST = TMS_NAME_STR.split(",")
        remote_list, tomcat_list, static_list = init_tms_list(tms_env, TMS_NAME_LIST)
        logger.info(">>>>「tms应用初始化」>> remote_list = {}".format(remote_list))
        logger.info(">>>>「tms应用初始化」>> tomcat_list = {}".format(tomcat_list))
        logger.info(">>>>「tms应用初始化」>> static_list = {}".format(static_list))

    # 伟敏应用名的临时匹配功能 zt@2020-09-16
    # 伟敏应用名的临时匹配功能优化 zt@2020-09-24
    tomcat_list = adapt_app_name(tomcat_list)
    logger.info(">>>> 特殊匹配：tomcat_list = {}".format(tomcat_list))

    update_repo(remote_list + tomcat_list + static_list)
    handle_tstatic(tms_env)
    app_to_img_factory(tms_env)
