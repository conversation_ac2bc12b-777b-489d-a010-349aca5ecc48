# tms初始化，用于测试环境发布
# 第1版 zt@2020-07-28
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger


def init_tms():
    logger.info(">>>>「tms初始化」>> init_tms() >> ENV_NAME = {}".format(ENV_NAME))
    logger.info(">>>>「tms初始化」>> init_tms() >> TMS_LIST = {}".format(TMS_LIST))
    logger.info(">>>>「tms初始化」>> init_tms() >> test_list = {}".format(test_list))


if __name__ == '__main__':
    ENV_NAME = sys.argv[1]
    TMS_LIST = sys.argv[2]
    test_list = [1, 2, 3]
    init_tms()
