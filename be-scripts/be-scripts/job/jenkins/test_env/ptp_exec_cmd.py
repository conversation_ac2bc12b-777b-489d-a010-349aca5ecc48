# 执行命令的工具类
# 第1版 zt@2020-09-04
import datetime
import os
import subprocess
import sys
import time

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log
from settings import PIPELINE_TEST_PUBLISH as PTP


class PtpCmdException(Exception):
    pass


def ptp_exec_cmd(cmd):
    log.info(">>>> exec_cmd() >> cmd: {}".format(cmd))
    p = subprocess.Popen(cmd, shell=True, close_fds=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = p.communicate(timeout=int(PTP['exec_cmd_timeout']))

    if p.returncode != 0:
        raise PtpCmdException(stderr)
    else:
        log.info(">>>> exec_cmd() >> stderr: {}".format(stdout))
        return stdout


if __name__ == '__main__':
    cur_time = datetime.datetime.now()
    cur_time_str = cur_time.strftime("%Y-%m-%d %H:%M:%S")
    log.info(">>>>流水线的测试环境发布（单应用+单环境）{}".format(cur_time_str))
    test_cmd = "ls -l"
    time.sleep(3)
    ptp_exec_cmd(test_cmd)

