import logging
import os
import sys
import time
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql import common_service_artifactinfo as mysql_common_service_artifactinfo
from settings import DOCKER_SCM_PROD_NODE_INFO
from settings import GITLAB_URL
from job.jenkins.nacos_util.generate_config import GenerateConfig
from job.jenkins.py_image_template import start_image_script
from job.jenkins.scm_test_publish import SSHConnectionManager, Prod_Publish


class ProdDockerPublish:

    def __init__(self, module_name, br_name, suite_code):
        self.module_name = module_name
        self.br_name = br_name
        self.suite_code = suite_code
        self.workspace = "/data/app/{}/".format(self.module_name)
        self.test_image_url = DOCKER_SCM_PROD_NODE_INFO[module_name].get('test_image_url')
        self.pd_prod_image_url = DOCKER_SCM_PROD_NODE_INFO[module_name].get('pd_prod_image_url')
        self.image_group = DOCKER_SCM_PROD_NODE_INFO[module_name].get('image_group')
        self.transfer_image_host = DOCKER_SCM_PROD_NODE_INFO[module_name].get('transfer_image_host')
        self.transfer_image_username = DOCKER_SCM_PROD_NODE_INFO[module_name].get('transfer_image_username')
        self.transfer_image_password = DOCKER_SCM_PROD_NODE_INFO[module_name].get('transfer_image_password')
        self.publish_host_list = DOCKER_SCM_PROD_NODE_INFO[module_name].get('publish_host_list')
        self.publish_username = DOCKER_SCM_PROD_NODE_INFO[module_name].get('publish_username')
        self.publish_password = DOCKER_SCM_PROD_NODE_INFO[module_name].get('publish_password')
        self.setting_file = DOCKER_SCM_PROD_NODE_INFO[module_name].get('setting_file')
        self.app_path = os.path.join(self.workspace, (self.suite_code + '_' + self.br_name))

    def pull_code(self):
        try:
            if os.path.exists(self.app_path):
                os.system('rm -rf {}/*'.format(self.app_path))
            rows = mysql_common_service_artifactinfo.get_scm_app()
            code_path = ''
            for row in rows:
                if row['appName'] == self.module_name:
                    code_path = row['gitCodePath']
            if not code_path:
                raise Exception('代码库信息为空')
            code_url = GITLAB_URL + ':' + code_path + '.git'
            local_path = os.path.join(self.app_path, self.module_name)

            cmd = 'git clone -b {branch_name} {code_url} {local_path} '.format(
                branch_name=self.br_name,
                code_url=code_url,
                local_path=local_path)
            logging.info('获取应用代码: {}'.format(cmd))
            os.system(cmd)

        except Exception as e:
            logging.error('拉取代码失败: {}'.format(str(e)))
            raise Exception('拉取代码失败: {}'.format(str(e)))

    def pull_zeus_config(self):
        config_path = os.path.join(self.app_path, self.module_name, self.module_name)
        gc = GenerateConfig(self.module_name, self.br_name, self.suite_code, config_path, self.setting_file)
        file_path = gc.create_settings()
        logging.info('--------生成配置的文件位置:{}'.format(file_path))

    def pull_image_to_transfer(self, host, username, password):
        logging.info('拉取镜像到镜像中转')
        with SSHConnectionManager(host, username, password) as ssh:

            login_cmd = "docker login {}".format(self.test_image_url)
            logging.info('登录镜像仓库: {}'.format(login_cmd))
            Prod_Publish.sshclient_execmd(login_cmd, ssh.SSH)

            # 检查镜像是否已存在
            old_image_tag = self.module_name + "_" + self.br_name
            check_cmd = "docker images | grep {}".format(old_image_tag)
            logging.info('检查镜像是否存在: {}'.format(check_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)

            if stat == 0 and resulto:
                logging.info('镜像已存在，准备删除: {}'.format(old_image_tag))
                delete_cmd = "docker rmi {}".format(old_image_tag)
                Prod_Publish.sshclient_execmd(delete_cmd, ssh.SSH)

            image_name = "{}/{}/{}:{}".format(self.test_image_url, self.image_group, self.module_name,
                                              (self.module_name + "_" + self.br_name))
            build_cmd = "docker pull {}".format(image_name)
            logging.info('拉取镜像: {}'.format(build_cmd))
            Prod_Publish.sshclient_execmd(build_cmd, ssh.SSH)

            # 再次检查镜像是否构建成功
            check_cmd = "docker images | grep {}".format((self.module_name + "_" + self.br_name))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
            logging.info('检查镜像是否存在stat: {}'.format(stat))
            logging.info('检查镜像是否存在resulto: {}'.format(resulto))
            logging.info('检查镜像是否存在resulte: {}'.format(resulte))
            if stat != 0 or not resulto:
                raise Exception('镜像拉取失败')
            logging.info('镜像拉取成功: {}'.format(image_name))
            new_image_name = "{}/{}/{}:{}".format(self.pd_prod_image_url, self.image_group, self.module_name,
                                          (self.module_name + "_" + self.br_name))
            logging.info('镜像重命名: {}'.format(new_image_name))
            pull_cmd = "docker tag {} {}".format(image_name, new_image_name)
            Prod_Publish.sshclient_execmd(pull_cmd, ssh.SSH)

    def push_image_to_image_repo(self, host, username, password):
        logging.info('推送镜像到产线镜像制品库')
        # 上传镜像到产线镜像仓库
        image_name = "{}/{}/{}:{}".format(self.pd_prod_image_url, self.image_group, self.module_name,
                                          (self.module_name + "_" + self.br_name))
        with SSHConnectionManager(host, username, password) as ssh:
            login_cmd = "docker login {}".format(self.pd_prod_image_url)
            logging.info('登录镜像仓库: {}'.format(login_cmd))
            Prod_Publish.sshclient_execmd(login_cmd, ssh.SSH)
            push_cmd = "docker push {}".format(image_name)
            logging.info('推送镜像到镜像仓库: {}'.format(push_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(push_cmd, ssh.SSH)
            logging.info('推送镜像结果状态: {}'.format(stat))
            logging.info('推送镜像结果: {}'.format(resulto))
            if stat != 0 or not resulto:
                raise Exception('镜像推送失败')

    def upload_code_to_pod(self):
        # 推代码到应用pod
        for publish_host in self.publish_host_list:
            logging.info('推送代码到pod: {}'.format(publish_host))
            logging.info('推送代码的publish_username：{}'.format(self.publish_username))
            logging.info('推送代码的publish_password：{}'.format(self.publish_password))
            code_target_path = self.workspace
            code_source_path = os.path.join(self.app_path, self.module_name)
            self.__upload_code_and_config(code_source_path, code_target_path,
                                          publish_host, self.publish_username, self.publish_password)

    def __upload_code_and_config(self, code_source_path, code_target_path, host,
                                 username, password):
        with SSHConnectionManager(host, username, password) as ssh:
            # 检查并创建目标目录
            check_dir_cmd = "test -d {}".format(code_target_path)
            logging.info('检查目标目录是否存在: {}'.format(check_dir_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_dir_cmd, ssh.SSH)
            if stat != 0:
                logging.info('目标目录不存在，开始创建')
                create_dir_cmd = "mkdir -p {}".format(code_target_path)
                logging.info('创建目标目录: {}'.format(create_dir_cmd))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(create_dir_cmd, ssh.SSH)
                if stat != 0:
                    raise Exception('创建目标目录失败: {}'.format(resulte))
                logging.info('目标目录创建成功')

        # 使用sshpass和scp上传代码文件
        scp_code_cmd = "sshpass -p '{}' scp -o StrictHostKeyChecking=no -r {}/* {}@{}:{}/".format(
            password, code_source_path, username, host, code_target_path)
        logging.info('执行代码文件上传: {}'.format(scp_code_cmd))
        os.system(scp_code_cmd)

    def pull_image_to_pod(self):
        # 推镜像到应用pod
        logging.info('推送镜像到pod')
        image_tag = self.module_name + "_" + self.br_name
        for publish_host in self.publish_host_list:
            logging.info('推送镜像到pod: {}'.format(publish_host))
            logging.info('推送镜像的publish_username：{}'.format(self.publish_username))
            logging.info('推送镜像的publish_password：{}'.format(self.publish_password))
            with SSHConnectionManager(publish_host, self.publish_username, self.publish_password) as ssh:
                login_cmd = "docker login {}".format(self.pd_prod_image_url)
                logging.info('登录镜像仓库: {}'.format(login_cmd))
                Prod_Publish.sshclient_execmd(login_cmd, ssh.SSH)
                image_script = "docker pull {image_url}/{image_group}/{app_name}:{image_version}".format(
                    image_url=self.pd_prod_image_url,
                    image_group=self.image_group,
                    image_version=image_tag,
                    app_name=self.module_name
                )
                logging.info('pod：{} 拉新镜像: {}'.format(publish_host, image_script))
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(image_script, ssh.SSH)
                logging.info(stat)
                logging.info(resulto)
                check_cmd = "docker images | grep {}".format(image_tag)
                resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
                logging.info('检查镜像是否存在stat: {}'.format(stat))
                logging.info('检查镜像是否存在resulto: {}'.format(resulto))
                logging.info('检查镜像是否存在resulte: {}'.format(resulte))
                if stat != 0 or not resulto:
                    raise Exception('推送镜像到pod失败')


    def publish_app(self):
        logging.info('开始部署 {} 应用'.format(self.module_name))
        # 发布应用prod
        try:
            image_script = start_image_script.format(image_url=self.pd_prod_image_url,
                                                     image_group=self.image_group,
                                                     image_version=(self.module_name + "_" + self.br_name),
                                                     app_name=self.module_name)
            code_target_path = self.workspace
            config_target_path = os.path.join(code_target_path, self.module_name)
            for publish_host in self.publish_host_list:
                with SSHConnectionManager(publish_host, self.publish_username, self.publish_password) as ssh:
                    self.__check_code_and_config(code_target_path, config_target_path, ssh)

                    # 检查容器是否存在
                    check_container_cmd = "docker ps -a | grep {}".format(self.module_name)
                    logging.info('检查容器是否存在: {}'.format(check_container_cmd))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_container_cmd, ssh.SSH)

                    if stat == 0 and resulto:
                        # 容器存在，执行重启
                        logging.info('容器已存在，执行重启')
                        self.__stop_and_remove_image(ssh)

                        # 启动新容器
                        logging.info('启动新容器: {}'.format(image_script))
                        resulto, resulte, stat = Prod_Publish.sshclient_execmd(image_script, ssh.SSH)
                        if stat != 0:
                            raise Exception('启动容器失败: {}'.format(resulte))
                    else:
                        # 容器不存在，直接启动
                        logging.info('容器不存在，执行启动')
                        resulto, resulte, stat = Prod_Publish.sshclient_execmd(image_script, ssh.SSH)
                        if stat != 0:
                            raise Exception('启动容器失败: {}'.format(resulte))

                    # 等待30秒，让容器完全启动
                    logging.info('等待30秒，让应用完全启动...')
                    time.sleep(30)

                    # 检查容器是否成功启动
                    check_container_cmd = "docker ps | grep {}".format(self.module_name)
                    logging.info('检查容器是否启动: {}'.format(check_container_cmd))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_container_cmd, ssh.SSH)
                    if stat != 0 or not resulto:
                        raise Exception('容器启动失败')

                    # 检查应用是否正常响应
                    check_app_cmd = "curl -s http://{}:8080".format(publish_host)
                    logging.info('检查应用是否正常响应: {}'.format(check_app_cmd))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_app_cmd, ssh.SSH)
                    if stat != 0 or not resulto:
                        raise Exception('应用未正常响应')

            logging.info('应用部署完成')
        except Exception as e:
            logging.error('应用部署失败: {}'.format(str(e)))
            traceback.print_exc()
            raise Exception('应用部署失败: {}'.format(str(e)))

    def __check_code_and_config(self, code_target_path, config_target_path, ssh):
        # 检查文件是否成功上传
        check_cmd = "test -d {} && ls -l {}".format(code_target_path, code_target_path)
        logging.info('检查文件是否存在: {}'.format(check_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_cmd, ssh.SSH)
        if stat != 0 or not resulto:
            raise Exception('文件上传失败或目录为空')
        # 检查关键文件是否存在
        key_files = ['manage.py', 'requirements.txt']
        for file in key_files:
            check_file_cmd = "test -e {}/{}".format(code_target_path, file)
            logging.info('检查关键文件是否存在: {}'.format(check_file_cmd))
            resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_file_cmd, ssh.SSH)
            if stat != 0:
                raise Exception('关键文件 {} 不存在'.format(file))
        # 检查配置文件是否存在
        check_settings_cmd = "test -e {}/settings.ini".format(config_target_path)
        logging.info('检查配置文件是否存在: {}'.format(check_settings_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_settings_cmd, ssh.SSH)
        if stat != 0:
            raise Exception('配置文件 settings.ini 不存在')

    def __stop_and_remove_image(self, ssh):
        stop_cmd = "docker stop {}".format(self.module_name)
        logging.info('停止容器: {}'.format(stop_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(stop_cmd, ssh.SSH)
        if stat != 0:
            raise Exception('停止容器失败: {}'.format(resulte))
        rm_cmd = "docker rm -f {}".format(self.module_name)
        logging.info('删除容器: {}'.format(rm_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(rm_cmd, ssh.SSH)
        if stat != 0:
            raise Exception('删除容器失败: {}'.format(resulte))
        self.__remove_image(ssh)

    def __remove_image(self, ssh):
        # 查询镜像ID，并删除镜像
        image_tag = "{}_{}".format(self.module_name, self.br_name)
        check_image_cmd = "docker images | grep '{}'".format(image_tag)
        logging.info('查询镜像ID: {}'.format(check_image_cmd))
        resulto, resulte, stat = Prod_Publish.sshclient_execmd(check_image_cmd, ssh.SSH)

        if stat == 0 and resulto:
            # 解析镜像ID（resulto是列表，取第一个元素）
            if resulto and len(resulto) > 0:
                image_line = resulto[0].strip()
                image_info = image_line.split()
                if len(image_info) >= 3:
                    image_id = image_info[2]
                    logging.info('找到镜像ID: {}'.format(image_id))

                    # 删除镜像
                    delete_image_cmd = "docker rmi {}".format(image_id)
                    logging.info('删除镜像: {}'.format(delete_image_cmd))
                    resulto, resulte, stat = Prod_Publish.sshclient_execmd(delete_image_cmd, ssh.SSH)
                    if stat != 0:
                        logging.warning('删除镜像失败: {}'.format(resulte))
                    else:
                        logging.info('镜像删除成功')
                else:
                    logging.warning('无法解析镜像信息: {}'.format(image_line))
            else:
                logging.warning('未找到镜像信息')
        else:
            logging.info('未找到需要删除的镜像')

    def deploy_app(self, op_type):
        '''
        mantis发布到容器
        :param op_type: 操作类型，可选值：
            - pull_code: 拉取代码
            - pull_zeus_config: 拉取宙斯配置
            - create_dockerfile: 创建Dockerfile
            - make_start_script: 创建启动脚本
            - make_image: 构建镜像
            - push_product: 推送到制品库
            - push_repo: 推送到中转库
            - push_zeus_config: 推送宙斯配置
            - publish_app: 发布应用
        '''
        logging.info('发布应用:{}'.format(self.module_name))
        logging.info('发布环境:{}'.format(self.suite_code))
        logging.info('发布分支:{}'.format(self.br_name))

        # 定义操作映射字典
        operation_map = {
            'pull_code': lambda: self.pull_code(),
            'pull_zeus_config': self.pull_zeus_config,
            'pull_image_to_transfer': lambda: self.pull_image_to_transfer(self.transfer_image_host,
                                                                          self.transfer_image_username,
                                                                          self.transfer_image_password),

            'push_image_to_image_repo': lambda: self.push_image_to_image_repo(self.transfer_image_host,
                                                                              self.transfer_image_username,
                                                                              self.transfer_image_password),
            'upload_code_to_pod': lambda: self.upload_code_to_pod(),
            'pull_image_to_pod': lambda: self.pull_image_to_pod(),

            'publish_app': lambda: self.publish_app()
        }

        try:
            # 检查操作类型是否有效
            if op_type not in operation_map:
                raise ValueError(f'无效的操作类型: {op_type}')

            # 执行对应的操作
            operation_map[op_type]()
            logging.info(f'操作 {op_type} 执行成功')

        except Exception as e:
            logging.error(f'操作 {op_type} 执行失败: {str(e)}')
            raise


if __name__ == '__main__':
    logging.info("调用 {}".format(sys.argv[1:]))
    op_type = sys.argv[1]
    module_name = sys.argv[2]
    br_name = sys.argv[3]
    suite_code = sys.argv[4]
    # op_type = 'pull_image_to_pod'
    # module_name = 'mantis'
    # br_name = '2.109.3'
    # suite_code = 'pd-prod'
    app = ProdDockerPublish(module_name, br_name, suite_code)
    app.deploy_app(op_type)
