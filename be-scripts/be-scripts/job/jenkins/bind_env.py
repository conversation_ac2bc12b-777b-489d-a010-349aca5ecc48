#
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from dao.connect.mysql_pipeline_env import PipelineEnv

if __name__ == '__main__':
    pipelineId = sys.argv[1]
    envs = sys.argv[2]
    operator = sys.argv[3]

    mysql_worker = PipelineEnv()
    mysql_worker.delete_pipeline_env_bind(pipelineId)
    for env in envs.split(','):
        if env:
            mysql_worker.create_pipeline_env_bind(pipelineId, env, operator)
    mysql_worker.close_mysql()
