import re
import sys
import os
import json
import datetime


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)


from ci_pipeline.ci_pipeline_bo.compile_bo import CompileBo
from ci_pipeline.pipeline_record.pipeline_record import PipelineRecorder
from job.jenkins.abstract_pipeline import CompilePipeline
from utils.compile.mvn import compiler
from utils.release.java_artifact import JavaArtifact
from settings import logger, GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN, SYNC, LIB_REPO
from settings import PIPELINE_TEST_PUBLISH, NGINX_LIB_REPO
from test_pipeline.test_pipeline_utils.junit_test_exec import JunitTest
from test_pipeline.test_pipeline_business.batch_unit_test import IterApplyBo
from common.ext_cmd.ssh.ssh_connect import SSHConnectionManager
from common.ext_cmd.ssh.sftp import SCP
from utils.call_external_interface.call_http_api import MantisHttpCaller
from qc_mgt.modles import GuardSwitchInfo
from time import sleep
from ci_pipeline.ci_pipeline_models.iter_models import IterAppInfoModel
from common.call_api.gitlab.gitlab_api import GitLabApi
from dao.get.mysql.artifact import get_artifact_repo
from job.jenkins.jenkins_xml import JenkinsXml
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from common.files import search


class JavaCompilePipeline(CompilePipeline):

    def check(self, iteration_id, app_name, *args):
        # java_validator = JavaValidator(iteration_id, app_name)
        # if not java_validator.check_merge_to_master():
        #     exit(1)
        # if not java_validator.check_merge_to_current():
        #     exit(1)
        # if not java_validator.notify():
        #     exit(1)
        exit(0)

    def compile(self, flag_file_dir, iteration_id, app_name, sid, pom_path, module_name, *args):
        comp = compiler.Compiler(
            iteration_id=iteration_id,
            app_name=module_name,
            pom_path=pom_path,
            is_mock_compile=False
        )
        # todo 为了临时生成单测报告 设置，以后需要设置开关从前面传参进来 20220127 by 帅
        # 临时修复「编译」报没有「branch_name」的问题 zt@2022-02-10
        iter_apply_bo = IterApplyBo()
        if iter_apply_bo.branch_name in iteration_id:
            need_unit_test = 1
        else:
            need_unit_test = 0
        status, output, pkg_path = comp.compile(need_unit_test)
        if pkg_path is not None:
            logger.info("包路径为：{}".format(pkg_path))
            with open(flag_file_dir, "w") as f:
                f.write(json.dumps({"sid": sid, "pkg_path": pkg_path, "is_mock_compile": False}))
        return status, output

    def junit_and_compile(self, flag_file_dir, iteration_id, app_name, sid, pom_path, module_name, *args):
        comp = compiler.Compiler(
            iteration_id=iteration_id,
            app_name=module_name,
            pom_path=pom_path,
            is_mock_compile=False
        )
        #  单元测试用新的 方法执行
        need_unit_test = 1
        status, output, pkg_path = comp.compile(need_unit_test)
        if pkg_path:
            logger.info("包路径为：{}".format(pkg_path))
            with open(flag_file_dir, "w") as f:
                f.write(json.dumps({"sid": sid, "pkg_path": pkg_path, "is_mock_compile": False}))
        return status, output

    def junit(self, flag_file_dir, iteration_id, app_name, sid, pom_path, module_name, *args):
        comp = JunitTest(
            iteration_id=iteration_id,
            app_name=module_name,
            pom_path=pom_path,
            is_mock_compile=False
        )
        status, output, pkg_path = comp.exec_junit_test()
        return status, output

    def record_report_info(self, *args):
        status, output = JunitTest.record_report_info()
        return status, output

    def push_product(self, flag_file_dir, iteration_id, app_name, sid, *args):
        # 打包成功后先清空iter_mgt_iter_app_info.git_repo_version
        iai = IterAppInfoModel.get_or_none(IterAppInfoModel.pipeline_id == iteration_id,
                                           IterAppInfoModel.appName == app_name)
        git_repo_version = iai.git_repo_version
        git_last_version = iai.git_last_version
        q = IterAppInfoModel.update({IterAppInfoModel.git_repo_version: '', IterAppInfoModel.git_repos_time: ''}) \
            .where(IterAppInfoModel.pipeline_id == iteration_id,
                   IterAppInfoModel.appName == app_name)
        q.execute()

        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
            pkg_path = json_dict["pkg_path"]
            logger.info("获取到文件中包路径为 {}".format(pkg_path))

        if pkg_path:
            package_stamp = self.__check_package_stamp(pkg_path, iteration_id, app_name)
            if not package_stamp:
                return 'failure', "上线包中没有添加构建版本号，详情参考： http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=63715980"

            java_artifact = JavaArtifact(iteration_id, app_name, pkg_path)
            if java_artifact.push_to_repo(git_last_version):
                java_artifact.record_info(package_stamp)

            # 插入一步备份config.xml的步骤 20230513 by fwm
            # jenkins pipeline 已国际化处理，备份功能下线 20230921 by fwm
            # self.__backup_jenkins_config_xml(iteration_id, app_name)

            # 增加推包是否成功校验 20230214 by fwm
            if self.__check_git_version_change(app_name, iteration_id, git_repo_version):
                return 'success', "包路径为{}".format(pkg_path)
            else:
                return 'failure', "推包出现异常，请联系PA排查解决！"

        else:
            logger.error("未找到包")
            return 'failure', "未找到包"

    def __check_git_version_change(self, app_name, iteration_id, git_repo_version):
        gl = GitLabApi(GITLAB_LIB_HTTP, GITLAB_LIB_TOKEN)
        git_repo = SYNC.get("sync_gitlab_group") + '/' + get_artifact_repo(app_name)
        commit_info = gl.get_one_git_repos_last_version(br_name=iteration_id.split("_")[-1],
                                                        git_repos=git_repo)
        commit_id = commit_info.get(git_repo)
        logger.info("上次的制品提交版本：{}".format(git_repo_version))
        logger.info("制品最新提交版本: {}".format(commit_id))

        # 编译前后的版本号发生变化，说明制品编译并提交成功
        if git_repo_version != commit_id:
            return True
        else:
            logger.info("制品包没有发生变化，不允许提交！")
            return False

    def __check_package_stamp(self, pkg_path, iteration_id, app_name):
        jenkins_workspace = os.environ["WORKSPACE"]
        check_package_stamp_dir = LIB_REPO.get("check_package_stamp_dir")
        repo_cache_dir = os.path.join(jenkins_workspace, check_package_stamp_dir)
        if not os.path.isdir(repo_cache_dir):
            logger.info("创建迭代应用的检测目录： {}".format(repo_cache_dir))
            os.makedirs(repo_cache_dir)

        cmd = 'cp -r {} {}'.format(pkg_path, repo_cache_dir)
        logger.info('拷贝包文件：{}'.format(cmd))
        os.system(cmd)

        app_file_name = pkg_path.split("/")[-1]

        os.chdir(repo_cache_dir)
        logger.info("解压应用包文件: {}".format(app_file_name))
        if pkg_path.endswith('jar') or pkg_path.endswith('war'):
            cmd = "jar -xvf {}".format(app_file_name)
            os.system(cmd)
        elif pkg_path.endswith('tar'):
            # tar包暂时不检测 20230824 by fwm
            # cmd = "tar -xvf {}".format(app_file_name)
            # os.system(cmd)
            return True

        for file_path in search.file_search(repo_cache_dir, file_name='MANIFEST.MF'):
            with open(file_path, "r+", encoding='utf-8', errors="ignore") as f:
                file_content = f.read()
                match = re.search(r"Package-Stamp: (\d+)", file_content)
                if match:
                    package_stamp = match.group(1)
                    logger.info("找到Package-Stamp: {}".format(package_stamp))
                    return package_stamp
        return False


    def __backup_jenkins_config_xml(self, iteration_id, app_name):
        jenkins_job_mgt = JenkinsJobMgt()
        job_name = "_".join((iteration_id, app_name))
        jenkins_server_info = jenkins_job_mgt.get_jenkins_server_info_by_job_name(job_name)
        jenkins_xml = JenkinsXml()
        jenkins_xml._scp_jenkins_config_xml(job_name, jenkins_server_info.server)

    def push_test_report(self, flag_file_dir, iteration_id, app_name, sid, *args):
        jenkins_workspace = os.environ["WORKSPACE"]
        test_report_src = os.path.join(jenkins_workspace, "target/allure-report-unit")
        time_stamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        report_file_name = 'allure-report-unit-{}.zip'.format(time_stamp)
        test_report_src_zip = os.path.join(jenkins_workspace, report_file_name)
        # zip -r 表示递归压缩 -D
        zip_cmd = 'cd {} && zip -r {} *'.format(test_report_src, test_report_src_zip)
        logger.info(zip_cmd)
        # 20220414 by shuai
        try:
            os.system(zip_cmd)
        except Exception as e:
            logger.error("zip 失败")
            raise str(e)
        # 有单元测试报告 则推送的 NG制品库 by 帅 20220406
        if os.path.exists(test_report_src_zip):
            test_report_target = os.path.join(NGINX_LIB_REPO["root_path"],
                                              NGINX_LIB_REPO["test_report_dir"],
                                              app_name,
                                              iteration_id,
                                              report_file_name)
            logger.info(test_report_target)
            with SSHConnectionManager(NGINX_LIB_REPO["ip"], NGINX_LIB_REPO["username"],
                                      NGINX_LIB_REPO["password"]) as ssh:
                scp = SCP(ssh.SFTP)
                # 先创建目录目录 在ssh copy报告
                if not scp.is_file_found(os.path.dirname(test_report_target)):
                    scp.mkdir_p(os.path.dirname(test_report_target))
                scp.push_file(test_report_src_zip, test_report_target)
            report_size = os.path.getsize(test_report_src_zip)
            test_report_url = os.path.join(NGINX_LIB_REPO["base_url"],
                                           NGINX_LIB_REPO["test_report_dir"],
                                           app_name,
                                           iteration_id,
                                           report_file_name)
            #
            # 记录文件 报告路径
            with open(flag_file_dir, "r+") as f:
                json_dict = json.loads(f.read())
                json_dict["test_report_url"] = test_report_url
                f.seek(0, 0)
                f.truncate()
                f.write(json.dumps(json_dict))
            # 记录单元测试报告制品数据 by帅 20220406
            JunitTest.record_report_info(iteration_id, app_name, test_report_url, report_size)
            return 'success', "测试报告下载路径{}".format(test_report_url)
        else:
            logger.error("未找到包")
            return 'failure', "未找到包"

    def analyze_test_report(self, flag_file_dir, iteration_id, app_name, sid, *args):
        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
            test_report_url = json_dict["test_report_url"]

        http_caller = MantisHttpCaller()
        data = {"business_name": "analyze_unit_test_data",
                "app_name": app_name,
                "iteration_id": iteration_id,
                "lib_repo_url": test_report_url
                }
        try:
            http_caller.login()
            res = http_caller.request("analyze_unit_test_data", json=data)
            logger.info(res)
            if res["status"] == "success":
                return 'success', res["msg"]
            else:
                return 'failure', res["msg"]
        except Exception as e:
            logger.error(e)
            return 'failure', "分析失败"

    def check_unit_test_result(self, flag_file_dir, iteration_id, app_name, sid, *args):
        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
            lib_repo_url = json_dict["test_report_url"]

        http_caller = MantisHttpCaller()
        params = {"app_name": app_name,
                  "iteration_id": iteration_id,
                  "lib_repo_url": lib_repo_url
                  }
        try:
            http_caller.login()
            sleep(3)
            res = http_caller.request("check_unit_test_result", params=params)
            logger.info(res)

            if GuardSwitchInfo.get(GuardSwitchInfo.guard_name == 'unit_test').guard_switch:
                if res["status"] == "success":
                    return 'success', res["msg"]
                else:
                    return 'failure', res["msg"]
            else:
                return 'success', '单元测试开关关闭，不检测结果！'
        except Exception as e:
            logger.error(e)
            return 'failure', "分析失败"

    def create_jenkins_pipeline(self, iteration_id, app_name, *args):
        exit(0)

    @PipelineRecorder()
    def run_step(self, step_name, compile_bo):
        acceptor = {
            "check": self.check,
            "junit": self.junit,
            "compile": self.compile,
            "junit_and_compile": self.junit_and_compile,
            "push_product": self.push_product,
            "push_test_report": self.push_test_report,
            "record_report_info": self.record_report_info,
            "analyze_test_report": self.analyze_test_report,
            "check_unit_test_result": self.check_unit_test_result
        }
        exec_status, exec_msg = acceptor[step_name](compile_bo.flag_file_dir, compile_bo.iteration_id,
                                                    compile_bo.app_name, compile_bo.sid,
                                                    compile_bo.pom_path, compile_bo.module_name)
        return exec_status, exec_msg

    def call(self, params):
        # 修改为传入值
        flag_file_dir = params[1]
        args_list = params[2].split("_")
        iteration_id = "_".join(args_list[:-1])
        app_name = args_list[-1]
        if "{" in app_name:
            app_name = app_name.split("{")[0]
        with open(flag_file_dir, "r") as f:
            json_dict = json.loads(f.read())
        sid = json_dict["sid"]

        compile_bo_builder = CompileBo.Builder().set_app_name(app_name).set_iteration_id(iteration_id) \
            .set_sid(sid).set_flag_file_dir(flag_file_dir)
        if params[0] in ("compile", "junit", "junit_and_compile"):
            module_name = params[3]
            pom_path = params[4]
            compile_bo = compile_bo_builder.set_module_name(module_name).set_pom_path(pom_path).build_bo()
        else:
            compile_bo = compile_bo_builder.build_product_bo()
        self.run_step(params[0], compile_bo)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    pipeline = JavaCompilePipeline()
    pipeline.call(sys.argv[1:])
