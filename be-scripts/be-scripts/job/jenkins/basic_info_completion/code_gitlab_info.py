import sys
import os
import requests

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql.app_info import get_all_projects_without_project_id
from dao.update.mysql.app_info import update_project_id
from settings import GITLAB_TOKEN, GITLAB_HTTP, logger


class CodeProjectInfo:
    url_project = '{host}/api/v4/projects?search_namespaces=true&search={group}{repository}'
    headers = {
        "PRIVATE-TOKEN": GITLAB_TOKEN
    }

    def complete_project_id(self):
        project_list = get_all_projects_without_project_id()
        if project_list:
            logger.info("总共需要处理{}条记录".format(len(project_list)))
            self.__update_projects(project_list)
        else:
            logger.info("没有需要处理的记录")

    def __get_project_id_by_path(self, group, repository):

        url = self.url_project.format(host=GITLAB_HTTP, group=group, repository=repository)

        try:
            response = requests.get(url, headers=self.headers)
            data = response.json() if response.status_code == 200 else None
            if data:
                for e in data:
                    if e["path_with_namespace"] == "{}{}".format(group, repository):
                        return e["id"]

            return -1

        except Exception as e:
            logger.error("{}{}获取project_id失败".format(group, repository))
            return -1

    def __update_projects(self, project_list):

        for project in project_list:
            project_id = self.__get_project_id_by_path(project["git_url"], project["git_path"])

            try:
                update_project_id(project["id"], project_id)
            except:
                logger.error("{}{}项目写入project_id失败".format(project["git_url"], project["git_path"]))
                continue

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))

        acceptor = {
            "complete_project_id": self.complete_project_id
        }

        acceptor[params[0]]()


if __name__ == '__main__':
    c = CodeProjectInfo()
    c.call(sys.argv[1:])
    #c.complete_project_id()



"""
pipeline {
    agent any
    stages{
        stage('update') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/basic_info_completion/code_gitlab_info.py 'complete_project_id' '''
            }
        }
    }
}
"""