import sys
import os
import requests

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.get.mysql import app_info
from dao.update.mysql import iterative_pipeline_branchincludesys
from settings import GITLAB_TOKEN, GITLAB_HTTP, logger


class IterMergeInfo:
    url_commit = '{host}/api/v4/projects/{project_id}/repository/commits/{commit_id}/refs'
    headers = {
        "PRIVATE-TOKEN": GITLAB_TOKEN
    }
    key_tem = "{}_{}"

    def complete_master_merged(self, interval_minute):
        iter_modules = app_info.get_project_ids(interval_minute)
        logger.info("本次处理迭代{}个".format(len(iter_modules)))
        project_dict = {}
        for iter_module in iter_modules:
            if iter_module["archived_commit"]:
                project_dict[self.key_tem.format(iter_module["project_id"], iter_module["archived_commit"])] = {
                    "project_id": iter_module["project_id"], "archived_commit": iter_module["archived_commit"]}

        for project in project_dict.values():
            project["merged_branches"] = self.__get_merged_branchs(project["project_id"], project["archived_commit"])

        for iter_module in iter_modules:
            if iter_module["archived_commit"]:
                k = self.key_tem.format(iter_module["project_id"], iter_module["archived_commit"])
                iter_module["merged_master"] = self.__is_merged(project_dict[k]["merged_branches"],
                                                                iter_module["br_name"])
                iterative_pipeline_branchincludesys.update_merged_master(iter_module["id"],
                                                                         iter_module["merged_master"])

    def __is_merged(self, merged_branches, br_name):
        for e in merged_branches:
            if e["name"] == br_name:
                return True

        return False

    def __get_merged_branchs(self, project_id, commit_id):
        url = self.url_commit.format(host=GITLAB_HTTP, project_id=project_id, commit_id=commit_id)

        try:
            response = requests.get(url, headers=self.headers)
            return response.json() if response.status_code == 200 else []

        except Exception as e:
            logger.error("{}_{}获取提交失败".format(project_id, commit_id))
            return []

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))

        acceptor = {
            "complete_master_merged": self.complete_master_merged
        }

        acceptor[params[0]](params[1])


if __name__ == '__main__':
    i = IterMergeInfo()
    i.call(sys.argv[1:])

"""
pipeline {
    agent any
    stages{
        stage('update') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/basic_info_completion/iter_merge_info.py 'complete_master_merged' 10 '''
            }
        }
    }
}
"""
