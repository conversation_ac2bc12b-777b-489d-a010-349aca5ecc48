import sys
import os

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from datetime import timedelta

import jenkins
from ci_pipeline.ci_pipeline_models.pipeline_log_model import PipelineLogMain
from dao.get.mysql.pipeline_log_main import get_last_running_pipelines, get_last_running_pipelines_with_sid
from jenkins_mgt.models import JenkinsInfo
from settings import logger


class JenkinsPipelineStatus:
    key_word = "缓存文件内容是 {'sid': "

    def __init__(self):
        self.jks_dict = {}
        jk_list = JenkinsInfo.raw("select * from jenkins_mgt_jenkins_info where jenkins_state = 1")
        for jk in jk_list:
            jks = jenkins.<PERSON>(username=jk.username, password=jk.password, url=jk.jenkins_url, timeout=50000)
            self.jks_dict[jk.jenkins_url] = jks

    def __get_last_status_by_job(self):
        return

    def update_running_jobs_number(self):
        job_list = get_last_running_pipelines()
        logger.info("update_running_jobs_number 本次处理 {} 条".format(len(job_list)))
        for job in job_list:
            try:
                if job.exec_jenkins in self.jks_dict.keys():
                    if self.jks_dict.get(job.exec_jenkins).job_exists(job.job_name):
                        last = self.jks_dict.get(job.exec_jenkins).get_job_info(job.job_name).get("lastBuild")["number"]
                        info = self.jks_dict.get(job.exec_jenkins).get_build_info(job.job_name, last)
                        if info["result"] == 'FAILURE' or info["result"] == 'ABORTED':
                            plm = PipelineLogMain.get(sid=job.sid)
                            plm.update_memo = '流水线已失败'
                            plm.status = info["result"].lower()
                            plm.end_at = plm.start_at + timedelta(seconds=info["duration"] / 1000)
                            plm.save()
                            logger.info("SID {} 流水线置失败".format(job.sid))
                        else:
                            text = self.jks_dict.get(job.exec_jenkins).get_build_console_output(job.job_name, last)
                            if self.key_word in text:
                                sid = text.split(self.key_word)[1].split("}")[0]
                                if str(job.sid) == sid:
                                    plm = PipelineLogMain.get(sid=job.sid)
                                    plm.update_memo = '更新sid'
                                    plm.job_number = last
                                    plm.save()
                                    logger.info("SID {} 更新了JOB号 {}".format(job.sid, last))
                            else:
                                logger.info("SID {} 没定位到确切number".format(job.sid))

                    else:
                        plm = PipelineLogMain.get(sid=job.sid)
                        plm.update_memo = '流水线已删除'
                        plm.status = 'aborted'
                        plm.end_at = plm.start_at + timedelta(seconds=600)
                        plm.save()
                        logger.info("SID {} 流水线已删除".format(job.sid))
            except Exception as err:
                logger.error("SID {} 处理失败".format(job.sid))
                logger.error(err)

        return

    def update_running_jobs_status_with_sid(self):
        job_list = get_last_running_pipelines_with_sid()
        logger.info("update_running_jobs_status_with_sid 本次处理 {} 条".format(len(job_list)))
        for job in job_list:
            try:
                if job.exec_jenkins in self.jks_dict.keys():
                    if self.jks_dict.get(job.exec_jenkins).job_exists(job.job_name):
                        info = self.jks_dict.get(job.exec_jenkins).get_build_info(job.job_name, job.job_number)
                        if info["result"] == 'FAILURE' or info["result"] == 'ABORTED':
                            plm = PipelineLogMain.get(sid=job.sid)
                            plm.update_memo = '流水线已失败'
                            plm.status = info["result"].lower()
                            plm.end_at = plm.start_at + timedelta(seconds=info["duration"] / 1000)
                            plm.save()
                            logger.info("SID {} 流水线置失败".format(job.sid))
                        else:
                            logger.info("SID {} 运行正常无需调整".format(job.sid))
            except Exception as err:
                logger.error("SID {} 处理失败".format(job.sid))
                logger.error(err)

    def test(self):
        a = self.jks1.get_job_info(name="mtx_s-1.61.3_zeus-service")
        print(self.jks1)

    def call(self, params):
        logger.info("参数总数 {}".format(len(params)))

        acceptor = {
            "update_running_jobs_number": self.update_running_jobs_number,
            "update_running_jobs_status_with_sid": self.update_running_jobs_status_with_sid
        }

        acceptor[params[0]]()


if __name__ == '__main__':
    jps = JenkinsPipelineStatus()
    jps.call(sys.argv[1:])
    # jps.update_running_jobs_number()
    # jps.update_running_jobs_status_with_sid()

"""
pipeline {
    agent any
    stages{
        stage('更新NUMBER') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/basic_info_completion/jenkins_pipeline_status.py 'update_running_jobs_number' '''
            }
        }
        stage('更新状态') {
            steps {
                sh label: '', script: '''python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/basic_info_completion/jenkins_pipeline_status.py 'update_running_jobs_status_with_sid' '''
            }
        }
    }
}
"""
