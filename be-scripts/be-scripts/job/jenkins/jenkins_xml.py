import sys
import os
import datetime
import json


PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)


from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from utils.compile.mvn import pom_sorter
from utils.jenkins import pipeline
from settings import logger
from dao.insert.mysql.pipeline_log import insert_pipeline_log_main, insert_pipeline_log_minor
from dao.update.mysql.pipeline_log import update_pipeline_minor_log
from dao.connect.mysql import DBConnectionManager
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_scp_from_remote



class JenkinsXml:
    __jenkins_job_mgt = JenkinsJobMgt()

    def update_pipeline(self, iteration_id, app_name, job_name, workspace, flag_path, cache_data_path, suite_code,
                        **params):
        logger.info(os.environ["NODE_NAME"])
        node_name = os.environ["NODE_NAME"]
        jenkins_server_info = self.__jenkins_job_mgt.get_jenkins_server_info_by_job_name(job_name)
        sid = self.get_log_main_inserted_sid(iteration_id, app_name, job_name, jenkins_server_info.jenkins_url)

        res = self._get_pom_sorted_result(iteration_id, app_name, workspace)
        # 单元测试的步骤固化，取消传参判断 20221222 by fwm
        # 存在is_junit 参数 并且为 True
        #
        # logger.info("单元测试参数 {}".format(params["is_junit"]))
        # if params["is_junit"] == "true":

        pipeline_updater = pipeline.PipelineJunitCompileUpdater(res, jenkins_server_info.server, job_name, flag_path,
                                                                suite_code=suite_code,
                                                                workspace=cache_data_path)
        # else:
        # pipeline_updater = pipeline.PipelineUpdater(res, jenkins_server_info.server, job_name, flag_path,
        #                                             suite_code=suite_code,
        #                                             workspace=cache_data_path)
        # self._scp_jenkins_config_xml(job_name, jenkins_server_info.server, 'before_read')

        pipeline_updater.update_pipeline(parameters={"is_junit": params["is_junit"],
                                                     "is_mock_agent": params["is_mock_agent"],
                                                     "label": node_name, "is_code_scan": params["is_code_scan"],
                                                     "dump_bis_code": params["dump_bis_code"],
                                                     "db_exec_type": params["db_exec_type"],
                                                     })

        with open(flag_path, "w") as f:
            f.write(json.dumps({"sid": sid}))

        flag_path_cp = flag_path + ".cp"
        if os.path.isfile(flag_path_cp):
            os.remove(flag_path_cp)

        with open(flag_path_cp, "w") as f:
            f.write(json.dumps({"sid": sid}))

        try:
            update_pipeline_minor_log(sid, "解析{}".format(job_name), "解析流水线完成", datetime.datetime.now(),
                                      "success")
            # 插入一步备份config.xml的步骤 20230513 by fwm
            # pipeline groovy已经改英文，不需要再备份config.xml 20231222 by fwm
            # self._scp_jenkins_config_xml(job_name, jenkins_server_info.server, 'after_read')
        except Exception as e:
            logger.error(str(e))

    def _scp_jenkins_config_xml(self, job_name, server, extend_name=None):

        source_file_path = os.path.join('/data/jenkins_ws/jobs', job_name, 'config.xml')
        jenkins_master_ip = self.__get_jenkins_master_ip(job_name)
        last_build_number = server.get_job_info(job_name).get("lastBuild").get("number")
        target_path = os.path.join('/data/jenkins_config_xml_backup', job_name)

        if not os.path.isdir(target_path):
            cmd = 'mkdir -p {}'.format(target_path)
            os.system(cmd)
        if extend_name:
            target_file_path = os.path.join(target_path, str(last_build_number) + '_{}_config.xml'.format(extend_name))
        else:
            target_file_path = os.path.join(target_path, str(last_build_number) + '_config.xml')

        exec_local_scp_from_remote(jenkins_master_ip, source_file_path, target_file_path)

    def __get_jenkins_master_ip(self, job_name):
        sql = ''' 
                SELECT i.jenkins_host FROM jenkins_mgt_jenkins_info i
                INNER JOIN jenkins_mgt_jenkins_job_info ji ON i.id = ji.jenkins_info_id
                WHERE ji.job_name = '{}'
            '''.format(job_name)

        with DBConnectionManager() as db:
            db.cur.execute(sql)
            data = db.cur.fetchone()
        return data.get("jenkins_host")

    def get_log_main_inserted_sid(self, iteration_id, app_name, job_name, jenkins_url):
        try:
            sid = insert_pipeline_log_main(exec_jenkins=jenkins_url, exec_parameter=job_name,
                                           start_at=datetime.datetime.now(), iteration_id=iteration_id,
                                           app_name=app_name, status="running", suite_name="")
            insert_pipeline_log_minor(sid, "解析{}".format(job_name), datetime.datetime.now(), app_name)
        except Exception as e:
            logger.error(str(e))

        return sid

    def _get_pom_sorted_result(self, iteration_id, app_name, workspace):

        pom_processor = pom_sorter.PomSorter(
            workspace=workspace,
            pipeline_id=iteration_id,
            app_name=app_name
        )
        res = pom_processor.main()

        return res

    def call(self, params):
        step_name = params[0]
        job_name = params[1]
        iteration_id = "_".join(job_name.split("_")[:-1])
        app_name = job_name.split("_")[-1]
        workspace = params[2]
        flag_path = params[3]
        cache_data_path = params[4]
        suite_code = params[5]

        self.update_pipeline(iteration_id, app_name, job_name, workspace, flag_path, cache_data_path, suite_code,
                             is_junit=params[6], is_mock_agent=params[7], is_code_scan=params[8],
                             dump_bis_code=params[9],db_exec_type=params[10])
    # def call(self, params):
    #     acceptor = {
    #         "update_pipeline": self.update_pipeline,
    #     }
    #     job_name = params[1]
    #     args_list = job_name.split("_")
    #     group_name = args_list[0]
    #     if params[0] == "update_pipeline_iteration":
    #         br_name = params[3]
    #         acceptor[params[0]](group_name, br_name, job_name, params[2], params[4])
    #
    #     else:
    #         iteration_id = "_".join(args_list[:-1])
    #         app_name = args_list[-1]
    #         if "{" in app_name:
    #             app_name = app_name.split("{")[0]
    #         logger.info("迭代版本为{}".format(iteration_id))
    #         logger.info("应用为{}".format(app_name))
    #         if params[0] == "update_mock_pipeline":
    #             acceptor[params[0]](iteration_id, app_name, job_name, params[2], params[3], params[4], params[5],
    #                                 params[6])
    #         elif params[0] in ("update_pipeline", "update_junit_pipeline"):
    #             if len(params) == 6:
    #                 acceptor[params[0]](iteration_id, app_name, job_name, params[2], params[3], params[4], params[5])
    #             elif len(params) == 7:
    #                 acceptor[params[0]](iteration_id, app_name, job_name, params[2], params[3], params[4], params[5],
    #                                     params[6])
    #             elif len(params) == 8:
    #                 acceptor[params[0]](iteration_id, app_name, job_name, params[2], params[3], params[4], params[5],
    #                                     params[6], params[7])
    #         else:
    #             acceptor[params[0]](iteration_id, app_name, job_name, params[2], params[3])


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    jenkins_xml = JenkinsXml()
    jenkins_xml.call(sys.argv[1:])
    # jenkins_xml._get_jenkins_master_ip('ztst_patest051204_howbuy-wechat-message-sample')
    # test_list = ["update_mock_pipeline", "otc_0.0.2_otc-batch-remote",
    #              "/home/<USER>/.jenkins/workspace/otc_0.0.2_otc-batch-remote",
    #              "/home/<USER>/dump/otc_0.0.2_otc-batch-remote.txt",False]
    # jenkins_xml.call(test_list)
