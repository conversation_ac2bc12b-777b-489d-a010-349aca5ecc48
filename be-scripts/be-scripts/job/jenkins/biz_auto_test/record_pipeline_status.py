import datetime
import json
import sys
import os
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from settings import logger, ROCKETMQ
from jenkins_mgt.models import JenkinsMgtBizJobInfoLog, JenkinsMgtBizJobRunTestset
from rocketmq.client import Producer, Message
from job.iter_mgt.models import TaskMgtRocketmqInfoLog
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import check_test_set_run_result, abolish_test_set
from job.jenkins.biz_auto_test.self_define_parse import TestSetRunStatus


class PipelineStatusRecorder:

    def record_status(self, job_name, status, job_build_id):
        with DBConnectionManagerForSqlalchemy() as db:
            jenkinsMgtBizJobInfoLog = (
                db.session.query(JenkinsMgtBizJobInfoLog)
                .filter_by(job_build_id=job_build_id, job_name=job_name)
                .order_by(JenkinsMgtBizJobInfoLog.id.desc())
                .first()
            )
            if jenkinsMgtBizJobInfoLog:
                logger.info("记录流水线状态：{}".format(status))
                jenkinsMgtBizJobInfoLog.status = status
                db.session.flush()
                db.session.commit()

    def send_mock_env_info_to_rmq(self, suite_code):
        """
        发送mock环境信息到mq
        :return:
        """
        logger.info("发送mock环境信息到mq")
        topic = ROCKETMQ['mock_env_topic']
        namesrv = ROCKETMQ['namesrv'] if ROCKETMQ['namesrv'] else ""
        namesrv_domain = ROCKETMQ['namesrv_domain'] if ROCKETMQ['namesrv_domain'] else ""
        group_id = topic + "_GROUP"
        try:
            pd = Producer(group_id, timeout=6000)
            if namesrv:
                logger.info("namesrv: {}".format(namesrv))
                pd.set_namesrv_addr(namesrv)
            else:
                logger.info("namesrv_domain: {}".format(namesrv_domain))
                pd.set_namesrv_domain(namesrv_domain)
            pd.start()
            mock_env_info = {
                "env": suite_code,
                "autoTestOpen": False
            }
            msg_body = json.dumps(mock_env_info).encode('utf-8')
            msg = Message(topic)
            msg.set_body(msg_body)

            ret = pd.send_sync(msg)
            # ret = pd.send_orderly(msg, 0)

            rmq_id = TaskMgtRocketmqInfoLog(name_server=namesrv, name_server_domain=namesrv_domain,
                                            group=group_id, topic=topic, message=msg_body, status=ret.status,
                                            msg_id=ret.msg_id, offset=ret.offset, creator="be-script",
                                            create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            rmq_id.save()
            pd.shutdown()
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)

    def update_testset_run_status(self, job_name):
        workspace = os.environ.get('WORKSPACE')
        logger.info("workspace: {}".format(workspace))
        update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        update_user = 'howbuyscm'
        file_path = os.path.join(workspace, job_name + "_testset_run_status_map.json")
        try:
            with open(file_path, "r", encoding='utf-8') as f:
                testset_run_status_map = json.loads(f.read())
        except FileNotFoundError:
            # 如果文件不存在，初始化为空字典
            testset_run_status_map = {}
        execute_id_list = []
        with DBConnectionManagerForSqlalchemy() as db:
            try:
                for k, v in testset_run_status_map.items():
                    jenkinsMgtBizJobRunTestset = db.session.query(JenkinsMgtBizJobRunTestset).filter(
                        JenkinsMgtBizJobRunTestset.execute_id == k).first()
                    if jenkinsMgtBizJobRunTestset:
                        execute_id_list.append(k)
                        if v == TestSetRunStatus.SUCCESS.status_name:
                            # 更新JenkinsMgtBizJobRunTestset中execute_id为k的记录的testset_run_status为TestSetRunStatus.SUCCESS.status_name
                            jenkinsMgtBizJobRunTestset.testset_run_status = TestSetRunStatus.SUCCESS.status_name
                        else:
                            jenkinsMgtBizJobRunTestset.testset_run_status = TestSetRunStatus.FAILURE.status_name
                        jenkinsMgtBizJobRunTestset.update_time = update_time
                        jenkinsMgtBizJobRunTestset.update_user = update_user
                # 批量提交所有更新
                db.session.commit()
            except Exception as e:
                logger.error(f"更新测试集状态失败: {str(e)}")
                db.session.rollback()
                raise
        return execute_id_list

    def notify_abolish_test_set(self, execute_id_list):
        """
        通知QA取消测试集执行
        :return:
        """
        logger.info("通知QA取消测试集执行: {}".format(execute_id_list))
        res = check_test_set_run_result(execute_id_list)
        running_executor_ids = []
        if res.get("code") == 200:
            for item in res.get("data"):
                logger.info("测试集运行状态检测结果： {}".format(item.get("status")))
                if 'running' == item.get("status"):
                    running_executor_ids.append(item.get("executeId"))
        abolish_test_set(running_executor_ids)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    job_name = sys.argv[1]
    status = sys.argv[2]
    job_build_id = sys.argv[3]
    suite_code = sys.argv[4]

    psr = PipelineStatusRecorder()

    psr.record_status(job_name, status, job_build_id)
    psr.send_mock_env_info_to_rmq(suite_code)
    execute_id_list = psr.update_testset_run_status(job_name)
    # 通知QA
    psr.notify_abolish_test_set(execute_id_list)
