from peewee import *
from dao.base_model import SpiderBaseModels


class BizTestFlowDumpInfo(SpiderBaseModels):
    biz_pipeline_name = CharField(verbose_name='jenkins_job名字', max_length=255)
    jenkins_build_id = IntegerField(verbose_name='jenkins_build_id')
    batch_no = BigIntegerField(verbose_name='批次号')
    suite_code = CharField(verbose_name='环境名称', max_length=50)
    db_name = CharField(verbose_name='逻辑数据库名', max_length=50)
    suite_db_name = CharField(verbose_name='实际数据库名', max_length=100)
    db_srv_hosts = CharField(verbose_name='数据库主机：IP或者域名', max_length=100)
    data_dump_dir = CharField(verbose_name='dump文件路径', max_length=500)
    dump_file_name = CharField(verbose_name='dump文件名', max_length=100)

    class Meta:
        table_name = 'biz_test_flow_dump_info'
        verbose_name = '编排线dump记录信息表'

