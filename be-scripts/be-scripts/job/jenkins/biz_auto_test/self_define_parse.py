import datetime
import sys
import os
import json
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, ROCKETMQ
from dao.connect.mysql import DBConnectionManager
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from jenkins_mgt.models import JenkinsMgtBizJobInfoLog
from job.pipeline_log.pipeline_log_decorator_mgt import BizAutoTestPipelineLog
from dao.get.mysql import db_mgt_bind_view
from test_publish_aio.test_publish_aio_models.test_publish_ser import db_info_reset
from rocketmq.client import Producer, Message
from job.iter_mgt.models import TaskMgtRocketmqInfoLog
from pathlib import Path
from enum import Enum


class TestSetRunStatus(Enum):
    SUCCESS = ('success', '成功')
    FAILURE = ('failure', '失败')
    RUNNING = ('running', '运行中')

    def __init__(self, status_name, status_name_desc):
        self.status_name = status_name
        self.status_name_desc = status_name_desc


class SelfDefineParse:
    def __init__(self, workspace, job_name, suite_code, biz_test_iter_id, job_build_id, batch_no):
        self.workspace = workspace
        self.job_name = job_name
        self.suite_code = suite_code
        self.biz_test_iter_id = biz_test_iter_id
        self.job_build_id = job_build_id
        self.step_order = "0"
        self.batch_no = batch_no

    @BizAutoTestPipelineLog(step_name="prepare_data")
    def prepare_data(self):
        """
        解析数据并存入缓存文件
        :return:
        """
        # biz_name = self.job_name.split("_")[0]
        # biz_flow_name = self.job_name.split("_")[1]
        sql = '''
                SELECT CONCAT(c.order_num, '-', a.order_num) AS step, a.action_name, a.action, p.param 
                FROM biz_test_flow_action a
                LEFT JOIN biz_test_flow_action_param p ON p.action_id = a.id
                INNER JOIN biz_test_flow_config c ON a.flow_config_id = c.id
                INNER JOIN biz_test_flow_info f ON c.flow_id = f.id
                WHERE f.biz_pipeline_name = '{}'
                ORDER BY step;
              '''.format(self.job_name)

        query_biz_bind_app_sql = '''
            SELECT b.app_module_name FROM biz_app_bind b 
            INNER JOIN biz_test_iter i ON b.biz_code = i.biz_code
            WHERE i.biz_test_iter_id = '{}' AND b.biz_app_bind_is_active = 1;
            '''.format(self.biz_test_iter_id)

        query_biz_job_run_testset_sql = '''
                                            SELECT batch_no, testset_id, order_no, execute_id 
                                            FROM jenkins_mgt_biz_job_run_testset
                                            WHERE batch_no = '{}';
                                        '''.format(self.batch_no)

        testset_detail_sql = '''
                                SELECT d.testset_id, d.app_name, d.script_branch, d.version_type 
                                FROM biz_test_flow_testset_detail d
                                INNER JOIN biz_test_flow_info t ON d.flow_id = t.id
                                WHERE t.biz_pipeline_name = '{}';
                             '''.format(self.job_name)

        param_dict = {}
        with DBConnectionManager() as db:
            db.cur.execute(testset_detail_sql)
            testset_detail_map = {}
            testset_run_status_map = {}
            for row in db.cur.fetchall():
                testset_id_value = {"appName": row.get("app_name"), "scriptBranch": row.get("script_branch")}
                if row.get("version_type"):
                    testset_id_value.update({"versionType": row.get("version_type")})
                if row.get("testset_id") not in testset_detail_map:
                    testset_detail_map.update({row.get("testset_id"): [testset_id_value]})
                else:
                    testset_detail_map.get(row.get("testset_id")).append(testset_id_value)

            logger.info(query_biz_job_run_testset_sql)
            db.cur.execute(query_biz_job_run_testset_sql)
            testset_order_map = {}
            for row in db.cur.fetchall():
                testset_order_map.update(
                    {str(row.get("order_no")) + "-" + str(row.get("testset_id")): row.get("execute_id")})
                testset_run_status_map.update({row.get("execute_id"): TestSetRunStatus.RUNNING.status_name})
            logger.info(query_biz_bind_app_sql)
            db.cur.execute(query_biz_bind_app_sql)
            biz_app_list = []
            for row in db.cur.fetchall():
                biz_app_list.append(row.get("app_module_name"))
            logger.info(sql)
            db.cur.execute(sql)
            db_data = db.cur.fetchall()
            for row in db_data:
                if row.get("action") == "is_checked":
                    json_param = {"app_list": biz_app_list}
                    param_dict.update({row.get("step"): json_param})
                elif row.get("action") == "make_dump":
                    json_param = json.loads(row.get("param"))
                    logger.info("module_name_list={}, suite_code={}".format(biz_app_list, self.suite_code))
                    db_info_result = db_mgt_bind_view.get(module_name_list=biz_app_list, suite_code=self.suite_code)
                    db_info_list = db_info_reset(db_info_result)
                    json_param.update({"db_info_list": db_info_list})
                    param_dict.update({row.get("step"): json_param})
                elif row.get("action") == "run_testset":
                    json_param = json.loads(row.get("param"))
                    json_param.update({"batch_no": self.batch_no, "testset_order_map": testset_order_map,
                                       "testset_detail_map": testset_detail_map})
                    param_dict.update({row.get("step"): json_param})
                else:
                    param_dict.update({row.get("step"): row.get("param")})

        logger.info("待写入缓存的数据：{}".format(testset_run_status_map))
        with open(os.path.join(self.workspace, self.job_name + "_testset_run_status_map.json"), "w",
                  encoding='utf-8') as f:
            f.write(json.dumps(testset_run_status_map))
            logger.info("数据写入缓存文件: {}".format(
                os.path.join(self.workspace, self.job_name + "_testset_run_status_map.json")))

        with open(os.path.join(self.workspace, self.job_name + ".json"), "w", encoding='utf-8') as f:
            f.write(json.dumps(param_dict))
            logger.info("数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + ".json")))

        test_set_order_file = Path(self.workspace, self.job_name + "_testset_order.json")
        if test_set_order_file.exists():
            logger.info("测试集执行顺序文件已存在，先删除")
            os.remove(test_set_order_file)

        logger.info("发送mock环境信息到mq")
        self._send_mock_env_info_to_rmq()

    def _send_mock_env_info_to_rmq(self):
        """
        发送mock环境信息到mq
        :return:
        """
        topic = ROCKETMQ['mock_env_topic']
        namesrv = ROCKETMQ['namesrv'] if ROCKETMQ['namesrv'] else ""
        namesrv_domain = ROCKETMQ['namesrv_domain'] if ROCKETMQ['namesrv_domain'] else ""
        group_id = topic + "_GROUP"
        try:
            pd = Producer(group_id, timeout=6000)
            if namesrv:
                logger.info("namesrv: {}".format(namesrv))
                pd.set_namesrv_addr(namesrv)
            else:
                logger.info("namesrv_domain: {}".format(namesrv_domain))
                pd.set_namesrv_domain(namesrv_domain)
            pd.start()
            mock_env_info = {
                "env": self.suite_code,
                "autoTestOpen": True
            }
            msg_body = json.dumps(mock_env_info).encode('utf-8')
            msg = Message(topic)
            msg.set_body(msg_body)

            ret = pd.send_sync(msg)
            # ret = pd.send_orderly(msg, 0)

            rmq_id = TaskMgtRocketmqInfoLog(name_server=namesrv, name_server_domain=namesrv_domain,
                                            group=group_id, topic=topic, message=msg_body, status=ret.status,
                                            msg_id=ret.msg_id, offset=ret.offset, creator="be-script",
                                            create_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
            rmq_id.save()
            pd.shutdown()
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    job_name = sys.argv[1]
    workspace = sys.argv[2]
    job_url = sys.argv[3]
    job_build_id = sys.argv[4]
    suite_code = sys.argv[5]
    biz_test_iter_id = sys.argv[6]
    build_user = sys.argv[7]
    batch_no = ''

    job_blue_url = job_url.split('job')[
                       0] + 'blue/organizations/jenkins/' + job_name + '/detail/' + job_name + '/' + str(
        job_build_id) + '/pipeline/'
    logger.info("job_build_id: {}".format(job_build_id))
    logger.info("job_name: {}".format(job_name))
    with DBConnectionManagerForSqlalchemy() as db:
        jenkinsMgtBizJobInfoLog = db.session.query(JenkinsMgtBizJobInfoLog).filter(
            JenkinsMgtBizJobInfoLog.job_build_id == job_build_id,
            JenkinsMgtBizJobInfoLog.job_name == job_name).order_by(
            JenkinsMgtBizJobInfoLog.create_time.desc()).first()
        if jenkinsMgtBizJobInfoLog:
            db.session.merge(jenkinsMgtBizJobInfoLog)
            jenkinsMgtBizJobInfoLog.job_url = job_blue_url
            jenkinsMgtBizJobInfoLog.update_user = build_user
            jenkinsMgtBizJobInfoLog.status = 'running'
            jenkinsMgtBizJobInfoLog.update_time = datetime.datetime.now()
            batch_no = jenkinsMgtBizJobInfoLog.batch_no
            logger.info("更新jenkinsMgtBizJobInfoLog")
        else:
            jenkinsMgtBizJobInfoLog = JenkinsMgtBizJobInfoLog(job_name=job_name,
                                                              job_build_id=job_build_id,
                                                              job_param={"suite_code": suite_code,
                                                                         "biz_test_iter_id": biz_test_iter_id},
                                                              job_url=job_blue_url,
                                                              status='running',
                                                              create_user=build_user,
                                                              create_time=datetime.datetime.now())
            logger.info("新增jenkinsMgtBizJobInfoLog")
            db.session.add(jenkinsMgtBizJobInfoLog)
        db.session.flush()
        db.session.commit()
    sdp = SelfDefineParse(workspace, job_name, suite_code, biz_test_iter_id, job_build_id, batch_no)
    sdp.prepare_data()
