import datetime
import random
import sys
import os
import json
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger, PIPELINE_TEST_PUBLISH, MOCK_SYSTEM_INTERFACE
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import get_cache_node_by_suite_code, \
    clean_cache_by_cache_node, exec_local_cmd, call_test_set_by_test_set_id, check_test_set_run_result, \
    exec_remote_cmd
from ci_pipeline.pipeline_record.pipeline_record import <PERSON>pelineStatus
from ci_pipeline.ci_pipeline_utils.publish_utils import Publish<PERSON><PERSON>us<PERSON><PERSON><PERSON>
from time import sleep
from dao.connect.mysql import DBConnectionManager
from test_publish_aio.test_publish_aio_exec.test_suite_restart import Starter
from job.pipeline_log.pipeline_log_decorator_mgt import BizAutoTestPipelineLog
from test_publish_aio.test_publish_aio_models.test_publish_ser import get_app_by_start_level, get_app_container_list
from job.jenkins.test_data_dev.make_dump_service import MakeDumpService
from concurrent.futures import ThreadPoolExecutor, as_completed
from job.jenkins.biz_auto_test.models import BizTestFlowDumpInfo
from pathlib import Path
from job.jenkins.biz_auto_test.self_define_parse import TestSetRunStatus


class BizAutoTester:
    def __init__(self, workspace, job_name, suite_code, biz_test_iter_id, job_build_id):
        self.workspace = workspace
        self.job_name = job_name
        self.suite_code = suite_code
        self.biz_test_iter_id = biz_test_iter_id
        self.job_build_id = job_build_id
        self.pipeline_node_dict = {
            "wait_check": self.wait_minutes,
            "clear_cache": self.clear_cache,
            "delete_file": self.delete_file,
            "set_time": self.set_time,
            "restart_app": self.restart_app,
            "run_testset": self.run_testset,
            "is_checked": self.check_result,
            "make_dump": self.make_dump
        }

    def setStepParam(self, step_param):
        self.step_param = step_param

    def setStepOrder(self, step_order):
        self.step_order = step_order

    @BizAutoTestPipelineLog(step_name="wait_minutes")
    def wait_minutes(self):
        """
        喝杯咖啡
        :param step_param:
        :return:
        """
        try:
            step_param = self.step_param
            logger.info("step_param==={}".format(step_param))
            logger.info("type(step_param)==={}".format(type(step_param)))
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            # 前端传入的变量名涉及地方较多，暂时不改传入的变量名为wait_seconds，待后续抽时间优化 20250408 by fwm
            wait_seconds = step_param.get("wait_minutes")
            # 将wait_seconds转成int类型
            wait_seconds = int(wait_seconds)
            logger.info(">>>> 将等待「{}」秒。".format(wait_seconds))
            # 改成按秒数来sleep，
            sleep(wait_seconds)
            return PipelineStatus.success, "等待结束"
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    @BizAutoTestPipelineLog(step_name="clear_cache")
    def clear_cache(self):
        """
        清缓存
        :param step_param:
        :return:
        """
        try:
            publish_starts_checker = PublishStatusChecker(self.suite_code, 'redis')
            if publish_starts_checker.check_node_status_once():
                cache_code = get_cache_node_by_suite_code(self.suite_code)
                if not cache_code:
                    raise ValueError(">>>> 获取「{}」对应的缓存cache_code为空，无法清理缓存。".format(self.suite_code))

                clean_cache_by_cache_node(cache_code)
                logger.info(">>>> 清理缓存「{}」。".format(cache_code))
            else:
                logger.info(">>>> 用户跳过「清理缓存」。")
            return PipelineStatus.success, "清理缓存结束"
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    @BizAutoTestPipelineLog(step_name="delete_file")
    def delete_file(self):
        """
        删除文件
        :param step_param:
        :return:
        """
        try:
            step_param = self.step_param
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            file_path = step_param.get("file_path")
            remote_ip = PIPELINE_TEST_PUBLISH['docker_delete_file_ip']
            shell_cmd = PIPELINE_TEST_PUBLISH['docker_delete_file_cmd']
            if not file_path:
                logger.info(">>>> 无效文件列表:{} 不删除文件".format(file_path))
                return PipelineStatus.success, "不删除文件"

            cmd = "{} {} {}".format(shell_cmd, self.suite_code, file_path)
            logger.info(cmd)
            res = exec_remote_cmd(remote_ip, cmd)
            logger.info(res)
            return PipelineStatus.success, "删除文件结束"
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    @BizAutoTestPipelineLog(step_name="set_time")
    def set_time(self):
        """
        设置时间
        :param step_param:
        :return:
        """
        try:
            step_param = self.step_param
            logger.info("step_param==={}".format(step_param))
            logger.info("type(step_param)==={}".format(type(step_param)))
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            sys_datetime = step_param.get("sys_datetime")

            # 3、容器执行帅维脚本
            script_name = PIPELINE_TEST_PUBLISH['docker_set_time_cmd']
            # 允许传None值给帅维的脚本
            k8s_cmd = "python3.x {} {} '{}'".format(script_name, self.suite_code, sys_datetime)
            exec_local_cmd(k8s_cmd)
            return PipelineStatus.success, "设置时间结束"
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    @BizAutoTestPipelineLog(step_name="restart_app")
    def restart_app(self):
        """
        应用重启
        :param step_param:
        :return:
        """
        try:
            step_param = self.step_param
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            app_list = step_param.get("app_list")
            self._start_app(app_list)
            return PipelineStatus.success, "应用重启完成"

        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def _start_app(self, app_list):
        app_list_dict = get_app_by_start_level(app_list)
        logger.info("按启动顺序分批：app_list_dict:{}".format(app_list_dict))
        starter = Starter()
        for k, v in app_list_dict.items():
            starter.docker_pods_restart_by_paramiko(self.suite_code, v)

    @BizAutoTestPipelineLog(step_name="run_testset")
    def run_testset(self):
        """
        运行测试集
        :param step_param:
        :return:
        """
        try:
            step_param = self.step_param
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            test_set_id = step_param.get("test_set")
            batch_no = step_param.get("batch_no")
            logger.info(">>>> batch_no:{}".format(batch_no))
            testset_order_map = step_param.get("testset_order_map")
            logger.info(">>>> testset_order_map:{}".format(testset_order_map))

            testset_detail_map = step_param.get("testset_detail_map")
            logger.info(">>>> testset_detail_map:{}".format(testset_detail_map))

            if not test_set_id or '0' == test_set_id:
                logger.info(">>>> 无效测试集ID:{} 不运行测试集".format(test_set_id))
                return PipelineStatus.success, "不运行测试集"
            result = None
            msg = None

            test_set_order_file = Path(self.workspace, self.job_name + "_testset_order.json")
            if test_set_order_file.exists():
                logger.info("测试集执行顺序文件已存在，读内容")
                with open(os.path.join(self.workspace, self.job_name + "_testset_order.json"), "r",
                          encoding='utf-8') as f:
                    order_dict = json.loads(f.read())
                    order = order_dict.get("order")
            else:
                order = 1

            if isinstance(test_set_id, list):
                for set_id in test_set_id:
                    result, msg = self._run_test_set_by_test_set_id(set_id, testset_order_map.get(
                        str(order) + "-" + str(set_id)), batch_no, testset_detail_map.get(set_id))
                    order += 1
            else:
                result, msg = self._run_test_set_by_test_set_id(test_set_id, testset_order_map.get(
                    str(order) + "-" + str(test_set_id)), batch_no, testset_detail_map.get(test_set_id))
                order += 1

            with open(os.path.join(self.workspace, self.job_name + "_testset_order.json"), "w", encoding='utf-8') as f:
                order_dict = {"order": order}
                f.write(json.dumps(order_dict))
                logger.info("写入测试集执行顺序文件内容： {}".format(order_dict))

            return result, msg
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def _run_test_set_by_test_set_id(self, test_set_id, execute_id=None, batch_no=None, testset_detail=None):
        logger.info(">>>> execute_id:{}".format(execute_id))
        if execute_id:
            result = call_test_set_by_test_set_id(test_set_id, self.suite_code, execute_id, batch_no, testset_detail)
        else:
            result = call_test_set_by_test_set_id(test_set_id, self.suite_code)

        if result.get("code") == 200:
            # 需要检测测试集的运行结果
            qa_execute_id = result.get("data").get("executeId")
            execution_time = int(result.get("data").get("executionTime")) if result.get("data").get(
                "executionTime") else 3600
            if execution_time == 3600:
                execution_time = 7200
            check_result = False
            sleep_time = 10
            while not check_result and execution_time > 0:
                try:
                    res = check_test_set_run_result([qa_execute_id])
                    if res.get("code") == 200:
                        for item in res.get("data"):
                            logger.info("测试集运行状态检测结果： {}".format(item.get("status")))
                            if item.get("status") == "end":
                                check_result = True
                                self._set_testset_run_status_map(qa_execute_id, TestSetRunStatus.SUCCESS.status_name)
                            elif item.get("status") == "abort":
                                sys.exit(1)
                                logger.error("测试集执行状态异常：{}，流水线中断".format(abort))
                                return PipelineStatus.failure, "测试集执行失败"
                            else:
                                sleep(sleep_time)
                                execution_time -= sleep_time
                except Exception as e:
                    logger.warn("服务暂不可用，等待重试！")
                    sleep(sleep_time)
                    execution_time -= sleep_time

            if check_result:
                return PipelineStatus.success, "测试集执行成功"
            else:
                sys.exit(1)
                logger.error("测试集执行超时：{}，流水线中断".format(executionTime))
                return PipelineStatus.failure, "测试集执行失败"
        else:
            sys.exit(1)
            return PipelineStatus.failure, "调用执行测试集失败，详情： {}".format(result)

    @BizAutoTestPipelineLog(step_name="check_result")
    def check_result(self):
        """
        检查启动结果
        :param app_list:
        :return:
        """
        try:
            step_param = self.step_param
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            app_list = step_param.get("app_list")
            logger.info("待检测的应用列表: {}".format(app_list))
            app_container_list = get_app_container_list(app_list)

            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_app = {executor.submit(self._check_app_status, container_name): (
                    container_name) for container_name in app_container_list}
                for future in as_completed(future_to_app):
                    container_name = future_to_app[future]
                    try:
                        future.result()
                    except Exception as exc:
                        raise IOError("容器{}启动失败".format(container_name))

            return PipelineStatus.success, "检测应用状态正常"
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    @BizAutoTestPipelineLog(step_name="make_dump")
    def make_dump(self):
        try:
            step_param = self.step_param
            if isinstance(step_param, str):
                step_param = json.loads(step_param)
            dump_file_prefix_name = step_param.get("dump_file_prefix_name")
            current_time_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
            db_info_list = step_param.get("db_info_list")
            suite_db_name_list = []
            batch_no = datetime.datetime.now().strftime("%Y%m%d%H%M%S") + str(random.randint(100, 999))

            for db_info in db_info_list:
                env_db_name = db_info.get('suite_db_name').upper()
                # 一个库只需要dump一次
                if env_db_name not in suite_db_name_list:
                    suite_db_name_list.append(env_db_name)
                else:
                    continue
                local_path = db_info['data_dump_dir']
                dump_file_name = None
                if db_info['db_srv_type'] == "oracle":
                    dump_file_name = "{}_{}_{}.dmp".format(dump_file_prefix_name, env_db_name, current_time_str)
                elif db_info['db_srv_type'] == "mysql":
                    dump_file_name = "{}_{}_{}.sql".format(dump_file_prefix_name, env_db_name, current_time_str)
                else:
                    raise Exception("暂不支持该数据库类型")
                make_dump_ser = MakeDumpService()
                dump_cmd = make_dump_ser.get_dump_cmd(db_info, env_db_name, dump_file_name)
                local_file = os.path.join(local_path, dump_file_name)
                make_dump_ser.make_dump_and_check_or_upload(db_info, dump_cmd, local_file, dump_file_name,
                                                            remote_path=None)
                logger.info("开始记录dump文件信息")
                BizTestFlowDumpInfo.create(biz_pipeline_name=self.job_name, suite_code=self.suite_code,
                                           jenkins_build_id=os.environ.get('BUILD_ID'),
                                           batch_no=batch_no,
                                           db_name=db_info.get('db_name'), suite_db_name=db_info.get('suite_db_name'),
                                           db_srv_hosts=db_info.get('db_srv_hosts'),
                                           data_dump_dir=db_info.get("data_dump_dir"),
                                           dump_file_name=dump_file_name, create_time=datetime.datetime.now(),
                                           create_user='howbuyscm')
                logger.info("生成dump文件成功！=====》 文件名：{}，批次号：{}".format(local_file, batch_no))
            return PipelineStatus.success, "生成dump文件成功"
        except Exception as e:
            traceback.print_exc()
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def _check_app_status(self, app_name):
        publish_starts_checker = PublishStatusChecker(self.suite_code, app_name)
        try:
            publish_starts_checker.check_node_status(limit_time=600, agent_check=False)
        except IOError as e:
            logger.info(">>> 检测应用状态失败, 将自动重启一次！")
            self._start_app([app_name])
            sleep(5)
            publish_starts_checker.check_node_status(limit_time=600, agent_check=False)

    def get_iteration_id(self, module_name, suite_code):
        sql = '''
                SELECT ai.pipeline_id FROM iter_mgt_iter_app_info ai 
                INNER JOIN iter_mgt_iter_info ii ON ai.pipeline_id = ii.pipeline_id
                WHERE (ai.appName, ii.br_name) = 
                (SELECT t.module_name, i.lib_repo_branch FROM env_mgt_node_bind t 
                LEFT JOIN env_mgt_suite s ON t.suite_id = s.id
                LEFT JOIN product_mgt_product_info i ON i.id = t.lib_repo_info_id
                WHERE t.module_name = '{}' AND s.suite_code = '{}');
              '''.format(module_name, suite_code)

        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            result_list = db.cur.fetchall()
            if result_list:
                return result_list[0]['pipeline_id']
            else:
                return ''

    def _set_testset_run_status_map(self, execution_id, testset_run_status):
        file_path = os.path.join(self.workspace, self.job_name + "_testset_run_status_map.json")
        try:
            with open(file_path, "r", encoding='utf-8') as f:
                testset_run_status_map = json.loads(f.read())
        except FileNotFoundError:
            # 如果文件不存在，初始化为空字典
            testset_run_status_map = {}

        testset_run_status_map[execution_id] = testset_run_status
        # 将更新后的数据写回文件
        with open(file_path, "w", encoding='utf-8') as f:
            json.dump(testset_run_status_map, f, ensure_ascii=False, indent=4)

    def parse_data(self):
        """
        从缓存文件解析数据
        :return:
        """
        with open(os.path.join(self.workspace, self.job_name + ".json"), "r", encoding='utf-8') as f:
            param_dict = json.loads(f.read())
            logger.info("从缓存文件读数据: {}".format(os.path.join(self.workspace, self.job_name + ".json")))
        return param_dict

    # @PipelineRecorder()
    def run_step(self, node_name):

        exec_status, exec_msg = self.pipeline_node_dict[node_name]()
        return exec_status, exec_msg

    def run(self, node_name):
        """
        运行入口程序
        :param node_name:
        :param step:
        :return:
        """
        self.run_step(node_name)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    business_name = sys.argv[1]
    job_name = sys.argv[2]
    workspace = sys.argv[3]
    step = sys.argv[4]
    suite_code = sys.argv[5]
    biz_test_iter_id = sys.argv[6]
    job_build_id = sys.argv[7]

    tep = BizAutoTester(workspace, job_name, suite_code, biz_test_iter_id, job_build_id)
    param_dict = tep.parse_data()
    step_param = param_dict.get(step)
    tep.setStepParam(step_param)
    tep.setStepOrder(step)
    tep.run(business_name)
