import sys
import os
import json
import logging
import requests
import paramiko
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from utils.call_external_interface.call_http_api import SpiderHttpCaller
from dao.connect.mysql import DBConnectionManager
from dao.get.mysql.validator import ValidatorMysqlWorker
from settings import INTERFACE_URL
from common.ext_cmd.shell_cmd import shell_cmd
from settings import GITLAB_URL
from datetime import datetime
from dao.delete.mysql import iterative_pipeline_branches as brs
from dao.get.mysql import branch_apply as branch_apply_get
from dao.delete.mysql import iterative_pipeline_branchincludesys as br_sys
from common.call_api.gitlab.merge import Merger
from dao.get.mysql import iter_info
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from dao.get.mysql.app_info import app_package_type

class ClearPipeline:

    def __init__(self, proposer, pipeline_list):
        self.proposer = proposer
        self.pipeline_list = pipeline_list
        self.backup_list = []
        self.backup_file = datetime.now().strftime("%Y%m%d%H%M%S")


    def login(self):
        pass
        # s = requests.session()
        # header = {"Authorization": "Basic aG93YnV5c2NtOkhvd2J1eSFAIw==",
        #           'Content-Type': 'application/json'}
        # http_caller = SpiderHttpCaller()
        # login = http_caller.post_request(INTERFACE_URL['spider']+'/spider/user/login/', {"userName": 'howbuyscm', "password": 'Howbuy!@#'})
        # # http_caller.spider_login()
        # logging.info(login)
        # return http_caller

    def update_owner(self):
        excep_list = []
        for pipeline_id in self.pipeline_list:
            try:
                with DBConnectionManager() as db:
                    db.cur.execute("UPDATE `iter_mgt_iter_app_info` SET proposer = '{}'"
                                   " WHERE pipeline_id = '{}'".format(self.proposer, pipeline_id))
                    db.connection.commit()
            except Exception as e:
                excep_list.append(pipeline_id)
        logging.info(excep_list)
        logging.info("更新迭代所有者失败的迭代信息：{}".format(excep_list))

    def get_delete_info(self, sid):
        status_list = []
        delete_info = {}
        with DBConnectionManager() as db:
            db.cur.execute("SELECT ma.status,mi.log FROM `public_be_script_main` ma"
                           " LEFT JOIN `public_be_script_minor` mi ON mi.sid = ma.sid WHERE"
                           " ma.sid = '{}'".format(sid))
        result = db.cur.fetchall()
        for item in result:
            logging.info(item)
            if item.get('status') not in status_list:
                status_list.append(item.get('status'))

                delete_info[item.get('status')] = item.get('log')
            logging.info(item.get('status'))
            logging.info(item.get('log'))
        logging.info(delete_info)
        return delete_info

    def get_iter_info(self, pipeline_id):
        with DBConnectionManager() as db:
            db.cur.execute("SELECT * FROM `iter_mgt_iter_info` WHERE pipeline_id = '{}'".format(pipeline_id))
        result = db.cur.fetchall()
        return result

    def get_iter_status(self, sid):
        try:
            header = {"Authorization": INTERFACE_URL['spider_api_Authorization'],
                      'Content-Type': 'application/json'}
            response = requests.get(INTERFACE_URL['spider']+'spider/iter_mgt/iter_apply_status_api',
                                       params={'sid': sid},
                                       headers=header)
            logging.info(response.json())

        except Exception as e:
            logging.info(e)

    @staticmethod
    def del_pipeline(data, pipeline_id):
        failed_list = []
        success_list = []
        # s = requests.session()
        try:
            header = {"Authorization": INTERFACE_URL['spider_api_Authorization'],
                      'Content-Type': 'application/json'}
            response = requests.delete(INTERFACE_URL['spider']+'spider/iter_mgt/iter_mgt_api',
                                       data=json.dumps(data),
                                       headers=header)
            logging.info(response.json())
            if response.json()['status'] != 'success':
                failed_list.append(pipeline_id)
            else:
                success_list.append(pipeline_id)
        except Exception as e:
            logging.info("删除迭代信息失败的迭代信息：{}".format(pipeline_id))
            raise ValueError("请求返回失败详情：".format(e))
        logging.info("删除迭代信息失败的迭代信息：{}".format(failed_list))
        logging.info("删除迭代信息成功的迭代信息：{}".format(success_list))
        return response.json()['data']['sid']

    @staticmethod
    def create_delete_data(pipeline_id):
        failed_list = []
        success_list = []
        delete_iter_data = {}
        # s = requests.session()
        header = {"Authorization": INTERFACE_URL['spider_api_Authorization'],
                  'Content-Type': 'application/json'}
        try:
            response = requests.get(INTERFACE_URL['spider']+'spider/iter_mgt/git_repos_info_api',
                                    params={'iterationID': pipeline_id},
                                    headers=header)
            logging.info(response.json())
            if response.json()['status'] != 'success':
                failed_list.append(pipeline_id)
            else:
                success_list.append(pipeline_id)
            for row in response.json()['data']['git_repo_list']:
                if pipeline_id in delete_iter_data:
                    delete_iter_data[pipeline_id]["repos_str"].append({"repos_path": row['gitRepo'],
                                                                       "module_name": row["app_name"]})
                else:
                    delete_iter_data[pipeline_id] = {"repos_str": [{"repos_path": row['gitRepo'],
                                                                    "module_name": row["app_name"]}],
                                                     "iteration_id": pipeline_id}
        except Exception as e:
            logging.info("获取迭代信息失败的迭代信息：{}".format(pipeline_id))
            raise ValueError("请求返回失败详情：".format(e))
        logging.info(delete_iter_data)
        logging.info("查询迭代信息失败的迭代信息{}".format(failed_list))
        logging.info("查询迭代信息成功的迭代信息{}".format(success_list))
        return delete_iter_data

    def git_clone_code(self, branch, repo_path, ssh, pipeline_id):
        error_info = []
        workspace = "/data/gitlab_for_clear/"+self.backup_file + "/{}".format(pipeline_id)
        sftp = ssh.open_sftp()
        try:
            sftp.stat(workspace)
        except Exception as e:
            stdin, stdout, stderr = ssh.exec_command("mkdir -p {}".format(workspace), get_pty=True, timeout=300)
            res = stdout.readlines()
            logging.info('创建备份目录信息：{}'.format(res))
            # 获取错误信息
            error = stderr.read()
            if error:
                raise ValueError("创建备份目录错误日志：{}".format(error))

        if (branch, repo_path) not in self.backup_list:
            cmd = "cd {} && git clone -b {} {}:{}.git".format(workspace, branch, GITLAB_URL, repo_path)
            logging.info(cmd)
            stdin, stdout, stderr = ssh.exec_command(cmd, get_pty=True, timeout=300)
            res = stdout.readlines()
            error = stderr.read()
            logging.info("备份clone返回信息".format(res))
            if error:
                error_info.append((branch, repo_path))
                raise ValueError("备份失败具体失败信息如下：{},错误信息是：{}".format((branch, repo_path), error))
            self.backup_list.append((branch, repo_path))

    def sshConnect(self, host, username, password):
        try:
            # 建立一个sshclient对象
            ssh = paramiko.SSHClient()
            # 允许将信任的主机自动加入到host_allow 列表，此方法必须放在connect方法的前面
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # 调用connect方法连接服务器
            ssh.connect(hostname=host, port=22, username=username, password=password)
        except Exception as e:
            logging.error("失败信息" + str(e))
            raise ValueError("失败信息" + str(e))
        return ssh

    def backup_branch_code(self, backup):
        ssh = self.sshConnect('***************', 'tomcat', 'howbuy2015')
        if backup:
            for pipeline_id in pipeline_list:
                data = self.create_delete_data(pipeline_id)
                if data:
                    for repo_paths in data[pipeline_id]['repos_str']:
                        with ValidatorMysqlWorker() as mysql_worker:
                            br_name = mysql_worker.get_br_name(pipeline_id)
                        self.git_clone_code(br_name, repo_paths['repos_path'], ssh, pipeline_id)
                else:
                    logging.info("{}未获取仓库信息".format(pipeline_id))
            logging.info("代码备份成功的分支仓库信息{}".format(self.backup_list))
        else:
            logging.info("《《《《《《《《《《此次清理不做代码备份》》》》》》》》》》》")

    def del_interation_jenkins(self, pipeline_id, repos_path_list):
        except_pipeline_list = []
        for repos_path in repos_path_list:
            exit_app_list = iter_info.get_iter_repos_app(pipeline_id, repos_path['repos_path'])
            jenkins_job_mgt = JenkinsJobMgt()
            try:
                for row in app_package_type(exit_app_list):
                    # 动态删除 jenkins job
                    logging.info("删除{}流水线".format(row["module_name"]))
                    jenkins_job_mgt.delete_pipeline(pipeline_id, row["module_name"])
            except Exception as e:
                if row["module_name"] not in except_pipeline_list:
                    except_pipeline_list.append(row["module_name"])
                logging.info(e)
        logging.info("删除流水线失败：{}".format(except_pipeline_list))

    def del_interation_info(self, pipeline_id):
        except_pipeline_list = []
        try:
            if branch_apply_get.get_git_code_path(pipeline_id):
                br_sys.del_iterative(pipeline_id)
            if self.get_iter_info(pipeline_id):
                brs.del_iterative(pipeline_id)
        except Exception as e:
            if pipeline_id not in except_pipeline_list:
                except_pipeline_list.append(pipeline_id)
            logging.info(e)
        logging.info("删除迭代信息失败：{}".format(except_pipeline_list))

    def del_branch_info(self, pipeline_id, data):
        except_pipeline_list = []
        try:
            with ValidatorMysqlWorker() as mysql_worker:
                br_name = mysql_worker.get_br_name(pipeline_id)
            if data:
                repos_path_list, app_list = self.analysis_repos_info(data[pipeline_id]['repos_str'])
                logging.info('删除仓库信息{}'.format(repos_path_list))
                logging.info('删除分支信息：{}'.format(br_name))
                me = Merger(br_name, repos_path_list)
                me.delete_branch()
            else:
                logging.info("{}没有仓库信息".format(pipeline_id))
        except Exception as e:
            if {"branch":br_name,"repo_path":repos_path_list} not in except_pipeline_list:
                except_pipeline_list.append({"branch":br_name,"repo_path":repos_path_list})
            logging.info('删除分支异常{}'.format(e))
        logging.info("删除分支失败：{}".format(except_pipeline_list))

    @staticmethod
    def analysis_repos_info(repos_path_info):
        repos_path_list = []
        app_list = []
        for row in repos_path_info:
            if row["module_name"] != "无":
                app_list.append(row["module_name"])
            if row["repos_path"] not in repos_path_list:
                repos_path_list.append(row["repos_path"])
        return repos_path_list, app_list

    # def batch_del_pipeline(self):
    #     sid_list = []
    #     for pipeline_id in self.pipeline_list:
    #         data = self.create_delete_data(pipeline_id)
    #         sid = self.del_pipeline({"iteration_id": pipeline_id, "repos_str": data[pipeline_id]['repos_str']},
    #                                 pipeline_id)
    #         data = self.create_delete_data(pipeline_id)
    #         if data:
    #             sid = self.del_pipeline({"iteration_id": pipeline_id, "repos_str": data[pipeline_id]['repos_str']},
    #                               pipeline_id)
    #         if branch_apply_get.get_git_code_path(pipeline_id):
    #             br_sys.del_iterative(pipeline_id)
    #         if self.get_iter_info(pipeline_id):
    #             brs.del_iterative(pipeline_id)
    #         try:
    #             with ValidatorMysqlWorker() as mysql_worker:
    #                 br_name = mysql_worker.get_br_name(pipeline_id)
    #             repos_path_list, app_list = self.analysis_repos_info(data[pipeline_id]['repos_str'])
    #             logging.info('删除仓库信息{}'.format(repos_path_list))
    #             logging.info('删除分支信息：{}'.format(br_name))
    #             me = Merger(br_name, repos_path_list)
    #             me.delete_branch()
    #         except Exception as e:
    #             logging.info('删除分支异常{}'.format(e))
    #         if sid not in sid_list:
    #             sid_list.append(sid)
    #     logging.info("批量清理任务id：{}".format(sid_list))
            # for item in data[pipeline_id]['repos_str']:
            #     logging.info({"iteration_id":pipeline_id,"repos_str":[item]})
            #     self.del_pipeline({"iteration_id":pipeline_id,"repos_str":[item]},pipeline_id)

    def batch_del_pipeline(self):
        for pipeline_id in self.pipeline_list:
            data = self.create_delete_data(pipeline_id)
            if data:
                self.del_interation_jenkins(pipeline_id, data[pipeline_id]['repos_str'])
            else:
                logging.info("{}没有仓库信息".format(pipeline_id))
            self.del_branch_info(pipeline_id, data)
            self.del_interation_info(pipeline_id)
        logging.info(self.pipeline_list)

if __name__ == '__main__':
    pipeline_list = sys.argv[1]
    backup = sys.argv[2]
    pipeline_list = pipeline_list.split(',')
    logging.info(pipeline_list)
    cp = ClearPipeline('howbuyscm', pipeline_list)
    cp.backup_branch_code(backup)
    # cp.update_owner()
    # http_caller = cp.login()
    cp.batch_del_pipeline()

