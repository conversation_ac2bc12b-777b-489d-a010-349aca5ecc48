# coding: utf-8
import os
import re
import sys
from functools import wraps

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from settings import logger, PRODUCT_STORE_URL, KUBECTL_TOMCAT, MIRROR_FACTORY_TOMCAT
from common.ext_cmd.shell_cmd import shell_cmd
from dao.connect.mysql_pipeline_env import PipelineEnv
from dao.get.mysql.validator import ValidatorMysqlWorker
from utils.release.docker.rebuild_docker import ImageBuild
from utils.release.docker.restart_docker import DockerRestart
from utils.release.virtual.restart_env import restart as virtual_restart


# 获取产线版本应用的目录
PRODUCT_PATH = "/data/env_repo/"
# VIRTUAL_TOMCAT_BASE_PATH = "/usr/local/"
VIRTUAL_TOMCAT_BASE_PATH = "/data/app/"
VIRTUAL_JAR_BASE_PATH = "/data/app/"
IMG_BASE = '/home/<USER>/img-factory-online/'

vm_jar_apps = {
    'mring-itest-service': ('mring-itest-service', 'mring-itest-service'),
    'mring-performance-service': ('mring-performance-service', 'mring-performance-service'),
}
vm_tomcat_apps = {
    'mring': ('tomcat-mring', 'mring'),
}
vm_apps = dict(vm_jar_apps, **vm_tomcat_apps)

k8s_jar_apps = {
    'asset-batch-center-remote': ('', ''),
    'asset-center-remote': ('', ''),
}
k8s_tomcat_apps = {}
k8s_apps = dict(k8s_jar_apps, **k8s_tomcat_apps)

all_apps = dict(vm_apps, **k8s_apps)


def step_descorator(name):
    def descorator(func):
        @wraps(func)
        def des(*args, **kwargs):
            try:
                logger.info("Step: {}".format(name))
                func(*args, **kwargs)
                logger.info("{}完成".format(name))
            except Exception as e:
                logger.error("{}失败: {}".format(name, str(e)))
                exit(1)

        return des

    return descorator


def get_test_env_address(pipeline_id, app_name):
    mysql_worker = PipelineEnv()
    envs = mysql_worker.get_env_bind_info(pipeline_id)

    env_address = []
    if app_name in vm_apps:
        for env in envs:
            if env['env'].startswith('tms'):
                res = mysql_worker.get_env_vm(env['env'])
                if res:
                    env_address.append(res['tp'])
            else:
                env_address.append(env['env'])
    else:
        for env in envs:
            env_address.append(env['env'])

    mysql_worker.close_mysql()
    return env_address


def exec_cmd(cmd):
    logger.info("Exec command: {}".format(cmd))
    code, msg = shell_cmd(cmd)
    if code != 0:
        raise Exception(msg)


class AppHandler(object):
    def __init__(self, app_name, br_name, env_address):
        self.app_name = app_name
        self.br_name = br_name
        self.env_address = env_address
        repo_path = self.get_artifact_repo()
        if not repo_path:
            logger.error('No git code path')
            exit(1)
        self.git_repo_path = PRODUCT_STORE_URL + '/' + repo_path + '.git'
        self.repo_name = repo_path.split('/')[-1]

    # 拉取最新的应用
    @step_descorator('拉包')
    def app_pull(self):
        os.chdir(PRODUCT_PATH)
        if not os.path.isdir(self.repo_name):
            os.system('git clone -b {} --depth 1 {}'.format(self.br_name, self.git_repo_path))
        else:
            os.chdir(self.repo_name)
            os.system('git config remote.origin.fetch +refs/heads/*:refs/remotes/origin/*')
            os.system('git pull origin {}'.format(self.br_name))
            os.system('git checkout {}'.format(self.br_name))

    def get_artifact_repo(self):
        with ValidatorMysqlWorker() as mysql_worker:
            return mysql_worker.get_artifact_repo_path(self.app_name)

    # 配置文件处理
    @step_descorator('配置更新')
    def config_update(self):
        pass


class VirtualMachineHandler(AppHandler):
    # 拷贝应用到测试服务器
    @step_descorator('推送')
    def scp(self):
        if self.app_name in vm_tomcat_apps:
            cmd = "rsync --timeout=120 -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
                PRODUCT_PATH + self.repo_name, self.env_address,
                VIRTUAL_TOMCAT_BASE_PATH + all_apps[self.app_name][0] + '/' +
                all_apps[self.app_name][1] + '/')
        else:
            cmd = "rsync --timeout=120 -LPa --exclude='*.git' {}/ tomcat@{}:{}".format(
                PRODUCT_PATH + self.repo_name, self.env_address, VIRTUAL_JAR_BASE_PATH + self.app_name + '/lib/')
        exec_cmd(cmd)

    # 测试环境中重启应用
    @step_descorator('重启')
    def restart(self):
        logger.info('restart ...')
        if self.app_name in vm_jar_apps:
            virtual_restart(self.env_address, self.app_name, 'jar')
        else:
            virtual_restart(self.env_address, all_apps[self.app_name][0], 'tomcat')

    def run(self):
        self.app_pull()
        self.config_update()
        self.scp()
        self.restart()


class DockerHandler(AppHandler, ImageBuild, DockerRestart):
    # 拷贝应用到容器服务器
    @step_descorator('推送')
    def scp(self):
        logger.info('scp {}'.format(self.app_name))
        if self.app_name in k8s_jar_apps:
            cmd = """
                  ssh {} "rm -rf {}/{}/{}/lib/*"
            """.format(MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)

            cmd = """
                  scp -r {}/{}/* {}:{}/{}/{}/lib/
            """.format(PRODUCT_PATH + self.repo_name, all_apps[self.app_name][1], MIRROR_FACTORY_TOMCAT,
                       IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)
        elif self.app_name in k8s_tomcat_apps:
            cmd = """
                  ssh {} "rm -rf {}/{}/{}/WEB-INF/lib/*"
            """.format(MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)

            cmd = """
                 scp -r {}/{}/WEB-INF/lib/* {}:{}/{}/{}/WEB-INF/lib/
            """.format(PRODUCT_PATH + self.repo_name, self.repo_name, MIRROR_FACTORY_TOMCAT,
                       IMG_BASE + self.env_address, self.app_name, self.app_name)
            exec_cmd(cmd)
        else:
            raise Exception("{} can not scp".format(self.app_name))

    @step_descorator('初始化镜像库')
    def init_img_dir(self):
        cmd = """
              ssh {} \
              'rm -rf {}/{} && \
               cp -a {}/tms_basic/{} {}/'
        """.format(MIRROR_FACTORY_TOMCAT, IMG_BASE + self.env_address, self.app_name, IMG_BASE, self.app_name,
                   IMG_BASE + self.env_address)
        exec_cmd(cmd)

        cmd = """
              ssh {} \
              'cp -a /home/<USER>/tmp/tms/dp-{}.yaml /home/<USER>/script/deployment/{} && \
               sed -i "s/xxx/{}/g" /home/<USER>/script/deployment/{}/dp-{}.yaml'
        """.format(KUBECTL_TOMCAT, self.app_name, self.env_address, self.env_address, self.env_address, self.app_name)
        exec_cmd(cmd)

    @step_descorator('镜像制作')
    def make_img(self):
        self.make_image(self.env_address, self.app_name)

    @step_descorator('重启')
    def restart(self):
        self.restart_pod(self.env_address, self.app_name)

    def run(self):
        self.app_pull()
        self.config_update()
        self.init_img_dir()
        self.scp()
        self.make_img()
        self.restart()


class EnvInit(object):
    """
    测试环境 更新该迭代应用的制品和配置到绑定的测试环境中
    :param pipeline_id: 迭代版本号
    :param app_name: 上线应用名
    """

    def __init__(self, pipeline_id, app_name):

        with ValidatorMysqlWorker() as mysql_worker:
            br_name = mysql_worker.get_br_name(pipeline_id)
        env_address = get_test_env_address(pipeline_id, app_name)

        if app_name not in all_apps:
            logger.info('The APP <{}> not record.'.format(app_name))
            return

        for env in env_address:
            if re.match(r"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$", env):
                self.worker = VirtualMachineHandler(app_name, br_name, env)
            else:
                self.worker = DockerHandler(app_name, br_name, env)
            self.worker.run()



if __name__ == '__main__':
    logger.info("调用成功")
    #  python3.x env_init.py mojie_1.0.2_mring-performance-service
    job_name = sys.argv[1]
    pipelineId = '_'.join(job_name.split('_')[:-1])
    appName = job_name.split('_')[-1]
    worker = EnvInit(pipelineId, appName)
    logger.info("调用结束")
