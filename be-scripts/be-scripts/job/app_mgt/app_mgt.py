import os
import sys
import json

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from common.files.xml import pom
from settings import logger
from common.log.record_log import record_log
from common.ext_cmd.shell_cmd import clean_workspace


class AppMgt:
    workspace_root = '/data/platform'
    workspace = os.path.join(workspace_root, "analysis")

    def __init__(self, send_log=record_log("jenkins", 0)):
        """
        分支管理功能，jenkins调用不用穿send_log
        :param send_log:
        """
        self.acceptor = {
            "svn_analysis": self.svn_analysis,  # 解析svn倉庫
            "git_analysis": self.git_analysis,  # 解析git倉庫

        }
        self.send_log = send_log

    def svn_analysis(self, svn_repos_url, *args):
        self.send_log.send({"step": "拉取代码"})
        try:
            os.system("cd {} && svn checkout {}".format(self.workspace, svn_repos_url))
            self.send_log.send({"status": "success", "step": "拉取代码", "content": "拉取代码成功"})
        except Exception as e:
            self.send_log.send({"status": "failure", "step": "拉取代码", "content": str(e)})

        self.send_log.send({"step": "解析pom"})
        try:
            artifact_list = self.analysis_pom()
            self.send_log.send({"status": "success", "step": "解析pom", "content": artifact_list})
        except Exception as e:
            self.send_log.send({"status": "failure", "step": "解析pom", "content": str(e)})

    def git_analysis(self, gitlab_repos_url, branch_name="master"):
        self.send_log.send({"step": "拉取代码"})
        try:
            os.system("cd {} && git clone --depth 1 -b {} {}".format(self.workspace, branch_name, gitlab_repos_url))
            self.send_log.send({"status": "success", "step": "拉取代码", "content": "拉取代码成功"})
        except Exception as e:
            self.send_log.send({"status": "failure", "step": "拉取代码", "content": str(e)})

        self.send_log.send({"step": "解析pom"})
        try:
            artifact_list = self.analysis_pom()
            self.send_log.send({"status": "success", "step": "解析pom", "content": artifact_list})
        except Exception as e:
            self.send_log.send({"status": "failure", "step": "解析pom", "content": str(e)})

    def analysis_pom(self):
        pm = pom.PomModifier(self.workspace)
        return pm.public_find_all_artifacts_in_workspace

    def platform_call(self, params):
        """
        平台调用分发器
        :param params: dict
        :return:
        """
        self.send_log.send({"step": "删除迭代内仓库"})

        #repos_path_list = params["repos_str"].split(",")
        if os.path.join(self.workspace):
            os.system("mkdir -p {}".format(self.workspace))
        self.acceptor[params["task"]](params["repos_url"], params["branch_name"])
        clean_workspace(self.workspace)

    def call(self):
        pass


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    am = AppMgt()
    am.call(sys.argv[1:])
