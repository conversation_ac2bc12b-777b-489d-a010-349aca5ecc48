from peewee import *
from dao.base_model import BaseModel


class BizTestIter(BaseModel):
    biz_test_iter_id = CharField(verbose_name="业务测试迭代ID", max_length=100)
    biz_code = CharField(verbose_name="业务编码", max_length=100)
    biz_test_iter_br = CharField(verbose_name="业务测试迭代分支", max_length=100)
    br_status = CharField(verbose_name="分支状态", max_length=100)
    br_start_time = DateTimeField(verbose_name="分支开始时间")
    br_end_time = DateTimeField(verbose_name="分支结束时间")
    br_from = CharField(verbose_name="从哪个分支拉出", max_length=100)
    stamp = BigIntegerField(verbose_name="版本")
    create_user = CharField(verbose_name="创建人", max_length=20)
    create_time = DateTimeField(verbose_name="创建时间")
    update_user = CharField(verbose_name="修改人", max_length=20)
    update_time = DateTimeField(verbose_name="修改时间")

    class Meta:
        db_table = 'biz_test_iter'
        verbose_name = '业务迭代信息表'


class BizTestIterApp(BaseModel):
    biz_test_iter_id = CharField(verbose_name="业务测试迭代ID", max_length=100)
    app_module_name = CharField(verbose_name="应用名字", max_length=100)
    archive_br_name = CharField(verbose_name="归档分支名", max_length=100)
    stamp = BigIntegerField(verbose_name="版本")
    create_user = CharField(verbose_name="创建人", max_length=20)
    create_time = DateTimeField(verbose_name="创建时间")
    update_user = CharField(verbose_name="修改人", max_length=20)
    update_time = DateTimeField(verbose_name="修改时间")

    class Meta:
        db_table = 'biz_test_iter_app'
