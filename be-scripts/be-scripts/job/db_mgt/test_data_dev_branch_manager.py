import sys
import os

from pymysql import IntegrityError

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from utils.form.branch_apply import TestDataDevIterCreate
from settings import logger
from common.log.record_log import record_log


class BranchManager:
    workspace_root = '/data/platform'

    def __init__(self, send_log=record_log("jenkins", 0)):
        """
        分支管理功能，jenkins调用不用穿send_log
        :param send_log:
        """
        self.acceptor = {
            "del_repos": self.del_repos,  # 删除仓库
            "add_repos": self.add_repos,  # 追加仓库
            "test_data_dev_iter_apply": self.test_data_dev_iter_apply,  # 迭代申请
        }
        self.send_log = send_log
        # self.send_log.send(None)

    def del_repos(self, ):
        """
        删除仓库功能

        """
        pass

    def add_repos(self):
        """
        """
        pass

    def test_data_dev_iter_apply(self, biz_test_iter_id, biz_test_iter_br, repos_path_list,
                                 bis_code, creator, biz_base_db_set):
        """
        测试数据开发迭代创建

        :return:
        """
        self.send_log.send({"step": "创建新迭代分支"})
        try:
            test_create = TestDataDevIterCreate(biz_test_iter_id, biz_test_iter_br, repos_path_list,
                                                bis_code, creator, biz_base_db_set)
            test_create.test_data_dev_branch_create()
            self.send_log.send({"status": "success", "step": "数据开发分支",
                                "content": "分支拉成功:分支名称为{}".format(biz_test_iter_br)})
        except Exception as e:
            import traceback
            logger.error(traceback.format_exc())
            self.send_log.send({"status": "failure", "step": "创建新迭代分支", "content": str(e)})
            raise Exception("分支号创建失败，请联系PA")
        self.send_log.close()

    def platform_call(self, params):
        """
        平台调用分发器
        :param params: dict
        :return:
        """
        logger.info("调用参数：{}".format(params))
        biz_test_iter_id = params["biz_test_iter_id"]
        self.acceptor[params["task"]](biz_test_iter_id, params["biz_test_iter_br"], params["repo_strs"],
                                      params['biz_code'], params['proposer'], params['biz_base_db_set'])
