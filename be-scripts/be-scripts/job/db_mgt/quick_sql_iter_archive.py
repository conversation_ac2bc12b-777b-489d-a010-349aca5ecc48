from settings import logger
from utils.form import archive_apply
from common.email import send_email
from common.routers.router import Result


class QuickSqlArchiveApplyForm:

    @classmethod
    def as_view(cls, request):
        publish_form = QuickSqlArchiveApplyForm()
        return publish_form.quick_sql_archive_apply_form(request)

    def quick_sql_archive_apply_form(self, request):
        logger.info("归档成功请求参数 {}".format(request))
        app_name = request.get('app_name')
        branch_name = request.get('branch_name')
        sql_dict = request.get('sql_dict')
        opt_user = request.get("opt_user")
        test_sql_iter_archive = archive_apply.QuickSqlIterArchive(app_name, branch_name, sql_dict, opt_user)
        test_sql_iter_archive.quick_sql_iter_archive()
        return Result.success_dict('归档成功')

    @staticmethod
    def _inform(iteration_id, member_list, msg):
        sd = send_email.SendMail()
        sd.set_to(",".join(member_list))
        sd.set_subject('<运维部署平台>[{}] 归档完成'.format(iteration_id))
        sd.set_content('{}'.format(msg))
        sd.send()


if __name__ == "__main__":
    aaf = QuickSqlArchiveApplyForm()
    aaf.quick_sql_archive_apply_form('it29_1.0.0')