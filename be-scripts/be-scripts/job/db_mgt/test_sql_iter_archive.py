import datetime
import json
import sys
import os

from settings import logger
from utils.form import archive_apply
from common.email import send_email
from common.routers.router import Result


class TestSqlArchiveApplyForm:

    @classmethod
    def as_view(cls, request):
        publish_form = TestSqlArchiveApplyForm()
        return publish_form.test_sql_archive_apply_form(request)

    def test_sql_archive_apply_form(self, request):
        logger.info("归档成功请求参数 {}".format(request))
        bis_pipeline_id = request["bis_pipeline_id"]
        operator = request["operator"]
        try:
            test_sql_iter_archive = archive_apply.TestSqlIterArchive(bis_pipeline_id, operator)
            res = test_sql_iter_archive.test_sql_iter_archive()
            return res
        except Exception as e:
            return Result.failed_dict(str(e))
        # http://gitlab-lib.howbuy.pa/test-dml-repo/it100/-/tree/tag_4.0.0/archive/4.0.0

    @staticmethod
    def _inform(iteration_id, member_list, msg):
        sd = send_email.SendMail()
        sd.set_to(",".join(member_list))
        sd.set_subject('<运维部署平台>[{}] 归档完成'.format(iteration_id))
        sd.set_content('{}'.format(msg))
        sd.send()


if __name__ == "__main__":
    aaf = TestSqlArchiveApplyForm()
    aaf.test_sql_archive_apply_form('it29_1.0.0')
