import os
import sys
import json
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from iter_mgt.publish_plan.dao.model.models import TaskMgtDeployResult
from settings import logger
from dao.connect.mysql import DBConnectionManager
from publish_tool.publish.publish_ser import release_reboot_lock, get_reboot_flow_json_by_action_id, \
    get_execute_flow_json_by_action_id, get_reboot_execute_history, update_reboot_flow_status, \
    update_reboot_execute_history_status, get_history_action_id


class PublishPipelineStatusRecorder:

    def __init__(self):
        self.pipeline_node_dict = {
            "batch_publish_record": self.batch_publish_record,
            "batch_reboot_record": self.batch_reboot_record,
        }

    def _get_action_id(self, job_name, job_build_id):
        sql = '''
            SELECT DISTINCT r.action_id FROM
            iter_mgt_jenkins_publish_pipeline_info r 
            WHERE r.job_name = "{}" and r.build_id = {};
         '''.format(job_name, job_build_id)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        result = db.cur.fetchone()
        action_id = None
        if result:
            action_id = result['action_id']
        return action_id

    def _get_batch_publish_action_id(self, job_name, job_build_id):
        sql = '''
            SELECT DISTINCT r.action_id FROM
            task_mgt_jenkins_publish_pipeline_info r 
            WHERE r.job_name = "{}" and r.build_id = {};
         '''.format(job_name, job_build_id)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        result = db.cur.fetchone()
        action_id = None
        if result:
            action_id = result['action_id']
        return action_id

    def _update_pipline_status(self, action_id, status):
        with DBConnectionManager() as db:
            sql = "update iter_mgt_jenkins_publish_pipeline_info set status = '{status}',execute_status = '{status}', lock_status = 'unlock' WHERE action_id = {action_id} ".format(status=status,
                                                                                                                 action_id=action_id)
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()

    def _update_deploy_status(self, action_id, status):
        with DBConnectionManager() as db:
            sql = "update task_mgt_deploy_result set status = '{}' WHERE action_id = {} and status = '{}'".format(
                status,
                action_id, TaskMgtDeployResult.RUNNING)
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()

    def _update_jenkins_pipeline(self, action_id, status):
        """
        UPDATE task_mgt_jenkins_publish_pipeline_info t
        JOIN (
            SELECT id
            FROM task_mgt_jenkins_publish_pipeline_info
            WHERE action_id = 337033 AND status = 'running'
            ORDER BY id DESC
            LIMIT 1
        ) sub ON t.id = sub.id
        SET t.status = 'failure';
        """
        inner_sql = " SELECT id FROM task_mgt_jenkins_publish_pipeline_info WHERE action_id = {} AND `status` = 'running' ORDER BY id DESC LIMIT 1 ".format(action_id)
        with DBConnectionManager() as db:
            sql = "UPDATE task_mgt_jenkins_publish_pipeline_info t JOIN ({}) sub ON t.id = sub.id SET t.status = '{}'".format(
                inner_sql,
                status)
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()

    def batch_publish_record(self, job_name, job_build_id, status):
        action_id = self._get_action_id(job_name, job_build_id)
        if not action_id:
            action_id = self._get_batch_publish_action_id(job_name, job_build_id)
        if action_id:
            self._update_pipline_status(action_id, status)
            if TaskMgtDeployResult.SUCCESS != status:
                self._update_deploy_status(action_id, status)

    def batch_reboot_record(self, job_name, job_build_id, status):
        action_id = get_history_action_id(job_name, job_build_id)
        self._update_jenkins_pipeline(action_id, status)
        flow_json = get_reboot_flow_json_by_action_id(action_id)
        if TaskMgtDeployResult.SUCCESS == status:
            execute_json = get_execute_flow_json_by_action_id(action_id)
            max_result = self.compare_max_phase(execute_json, flow_json)
            if max_result:
                logger.info('执行最后一阶段结束')
                release_reboot_lock(status)
                # 修改编排状态和历史记录状态
                update_reboot_flow_status(5, action_id)
                update_reboot_execute_history_status(action_id, status=5)
        else:
            logger.info('执行失败结束')
            release_reboot_lock(status)
            #修改编排状态和历史记录状态
            update_reboot_flow_status(6, action_id)
            self._update_deploy_status(action_id, status)
            execute_history = get_reboot_execute_history(action_id)
            fail_json = execute_history.get('fail_node_json',[])
            if fail_json:
                phase_stage = self.find_phase_for_fail_nodes(json.loads(flow_json), json.loads(fail_json))
                update_reboot_execute_history_status(action_id, status=6,phase_stage=phase_stage)



    def find_phase_for_fail_nodes(self, flow_json, fail_json):
        found_phase = None
        for fail_node in fail_json:
            fail_node_dict = json.loads(fail_node)
            for phase, nodes in flow_json.items():
                for node in nodes:
                    if node["node_ip"] == fail_node_dict["node_ip"] and node["module_name"] == fail_node_dict["module_name"]:
                        found_phase = phase
                        break
                if found_phase:
                    return found_phase
        return found_phase

    def compare_max_phase(self, execute_json, flow_json):
        # 解析JSON字符串
        try:
            if not flow_json or not execute_json:
                return False
            data_a = json.loads(execute_json)
            data_b = json.loads(flow_json)

            # 获取字符串a中的最大phase编号
            max_phase_a = max([int(k.split('_')[1]) for k in data_a.keys() if k.startswith('phase_')])

            # 获取字符串b中的最大phase编号
            max_phase_b = max([int(k.split('_')[1]) for k in data_b.keys() if k.startswith('phase_')])

            # 比较两个最大phase是否相同
            return max_phase_a == max_phase_b
        except Exception as e:
            traceback.print_exc()
            return False

    def record_status(self, step_name, job_name, job_build_id, status):
        try:
            self.pipeline_node_dict[step_name](job_name, job_build_id, status)
        except Exception as e:
            logger.error(e)
            raise Exception(e)



if __name__ == "__main__":
    try:
        logger.info("调用 {}".format(sys.argv[1:]))
        step_name = sys.argv[1]
        job_name = sys.argv[2]
        status = sys.argv[3]
        job_build_id = sys.argv[4]

        psr = PublishPipelineStatusRecorder()
        psr.record_status(step_name, job_name, job_build_id, status)
    except Exception as e:
        logger.error('修改状态失败' + e)
