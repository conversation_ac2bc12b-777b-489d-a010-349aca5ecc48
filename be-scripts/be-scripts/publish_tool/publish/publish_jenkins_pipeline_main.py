import os
import sys
import json

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_remote_cmd
from settings import logger, PUBLISH_TOOL


class JenkinsPublishMain:
    def jenkins_publish_main(self, node, sys_param):
        logger.info("发布工具参数params:{}".format(sys_param))
        try:
            cmd = "python3.x {} {} '{}'".format(PUBLISH_TOOL['jenkins_publish_cmd'], node, sys_param)
            exec_remote_cmd(PUBLISH_TOOL['node_ip'], cmd)
        except Exception as e:
            logger.error(e)
            raise Exception(e)


if __name__ == '__main__':
    node = sys.argv[1]
    sys_param = sys.argv[2:][0]
    # sys_param = json.loads(sys_param)
    jenkinspublishmain = JenkinsPublishMain()
    jenkinspublishmain.jenkins_publish_main(node, sys_param)
