from peewee import *
from dao.base_model import BaseModel


class ScriptExecRecordModel(BaseModel):
    (RUNNING, SUCCESS, FAILURE) = ('running', 'success', 'failure')
    STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))

    script_key = CharField(max_length=32, verbose_name='配置的业务key')
    exec_exec = CharField(max_length=256, verbose_name='执行的命令')
    script_params = TextField(verbose_name='参数')
    log_path = CharField(max_length=128, verbose_name='日志路径')
    status = CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    detail = TextField(verbose_name='详情')
    start_at = DateTimeField(verbose_name='执行开始时间')
    end_at = DateTimeField(verbose_name='执行结束时间')

    class Meta:
        table_name = 'publish_tool_script_exec_record'


class IterMgtJenkinsPublishPipelineInfo(BaseModel):
    (RUNNING, ABORTED, SUCCESS, FAILURE) = ('running', 'aborted', 'success', 'failure')
    STATUS_CHOICE = ((RUNNING, '执行中'), (ABORTED, '取消'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
    action_id = IntegerField(verbose_name='action_id')
    job_name = CharField(verbose_name='job_name', max_length=255)
    build_id = IntegerField(verbose_name='job构建id')
    exec_id = CharField(verbose_name='随机id，防重放', max_length=200)
    status = CharField(verbose_name='状态：running,aborted,failure,success', max_length=20)
    execute_status = CharField(verbose_name='状态：check,check_success, check_failure,running,aborted,failure,success', max_length=20)

    class Meta:
        db_table = 'iter_mgt_jenkins_publish_pipeline_info'
        verbose_name = '分组jenkins批量发布流水线信息表'


class SaltOperationResults(BaseModel):
    """salt 产线操作记录
    """
    (RUNNING, SUCCESS, FAILURE, WARNING) = ('running', 'success', 'failure', 'warning')
    STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
    action_id = IntegerField(verbose_name='行为id')
    exec_cmd = TextField(verbose_name='请求参数')
    request_url = TextField(verbose_name='请求地址')
    start_at = DateTimeField(verbose_name='执行开始时间')
    end_at = DateTimeField(verbose_name='执行结束时间')
    request_status = CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    request_status_code = IntegerField()
    request_result = TextField(verbose_name='请求结果')
    cmd_type = CharField(max_length=72, verbose_name='cmd_type', default=None)
    ip = CharField(max_length=100, verbose_name='ip', default=None)
    deploy_stage = CharField(max_length=50, verbose_name='deploy_stage', default=None)

    class Meta:
        db_table = 'task_mgt_salt_operation_results'
        verbose_name = 'salt请求记录'


# class SaltVerifyResults(BaseModel):
#     """salt 产线操作记录
#     """
#     (RUNNING, SUCCESS, FAILURE, WARNING) = ('running', 'success', 'failure', 'warning')
#     STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
#     app_name = CharField(max_length=255, verbose_name='执行状态')
#     node_ip = CharField(max_length=255, verbose_name='执行节点')
#     suite_code = CharField(max_length=255, verbose_name='环境')
#     service_status = CharField(max_length=255, verbose_name='服务状态')
#     result = TextField(verbose_name='请求参数')
#     update_time = DateTimeField(verbose_name='执行开始时间')
#     create_time = DateTimeField(verbose_name='执行结束时间')
#     request_status = CharField(choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
#     request_status_code = IntegerField()
#
#     class Meta:
#         db_table = 'publish_app_service_status'
#         verbose_name = '应用服务记录表'


class PublishSaltResultCheckReg(BaseModel):
    """
    salt 产线操作记录
    """
    opt_type = CharField(max_length=20, verbose_name='操作类型')
    regex_list = CharField(max_length=500, verbose_name='正则表达式列表')
    create_time = DateTimeField(verbose_name='创建时间')
    create_user = CharField(max_length=50, verbose_name='创建人')
    update_time = DateTimeField(verbose_name='更新时间')
    update_user = CharField(max_length=50, verbose_name='更新人')
    stamp = IntegerField(verbose_name='版本')

    class Meta:
        db_table = 'publish_salt_result_check_reg'
        verbose_name = 'salt结果检测正则匹配规则表'

class ServiceResults(BaseModel):
    """服务结果"""
    (RUNNING, SUCCESS, FAILURE) = ('running', 'success', 'failure')
    STATUS_CHOICE = ((RUNNING, '执行中'), (SUCCESS, '执行成功'), (FAILURE, '执行失败'))
    business_name = CharField(max_length=32, verbose_name='业务名称', default=None)
    operator = CharField(null=True, max_length=24, verbose_name='操作人')
    exec_cmd = CharField(max_length=1024, verbose_name='脚本路径')
    script_params = TextField(verbose_name='参数')
    log_path = CharField(max_length=128, verbose_name='日志路径')
    status = CharField(null=True, choices=STATUS_CHOICE, max_length=10, verbose_name='执行状态')
    detail = TextField(null=True, verbose_name='详情')
    start_at = DateTimeField(null=True, verbose_name='执行开始时间')
    end_at = DateTimeField(null=True, verbose_name='执行结束时间')

    class Meta:
        db_table = 'task_mgt_service_results'
        verbose_name = '服务结果'

class NacosNamespaceInfo(BaseModel):

    module_name = CharField(verbose_name='标准统一模块名', max_length=255)
    namespace = CharField(verbose_name='nacos的命名空间', max_length=255)

    class Meta:
        db_table = 'app_mgt_nacos_namespace_info'
        verbose_name = '应用和nacos命名空间的对应关系'

class EnvMgtNodeBindDynamic(BaseModel):
    bind_id = BigIntegerField(verbose_name='绑定表ID')
    bind_dynamic_desc = CharField(verbose_name='动态绑定说明', max_length=255)
    node_docker_service_ip = CharField(verbose_name='k8s容器中的service_ip', max_length=50)
    node_docker_dubbo_port = IntegerField(verbose_name='k8s容器中的dubbo端口')
    node_docker_service_hosts = CharField(verbose_name='k8s容器的hosts', max_length=100)
    config_repo_info_id = IntegerField(verbose_name='config_repo_info_id')
    node_config_repo_update_time = DateTimeField(verbose_name='配置制品更新时间')
    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')

    class Meta:
        db_table = 'env_mgt_node_bind_dynamic'
        verbose_name = '环境绑定动态表'

class TaskMgtJenkinsPublishPipelineInfo(BaseModel):
    action_id = IntegerField(verbose_name='action_id')
    job_name = CharField(verbose_name='job_name', max_length=255)
    exec_id = CharField(verbose_name='exec_id', max_length=200)
    build_id = IntegerField(verbose_name='build_id')
    create_time = DateTimeField(verbose_name='创建时间')
    create_user = CharField(verbose_name='创建人', max_length=100)
    update_time = DateTimeField(verbose_name='更新时间')
    update_user = CharField(verbose_name='更新人', max_length=100)
    class Meta:
        db_table = 'task_mgt_jenkins_publish_pipeline_info'
        verbose_name = 'jenkins批量发布流水线信息表'

class PublishMgtBackupInfo(BaseModel):

    create_user = CharField(verbose_name='创建人', max_length=20)
    create_time = DateTimeField(verbose_name='创建时间')
    update_user = CharField(verbose_name='修改人', max_length=20)
    update_time = DateTimeField(verbose_name='修改时间')
    stamp = IntegerField(verbose_name='版本')
    module_name = CharField(verbose_name='应用名', max_length=100)
    node_ip = CharField(verbose_name='节点IP', max_length=100)
    backup_date = DateTimeField(verbose_name='备份时间')
    backup_path = CharField(verbose_name='备份路径', max_length=255)
    salt_upd_log = CharField(verbose_name='salt更新日志', max_length=255)
    lib_build_time = DateTimeField(verbose_name='lib构建时间')
    publish_user = CharField(verbose_name='发布人', max_length=100)
    publish_time = DateTimeField(verbose_name='发布时间')
    publish_iteration_id = CharField(verbose_name='发布迭代ID', max_length=100)
    product_info_id = IntegerField(verbose_name='产品线ID')
    node_bind_id = IntegerField(verbose_name='节点绑定ID')
    backup_desc = CharField(verbose_name='备份描述', max_length=255)
    class Meta:
        db_table = 'publish_mgt_backup_info'
        verbose_name = '发布备份信息表'