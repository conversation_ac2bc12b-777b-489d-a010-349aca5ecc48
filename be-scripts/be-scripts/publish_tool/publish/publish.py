import sys
import datetime
import traceback
from enum import Enum, IntEnum
import subprocess
import json
import os
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)

from publish_tool.config.analysis_ini import LoadConfig
from app_mgt.models import AppBuildModel
from settings import logger
from publish_tool.utils.record import ScriptExecRecorder
from publish_tool.utils.call_salt import SaltTask
from publish_tool.publish.publish_ser import get_deploy_info, get_container_name_by_app_name


class PublishParamsBo:
    """建造者模式"""
    def __init__(self, builder):
        self._app_name = builder.app_name
        self._suite_code = builder.suite_code
        self._res_type = builder.res_type
        self._opt_type = builder.opt_type
        self._iteration_id = builder.iteration_id
        self._container_name = builder.container_name
        self._region_code = builder.region_code
        self._ip = builder.ip
        self._action_id = builder.action_id
        self._end_ver = builder.end_ver

    @property
    def app_name(self):
        return self._app_name

    @property
    def region_code(self):
        return self._region_code

    @property
    def suite_code(self):
        return self._suite_code

    @property
    def res_type(self):
        return self._res_type

    @property
    def iteration_id(self):
        return self._iteration_id

    @property
    def opt_type(self):
        return self._opt_type

    @property
    def container_name(self):
        return self._container_name

    @property
    def ip(self):
        return self._ip

    @property
    def action_id(self):
        return self._action_id

    @property
    def end_ver(self):
        return self._end_ver

    class Builder:
        app_name = ""
        suite_code = ""
        res_type = ""
        iteration_id = ""
        opt_type = ""
        container_name = ""
        ip = ""
        action_id = 0
        end_ver = ""
        region_code = ""
        __app_build_dto = AppBuildModel

        def set_region_code(self, region_code):
            self.region_code = region_code
            return self

        def set_app_name(self, app_name):
            self.app_name = app_name
            return self

        def set_suite_code(self, suite_code):
            self.suite_code = suite_code
            return self

        def set_res_type(self, res_type):
            self.res_type = res_type
            return self

        def set_iteration_id(self, iteration_id):
            self.iteration_id = iteration_id
            return self

        def set_opt_type(self, opt_type):
            self.opt_type = opt_type
            return self

        def set_container_name(self, container_name):
            self.container_name = container_name
            return self

        @staticmethod
        def _get_container_name(app_name, suite_code):
            container_name = get_container_name_by_app_name(app_name, suite_code)
            if not container_name:
                container_name = app_name
            return container_name

        def set_ip(self, ip):
            self.ip = ip
            return self

        def set_action_id(self, action_id):
            self.action_id = action_id
            return self

        def set_end_ver(self, end_ver):
            self.end_ver = end_ver
            return self

        def verify_basic_property(self):
            if self.app_name == "":
                raise Exception("app_name 不可为空")
            if self.suite_code == "":
                raise Exception("suite_code 不可为空")
            if self.res_type == "":
                raise Exception("res_type 不可为空")
            if self.opt_type == "":
                raise Exception("opt_type 不可为空")
            if self.region_code == "":
                raise Exception("region_code 不可为空")

        def build_script_bo(self):
            self.verify_basic_property()
            self.set_container_name(self._get_container_name(self.app_name, self.suite_code))
            if self.container_name == "":
                raise Exception("container_name 不可为空")
            return PublishParamsBo(self)

        def build_salt_bo(self):
            self.verify_basic_property()
            if self.ip == "":
                raise Exception("ip 不可为空")
            if self.action_id == 0:
                raise Exception("action_id 不可为0")
            package_type = ""
            for row in self.__app_build_dto.select().where(self.__app_build_dto.module_name == self.app_name):
                package_type = row.package_type
            if package_type == "":
                raise Exception("package_type 没获取到")
            if package_type == "dist":
                if self.end_ver == "":
                    raise Exception("end_ver 不可为空")
            return PublishParamsBo(self)

        def build_bo(self, publish_tool):
            exec_build = {"script": self.build_script_bo, "salt": self.build_salt_bo}
            return exec_build[publish_tool]()


class PublishTool:
    __load_config = LoadConfig
    # 所有的操作类型
    operation = Enum('operation', ("update", "restart", "deploy", "stop", "rollback"))
    # 所有的资源类型
    resource = Enum('resource', ("pkg", "config"))
    # 可用域
    region = Enum('region', ("test", "hd", "prod"))
    # 部署类型
    deploy_type = (1, 2) # 1、虚拟机 2、 k8s
    # 使用的发布工具
    exec_tool = Enum('exec_tool', ("salt", "script", "cmd"))

    # 发布状态
    _exec_res_status = Enum('_exec_res_status', ('success', 'failure'))

    # 可用域 + 部署类型 （1、虚拟机 2、 k8s  = 执行工具
    _map_exec_tool = {(region.prod.name, 1): exec_tool.salt.name,
                      (region.hd.name, 1): exec_tool.salt.name,
                      (region.test.name, 2): exec_tool.script.name,
                      (region.test.name, 1): exec_tool.salt.name}

    _map_script = {(resource.config.name, operation.update.name): ("DELETE_CONFIG", "CREATE_CONFIG"),
                   (resource.pkg.name, operation.deploy.name): ("UPDATE_PKG", "RESTART_PKG"),
                   (resource.pkg.name, operation.stop.name): ("DELETE_PKG",)}
    _project_dir = PROJECT_DIR
    _script_log_dir = os.path.join(os.path.dirname(os.path.dirname(_project_dir)), "logs", "publish_tool")
    _salt_task = SaltTask
    _publish_params_bo = PublishParamsBo

    def __init__(self):
        logger.info(self._script_log_dir)
        if not os.path.isdir(self._script_log_dir):
            os.makedirs(self._script_log_dir)

    @staticmethod
    def __get_deploy_info(*args):
        return get_deploy_info(*args)

    def _get_exec_tool(self, region_code, deploy_type):
        """
          目前发布工具 产线（prod，zb，hd）环境使用salt发布，测试环境（it01,it02...）使用 运维提供的 script的脚本发布，
        所以当前业务背景下使用 region_code+deploy_type 来确定 需要发发布工具，
        未来如果 有sql，MQ 等资源 类型发布 选用不同的工具再进行扩展 by 帅 20220622
        :param region_code: str 可用区
        :param deploy_type: init 部署类型
        :return: str 执行工具  1、虚拟机 2、 k8s
        """
        try:
            return self._map_exec_tool[(region_code, deploy_type)]
        except Exception as e:
            logger.error(str(e))
            raise IOError("{} 的可用区域，{} 的部署类型，找不到对应的发布工具！".format(region_code, deploy_type))

    def _get_exec_script_info(self, script_key):
        """

        :param script_key:
        :return:
        """
        lc = LoadConfig()
        res = lc.loading("k8s")[script_key]
        return res

    def _salt_publish(self, p_bo):
        salt_task = self._salt_task()
        status, result = salt_task.run_salt_task(p_bo.app_name, p_bo.res_type,
                                                 p_bo.opt_type, p_bo.ip, p_bo.suite_code,
                                                 p_bo.action_id, p_bo.region_code, p_bo.iteration_id)
        return status, result

    def _get_exec_script_key(self, res_type, opt_type):
        """
        获取 执行脚本业务key
        :param res_type: str "pkg", "config"...
        :param opt_type: str "update", "restart", "deploy", "stop", "rollback"...
        :return: tuple.  ("DELETE_CONFIG", "CREATE_CONFIG")
        """
        try:
            return self._map_script[(res_type, opt_type)]
        except Exception as e:
            logger.error(str(e))
            raise IOError("{} 的资源类型，{} 的部署类型，找不到对应的部署脚本！".format(res_type, opt_type))

    def _get_exec_script_cmd(self, script_key, script_log_file, p_bo):
        """

        :param script_info:
        :param p_bo:
        :return:
        """
        script_info = self._get_exec_script_info(script_key)
        logger.info("=========定义的脚本执行信息===========")
        logger.info(script_info)
        script_cmd = script_info['script']
        call_params = json.loads(script_info['call_params'])
        for script_param_name in call_params:
            try:
                script_param_value = None
                if hasattr(p_bo, script_param_name):
                    script_param_value = getattr(p_bo, script_param_name)
                else:
                    script_param_value = call_params.get(script_param_name)
                script_cmd = script_cmd + " " + script_param_value
            except Exception as e:
                logger.error(str(e))
                raise IOError("缺少参数 {}".format(script_param_name))
        return "python3.x {} &> {} && cat {}".format(script_cmd, script_log_file, script_log_file)

    def _exec_cmd(self, cmd):
        logger.info("Exec: " + cmd)
        p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = p.communicate()
        code = p.returncode
        logger.info("命令执行返回码 {}".format(code))
        if code != 0:
            logger.error("执行{}脚本失败".format(cmd))
            raise Exception(stderr.decode("utf-8"))
        else:
            stdout_str = stdout.decode("utf-8")
            logger.info(stdout_str)
            return stdout_str

    def script_log_file(self, script_key):
        script_log_dir = os.path.join(os.path.dirname(os.path.dirname(self._project_dir)), "logs", "publish_tool")
        logger.info("脚本日志目录{}".format(script_log_dir))
        if not os.path.isdir(script_log_dir):
            os.makedirs(script_log_dir)
        log_name = script_key + '_' + datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f") + '.log'
        logger.info(log_name)
        return os.path.join(script_log_dir, log_name)

    @ScriptExecRecorder()
    def _exec_script(self, script_key, script_log_file, p_bo):
        try:
            script_cmd = ""
            msg = ""
            script_cmd = self._get_exec_script_cmd(script_key, script_log_file, p_bo)
            logger.info("==========脚本执行命令==========")
            logger.info(script_cmd)
            msg = self._exec_cmd(script_cmd)
        except Exception as e:
            err_msg = traceback.format_exc()
            logger.error(err_msg)
            msg = err_msg
            raise Exception(msg)
        finally:
            return script_cmd, msg

    def _script_publish(self, p_bo):
        """

        :param p_bo:
        :return:
        """
        exec_script_key_tuple = self._get_exec_script_key(p_bo.res_type, p_bo.opt_type)
        logger.info("需要执行的脚本key为 {}".format(exec_script_key_tuple))
        status = self._exec_res_status.success.name
        try:
            for script_key in exec_script_key_tuple:
                script_log_file = self.script_log_file(script_key)
                script_cmd, msg = self._exec_script(script_key, script_log_file, p_bo)

        except Exception as e:
            err_msg = traceback.format_exc()
            logger.error(err_msg)
            msg = err_msg
            status = self._exec_res_status.failure.name

        finally:
            return status, msg

    def main(self, params):
        exec_publish = {"salt": self._salt_publish,
                        "script": self._script_publish}
        region_code, deploy_type = self.__get_deploy_info(params["app_name"], params["suite_code"])
        exec_tool = self._get_exec_tool(region_code, deploy_type)
        logger.info("发布工具参数params:{}".format(params["opt_type"]))
        p_bo_builder = self._publish_params_bo.Builder().set_app_name(params["app_name"])\
            .set_suite_code(params["suite_code"])\
            .set_opt_type(params["opt_type"])\
            .set_res_type(params["res_type"])\
            .set_iteration_id(params.get("iteration_id"))\
            .set_region_code(region_code)
        if exec_tool == "script":
            pass
        elif exec_tool == "salt":
            p_bo_builder.set_ip(params["ip"])
            p_bo_builder.set_action_id(params["action_id"])
            if "end_ver" in params:
                p_bo_builder.set_end_ver(params["end_ver"])
        p_bo = p_bo_builder.build_bo(exec_tool)
        logger.info(p_bo.__dict__)
        status, msg = exec_publish[exec_tool](p_bo)
        if status == self._exec_res_status.failure.name:
            sys.exit(1)
        return status, msg


if __name__ == "__main__":
    # sys_param =''' {"suite_code": "it29", "app_name": "acc-center-web",
    #              "res_type": "pkg", "opt_type": "deploy"}'''
    # sys_param =''' {"suite_code": "pd-prod", "app_name": "mring-itest-service",
    #              "res_type": "pkg", "opt_type": "deploy", "ip": "***************", "action_id": 11}'''
    # sys_param =''' {"suite_code": "it29", "app_name": "elephant-weapp",
    #              "res_type": "pkg", "opt_type": "update", "ip": "***************", "action_id": 11}'''
    try:
        sys_param = sys.argv[1]
        publish_tool = PublishTool()
        publish_tool.main(json.loads(sys_param))
        sys.exit(0)
    except Exception as e:
        err_msg = traceback.format_exc()
        logger.error(err_msg)
        sys.exit(1)


