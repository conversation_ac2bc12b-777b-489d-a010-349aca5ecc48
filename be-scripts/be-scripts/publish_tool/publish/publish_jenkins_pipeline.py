import os
import sys
import json
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from common.log.record_public_log import script_record
from publish_tool.publish.publish import PublishTool, PublishParamsBo
from publish_tool.publish.publish_ser import get_deploy_info, update_exec_verify, H5DeployStatusCollector, \
    update_publish_pipeline_build_id, update_execute_status, update_build_id, update_reboot_execute_history_status, \
    get_reboot_flow_status, update_reboot_execute_history_info
from settings import logger
class JenkinsPublishParamsBo(PublishParamsBo):
    pass
class JenkinsPublishTool(PublishTool):
    def __init__(self):
        super().__init__()
        self.pipeline_node_dict = {
            "run_publish_step_main": self.run_publish_step_main,
            "run_handle_data": self.run_handle_data,
            "reboot_handle_data": self.reboot_handle_data,
            "run_node_reboot": self.run_node_reboot,
        }

    def _salt_publish(self, p_bo):
        salt_task = self._salt_task()
        status, result = salt_task.jenkins_run_salt_task(app_name=p_bo.app_name, resource=p_bo.res_type,
                                                 op_type=p_bo.opt_type, ip=p_bo.ip, suite_code=p_bo.suite_code,
                                                 action_id=p_bo.action_id, region_code=p_bo.region_code,
                                                 iteration_id=p_bo.iteration_id)
        return status, result

    @staticmethod
    def __get_deploy_info(*args):
        return get_deploy_info(*args)

    def run_handle_data(self, params):
        update_publish_pipeline_build_id(params["build_id"], params["action_id"], params["exec_id"])
        update_execute_status(params["action_id"])
        update_exec_verify(params["exec_id"], params["build_id"], params["action_id"], params["job_name"])

    def reboot_handle_data(self, params):
        logger.info("重启:reboot_handle_data: {}".format(params))
        # 检查编排的状态
        flow_status = get_reboot_flow_status(params["action_id"])
        if not flow_status:
            raise Exception("非法执行：{}".format(params["action_id"]))
        # if 4 != flow_status.get('status'):
        #     raise Exception("批次号：{}, 服务端重启的编排非执行中状态".format(flow_status.get('batch_no')))
        update_reboot_execute_history_status(params["action_id"], params["build_id"])
        # 放重放
        update_exec_verify(params["exec_id"], params["build_id"], params["action_id"], params["job_name"])



    def run_node_reboot(self, params):
        logger.info("run_node_reboot: {}".format(params))
        action_id = params.get('action_id')
        publish_result = {"module_name":params.get('app_name'), "node_ip":params.get('ip')}
        final_status = "success"
        try:
            self.run_publish_step_main(params)
            params['opt_type'] = 'verify'
            self.run_publish_step_main(params)
        except Exception as e:
            final_status = "failure"
            logger.error(e)
            traceback.print_exc()
            raise Exception(e)
        finally:
            update_reboot_execute_history_info(action_id, final_status, json.dumps(publish_result))


    @script_record
    def run_publish_step_main(self, params):
        logger.info("run_publish_step_main:{}".format(params))
        exec_publish = {"salt": self._salt_publish,
                        "script": self._script_publish}
        region_code, deploy_type = self.__get_deploy_info(params["app_name"], params["suite_code"])
        exec_tool = self._get_exec_tool(region_code, deploy_type)
        logger.info("发布工具参数params:{}".format(params))
        p_bo_builder = self._publish_params_bo.Builder().set_app_name(params["app_name"])\
            .set_suite_code(params["suite_code"])\
            .set_opt_type(params["opt_type"])\
            .set_res_type(params["res_type"]) \
            .set_iteration_id(params.get("iteration_id")) \
            .set_region_code(region_code)
        if exec_tool == "script":
            pass
        elif exec_tool == "salt":
            p_bo_builder.set_ip(params["ip"])
            p_bo_builder.set_action_id(params["action_id"])
            if "end_ver" in params:
                p_bo_builder.set_end_ver(params["end_ver"])
        p_bo = p_bo_builder.build_bo(exec_tool)
        logger.info(p_bo.__dict__)
        status, msg = exec_publish[exec_tool](p_bo)
        if status == self._exec_res_status.failure.name:
            raise Exception(msg)
        return status, msg
    def run(self, node, params):
        logger.info("发布工具参数params:{}".format(params))
        logger.info("发布工具参数node:{}".format(node))
        try:
            self.pipeline_node_dict[node](params)
        except Exception as e:
            logger.error(e)
            raise Exception(e)

if __name__ == '__main__':
    param_list = sys.argv
    logger.info('参数：{}'.format(param_list))
    node = sys.argv[1]
    logger.info("发布工具参数node:{}".format(node))
    sys_param = sys.argv[2:][0]
    build_id = None
    if len(param_list) > 3:
        build_id = sys.argv[3:][0]
        logger.info("发布工具参数build_id:{}".format(build_id))
    sys_param = json.loads(sys_param)
    sys_param['exec_cmd'] = sys.argv[0]
    if build_id:
        sys_param['build_id'] = build_id
    logger.info("发布工具参数sys_param:{}".format(sys_param))
    jenkinspublishtool = JenkinsPublishTool()
    jenkinspublishtool.run(node, sys_param)