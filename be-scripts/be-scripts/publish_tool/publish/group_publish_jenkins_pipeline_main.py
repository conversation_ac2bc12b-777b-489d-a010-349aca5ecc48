import json
import os
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import logger, TEST_PUBLISH_AIO, PUBLISH_TOOL
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_remote_cmd


class GroupJenkinsPublishMain:

    def __init__(self, node, job_name, build_id, step_key, exec_id):
        self.node = node
        self.job_name = job_name
        self.build_id = build_id
        self.step_key = step_key
        self.exec_id = exec_id

    def parse_data(self):
        """
        从缓存文件解析数据
        :return:
        """
        file_name = self.job_name + "_" + self.exec_id + ".json"
        logger.info('读取文件的文件名{}'.format(file_name))
        with open(os.path.join(TEST_PUBLISH_AIO['nfs_batch_publish'], file_name), "r", encoding='utf-8') as f:
            content = f.read()
            logger.info("从缓存文件读数据: {}".format(content))
            param_dict = json.loads(content)
        return param_dict

    def jenkins_publish_main(self):

        try:
            param_dict = self.parse_data()
            sys_param = param_dict.get(self.step_key)
            sys_param["build_id"] = self.build_id
            sys_param["job_name"] = self.job_name
            dicta_json = json.dumps(sys_param)
            dicta_double_quotes = dicta_json.replace("'", "\"")
            logger.info("发布工具参数params:{}".format(dicta_double_quotes))
            cmd = "python3.x {} {} '{}'".format(PUBLISH_TOOL['jenkins_publish_cmd'], self.node, dicta_double_quotes)
            exec_remote_cmd(PUBLISH_TOOL['node_ip'], cmd)
        except Exception as e:
            logger.error(e)
            raise Exception(e)


if __name__ == '__main__':
    logger.info(sys.argv[1:])
    node = sys.argv[1]
    job_name = sys.argv[2]
    build_id = sys.argv[3]
    step_key = sys.argv[4]
    exec_id = sys.argv[5]
    jenkinspublishmain = GroupJenkinsPublishMain(node, job_name, build_id, step_key, exec_id)
    jenkinspublishmain.jenkins_publish_main()
