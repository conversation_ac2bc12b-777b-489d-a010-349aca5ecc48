import json

from settings import logger
import datetime
from publish_tool.publish.models import NacosNamespaceInfo, EnvMgtNodeBindDynamic, TaskMgtJenkinsPublishPipelineInfo, PublishMgtBackupInfo
from dao.connect.mysql import DBConnectionManager
try:
    from pymysql.converters import escape_string
except:
    import MySQLdb.escape_string

def get_deploy_info(app_name, suite_code):
    sql = """SELECT DISTINCT t.suite_code,f.module_name,m.region_group ,f.deploy_type 
             FROM env_mgt_node_bind f LEFT JOIN 
             env_mgt_suite t ON f.suite_id = t.id LEFT JOIN env_mgt_region m ON t.region_id=m.id 
             WHERE f.module_name = "{}" AND t.suite_code = "{}"
             AND m.region_is_active IN ("1",1)
             """.format(app_name, suite_code)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["region_group"], row["deploy_type"]
    return "", ""


def get_container_name_by_app_name(app_name, suite_code):
    sql = """SELECT IF(b.container_name is NULL, pdi.container_name,b.container_name)  AS container_name 
             FROM env_mgt_node_bind b
             LEFT JOIN publish_deploy_info pdi ON b.module_name = pdi.module_name
             LEFT JOIN env_mgt_suite s ON s.id = b.suite_id
             WHERE s.suite_code = '{}' AND b.module_name = '{}'
          """.format(suite_code, app_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row["container_name"]
    return ""

def get_build_id(action_id,exec_id,job_name):
    result = ''
    sql = '''
    SELECT build_id FROM `task_mgt_jenkins_publish_pipeline_info` WHERE action_id = '{}' 
    AND exec_id = '{}' AND job_name = '{}'
    '''.format(action_id, exec_id, job_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchone()
    if result:
        return result['build_id']
    logger.info('获取到build_id{}'.format(result))
    logger.info(result)
    return result

def update_build_id(exec_id, build_id, action_id, job_name):
    update_time = datetime.datetime.now()
    sql = '''
    update task_mgt_jenkins_publish_pipeline_info set build_id="{}" ,update_time="{}" where exec_id ="{}" 
    and action_id = "{}" and job_name = "{}"
    '''.format(build_id, update_time, exec_id, action_id, job_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def update_reboot_execute_history_status(action_id, build_id=None, status=None, phase_stage=None):
    sql = '''
        update publish_mgt_reboot_execute_history set update_time=now(), finish_time=now()
    '''
    if build_id:
        sql += '''
         ,start_build_id="{}"
        '''.format(build_id)
    if status:
        sql += '''
             ,status="{}"
            '''.format(status)
    if phase_stage:
        sql += '''
             ,fail_phase="{}"
            '''.format(phase_stage)
    sql += '''
        where action_id = "{}" 
        '''.format(action_id)
    if build_id:
        sql += ''' 
        and start_build_id is null
        '''
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()

def update_reboot_flow_status(status, action_id):
    sql = '''
        UPDATE publish_mgt_reboot_flow SET `status` = {}
        WHERE id IN (
        SELECT f_id FROM publish_mgt_reboot_execute_history WHERE action_id = {}
        )
    '''.format(status, action_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        db.connection.commit()


def get_reboot_flow_status(action_id):
    sql = '''
        SELECT f.batch_no,f.status FROM `publish_mgt_reboot_execute_history` h
        inner join publish_mgt_reboot_flow f on h.f_id = f.id
        WHERE h.action_id = {}
    '''.format(action_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchone()
    if result:
        return {"batch_no": result['batch_no'], "status": result['status']}
    return None

def update_exec_verify(exec_id, build_id, action_id, job_name):
    try:
        if get_build_id(action_id, exec_id, job_name):
            logger.info("{}重复执行了".format(exec_id))
            raise Exception("{}重复执行了".format(exec_id))
    except Exception as e:
        logger.error(e)
        raise Exception(e)
    finally:
        update_build_id(exec_id, build_id, action_id, job_name)
    return True

def update_publish_pipeline_build_id(build_id, action_id, exec_id):
    try:
        sql = '''
            update iter_mgt_jenkins_publish_pipeline_info set build_id={}, update_time = now(), lock_status = 'unlock'
            where action_id = {} and exec_id = {}
            '''.format(build_id, action_id, exec_id)
        with DBConnectionManager() as db:
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()
    except Exception as e:
        logger.error(e)


def release_reboot_lock(status):
    try:
        sql = '''
            update publish_mgt_reboot set status='{}', update_time = now()
            where op_type = 'batch_reboot' and status = 'running'
            '''.format(status)
        with DBConnectionManager() as db:
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()
    except Exception as e:
        logger.error(e)


def update_reboot_execute_history_info(action_id, status, publish_result):
    try:
        if 'failure' == status:
            sql = '''
                    UPDATE publish_mgt_reboot_execute_history 
                    SET fail_node_json = JSON_ARRAY_APPEND(fail_node_json, '$', '{publish_result}')
                    WHERE action_id = {action_id}
                '''.format(publish_result=publish_result, action_id=action_id)
        else:
            sql = '''
                    UPDATE publish_mgt_reboot_execute_history 
                    SET result_json = JSON_ARRAY_APPEND(result_json, '$', '{publish_result}')
                    WHERE action_id = {action_id}
                '''.format(publish_result=publish_result, action_id=action_id)
        with DBConnectionManager() as db:
            logger.info(sql)
            db.cur.execute(sql)
            db.connection.commit()
    except Exception as e:
        logger.error(e)

def update_execute_status(action_id):
    # 检查是否通过，check_success--->running
    sql = '''update iter_mgt_jenkins_publish_pipeline_info set execute_status="running" where action_id = %s and execute_status = "check_success"
          '''
    with DBConnectionManager() as db:
        result = db.cur.execute(sql, (action_id,))
        db.connection.commit()
        logger.info("更新执行状态成功，影响行数{}".format(result))


def get_legitimacy_status(action_id, job_name, exec_id, build_id):
    if TaskMgtJenkinsPublishPipelineInfo.select().where(TaskMgtJenkinsPublishPipelineInfo.action_id == action_id
                                                     and TaskMgtJenkinsPublishPipelineInfo.job_name == job_name
                                                     and TaskMgtJenkinsPublishPipelineInfo.exec_id == exec_id
                                                     and TaskMgtJenkinsPublishPipelineInfo.build_id == build_id).exists():
        return True
    else:
        return False

def get_publish_type_by_action_id(action_id):
    sql = '''SELECT DISTINCT r.action_item, r.action_value FROM
     user_action_record r WHERE r.id = "{}";'''.format(action_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchone()
    action_item = result['action_item']
    return action_item


def get_execute_flow_json_by_action_id(action_id):
    sql = '''
    SELECT a.script_params FROM task_mgt_service_results a 
    LEFT JOIN task_mgt_jenkins_publish_pipeline_info b ON a.id = b.exec_id
    WHERE b.action_id = {} '''.format(action_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchone()
    if not result:
        return None
    script_params = json.loads(result.get('script_params'))
    return script_params

def get_history_action_id(job_name, build_id):
    sql = '''
            SELECT action_id
            FROM task_mgt_jenkins_publish_pipeline_info
            WHERE job_name = '{}' AND build_id = {} 
        '''.format(job_name, build_id)
    action_id = None
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result = db.cur.fetchone()
        if result:
            action_id = result.get('action_id')
        if not action_id:
            job_sql = """
                SELECT action_id
                FROM task_mgt_jenkins_publish_pipeline_info
                WHERE job_name = '{}'
                ORDER BY id DESC LIMIT 1 
            """.format(job_name)
            db.cur.execute(job_sql)
            data = db.cur.fetchone()
            action_id = data.get('action_id')

    return action_id


def get_reboot_flow_json_by_action_id(action_id):
    sql = '''
    SELECT a.flow_json
    FROM publish_mgt_reboot_flow a 
    LEFT JOIN publish_mgt_reboot_execute_history b ON a.id = b.f_id
    WHERE b.action_id = {} '''.format(action_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchone()
    action_item = result.get('flow_json')
    return action_item


def get_reboot_execute_history(action_id):
    sql = '''
    SELECT f_id, `status`,execute_time,start_build_id,retry_times,fail_phase,fail_node_json,result_json
    FROM publish_mgt_reboot_execute_history
    WHERE action_id = {} '''.format(action_id)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result = db.cur.fetchone()
        if not result:
            return None
        return {
            "f_id": result["f_id"],
            "status": result["status"],
            "execute_time": result["execute_time"],
            "start_build_id": result["start_build_id"],
            "retry_times": result["retry_times"],
            "fail_phase": result["fail_phase"],
            "fail_node_json": result["fail_node_json"],
            "result_json": result["result_json"]
        }

def get_publish_salt_result(action_id, ip):
    status_list = []
    stage_list = []
    sql = '''SELECT t2.request_status, t2.request_result, t2.deploy_stage  
        FROM (  
            SELECT request_status, request_result, deploy_stage, MAX(end_at) AS max_end_at  
            FROM task_mgt_salt_operation_results  
            WHERE action_id = "{}" AND ip = "{}"  
            GROUP BY  action_id,ip,deploy_stage  
        ) AS t1  
        JOIN task_mgt_salt_operation_results AS t2 ON
         t1.deploy_stage = t2.deploy_stage AND
          t1.max_end_at = t2.end_at;'''.format(action_id, ip)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    result = db.cur.fetchall()
    for item in result:
        if item['request_status'] not in status_list:
            status_list.append(item['request_status'])
        if item['deploy_stage'] not in stage_list:
            stage_list.append(item['deploy_stage'])
    return status_list, stage_list
class H5DeployStatusCollector:
    @staticmethod
    def update_h5_deploy_status(action_id, status, message, app_name=None, ip=None):
        """
        """
        sql = ''' update task_mgt_deploy_result set status="{}",message='{}' where action_id ="{}" ''' \
            .format(status, escape_string("{}".format(message)), action_id)
        if status == 'failure':
            sql = ''' update task_mgt_deploy_result set status="{}",message='{}' where action_id ="{}"
                 and status not in ("success","failure")''' \
                .format(status, escape_string("{}".format(message)), action_id)
        if app_name:
            sql += ' and app_name = "{}"'.format(app_name)
        if ip:
            sql += ' and ip = "{}"'.format(ip)
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            db.connection.commit()
        logger.debug(sql)

    def update_deploy_status(self, action_id, status, message, app_name=None, ip=None, op_type=None):
        action_item = get_publish_type_by_action_id(action_id)
        status_list, stage_list = get_publish_salt_result(action_id, ip)

        if status == 'failure':
            self.update_h5_deploy_status(action_id, 'failure', message, None)
        if action_item == "update_and_deploy_and_verify":  # 此处的action_item是update_and_deploy_and_verify以verify为结束标志更新节点状态
            if op_type == 'verify':
                if status_list and 'failure' in status_list:
                    self.update_h5_deploy_status(action_id, 'failure', message, None)
                else:
                    if status_list:
                        self.update_h5_deploy_status(action_id, 'success', message, None, ip)
        elif action_item == "update_and_deploy":  # 此处的action_item是update_and_deploy以deploy为结束标志更新节点状态
            if op_type == 'deploy':
                if status_list and 'failure' in status_list:
                    self.update_h5_deploy_status(action_id, 'failure', message, None)
                else:
                    if status_list:
                        self.update_h5_deploy_status(action_id, 'success', message, None, ip)
        else:
            self.update_h5_deploy_status(action_id, status, message, None, ip)

    # def insert_deploy_detail(self, exec_status, detail, start_at, end_at, opt_type, action_id, app_name, ip):
    #     result_id = self.get_deploy_result_id(action_id, app_name, ip)
    #     logger.info('开始写入发布记录表，发布ID：{}'.format(result_id))
        # if result_id:
        #     TaskMgtDeployDetail.create(
        #         result_id=result_id,
        #         status=exec_status,
        #         message=detail,
        #         op_type=opt_type,
        #         begin_time=start_at,
        #         end_time=end_at,
        #     )

    # @staticmethod
    # def get_deploy_result_id(action_id, app_name, ip):
    #     sql = '''SELECT t1.id
    #         FROM task_mgt_deploy_result t1
    #         WHERE action_id = "{}" and app_name = "{}" and ip = "{}"
    #     '''.format(action_id, app_name, ip)
    #     with DBConnectionManager() as db:
    #         db.cur.execute(sql)
    #     for row in db.cur.fetchall():
    #         return row['id']
    #     return None



class RepoInfoRecorder:
    _repo_table_name = "product_mgt_product_info"
    _bind_table_repo_id_field = "lib_repo_info_id"
    _bind_table_repo_update_time_field = "node_lib_repo_update_time"

    def __init__(self, app_name, iteration_id, node_ip=None, suite_code=None, node_docker=None):
        """
        制品信息记录者
        """
        self.app_name = app_name
        self.iteration_id = iteration_id
        self.node_ip = node_ip
        self.suite_code = suite_code
        self.node_docker = node_docker

    def set_node_ip(self, node_ip):
        self.node_ip = node_ip

    def set_suite_code(self, suite_code):
        self.suite_code = suite_code

    def set_node_docker(self, node_docker):
        self.node_docker = node_docker

    def _get_repo_id(self, module_name, iteration_id=None, suite_code=None):
        """
        获取制品信息
        return: num 0 表示没有
        """
        lib_repo_id = 0
        if iteration_id:
            sql = 'SELECT id FROM {} WHERE module_name="{}" AND iteration_id="{}"'. \
                format(self._repo_table_name, module_name, iteration_id)
            if suite_code:
                sql = sql + ' and suite_code = "{}"'.format(suite_code)
        else:
            sql = 'SELECT id FROM {} WHERE module_name="{}" and lib_repo_version_log like "回滚制品%"'. \
                format(self._repo_table_name, module_name)
        sql = sql + ' ORDER BY create_time DESC'
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row['id']
        return lib_repo_id


    def record_info(self):
        lib_repo_id = self._get_repo_id(self.app_name, self.iteration_id, suite_code=self.suite_code)
        if lib_repo_id == 0:
            lib_repo_id = self._get_repo_id(self.app_name, self.iteration_id)
        if not self.iteration_id:
            lib_repo_id = self._get_repo_id(self.app_name)
        node_bind_id_list = self._get_env_node_bind_id(self.app_name,
                                                       node_ip=self.node_ip,
                                                       node_docker=self.node_docker,
                                                       suite_code=self.suite_code)
        logger.info("需要更新的node_bind list {}".format(node_bind_id_list))
        self._update_node_bind_lib_repo_info(node_bind_id_list, lib_repo_id)

    def _get_env_node_bind_id(self, module_name, node_ip=None, node_docker=None, suite_code=None):
        """
        """
        node_bind_id_list = []
        if node_ip:
            if suite_code:
                sql = '''SELECT b.id FROM env_mgt_node_bind b LEFT JOIN env_mgt_node n ON b.node_id = n.id
                        LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
                        WHERE n.node_ip="{}" AND b.module_name = "{}" AND s.suite_code="{}"
                           '''.format(node_ip, module_name, suite_code)
            else:
                sql = '''SELECT b.id FROM env_mgt_node_bind b LEFT JOIN env_mgt_node n ON b.node_id = n.id
                         WHERE n.node_ip="{}" AND b.module_name = "{}"
                       '''.format(node_ip, module_name)
        elif node_docker:
            sql = '''SELECT b.id FROM env_mgt_node_bind b WHERE b.node_docker="{}" AND b.module_name = "{}"
                       '''.format(node_docker, module_name)
        elif suite_code:
            sql = '''SELECT b.id FROM env_mgt_node_bind b LEFT JOIN env_mgt_suite n ON b.suite_id = n.id
             WHERE n.suite_code="{}" AND b.module_name = "{}"
                        '''.format(suite_code, module_name)
        else:
            raise IOError("node_ip 和 node_docker 至少有一个变量")
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            node_bind_id_list.append(row['id'])

        return node_bind_id_list

    def _update_node_bind_lib_repo_info(self, node_bind_id_list, repo_info_id):
        """
        """
        sql = 'update env_mgt_node_bind set {} = {},{}="{}" '.format(self._bind_table_repo_id_field, repo_info_id,
                                                                     self._bind_table_repo_update_time_field,
                                                                     datetime.datetime.now())
        if len(node_bind_id_list) == 1:
            condition = ' where id = {}'.format(node_bind_id_list[0])
        elif len(node_bind_id_list) > 1:
            condition = ' where id in {}'.format(tuple(node_bind_id_list))
        else:
            raise IOError("未获取到应用绑定环境的信息")
        sql = sql + condition
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            db.connection.commit()
class LibRepoInfoRecorder(RepoInfoRecorder):
    _repo_table_name = "product_mgt_product_info"
    _bind_table_repo_id_field = "lib_repo_info_id"
    _bind_table_repo_update_time_field = "node_lib_repo_update_time"

class ConfigRepoInfoRecorder(RepoInfoRecorder):
    _repo_table_name = "product_mgt_config_repo_info"
    _bind_table_repo_id_field = "config_repo_info_id"
    _bind_table_repo_update_time_field = "node_config_repo_update_time"

    def record_info(self):
        if NacosNamespaceInfo.select().where(NacosNamespaceInfo.module_name == self.app_name).exists():
            namespace = NacosNamespaceInfo.select().where(NacosNamespaceInfo.module_name == self.app_name).last().namespace
        else:
            namespace = self.app_name

        repo_id = self._get_repo_id(namespace, self.iteration_id, suite_code=self.suite_code)

        node_bind_id_list = self._get_env_node_bind_id(self.app_name,
                                                       node_ip=self.node_ip,
                                                       node_docker=self.node_docker,
                                                       suite_code=self.suite_code)
        logger.info("需要更新的node_bind list {}".format(node_bind_id_list))
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        for node_bind_id in node_bind_id_list:
            p, created = EnvMgtNodeBindDynamic.get_or_create(bind_id=node_bind_id,
                                                                     defaults={'config_repo_info_id': repo_id,
                                                                               'node_config_repo_update_time': cur_time,
                                                                               'create_user': 'spider',
                                                                               'create_time': cur_time})
            if not created:
                EnvMgtNodeBindDynamic.update(config_repo_info_id=repo_id, node_config_repo_update_time=cur_time)\
                    .where(EnvMgtNodeBindDynamic.bind_id == node_bind_id).execute()

class PublishMgtBackupInfoRecorder(RepoInfoRecorder):
    _repo_table_name = "publish_mgt_backup_info"
    def __init__(self, module_name,
                 node_ip, backup_path, salt_upd_log,  publish_user,
                 publish_iteration_id, backup_desc, node_docker=None, suite_code=None,
                 opt_type=None):
        # self.create_user = create_user
        # self.create_time = create_time
        # self.update_user = update_user
        # self.update_time = update_time
        self.module_name = module_name
        self.node_ip = node_ip
        # self.backup_date = backup_date
        self.backup_path = backup_path
        self.salt_upd_log = salt_upd_log
        # self.lib_build_time = lib_build_time
        self.publish_user = publish_user
        # self.publish_time = publish_time
        self.publish_iteration_id = publish_iteration_id
        # self.product_info_id = product_info_id
        # self.node_bind_id = node_bind_id
        self.backup_desc = backup_desc
        self.node_docker = node_docker
        self.suite_code = suite_code
        self.opt_type = opt_type

    def _get_env_node_bind_info(self, module_name, node_ip=None, node_docker=None, suite_code=None):
        """
        获取节点绑定表得id
        :param module_name: 应用名
        :param node_ip: ip
        :param node_docker: docker环境标识
        :return: list [] 表示没获取到
        """
        node_bind_info = {}
        if node_ip:
            if suite_code:
                sql = '''SELECT b.id,b.lib_repo_info_id,b.node_lib_repo_update_time 
                FROM env_mgt_node_bind b LEFT JOIN env_mgt_node n ON b.node_id = n.id
                        LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
                        WHERE n.node_ip="{}" AND b.module_name = "{}" AND s.suite_code="{}"
                           '''.format(node_ip, module_name, suite_code)
            else:
                sql = '''SELECT b.id,b.lib_repo_info_id,b.node_lib_repo_update_time 
                FROM env_mgt_node_bind b LEFT JOIN env_mgt_node n ON b.node_id = n.id
                         WHERE n.node_ip="{}" AND b.module_name = "{}"
                       '''.format(node_ip, module_name)
        elif node_docker:
            sql = '''SELECT b.id,b.lib_repo_info_id,b.node_lib_repo_update_time 
            FROM env_mgt_node_bind b WHERE b.node_docker="{}" AND b.module_name = "{}"
                       '''.format(node_docker, module_name)
        elif suite_code:
            sql = '''SELECT b.id,b.lib_repo_info_id,b.node_lib_repo_update_time 
            FROM env_mgt_node_bind b LEFT JOIN env_mgt_suite n ON b.suite_id = n.id
             WHERE n.suite_code="{}" AND b.module_name = "{}"
                        '''.format(suite_code, module_name)
        else:
            raise IOError("node_ip 和 node_docker 至少有一个变量")
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            node_bind_info['node_id'] = row['id']
            node_bind_info['lib_repo_info_id'] = row['lib_repo_info_id']
            node_bind_info['node_lib_repo_update_time'] = row['node_lib_repo_update_time']

        return node_bind_info

    def _get_repo_info(self, module_name, node_bind_repo_id):
        """
        获取制品信息
        :param module_name: 应用名
        :param iteration_id: 迭代名称
        :param suite_code: 环境套
        :return: num 0 表示没有
        """
        lib_repo_id = 0
        lib_build_time = 0
        sql = '''
        SELECT id,create_time FROM product_mgt_product_info WHERE module_name="{}" AND id="{}"
        '''.format(module_name, node_bind_repo_id)
        # if suite_code:
        # sql = sql + ' and id = "{}"'.format(node_bind_repo_id)
        sql = sql + ' ORDER BY create_time DESC'
        logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row['id'], row['create_time']
        return lib_repo_id, lib_build_time

    def record_info(self):
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        node_bind_info = self._get_env_node_bind_info(self.module_name, node_ip=self.node_ip,
                                                      node_docker=self.node_docker,
                                                      suite_code=self.suite_code)
        try:
            if node_bind_info:
                node_bind_id = node_bind_info['node_id']
                node_bind_time = node_bind_info['node_lib_repo_update_time']
                node_bind_repo_id = node_bind_info['lib_repo_info_id']
            lib_repo_id, lib_build_time = self._get_repo_info(self.module_name, node_bind_repo_id)
            logger.info("node_bind_id {}".format(node_bind_id))
            logger.info("需要更新的node_bind_time {}".format(node_bind_time))
            logger.info("需要更新的node_bind_time {}".format(node_bind_time.day))
            logger.info("需要更新的node_bind_repo_id {}".format(node_bind_repo_id))
            cur_day_time = datetime.datetime.strptime(cur_time, "%Y-%m-%d %H:%M:%S")
            logger.info("需要更新的cur_time {}".format(cur_day_time.day))
            logger.info("需要更新的cur_time {}".format(cur_day_time.month))
            logger.info("需要更新的cur_time {}".format(cur_day_time.year))

            backupobj = PublishMgtBackupInfo.select().where(PublishMgtBackupInfo.node_bind_id == node_bind_id
                                                            and PublishMgtBackupInfo.module_name == self.module_name
                                                            and PublishMgtBackupInfo.node_ip == self.node_ip).order_by(
                PublishMgtBackupInfo.backup_date.desc()).get_or_none()
            if backupobj:
                backuptime = backupobj.backup_date
            else:
                PublishMgtBackupInfo.create(
                    create_user='howbuyscm', create_time=cur_time,
                    update_user='howbuyscm', update_time=cur_time,
                    stamp=0, module_name=self.module_name, node_ip=self.node_ip,
                    backup_date=cur_time, backup_path=self.backup_path,
                    salt_upd_log=self.salt_upd_log, lib_build_time=lib_build_time,
                    publish_user=self.publish_user, publish_time=node_bind_time,
                    publish_iteration_id=self.publish_iteration_id,
                    product_info_id=node_bind_repo_id, node_bind_id=node_bind_id,
                    backup_desc=self.backup_desc
                )
                return True
                # backuptime = backupobj.values('backup_date').last()['backup_date']
            if backuptime:
                if cur_day_time.day == backuptime.day and cur_day_time.month == backuptime.month and cur_day_time.year == backuptime.year:
                    logger.info('当天已经备份过，不再备份')
                else:
                    PublishMgtBackupInfo.create(
                        create_user='howbuyscm', create_time=cur_time,
                        update_user='howbuyscm', update_time=cur_time,
                        stamp=0, module_name=self.module_name, node_ip=self.node_ip,
                        backup_date=cur_time, backup_path=self.backup_path,
                        salt_upd_log=self.salt_upd_log, lib_build_time=lib_build_time,
                        publish_user=self.publish_user, publish_time=node_bind_time,
                        publish_iteration_id=self.publish_iteration_id,
                        product_info_id=node_bind_repo_id, node_bind_id=node_bind_id,
                        backup_desc=self.backup_desc
                    )
        except Exception as ex:
            logger.error(ex)
