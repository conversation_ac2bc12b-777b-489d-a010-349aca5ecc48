[DELETE_CONFIG]
script = /home/<USER>/hb-k8s/k8s_build_script/k8s_app_deploy/k8s_building_cm.py delete
call_params = {"suite_code":"环境套","container_name":"容器名"}
desc = 删除外移配置

[CREATE_CONFIG]
script = /home/<USER>/hb-k8s/k8s_build_script/k8s_app_deploy/k8s_building_cm.py create
call_params = {"suite_code":"环境套","container_name":"容器名"}
desc = 创建外移配置

[UPDATE_PKG]
script = /home/<USER>/hb-k8s/k8s_build_script/k8s_app_deploy/k8s_kustomsize_build.py
call_params = {"suite_code":"环境套","container_name":"容器名"}
desc = 制作镜像》》中转库到目标服务

[RESTART_PKG]
script = /home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_pod_restart.py
call_params = {"suite_code":"环境套","container_name":"容器名"}
desc = 重启镜像 》》 重启服务

[DELETE_PKG]
script = /home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_pod_stop.py
call_params = {"suite_code":"环境套","container_name":"容器名", "operation":"delete"}
desc = 删除POD