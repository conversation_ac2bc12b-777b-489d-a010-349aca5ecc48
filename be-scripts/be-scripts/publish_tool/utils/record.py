import datetime
from settings import logger
from publish_tool.publish.models import ScriptExecRecordModel
from enum import Enum, IntEnum


class ScriptExecRecorder:
    __script_exec_record = ScriptExecRecordModel
    _exec_status = Enum('_exec_status', ('running', 'success', 'failure'))

    def _begin_record(self, script_key, log_path, status, start_at):
        p = self.__script_exec_record.create(script_key=script_key,
                                         log_path=log_path,
                                         status=status,
                                         start_at=start_at)
        return p

    def _end_record(self, id, script_cmd, status, detail, end_at):
        self.__script_exec_record.update(exec_exec=script_cmd, detail=detail, status=status, end_at=end_at)\
            .where(self.__script_exec_record.id == id).execute()

    def __call__(self, func):
        def inner(*args, **kwargs):
            try:
                logger.debug(args)
                script_key = args[1]
                log_path = args[2]
                start_time = datetime.datetime.now()
                record_id = self._begin_record(script_key, log_path, self._exec_status.running.name, start_time)
                script_cmd, msg = func(*args, **kwargs)
                exec_status = self._exec_status.success.name
            except Exception as err:
                logger.info(str(err))
                exec_status = self._exec_status.failure.name
                raise str(err)
            finally:
                end_time = datetime.datetime.now()
                self._end_record(record_id, script_cmd, exec_status, msg, end_time)
                logger.info('{} 耗时: {}s'.format(script_key, str((end_time - start_time).seconds)))
                return script_cmd, msg
        return inner
