import json
from abc import ABCMeta, abstractmethod

from iter_mgt.models import UserActionRecord
from product_mgt.models import ProductMgtProductInfo
from publish_tool.publish.models import SaltOperationResults
from settings import logger


class ComparePublishResult:

    def get_real_result_dict(self, minion_id, result):
        logger.info("minion_id: {}".format(minion_id))
        logger.info("result.get(return)： {}".format(result.get("return")[0]))
        real_result_dict = {}
        if minion_id in result.get("return")[0]:
            real_result = result.get("return")[0].get(minion_id)
            real_result_dict = json.loads(real_result)
        return real_result_dict

    def get_compare_info(self, first_details):
        package_info = first_details.get("packageInfo")
        logger.info("返回结果中的packageInfo信息：{}".format(package_info))

        if not package_info:
            logger.error("返回结果中没找到packageInfo信息")
            return '', '', ''
        details = package_info.get("details")
        if not details:
            logger.error("返回结果中的packageInfo没找到details信息")
            return '', '', ''
        nacos_name_space = details.get("nacosNamespace")
        nacos_group = details.get("nacosGroup")
        package_stamp = details.get("packageStamp")

        logger.info("返回details中的nacosNamespace：{}".format(nacos_name_space))
        logger.info("返回details中的nacosGroup：{}".format(nacos_group))
        logger.info("返回details中的packageStamp：{}".format(package_stamp))

        return nacos_name_space, nacos_group, package_stamp

    def compare(self, nacos_name_space, nacos_group, package_stamp, action_id, suite_code, module_name, iteration_id):
        uar = UserActionRecord.select().where(UserActionRecord.id == action_id)
        if uar:
            # 20250715 batch_reboot 重启不做包版本检查 wenlong.zhang
            if "batch_reboot" == uar[0].action_item:
                logger.info("batch_reboot校验开始,iteration_id: {}".format(iteration_id))
                if nacos_name_space == suite_code and iteration_id and nacos_group == iteration_id.split("_")[1:][0]:
                    logger.info("batch_reboot校验成功")
                    return SaltOperationResults.SUCCESS
                else:
                    logger.error("服务返回的配置的环境和版本信息与用户发布的信息不一致")
                    return SaltOperationResults.FAILURE
            action_value_dict = eval(uar[0].action_value)
            logger.info("用户发布的应用是: {}".format(module_name))
            logger.info("用户发布的环境是: {}".format(suite_code))
            logger.info("用户发布的迭代信息是: {}".format(action_value_dict.get("iteration_id")))
            logger.info("用户发布的分支信息是: {}".format(action_value_dict.get(
                "iteration_id").split("_")[1:][0]))
            if nacos_name_space == suite_code and nacos_group == action_value_dict.get(
                    "iteration_id").split("_")[1:][0]:
                pmp = ProductMgtProductInfo.select().where(
                    ProductMgtProductInfo.iteration_id == action_value_dict.get("iteration_id"),
                    ProductMgtProductInfo.module_name == module_name)
                logger.info("最后一次构建的packageStamp：{}".format(pmp[-1].package_stamp))

                if pmp and pmp[-1].package_stamp == package_stamp:
                    logger.info("校验成功")
                    return SaltOperationResults.SUCCESS
                else:
                    logger.error("服务返回的packageStamp和最后一次构建的packageStamp不一致")
            else:
                logger.error("服务返回的配置的环境和版本信息与用户发布的信息不一致")
        else:
            logger.error("没有找到用户发布的信息")
        return SaltOperationResults.FAILURE


class AnalyzeVerifyResultStrategy(metaclass=ABCMeta):

    @abstractmethod
    def analyze(self, data):
        pass


class AnalyzeSpring2312Strategy(AnalyzeVerifyResultStrategy, ComparePublishResult):
    # 分析spring 2.3.12 包的返回结果

    def analyze(self, data):
        minion_id = data.get("minion_id")
        result = data.get("result")
        action_id = data.get("action_id")
        suite_code = data.get("suite_code")
        app_name = data.get("app_name")
        iteration_id = data.get("iteration_id")
        real_result_dict = self.get_real_result_dict(minion_id, result)
        real_status = real_result_dict.get("status")
        logger.info("real_status: {}".format(real_status))
        if real_status != "UP":
            return SaltOperationResults.FAILURE
        components = real_result_dict.get("components")
        logger.info("返回结果中的components信息：{}".format(components))

        if not components:
            return SaltOperationResults.FAILURE

        nacos_name_space, nacos_group, package_stamp = self.get_compare_info(components)
        if not nacos_name_space or not nacos_group or not package_stamp:
            return SaltOperationResults.FAILURE

        return self.compare(nacos_name_space, nacos_group, package_stamp, action_id, suite_code, app_name, iteration_id)


class AnalyzeSpring218Strategy(AnalyzeVerifyResultStrategy, ComparePublishResult):
    # 分析spring 2.1.8 包的返回结果
    def analyze(self, data):
        minion_id = data.get("minion_id")
        result = data.get("result")
        action_id = data.get("action_id")
        suite_code = data.get("suite_code")
        app_name = data.get("app_name")
        iteration_id = data.get("iteration_id")
        real_result_dict = self.get_real_result_dict(minion_id, result)
        real_status = real_result_dict.get("status")
        logger.info("real_status: {}".format(real_status))
        if real_status != "UP":
            return SaltOperationResults.FAILURE
        first_details = real_result_dict.get("details")
        logger.info("返回结果中第一层的details信息：{}".format(first_details))
        if not first_details:
            return SaltOperationResults.FAILURE

        nacos_name_space, nacos_group, package_stamp = self.get_compare_info(first_details)

        if not nacos_name_space or not nacos_group or not package_stamp:
            return SaltOperationResults.FAILURE
        return self.compare(nacos_name_space, nacos_group, package_stamp, action_id, suite_code, app_name, iteration_id)


class AnalyzeContext:
    def __init__(self, strategy, data):
        self.strategy = strategy
        self.data = data

    def set_strategy(self, strategy):
        self.strategy = strategy

    def do_strategy(self):
        return self.strategy.analyze(self.data)
