from settings import logger
from dao.connect.mysql import DBConnectionManager


def get_salt_cmd_info(app_name, operate_type, node_ip, suite_code=None):
    """
    获取salt cmd执行
    :param app_name:
    :param operate_type:
    :param node_ip:
    :param suite_code:
    :return:
    """
    sql = '''  SELECT c.salt_func,c.exec_cmd,s.suite_code, n.minion_id FROM publish_exec_salt_cmd c LEFT JOIN
   env_mgt_node_bind b 
    ON c.bind_id = b.id LEFT JOIN env_mgt_node n ON n.id = b.node_id LEFT JOIN env_mgt_suite s ON s.id = b.suite_id    
          WHERE c.app_name = "{}" AND c.operate_type = "{}"  AND  n.node_ip = "{}" '''.format(app_name,
                                                                                              operate_type,
                                                                                              node_ip)
    if suite_code:
         sql = sql + ' AND s.suite_code ="{}"'.format(suite_code)
    salt_info = {}
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        if salt_info == {}:
            salt_info = row
        else:
            raise IOError("{}应用，{}操作，{}节点，{}环境套，维护了重复数据".format(app_name,
                                                                operate_type,
                                                                node_ip,
                                                                suite_code))
    return salt_info
