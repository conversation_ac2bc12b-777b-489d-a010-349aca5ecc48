import traceback
import os
import json
import datetime
import time
import requests
import re

from urllib3.exceptions import InsecureRequestWarning

from app_mgt.models import AppMgtAppModule
from iter_mgt.models import UserActionRecord
from product_mgt.models import ProductMgtProductInfo
from publish_tool.utils.analyze_verify_result_strategy import Analyze<PERSON>pring2312Strategy, AnalyzeContext, \
    AnalyzeSpring218Strategy
from settings import logger
from settings import SALT_API_USER, SALT_API_PASSWORD, SALT_API, TRANSIT_IP
from publish_tool.publish.models import SaltOperationResults, PublishSaltResultCheckReg
from publish_tool.utils.salt_ser import get_salt_cmd_info
from test_publish_aio.test_suite_init_constants import PROCESS_CODE_PD_PROD, PROCESS_CODE_TEST


class SaltLogConfigMissingException(Exception):
    def __init__(self, msg):
        self.msg = msg


class SaltTask:
    __salt_api_user = SALT_API_USER
    __salt_api_password = SALT_API_PASSWORD
    __salt_api = SALT_API
    __transit_ip = TRANSIT_IP
    __salt_operation_results = SaltOperationResults
    # __salt_verify_results = SaltVerifyResults
    __token_file = "cache_token.txt"
    __token_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), __token_file)

    def __init__(self):
        pass

    @staticmethod
    def __get_salt_cmd_info(*args):
        return get_salt_cmd_info(*args)

    def post_request(self, interface, data, headers={}):
        # encode = urllib.parse.urlencode(data)
        # obj = urllib.parse.unquote(encode).encode("utf-8")
        # ssl_context = ssl._create_unverified_context()
        # logger.info(ssl_context)
        # logger.info(data)
        # logger.info(headers)
        # req = urllib.request.Request(interface, obj, headers=headers)
        #
        # with urllib.request.urlopen(req, timeout=60, context=ssl_context) as r:
        #     time.sleep(10)
        #     code = r.code
        #     msg = r.read().decode('utf-8')
        #     logger.info(msg)
        # res = urllib.request.urlopen(req, timeout=10, context=ssl_context)
        logger.info(interface)
        logger.info(data)
        try:
            requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
            res = requests.post(interface, data=data, headers=headers, verify=False)
        except Exception as e:
            logger.error('调用失败：{}'.format(e))
            traceback.print_exc()
            raise Exception('平台调用salt服务器失败，请联系运维协助处理')
        logger.info('调用salt返回结果：{}, {}'.format(res.status_code, res.text))
        return res.status_code, res.text

    def _recode_cache_token(self, content):
        if os.path.isfile(self.__token_file_path):
            os.remove(self.__token_file_path)

        with open(self.__token_file_path, "w") as f:
            f.write(content)
        return content

    def _get_cache_token(self):
        if os.path.isfile(self.__token_file_path):
            with open(self.__token_file_path, "r") as f:
                return f.read()
        return None

    def _salt_login(self, suite_code):
        try:
            ip = self.__salt_api[suite_code]
            user = self.__salt_api_user[suite_code]
            password = self.__salt_api_password[suite_code]
        except Exception as e:
            logger.error(str(e))
            raise SaltLogConfigMissingException(
                "没有配置{}环境套的salt master 节点信息，请联系张怀天！ 电话： 13524286437 ".format(suite_code))
        path = "login"
        data = {'eauth': 'pam', 'username': user, 'password': password}
        # headers = {'Content-Type': "application/json"}
        code, results = self.post_request(ip + path, data)
        if code == 200:
            try:
                token_id = json.loads(results)['return'][0]['token']
                logger.info(token_id)
                self._recode_cache_token(token_id)
                return token_id
            except KeyError:
                raise KeyError
        else:
            raise Exception("salt 登入失败")

    def _record_table(self, cmd, request_url, cmd_type, action_id, ip, deploy_stage):
        return self.__salt_operation_results.create(exec_cmd=cmd, request_url=request_url,
                                                    start_at=datetime.datetime.now(), cmd_type=cmd_type,
                                                    action_id=action_id,
                                                    ip=ip,
                                                    deploy_stage=deploy_stage)

    # def _record_verify_table(self, op_type, app_name, ip, suite_code):
    #     if op_type == "verify":
    #         return self.__salt_verify_results.get_or_create(app_name=app_name, node_ip=ip, suite_code=suite_code,
    #                                                         defaults={"app_name": app_name,
    #                                                                   "node_ip": ip,
    #                                                                   "create_time": datetime.datetime.now(),
    #                                                                   "suite_code": suite_code})[0]
    #
    # def _update_verify_table(self, op_type, request_status, request_status_code, result, verify_table):
    #     if op_type == "verify":
    #         return self.__salt_verify_results.update(update_time=datetime.datetime.now(), result=result,
    #                                                  request_status=request_status,
    #                                                  request_status_code=request_status_code) \
    #             .where(self.__salt_verify_results.id == verify_table).execute()

    def _analyze_salt_result(self, minion_id, cmd_type, result, status, op_type, action_id, app_name, ip, suite_code, iteration_id):
        """
        用来分析salt的返回结果
        :param minion_id:
        :param cmd_type:
        :param result: salt返回结果
        :param status: salt返回状态
        :param op_type:
        :param action_id:
        :param app_name:
        :param ip:
        :param suite_code:
        :return: models.InterfaceResults
        """
        logger.info("############salt请求结果#######################")
        result = json.loads(result)
        logger.info(result)
        logger.info("_analyze_salt_result中的cmd_type: {}, op_type: {}, iteration_id:{}".format(cmd_type, op_type, iteration_id))
        retry_flag = False
        if op_type == "verify":
            obj = AppMgtAppModule.select().where(AppMgtAppModule.module_name == app_name).get_or_none()
            s1 = AnalyzeSpring2312Strategy()
            data = {"minion_id": minion_id, "result": result, "action_id": action_id, "suite_code": suite_code, "app_name": app_name, "iteration_id": iteration_id}
            context = AnalyzeContext(s1, data)
            if obj.spring_version == 2:
                s2 = AnalyzeSpring218Strategy()
                context.set_strategy(s2)
            return context.do_strategy(), retry_flag
        obj = PublishSaltResultCheckReg.get_or_none(PublishSaltResultCheckReg.opt_type == op_type)
        if obj:
            regex_list = obj.regex_list.split(",")
            if cmd_type in ("rsync.rsync", "cmd.script"):
                if "retcode" in result['return'][0][minion_id]:
                    if result['return'][0][minion_id]["retcode"] != 0:
                        status = self.__salt_operation_results.FAILURE
                else:
                    status = self.__salt_operation_results.FAILURE
            elif cmd_type == "cmd.run":
                for regex in regex_list:
                    new_regex = regex.format(app_name)
                    logger.info("当前匹配规则为：{}".format(new_regex))
                    matches_result = re.findall(new_regex, str(result))
                    if matches_result:
                        logger.info("找到匹配结果为：{}".format(matches_result))
                        break
                if not matches_result:
                    logger.error("根据匹配规则【{}】没有找到匹配结果！".format(regex_list))
                    status = self.__salt_operation_results.FAILURE
            elif cmd_type == "state.sls":
                if minion_id in result['return'][0]:
                    if len(result['return'][0][minion_id]) > 0:
                        if isinstance(result['return'][0][minion_id], dict):
                            config_res_dict = result['return'][0][minion_id].values()
                            logger.info(config_res_dict)
                            for config_res in config_res_dict:
                                logger.info(config_res)
                                if "result" in config_res:
                                    logger.info(config_res["result"])
                                    if config_res["result"] == True:
                                        pass
                                    else:
                                        status = self.__salt_operation_results.FAILURE
                                        break
                        else:
                            for regex in ['state.sls.*is running as PID']:
                                matches_result = re.findall(regex, str(result))
                                if matches_result:
                                    logger.info("找到匹配结果为：{}".format(matches_result))
                                    retry_flag = True
                                    break
                            status = self.__salt_operation_results.FAILURE
            else:
                raise ValueError(f"未知的cmd_type: {cmd_type}，支持的类型包括: rsync.rsync, cmd.script, cmd.run, state.sls") 
        return status, retry_flag

    def _salt_run(self, app_name, op_type, ip, minion_id, cmd, suite_code, cmd_type="cmd.run", action_id="",
                  region_code="", retry=3, increment=5, iteration_id=None):
        logger.info("_salt_run中的op_type：{}, iteration_id:{}".format(op_type, iteration_id))

        params = [('client', 'local'), ('tgt', minion_id), ('fun', cmd_type), ('expr_form', 'list')]
        if isinstance(cmd, list):
            for x in cmd:
                params.append(('arg', x))
        else:
            params.append(('arg', cmd))
        headers = {'X-Auth-Token': self._get_cache_token()}
        # 测试可用区 取浦东内网的salt by 帅 20220720
        logger.info("region_code: {}".format(region_code))
        logger.info("suite_code: {}".format(suite_code))
        if region_code and region_code == PROCESS_CODE_TEST:
            suite_code = PROCESS_CODE_PD_PROD
        logger.info("salt_master {}".format(self.__salt_api[suite_code]))
        logger.info("salt请求头 {}".format(headers))
        logger.info("slat请求参数为{}".format(params))
        # verify_table = self._record_verify_table(op_type, app_name, ip, suite_code)
        record_id = self._record_table(cmd, self.__salt_api[suite_code], cmd_type, action_id=action_id,
                                       ip=ip,
                                       deploy_stage=op_type)
        result_code, result = self.post_request(self.__salt_api[suite_code], params, headers=headers)
        # result_code, result = 200, "测试结果"
        if result_code == 401:
            logger.warning("token 过期")
            logger.info("失败尝试重新登入")
            self.__salt_operation_results.update(request_status=self.__salt_operation_results.FAILURE,
                                                 request_result=result,
                                                 request_status_code=result_code, end_at=datetime.datetime.now()) \
                .where(self.__salt_operation_results.id == record_id).execute()
            token_id = self._salt_login(suite_code=suite_code)
            headers = {'X-Auth-Token': token_id}
            record_id = self._record_table(cmd, self.__salt_api[suite_code], cmd_type, action_id=action_id, ip=ip,
                                       deploy_stage=op_type)
            result_code, result = self.post_request(self.__salt_api[suite_code], params, headers=headers)

        # 状态码 不是 200  失败
        if result_code != 200:
            logger.info("平台调用salt命令返回码：{}，salt操作失败，请及时联系运维排查解决！".format(result_code))
            status = self.__salt_operation_results.FAILURE
        else:
            status = self.__salt_operation_results.SUCCESS

        logger.info(status)
        # salt 返回成功的时候 单独分析返回结果 by帅 2022-01-28
        retry_flag = False
        if status == self.__salt_operation_results.SUCCESS and result:
            try:
                status, retry_flag = self._analyze_salt_result(minion_id, cmd_type, result, status,
                                                               op_type, action_id, app_name, ip, suite_code, iteration_id)
            except Exception as e:
                status = self.__salt_operation_results.FAILURE
                logger.warning("无法分析的结果")
                err_msg = traceback.format_exc()
                logger.error(err_msg)
        self.__salt_operation_results.update(request_status=status,
                                             request_result=result,
                                             request_status_code=result_code, end_at=datetime.datetime.now()) \
            .where(self.__salt_operation_results.id == record_id).execute()
        # minion_id 不在返回结果的时候 递归循环调用 by 帅 20220614
        # 配置更新如果是处于“正在进行中”的状态，则进入重试 20230818 by fwm
        if retry_flag:
            result = '{"return": [{}]}'
        # increment = 5
        while result_code == 200 and result == '{"return": [{}]}' and retry > 0:
            logger.info("进入重试！！！")
            time.sleep(increment)
            increment += 5
            retry = retry - 1
            return self._salt_run(app_name, op_type, ip, minion_id, cmd, suite_code, cmd_type, action_id, region_code,
                                  retry=retry, increment=increment, iteration_id=iteration_id)

        if result == '{"return": [{}]}':
            status = self.__salt_operation_results.FAILURE
            logger.error('——————————————调用salt失败，请联系运维处理————————————')
        # self._update_verify_table(op_type, status, result_code, result, verify_table)
        return status, result

    def _get_salt_cmd(self, app_name, op_type, ip, suite_code, resource, end_ver=None):
        # todo 临时兼容 数据还没支持资源查询 by帅 20220701
        logger.info("_get_salt_cmd中的resource:{}, op_type：{}".format(resource, op_type))
        if resource == "pkg" and op_type == "update":
            op_type = "code_update"
        salt_info = self.__get_salt_cmd_info(app_name, op_type, ip, suite_code)
        logger.info(salt_info)
        if len(salt_info) == 0:
            raise Exception("应用：{}、IP：{}、操作：{} 找不到 salt命令，请联系运维维护".format(app_name, ip, op_type))

        run_cmd = salt_info["exec_cmd"]
        salt_func = salt_info["salt_func"]
        minion_id = salt_info["minion_id"]
        if "transit_ip" in run_cmd:
            if "end_ver" in run_cmd:
                run_cmd = run_cmd.format(transit_ip=self.__transit_ip[suite_code], end_ver=end_ver)
            else:
                run_cmd = run_cmd.format(transit_ip=self.__transit_ip[suite_code])
        if salt_func in ('rsync.rsync', 'cp.get_dir', 'cmd.script'):
            run_cmd = run_cmd.split(' ')
        return minion_id, salt_func, run_cmd, op_type

    def __create_verify_cmd(self):
        pass

    def run_salt_task(self, app_name, resource, op_type, ip, suite_code, action_id, region_code=None, end_ver=None, iteration_id=None):
        logger.info("run_salt_task中的resource:{}, op_type：{}, iteration_id:{}".format(resource, op_type, iteration_id))
        minion_id, salt_func, run_cmd, op_type = self._get_salt_cmd(app_name, op_type, ip, suite_code, resource,
                                                                    end_ver)
        status, result = self._salt_run(app_name, op_type, ip, minion_id, run_cmd, suite_code, cmd_type=salt_func,
                                        action_id=action_id,
                                        region_code=region_code,
                                        iteration_id=iteration_id)
        logger.info("执行状态 {}".format(status))
        logger.info("执行结果 {}".format(json.loads(result)))
        return status, result

    def jenkins_run_salt_task(self, app_name, resource, op_type, ip, suite_code, action_id, region_code=None,
                              end_ver=None, exec_batch_number=None, build_id=None, iteration_id=None):
        # start_at = datetime.datetime.now()
        logger.info("run_salt_task中的resource:{}, op_type：{}".format(resource, op_type))
        minion_id, salt_func, run_cmd, op_type = self._get_salt_cmd(app_name, op_type, ip, suite_code, resource,
                                                                    end_ver)

        status, result = self._salt_run(app_name, op_type, ip, minion_id, run_cmd, suite_code, cmd_type=salt_func,
                                        action_id=action_id,
                                        region_code=region_code,
                                        iteration_id=iteration_id)
        logger.info("执行状态 {}".format(status))
        logger.info("执行结果 {}".format(json.loads(result)))
        return status, result


if __name__ == "__main__":
    # salt_task = SaltTask()
    # status, result = salt_task.run_salt_task("fin-online-service", "pkg",
    #                                          "verify", "************", "bs-zb", "83447", "prod")
    # result = SaltOperationResults.select(SaltOperationResults.request_result).where(
    #     SaltOperationResults.action_id == 156166).get()
    # salt_task._analyze_salt_result('w-pay-online-server-10-12-102-183', 'cmd.run', result.request_result, '', 'verify', 156166, 'pay-online-server')
    # logger.info(status)
    # logger.info(result)
    result = {"return": [{"j-dtms-settle-remote-10-11-22-80": "\u4eca\u5929\u5df2\u5907\u4efd\u8fc7\uff0c\u65e0\u9700\u518d\u6b21\u5907\u4efd\u3002\nreceiving incremental file list\n\nsent 25 bytes  received 105 bytes  260.00 bytes/sec\ntotal size is 5,396  speedup is 41.51\n[ INFO ]: Update The dtms-settle-remote conf ...... OK!"}]}
    if re.findall(r'Update The {} conf.*OK!'.format('dtms-settle-remote'), str(result)):
        print("ok")
