import sys
import os
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from settings import URL_DEF, logger
from common.routers.router import Router
from utils.check.validator import check_link
from utils.publish import publish_apply, rollback_apply
from job.iter_mgt.iter_archive import ArchiveApplyForm
from job.db_mgt.test_sql_iter_archive import TestSqlArchiveApplyForm

urls = {
        "publish_apply_check": check_link,
        "mobile_publish_apply_check": check_link,
        "publish_check": check_link,
        "archive_check": check_link,
        "compile_check": check_link,
        "package_check": check_link,
        "publish_apply": publish_apply.PublishApplyForm.as_view,
        "archive": ArchiveApplyForm.as_view,
        "app_commit_check": check_link,
        "app_merge_check": check_link,
        "rollback_apply": rollback_apply.RollbackApplyForm.as_view,
        }


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    rt = Router(urls, url_def=URL_DEF)
    rt.dispatch(sys.argv[1])
    #rt.dispatch(75)

