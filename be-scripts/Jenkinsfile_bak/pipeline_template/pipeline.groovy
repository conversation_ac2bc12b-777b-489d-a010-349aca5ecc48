import groovy.json.JsonSlurperClassic
pipeline {
   agent {label label}
   environment{
	    BEP="/home/<USER>/be-scripts/be-scripts"
    }
    stages{
        stage("Determine if parsing is necessary"){

                steps{
                    echo "${env.JOB_NAME}"
                    echo is_code_scan
                    echo dump_bis_code
                    script {
                        cache_data_path="/home/<USER>/dump/${env.JOB_NAME}"
                       flagPath="/home/<USER>/dump/${env.JOB_NAME}.txt"
                       analysis_flag = fileExists flagPath
                       testSkipPath="/home/<USER>/dump/${env.JOB_NAME}_test_skip.txt"
                       //bind_suite
 suite_code = "无"
//bind_suite
                       analysis_test_skip = fileExists testSkipPath
					   scannerHome = tool 'sonar-scanner'
                    }
                }
        }
         stage("parsing"){
               when{
                        expression  {analysis_flag == false}
                }
                steps {

                   sh label: '', script: 'python3.x ${BEP}/job/jenkins/jenkins_xml.py "update_pipeline" ${JOB_NAME} ${WORKSPACE} '+flagPath+' '+cache_data_path+' '+suite_code+' '+is_junit+' '+is_mock_agent+' '+is_code_scan+' '+dump_bis_code+' '+db_exec_type
                }
         }

	   stage("preparation"){
                   when{
                            expression  {analysis_flag == true}
                    }
                    failFast true

                    parallel {
                      stage("ccn"){
                            when{
                                        expression  {is_code_scan == "true"}
                                    }
                             stages{
                                stage("ccn scan"){
                                        steps {
											ccn_scan()
                                        }
                                }
                                stage("push ccn report"){
                                    steps {
                                            push_ccn_report()
                                    }
								}
								 stage("analysis ccn report"){
									steps {
										analysis_ccn_report()
									}
								 }
								 stage("ccn result check"){
									steps {
										ccn_result_check()
									}
								}
                         }
                           }
                         stage("p3c"){
                            when{
                                        expression  {is_code_scan == "true"}
                                    }
                             stages{
                                stage("p3c scan"){
                                        steps {
											p3c_scan()
                                        }
                                }
                                stage("push p3c report"){
                                    steps {
										push_p3c_report()
                                    }
                              }
                             stage("analysis p3c report"){
                                    steps {
										analysis_p3c_report()
                                    }
                             }
                              stage("p3c result check"){
                                    steps {
										p3c_result_check()
                                    }
                                }
                         }
                           }
                            stage("scan api"){
                             stages{
                                stage("generate apidoc"){
                                        steps {
											generate_api_doc()
                                        }
                                }
                                stage("parse apidoc"){
                                    steps {
										parse_api_doc()
                                    }
                              }
                                stage("synchronize api"){
                                    steps {
										sync_api()
                                    }
                              }
                         }
                          }
						  stage("shift left"){
                             stages{
                                stage("shift left test"){
                                        steps {
											shift_left_test()
                                        }
                                }
                         }
                          }
                    }
            }

//#####

//#####
//process_product
        //starting_agent无//starting_agent

	stage("preparation before pushing product"){
	   when{
				expression  {analysis_flag == true}
		}
		failFast true

		parallel {
		  //unit_test
		  stage("SQL handle") {
 			stages{
 				stage("pull SQL "){
 					steps{
						pull_sql()
 						}
 					}
 				stage("make SQL "){
 					steps{
						make_sql()
 						}
 					}
				stage("check SQL "){
 					steps{
						check_sql()
 						}
 					}
 				}
 		  }

		  stage("unit testing"){
			when{
				expression  {analysis_flag == true}
			}
			stages{
				stage("allure"){
					steps {
						allure_execute()
					}
				 }
				stage("push report"){
					steps {
						push_unit_test_report()
					}
				}
				stage("analysis report"){
				   steps {
						analysis_unit_test_report()
				   }
				}
				stage("check result"){
				   steps {
						check_unit_test_result()
				   }
				}
			}
		  }
		  //unit_test

		  stage("sonar"){
			when{
				expression  {analysis_flag == true}
			}
			stages{
				stage("sonar scan "){
					steps{
						script {
							sonar_scan()
						}
					}
				}
			}
		  }

        //docker_start无//docker_start
		}
	}
    stage("product handle"){
                   when{
                            expression  {analysis_flag == true}
                    }
                    failFast true

                    parallel {
                        stage("push repo"){
                               when{
                                        expression  {analysis_flag == true}
                                }
                                steps {
									push_repo()
                                }
                         }

                        stage("copy code and class"){
                               when{
                                        expression  {analysis_flag == true}
                                }
                                steps {
									copy_src_class()
                                }
                         }

						stage("push sql repo"){
                               when{
                                        expression  {analysis_flag == true}
                                }
                                steps {
									push_sql_repo()
                                }
                         }
                    }
            }


//process_product
         //publish_env无//publish_env
    }
    post{
        failure{
            echo 'failure'
            sh label: '', script: 'rm -rf '+flagPath
        }
        aborted {
			script {
				sh 'echo cancel'
				sh  'python3.x ${BEP}/ci_pipeline/ci_pipeline/base_pipeline.py aborted '+flagPath
                sh 'echo ' + analysis_flag
                sh 'echo ' + analysis_test_skip
                if ( analysis_flag && analysis_test_skip ) {
                    sh 'rm -f ' + testSkipPath
                }
                sh 'rm -rf '+flagPath
            }
        }
        success{
			script {
				sh 'echo success'
                sh 'echo ' + analysis_flag
                sh 'echo ' + analysis_test_skip
                if ( analysis_flag && analysis_test_skip ) {
                    sh 'rm -f ' + testSkipPath
                }
                if (analysis_flag){
				    sh  'python3.x ${BEP}/ci_pipeline/ci_pipeline/base_pipeline.py end_pipeline '+flagPath
                    sh 'rm -rf '+flagPath
                }
            }
        }
    }
}

void ccn_scan() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "ccn_scan" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void push_ccn_report() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "push_ccn_report" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void analysis_ccn_report() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "analyze_ccn_report" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void ccn_result_check() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "get_ccn_result" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void p3c_scan() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "p3c_scan" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void push_p3c_report() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "push_p3c_report" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void analysis_p3c_report() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "analyze_p3c_report" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void p3c_result_check() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "get_p3c_result" '+flagPath+'  ${JOB_NAME} ${WORKSPACE}'
}

void generate_api_doc() {
	sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/app_mgt/app_apidoc/generate_api_doc.py "generate_api_doc" ${JOB_NAME} ${WORKSPACE}'
}

void parse_api_doc() {
	sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/app_mgt/app_apidoc/parse_api_doc.py "parse_api_doc" ${JOB_NAME} ${WORKSPACE}'
}

void sync_api() {
	sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/app_mgt/app_apidoc/sync_api.py "sync_api" ${JOB_NAME} ${WORKSPACE}'
}

void pull_sql() {
	sh   'export PYTHONPATH=/home/<USER>/be-scripts/be-scripts && python3.x /home/<USER>/be-scripts/be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py "STEP_PULL_SQL" '+flagPath+' ${JOB_NAME} ${WORKSPACE} '+dump_bis_code
}

void make_sql() {
	sh   'export PYTHONPATH=/home/<USER>/be-scripts/be-scripts && python3.x /home/<USER>/be-scripts/be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py "STEP_MAKE_SQL" '+flagPath+' ${JOB_NAME} ${WORKSPACE} '+dump_bis_code
}

void check_sql(){
    sh   'export PYTHONPATH=/home/<USER>/be-scripts/be-scripts && python3.x /home/<USER>/be-scripts/be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py "STEP_CHECK_SQL" '+flagPath+' ${JOB_NAME} ${WORKSPACE} '+dump_bis_code
}

void allure_execute() {
	script {
	    allure jdk: '',report:"target/allure-report-unit", results: [[path: '**/target/surefire-reports/']]
	}
}

void push_unit_test_report() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/java_pipeline.py "push_test_report" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void analysis_unit_test_report() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/java_pipeline.py "analyze_test_report" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void check_unit_test_result() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/java_pipeline.py "check_unit_test_result" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void sonar_scan() {
	sh   'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "spider_sonar_api" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void shift_left_test() {
	sh   'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "shift_left_test_api" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void push_repo() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/java_pipeline.py "push_product" '+flagPath+' ${JOB_NAME} ${WORKSPACE}'
}

void copy_src_class() {
	sh label: '', script:  'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/java_job/copy_src_class_processor.py "copy_src_class" ${JOB_NAME} ${WORKSPACE}'
}

void push_sql_repo() {
	sh 'export PYTHONPATH=/home/<USER>/be-scripts/be-scripts && python3.x /home/<USER>/be-scripts/be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py "STEP_PUSH_SQL" '+flagPath+' ${JOB_NAME} ${WORKSPACE} '+dump_bis_code
}