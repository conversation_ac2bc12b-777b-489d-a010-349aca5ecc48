#!groovy
import groovy.json.JsonSlurper;
pipeline {
    agent {
        label 'mac'
    }
    environment {
        ONLINE_BRANCH="2.16.0"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/hm-scripts/app_pipeline/app_pipeline_business/app_pipeline_ios.py"
        SCRIPT_INSERT_MAIN_PATH="/home/<USER>/hm-scripts/app_pipeline/app_pipeline_modle/app_dao/insert/ios_pipeline.py"
        SCRIPT_UPDATE_MAIN_PATH="/home/<USER>/hm-scripts/app_pipeline/app_pipeline_modle/app_dao/update/ios_pipeline.py"
        def datetime = sh(script: "echo `date +%Y-%m-%d+%H:%M:%S`", returnStdout: true).trim()
        //params = "{\"appVersion\":\"1\",\"h5ZipVersion\":\"30.0.0\",\"packageType\":\"ios\",\"appName\":\"fund-ios\",\"appEnv\":\"test\",\"appBranch\":\"210518-test\",\"repoPath\":\"howbuy_ios/fund\",\"h5AppName\":\"fund\",\"h5Env\":\"test\",\"actionItem\":\"test_publish\",\"iterationID\":\"howbuy_ios_21-04-26\",\"actionId\":3947,\"operator\":\"min.huang\",\"deploy_status_id\":970,\"branchType\":\"tag\"}"
        //args = "{\"app_name\":\"fund\",\"app_env\":\"vph\",\"br_name\":\"7.4.6\",\"repo_path\":\"howbuy_ios/fund\",\"workspace\":\"${workspace}\",\"h5_env\":\"vph\",\"suite_code\":\"\",\"h5_app_name\":\"fund\",\"h5_env\":\"vph\",\"h5_end_ver\":\"30.0.0\",\"app_version\":\"20.3.0\",\"package_type\":\"ios\",\"datetime\":\"${datetime}\"}"
        local_args = "{\"workspace\":\"${workspace}\",\"datetime\":\"${datetime}\"}"
        // def project = sh(script: "${env.JOB_NAME}".split('_')[-1], returnStdout: true).trim()
        // def project ="${env.JOB_NAME}".split('_')[-1]
        def sampleText ="${env.JOB_NAME}".split('_')

    }
    options {
        timeout(time: 1, unit: 'HOURS')
        // timestamps()
        retry(1)
    }

    stages {
        stage('解析') {

            steps {
                script{
                    def l= new JsonSlurper().parseText(params.params)
                    println('00000000000000000000000000000000000000000000')
                    println(l.appName)
                    println(l.packageType)
                    app_name = l.appName
                    package_type = l.packageType
                }
                sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_INSERT_MAIN_PATH} ${params} ${local_args}'

            }
        }

        stage('拉代码') {

            steps {
                echo "buildId = " + BUILD_ID
                wrap([$class: 'BuildUser']) {
                sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} get_code'
                 }
                }
        }

        stage('「资源准备阶段」') {
            failFast false

            parallel {
                stage('1-资源包') {
                    stages {
                        stage('「checkLib_NewestMsg」') {
                            when{
                                expression  {app_name != "crm-ios" && package_type != "ios-global" }
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                                echo "package_type = " + package_type
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} exec_checkLibIsNewestMsg'
                                }
                            }
                        }
                        stage('获取H5资源') {
                            when{
                                expression  {app_name != "crm-ios"}
                            }
                            steps {
                              echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} get_h5_res'
                                }
                            }
                        }
                        stage('资源包解压') {
                            when{
                                expression  {app_name != "crm-ios"}
                            }
                            steps {
                              echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} unzip_h5_res'
                                }
                            }
                        }
                        stage('复制description文件') {
                            when{
                                expression  {app_name != "crm-ios"}
                            }
                            steps {
                              echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} copy_descriptions'
                                }
                            }
                        }
                    }
                }
                stage('2-cgi') {
                    stages {
                        stage('获取cgi_urls') {
                            when{
                                expression  {app_name != "crm-ios"}
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} get_cgi_file'
                                }
                            }
                        }
                    }
                }
                stage('3-获取xcode版本') {
                    stages {
                        stage('build_config.plist') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} check_buildconfig_exists'
                                }
                            }
                        }
                        stage('Xcode') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} switch_xcode_ver'
                                }
                            }
                        }
                    }
                }
                stage('4-修改Podfile') {
                    stages {
                        stage('修改Podfile') {
                            when{
                                expression  { package_type != "ios-global" }
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} alter_pod_file'
                                }
                            }
                        }

                    }
                }
            } //parallel
        }

        stage('「hook_begin」') {
            parallel {
                stage('fund_hook_begin') {
                    stages {
                        stage('「拉取公募私募账号相关库」') {
                            when{
                                expression  {app_name != "crm-ios"}
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} clone_gongmu_code'
                                }
                            }
                        }
                        stage('清除目录，并根据flag copy新的内容') {
                            when{
                                expression  {app_name != "crm-ios" && package_type != "ios-global"}
                            }
                            steps {
                               echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} clean_and_copyfils_newpath'
                                }
                            }
                        }
                        stage('壳子工程中引用公募/私募、账号中心的源码或者制品库') {
                            when{
                                expression  {app_name != "crm-ios" && package_type != "ios-global"}
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} quote_code_lib'
                                }
                            }
                        }
                    }
                }
            }
        }
        stage('「hookbegin并行阶段」') {
            parallel {
                stage('1-hookbegin') {
                    stages {
                       stage('修改工程模式') {
                           when{
                            expression  {app_name != "crm-ios"}
                            }
                            steps {
                               echo "buildId = " + BUILD_ID
                               wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} modify_project_mode'
                                }

                            }
                        }
                    }
                }
                stage('2-hookbegin') {
                    stages {
                       stage('切换库资源') {
                           when{
                                expression  {app_name != "crm-ios" && package_type != "ios-global"}
                            }
                            steps {
                               echo "buildId = " + BUILD_ID
                               wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} checkout_third_Store'
                                }

                            }
                        }
                    }
                }
                stage('3-hookbegin') {
                    stages {
                       stage('同步资源文件') {
                           when{
                                expression  {app_name != "crm-ios"}
                            }
                            steps {
                               echo "buildId = " + BUILD_ID
                               wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} change_resource_file_guomi'
                                }

                            }
                        }
                    }
                }
                stage('4-hookbegin') {
                    stages {
                       stage('执行crm-ioshookbegin') {
                           when{
                                expression  {app_name == "crm-ios"}
                            }
                            steps {
                               echo "buildId = " + BUILD_ID
                               wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} exec_compilehookbegin'
                                }

                            }
                        }
                    }
                }
            } //parallel
        }
        stage('非开发环境设置Testmodules等库的md5值') {
                            when{
                            expression  {app_name != "crm-ios" && package_type != "ios-global"}
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} set_store_md5'
                                }

                            }
                        }
        stage('「构建阶段」') {
            failFast false

            parallel {
                stage('构建') {
                    stages {
                        stage('clean') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} exec_xcode_clean'
                                }
                            }
                        }
                        stage('make') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} exec_xcode_make'
                                }
                            }
                        }
                        stage('install') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} exec_xcode_install'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }

        stage('「发布准备阶段」') {
                            failFast false

                            parallel {
                                stage('准备') {
                                    stages {
                                        stage('创建发布目录') {
                                            steps {
                                                echo "buildId = " + BUILD_ID
                                                wrap([$class: 'BuildUser']) {
                                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} mkdir_publish'
                                            }
                                        }
                                    }
                                        stage('清理发布目录') {
                                            steps {
                                                echo "buildId = " + BUILD_ID
                                                wrap([$class: 'BuildUser']) {
                                                    sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} clean_publish_dir'
                                            }
                                        }
                                    }

                                    }
                                }
                            } //parallel
                        }

        stage('「发布阶段」') {
            failFast false

            parallel {
                stage('1-apk') {
                    stages {
                        stage('推送ipa-apk') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} publish_ipa_apk'
                                }
                            }
                        }
                        // stage('推送zip') {
                        //     steps {
                        //         echo "buildId = " + BUILD_ID
                        //         wrap([$class: 'BuildUser']) {
                        //         sh '${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} deal_with_zip_file'
                        //         }
                        //     }
                        // }
                    }
                }
                stage('2-xc') {
                    stages {
                        stage('推送xc_archive') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                sh '. /Users/<USER>/.zshrc &&${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} deal_with_zip_file'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }
stage('「结束阶段」') {
            parallel {
                stage('1-结束') {
                    stages {
                       stage('「record_data」') {
                    steps {
                        echo "buildId = " + BUILD_ID
                        wrap([$class: 'BuildUser']) {
                                        sh '. /Users/<USER>/.zshrc && ${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} deal_with_build_data'
                                        }
                    }
                }
                    }
                }
                stage('2-结束') {
                    stages {
                       stage('「hook_end」') {
                           when{
                            expression  {app_name != "crm-ios"}
                            }
            steps {
                echo "buildId = " + BUILD_ID
                wrap([$class: 'BuildUser']) {
                                sh '. /Users/<USER>/.zshrc && ${PYTHON_CMD} ${SCRIPT_PATH}  ${params} ${local_args} exec_compilehookend'
                                }
            }
        }
                    }
                }
            } //parallel
        }
    }
    post {
        always {
            echo "========== 记录构建结果：=========="
        }
        aborted {
            sh '. /Users/<USER>/.zshrc && ${PYTHON_CMD} ${SCRIPT_UPDATE_MAIN_PATH} ${params} ${local_args} aborted'
            //echo "aborted：buildId = " + BUILD_ID
        }
        success {
            sh '. /Users/<USER>/.zshrc && ${PYTHON_CMD} ${SCRIPT_UPDATE_MAIN_PATH} ${params} ${local_args}'
            //echo "success：buildId = " + BUILD_ID
        }
        // failure {
        //     sh '. /Users/<USER>/.zshrc && ${PYTHON_CMD} ${SCRIPT_UPDATE_MAIN_PATH} ${params} ${local_args} failure'
        //     //echo "failure：buildId = " + BUILD_ID
        // }
    }
}