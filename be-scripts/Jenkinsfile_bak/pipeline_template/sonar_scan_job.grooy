import groovy.json.JsonSlurperClassic
pipeline {
   agent {label VM}
   environment{
	    BEP="/home/<USER>/be-scripts/be-scripts"

    }
	stages{
		stage("init"){

			steps{
				echo JOB_NAME
				script {
				   flagPath="/home/<USER>/dump/"+JOB_NAME+".txt.cp"
				   scannerHome = tool 'sonar-scanner'
				}
			}
        }
		stage("sonar scan "){
			steps{
				script {
					sonar_scan()
				}
				withSonarQubeEnv('sonarqube'){
					sh sonarCmd
				}
			}
		}
		stage("analysis sonar report "){
			steps{
				script {
					analysis_sonar_report()
				}
			}
		}

	}
    post{
        failure{
            echo 'failure'
            //sh label: '', script: 'rm -rf '+flagPath
        }
        aborted {
			script {
				sh 'echo cancel'
				sh  'python3.x ${BEP}/ci_pipeline/ci_pipeline/base_pipeline.py aborted '+flagPath
                //sh 'rm -rf '+flagPath
            }
        }
        success{
			script {
				sh 'echo success'
				sh  'python3.x ${BEP}/ci_pipeline/ci_pipeline/base_pipeline.py end_pipeline '+flagPath
                //sh 'rm -rf '+flagPath
            }
        }
    }
}

void sonar_scan() {
	sh   'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "get_sonar_scan_cmd" '+flagPath+' '+JOB_NAME+' '+WORKSPACE
	def jsonString = readFile(file: flagPath) // '{"k":"1", "n":"2"}'
	def dataObject = new JsonSlurperClassic().parseText(jsonString)
	sonarCmd = dataObject.sonar_cmd
	echo  sonarCmd
	echo  scannerHome
	sonarCmd = sonarCmd.replace("{scannerHome}", scannerHome)
	echo  sonarCmd
}

void analysis_sonar_report() {
	sh   'python3.x /home/<USER>/be-scripts/be-scripts/qc_mgt/measure_business/measure_pipeline.py "analysis_sonar_report" '+flagPath+' '+JOB_NAME+' '+WORKSPACE
}
