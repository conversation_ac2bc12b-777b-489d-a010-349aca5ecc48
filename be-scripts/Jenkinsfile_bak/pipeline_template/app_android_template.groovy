#!groovy
import groovy.json.JsonSlurper;
pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        def datetime = sh(script: "echo `date +%Y-%m-%d+%H:%M:%S`", returnStdout: true).trim()
        SCRIPT_PATH="/home/<USER>/hm-scripts/app_pipeline/app_pipeline_business/app_pipeline_android.py"
        args = "{\"workspace\":\"${workspace}\",\"datetime\":\"${datetime}\"}"

    }

    stages {

        stage('解析') {
            steps {
                script{
                    def l= new JsonSlurper().parseText(params.params)
                    println('00000000000000000000000000000000000000000000')
                    println(l.appName)
                    println(l.packageType)
                    app_name = l.appName
                    package_type = l.packageType
                }
                wrap([$class: 'BuildUser']) {
                    echo scan
                    sh 'gradle --version'
                }
            }
        }

        stage('「拉取阶段」') {
            failFast false

            parallel {
                stage('1-代码') {
                    stages {
                        stage('拉代码') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                 wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} get_code'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }

        stage('「资源准备阶段」') {
            failFast false

            parallel {
                stage('1-资源包') {


                    stages {
                        stage('获取H5资源') {
                            steps {
                               echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} get_h5_res'
                                }
                            }
                        }
                        stage('资源包解压') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} unzip_h5_res'
                                }
                            }
                        }
                         stage('重命名h5资源') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} rename_h5_res'
                                }
                            }
                        }
                        stage('复制description文件') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} copy_descriptions'
                                }
                            }
                        }
                    }
                }
                stage('2-cgi') {
                    stages {
                        stage('获取cgi_urls') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} get_cgi_file'
                                }
                            }
                        }
                    }
                }
                stage('3-子模块') {
					when {
						expression { package_type != "android-global" }
					}
                    stages {
                        stage('更新子模块') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} update_son_module'
                                }
                            }
                        }
                    }
                }
                stage('4-build') {
					when {
						expression { package_type != "android-global" }
					}
                    stages {
                        stage('修改build.gradle') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} alter_build_gradle'
                                }
                            }
                        }
                        stage('修改support.gradle') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} alter_tinker_support_gradle'
                                }
                            }
                        }
                        stage('修改ext.gradle') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} alter_ext_gradle'
                                }
                            }
                        }
                    }
                }
                stage('5-渠道') {
                    stages {
                        stage('生成渠道json') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} make_build_json'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }

        stage('「静态扫描阶段」') {
            failFast false
            when {
               expression  {scan == "true"}
            }

            parallel {
                stage('1-sonar') {
                    stages {
                        stage('sonar扫描') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} sonar_scan'
                                }
                            }
                        }
                        stage('lint扫描') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} lint_scan'
                                }
                            }
                        }
                        stage('lint报告') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} publish_lint_report'
                                }
                            }
                        }

                    }
                }

            } //parallel
        }

        stage('「构建阶段」') {
            failFast false

            parallel {
                stage('1-构建') {
                    stages {
                        stage('构建') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                  sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} app_build'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }
    stage('「发布准备阶段」') {
            failFast false

            parallel {
                stage('1-clean-remote-dir') {
                    stages {

                        stage('清理发布目录') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                     sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} clean_publish_dir'
                                }
                            }
                        }
                         stage('创建发布目录') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                     sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} pre_publish'
                                }
                            }
                        }
                    }

                }
                stage('2-md5') {
                    stages {
                        stage('生成md5') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} make_md5_file'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }
        stage('「发布阶段」') {
            failFast false

            parallel {
                stage('1-apk') {
                    stages {
                        stage('推送apk') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} publish_apk'
                                }
                            }
                        }
                    }
                }
                stage('2-mapping') {
                    stages {
                        stage('推送mapping') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} publish_mapping'
                                }
                            }
                        }

                    }
                }
                stage('3-md5') {
                    stages {
                        stage('推送md5') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} publish_md5_file'
                                }
                            }
                        }
                    }
                }
            } //parallel
        }
        stage('压缩并推送zip') {
                            steps {
                                echo "buildId = " + BUILD_ID
                                wrap([$class: 'BuildUser']) {
                                  sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} deal_with_zip_file'
                                }
                            }
                        }
        stage('记录') {
            steps {
                echo "========== 构建『成功』执行！=========="
                wrap([$class: 'BuildUser']) {
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} ${params} ${args} record_info'
                                }
            }
        }
    }
    post {
        always {
            echo "========== 记录构建结果：=========="
        }
        aborted {
            echo "aborted：buildId = " + BUILD_ID
            sh '${PYTHON_CMD} /home/<USER>/hm-scripts/common/wechat/send_wechat.py ${params} ${args}'
        }
        success {
            echo "success：buildId = " + BUILD_ID

        }
        failure {
            echo "failure：buildId = " + BUILD_ID
            sh '${PYTHON_CMD} /home/<USER>/hm-scripts/common/wechat/send_wechat.py ${params} ${args}'
        }
    }
}