#!groovy
/********************************
# Descript：
#   环境变量验证Job。 zt@2022-10-25
# History:
# 	2022-11-03  Zt  优化并行执行。
# 	2022-11-14  Zt  添加git的版本和配置验证。
********************************/
pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
    }
    options {
        timeout(time: 90, unit: 'MINUTES')
        retry(1)
    }

    
    stages {

        stage('节点') {
            failFast false

            parallel {
                stage('01-name') {
                    steps {
                        sh "echo '>>>> NODE_NAME:' $NODE_NAME"
                    }
                }
                stage('02-ip') {
                    steps {
                        sh "echo '>>>> SSH_CONNECTION:' $SSH_CONNECTION"
                    }
                }
            }
        }

        stage('环境') {
            failFast false

            parallel {

                stage('01-path') {
                    steps {
                        sh "echo '>>>> PATH(os):' $PATH"
                    }
                }
                stage('02-env') {
                    steps {
                        sh "env"
                    }
                }
            }
        }

        stage('git') {
            failFast false

            parallel {
                stage('01-git') {
                    steps {
                        sh "git -v"
                    }
                }
                stage('02-system') {
                    steps {
                        sh "git config --system --list"
                    }
                }
                stage('03-global') {
                    steps {
                        sh "git config --global --list"
                    }
                }

            }
        }

        stage('java') {
            failFast false

            parallel {
                stage('01-JAVA_HOME') {
                    steps {
                        sh "echo '>>>> JAVA_HOME:' $JAVA_HOME"
                        sh "$JAVA_HOME/bin/java -version"
                    }
                }
                stage('02-jdk1.7') {
                    steps {
                        sh "/data/jdk/jdk1.7.0/bin/java -version"
                    }
                }
                stage('03-jdk1.8') {
                    steps {
                        sh "/data/jdk/jdk1.7.0/bin/java -version"
                    }
                }
                stage('04-mvn') {
                    steps {
                        sh "echo '>>>> M2_HOME:' $M2_HOME"
                        sh "mvn -v"
                    }
                }

            }
        }

        stage('nodejs') {
            failFast false

            parallel {
                stage('01-NVM_DIR') {
                    steps {
                        sh "echo '>>>> NVM_DIR:' $NVM_DIR"
                    }
                }
                stage('02-nvm') {
                    steps {
                        sh ". /usr/local/nvm/nvm.sh && nvm -v"
                    }
                }
                stage('03-node v9.x') {
                    steps {
                        sh ". /usr/local/nvm/nvm.sh && nvm use v9.2.1 && node -v"
                    }
                }
                stage('04-node v12.x') {
                    steps {
                        sh ". /usr/local/nvm/nvm.sh && nvm use v12.13.1 && node -v"
                    }
                }
                stage('05-node v14.x') {
                    steps {
                        sh ". /usr/local/nvm/nvm.sh && nvm use v14.15.5 && node -v"
                    }
                }
                stage('06-node v16.x') {
                    steps {
                        sh ". /usr/local/nvm/nvm.sh && nvm use v16.14.0 && node -v"
                    }
                }
                stage('07-yarn') {
                    steps {
                        sh "yarn -v"
                        sh "yarn config list"
                    }
                }
            }
        }

        stage('android') {
            failFast false

            parallel {
                stage('01-ANDROID_HOME') {
                    steps {
                        sh "echo '>>>> ANDROID_HOME:' $ANDROID_HOME"
                    }
                }
                stage('02-sdk') {
                    steps {
                        sh "ls -al /usr/local/android-sdk-linux/tools/android"
                    }
                }
                stage('03-gradle') {
                    steps {
                        sh "echo '>>>> GRADLE_HOME:' $GRADLE_HOME"
                        sh "gradle -v"
                    }
                }
            }
        }
    }
    /*
    post {
        always {
            echo "========== zt@2022-10-25 =========="
        }
    }
    */
}