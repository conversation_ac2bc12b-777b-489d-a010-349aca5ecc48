#!groovy
/********************************
# Descript：
#   「DevOps」系统自动发布脚本（测试环境）。
#   目前只支持新阵列的「be-scripts、hm-scripts」。
#   sh「多行模式」不支持「局部变量」只能使用「环境变量」。
#   sh「多行模式」不支持「env.、params.」写法。
# History:
# 	2022-11-21  Zt  First Release。
# 	2022-12-13  Zt  简化日志输出。
# 	2022-12-13  Zt  优化结果列表。
# 	2022-12-19  Zt  简化 & Debug模式。
********************************/
def getDateStr() {
    return new Date().format('yyyyMMdd')
}

pipeline {
    agent {
        label 'nfs1'
    }
    environment {
        PYTHON_CMD="python3.x"
        //同步「根」目录（同步目录汇总，但本身并不会同步）
        SYNC_PATH="/data/devops_sync"
        //同步「环境」目录（具体的一个同步目录）
        SYNC_ENV_PATH="$SYNC_PATH/devops_sync_$PUBLISH_SUITE"
    }
    options {
        timeout(time: 90, unit: 'MINUTES')
        retry(1)
    }

    parameters {
        choice(
            name:'PUBLISH_APP',
            choices:['ALL', 'be-scripts', 'hm-scripts'],
            description:'需要发布的「应用名」，ALL代表所有应用。'
        )
        string(
            name: 'PUBLISH_BRANCH', 
            defaultValue: '2.51.1',
            description: "需要发布的「分支」。"
        )
        choice(
            name:'PUBLISH_SUITE',
            choices:['test'],
            description:'需要发布的「环境」：test、prod'
        )
        booleanParam(
            name: 'IS_DEBUG', 
            defaultValue: false, 
            description: '调试模式：打印环境信息。'
        )
    }
    
    stages {

        stage('format') {
            steps {
                script {
                    manager.addShortText("${env.BUILD_USER}",'black','lightgreen','5px','yellow')
                    currentBuild.description = "发布应用: ${params.PUBLISH_APP}\n"
                    currentBuild.description += "发布环境: ${params.PUBLISH_SUITE}\n"
                    currentBuild.description += "发布版本: ${params.PUBLISH_BRANCH}"
                }
            }
        }

        stage('节点Debug') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false

            parallel {
                stage('01-node') {
                    steps {
                        println "==== NODE_NAME: ${env.NODE_NAME}"
                    }
                }
                stage('02-ssh') {
                    steps {
                        println "==== SSH_CONNECTION: ${env.SSH_CONNECTION}"
                    }
                }
            }
        }

        stage('环境Debug') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false

            parallel {
                stage('01-「PATH」') {
                    steps {
                        println "==== PATH(os): ${env.PATH}"
                    }
                }
                stage('02-环境变量') {
                    steps {
                        sh "printenv"
                    }
                }
                stage('03-全局变量') {
                    steps {
                        println "==== 同步「根」目录 SYNC_PATH: ${env.SYNC_PATH}"
                        println "==== 同步「环境」目录 SYNC_ENV_PATH: ${env.SYNC_ENV_PATH}"
                    }
                }
                stage('04-入参') {
                    steps {
                        println "==== 「应用名」 PUBLISH_APP: ${params.PUBLISH_APP}"
                        println "==== 「分支名」 PUBLISH_BRANCH: ${params.PUBLISH_BRANCH}"
                        println "==== 「发布环境」 PUBLISH_SUITE: ${params.PUBLISH_SUITE}"
                        println "==== 「调试模式」 IS_DEBUG: ${params.IS_DEBUG}"
                    }
                }
            }
        }

        stage('前置处理') {
            failFast false

            parallel {
                stage('01-宙斯环境') {
                    steps {
                        script {
                            if ("${params.PUBLISH_SUITE}" == "prod") {
                                env.SUITE_CODE = "pd-prod"
                                println "==== 「产线(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                            } else if ("${params.PUBLISH_SUITE}" == "test") {
                                env.SUITE_CODE = "test"
                                println "==== 「测试(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                            } else {
                                env.SUITE_CODE = "${params.PUBLISH_SUITE}"
                                println "==== 「其它(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                            }
                        }
                        
                    }
                }
            }
        }

        stage('发布') {
            failFast false

            parallel {
                stage('be-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: params.PUBLISH_APP
                            equals expected: 'be-scripts', actual: params.PUBLISH_APP
                        }
                    }
                    //「be-scripts」的「文件夹」和「绝对路径」
                    environment {
                        BES_FOLDER = "be-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        BES_ABS_PATH = "$SYNC_ENV_PATH/$BES_FOLDER"
                        BES_LN_NAME = "be-scripts_${params.PUBLISH_SUITE}"
                        BES_ABS_LN = "$SYNC_ENV_PATH/$BES_LN_NAME"
                    }
                    stages {
                        stage('01-dir') {
                            steps {
                                script {
                                    if (fileExists("${env.SYNC_ENV_PATH}") == false) {
                                        println "==== 创建环境目录: ${env.SYNC_ENV_PATH}"
                                        sh "mkdir -p ${env.SYNC_ENV_PATH}"
                                    } else {
                                        println "==== 已存在环境目录: ${env.SYNC_ENV_PATH}"
                                    }
                                }
                            }
                        }
                        stage('02-bak') {
                            when {
                                anyOf {
                                    equals expected: 'prod', actual: PUBLISH_SUITE
                                }
                            }
                            //「be-scripts」的「备份目录」
                            environment {
                                _dateStr = getDateStr()
                                // 备份文件夹
                                BES_BAK_FOLDER = "bak_be-scripts_${_dateStr}"
                                // 备份绝对路径（父目录）
                                BES_BAK_PATH = "$SYNC_ENV_PATH/$BES_BAK_FOLDER"
                                // 备份绝对路径（具体目录，多放一级方便还原）
                                BES_BAK_ABS_PATH = "$BES_BAK_PATH/$BES_LN_NAME"
                            }
                            steps {
                                script {
                                    if (fileExists("${env.BES_BAK_ABS_PATH}") == false) {
                                        println "==== 当日无备份: ${env.BES_BAK_PATH}"
                                        if (fileExists("${env.BES_BAK_PATH}") == false) {
                                            sh "mkdir -p ${env.BES_BAK_PATH}"
                                            println "==== 创建当日备份目录: ${env.BES_BAK_PATH}"
                                        }
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            cp -a ${BES_LN_NAME}/ ${BES_BAK_FOLDER}/
                                        '''
                                        println "==== 成功备份目录: ${env.BES_BAK_FOLDER}"
                                    }
                                }
                            }
                        }
                        stage('03-pull') {
                            steps {
                                script {
                                    if (fileExists("${env.BES_ABS_PATH}")) {
                                        println "==== 老版本「重发」：${env.BES_FOLDER}"
                                        sh '''
                                            cd ${BES_ABS_PATH} && git status
                                            git checkout . && git pull
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.BES_FOLDER}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            git clone -b ${PUBLISH_BRANCH} *************************:scm/be-scripts.git ${BES_FOLDER}
                                        '''
                                    }
                                }
                            }
                        }
                        stage('04-conf') {
                            steps {
                                script {
                                    println "==== 重新生成「配置」：${env.BES_FOLDER}"
                                    sh '''
                                        cd ${BES_ABS_PATH}
                                        ${PYTHON_CMD} generate_config.py ${BES_ABS_PATH}/be-scripts/ ${PUBLISH_BRANCH} ${SUITE_CODE}
                                        git status
                                    '''
                                }
                            }
                        }
                        stage('05-ln') {
                            steps {
                                script {
                                    if (fileExists("${env.BES_ABS_LN}")) {
                                        println "==== 老版本「重发」：${env.BES_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            rm -rf ${BES_LN_NAME}
                                            ln -s ${BES_FOLDER} ${BES_LN_NAME}
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.BES_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            ln -s ${BES_FOLDER} ${BES_LN_NAME}
                                        '''
                                    }
                                }
                            }
                        }
                    }
                }
                stage('hm-s') {
                    when {
                        anyOf {
                            equals expected: 'ALL', actual: PUBLISH_APP
                            equals expected: 'hm-scripts', actual: PUBLISH_APP
                        }
                    }
                    //hm-scripts」的「文件夹」和「绝对路径」
                    environment {
                        HMS_FOLDER = "hm-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                        HMS_ABS_PATH = "$SYNC_ENV_PATH/$HMS_FOLDER"
                        HMS_LN_NAME = "hm-scripts_${params.PUBLISH_SUITE}"
                        HMS_ABS_LN = "$SYNC_ENV_PATH/$HMS_LN_NAME"
                    }
                    stages {
                        stage('01-dir') {
                            steps {
                                script {
                                    if (fileExists("${env.SYNC_ENV_PATH}") == false) {
                                        println "==== 创建环境目录: ${env.SYNC_ENV_PATH}"
                                        sh "mkdir -p ${env.SYNC_ENV_PATH}"
                                    } else {
                                        println "==== 已存在环境目录: ${env.SYNC_ENV_PATH}"
                                    }
                                }
                            }
                        }
                        stage('02-bak') {
                            when {
                                anyOf {
                                    equals expected: 'prod', actual: PUBLISH_SUITE
                                }
                            }
                            //「hm-scripts」的「备份目录」
                            environment {
                                _dateStr = getDateStr()
                                // 备份文件夹
                                HMS_BAK_FOLDER = "bak_hm-scripts_${_dateStr}"
                                // 备份绝对路径（父目录）
                                HMS_BAK_PATH = "$SYNC_ENV_PATH/$HMS_BAK_FOLDER"
                                // 备份绝对路径（具体目录，多放一级方便还原）
                                HMS_BAK_ABS_PATH = "$HMS_BAK_PATH/$HMS_LN_NAME"
                            }
                            steps {
                                script {
                                    if (fileExists("${env.HMS_BAK_ABS_PATH}") == false) {
                                        println "==== 当日无备份: ${env.HMS_BAK_PATH}"
                                        if (fileExists("${env.HMS_BAK_PATH}") == false) {
                                            sh "mkdir -p ${env.HMS_BAK_PATH}"
                                            println "==== 创建当日备份目录: ${env.HMS_BAK_PATH}"
                                        }
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            cp -a ${HMS_LN_NAME}/ ${HMS_BAK_FOLDER}/
                                        '''
                                        println "==== 成功备份目录: ${env.HMS_BAK_FOLDER}"
                                    }
                                }
                            }
                        }
                        stage('03-pull') {
                            steps {
                                script {
                                    if (fileExists("${env.HMS_ABS_PATH}")) {
                                        println "==== 老版本「重发」：${env.HMS_FOLDER}"
                                        sh '''
                                            cd ${HMS_ABS_PATH} && git status
                                            git checkout . && git pull
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.HMS_FOLDER}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            git clone -b ${PUBLISH_BRANCH} *************************:scm/hm-scripts.git ${HMS_FOLDER}
                                        '''
                                    }
                                }
                            }
                        }
                        stage('04-conf') {
                            steps {
                                script {
                                    println "==== 重新生成「配置」：${env.HMS_FOLDER}"
                                    sh '''
                                        cd ${HMS_ABS_PATH}
                                        ${PYTHON_CMD} get_zeus_config.py ${PUBLISH_BRANCH} ${SUITE_CODE} ${HMS_ABS_PATH}
                                        git status
                                    '''
                                }
                            }
                        }
                        stage('05-ln') {
                            steps {
                                script {
                                    if (fileExists("${env.HMS_ABS_LN}")) {
                                        println "==== 老版本「重发」：${env.HMS_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            rm -rf ${HMS_LN_NAME}
                                            ln -s ${HMS_FOLDER} ${HMS_LN_NAME}
                                        '''
                                    } else {
                                        println "==== 新版本「首发」：${env.HMS_ABS_LN}"
                                        sh '''
                                            cd ${SYNC_ENV_PATH}
                                            ln -s ${HMS_FOLDER} ${HMS_LN_NAME}
                                        '''
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        stage('后置处理') {
            failFast false

            parallel {
                stage('01-step1') {
                    steps {
                        sh "echo '>>>> step1:' $PYTHON_CMD"
                    }
                }
                stage('02-step2') {
                    steps {
                        sh "echo '>>>> 后置处理2:' $PYTHON_CMD"
                    }
                }
                stage('03-step3') {
                    steps {
                        sh "echo '>>>> 后置处理3:' $PYTHON_CMD"
                    }
                }
            }
        }
    }
    /*
    post {
        always {
            echo "========== zt@2022-11-21 =========="
        }
    }
    */
}