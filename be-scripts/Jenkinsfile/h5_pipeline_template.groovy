#!groovy
/********************************
# Descript：
#   小程序、小程序remote、h5-remote、ssr-remote的流水线模板
********************************/
import groovy.json.JsonSlurper
import groovy.json.JsonOutput

pipeline {
    agent {
        label 'pc'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/hm-scripts/ci_pipeline/ci_pipeline_business/h5_pipeline_main.py"
        workspace = "${workspace}"
        jenkins_id="${jenkins_id}"

    }
    stages {
        stage('start') {
            steps {
                wrap([$class: 'BuildUser']) {
                    script {
                        // 解析最外层的 params 对象
                        def jsonSlurper = new JsonSlurper()
                        def defaultValue = jsonSlurper.parseText(params.params)
                        println("解析后的默认参数: ${defaultValue}")
                        // def dataMap = defaultValue.clone()
                        dataMap = new HashMap(defaultValue)
                        // 从解析后的对象中提取 package_type 和 h5_env
                        env.package_type = dataMap.package_type
                        env.region_group = dataMap.h5_env
                        println(dataMap.iteration_id)
                        println(dataMap.app_name)
                        println "Package Type: ${env.package_type}"
                        println "Region Group: ${env.region_group}"
                    }
                    sh '${PYTHON_CMD} ${SCRIPT_PATH} start_pipeline ${WORKSPACE} ${jenkins_id} ${params}'
                }
            }
        }
        stage('<生产阶段>') {
            failFast false
            stages {

                stage('物料准备') {
                    parallel{
                        stage('拉取代码') {
                            steps {
                                wrap([$class: 'BuildUser']) {
                                    println('pull_code')
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} pull_code ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                        stage("拉取宙斯配置" ){
                            steps {
                                wrap([$class: 'BuildUser']) {
                                    println('pull_zeus_config')
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} pull_zeus_config ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }

                        }
                        stage('外移文件') {
                            when{
                                expression  { package_type == "remote"}
                            }
                            steps {
                                wrap([$class: 'BuildUser']) {
                                   println('pull_gitlab_config')
                                   sh '${PYTHON_CMD} ${SCRIPT_PATH} pull_gitlab_config ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                    }
                }
                stage('编译') {
                    steps {
                        wrap([$class: 'BuildUser']) {
                            println('compile_build')
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} compile ${WORKSPACE} ${jenkins_id} ${params}'
                        }
                    }
                }
            }
        }
        stage('<成品阶段>') {
            failFast false
            stages {
                stage('筛选制品') {
                    steps {
                        wrap([$class: 'BuildUser']) {
                            println('pick_product')
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} pick_product ${WORKSPACE} ${jenkins_id} ${params}'
                        }
                    }
                }
                stage('后置准备') {
                    parallel{
                        stage('拷贝描述文件') {
                            when{
                                expression  { package_type == "mini-program"}
                            }
                            steps {
                                wrap([$class: 'BuildUser']) {
                                   println('create_upload_desc')
                                   sh '${PYTHON_CMD} ${SCRIPT_PATH} create_upload_desc ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                    }
                }
                stage('入库') {
                    steps {
                        wrap([$class: 'BuildUser']) {
                            println('push_product')
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} push_product ${WORKSPACE} ${jenkins_id} ${params}'
                        }
                    }
                }
            }
        }
        stage('<交付阶段>'){
            failFast false
            stages{
                stage('交付'){
                    parallel {
                        stage("推送外移配置"){
                           when{
                                expression  { package_type == "remote"}
                            }
                            steps {
                                wrap([$class: 'BuildUser']) {
                                   println('push_config')
                                   sh '${PYTHON_CMD} ${SCRIPT_PATH} push_config ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                        stage('出库转运') {
                            steps {
                                wrap([$class: 'BuildUser']) {
                                   println('transit_clone_product')
                                   sh '${PYTHON_CMD} ${SCRIPT_PATH} transit_clone_product ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                        stage("制品Nginx备份"){
                            when{
                                expression  {package_type=="mini-program" }
                            }
                            steps {
                                wrap([$class: 'BuildUser']) {
                                    println('push_product_nginx')
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} push_product_nginx ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                    }
                }
            }
        }
        stage("<发布阶段>"){
            failFast false
            stages {
                stage('测试环境发布'){
                    parallel {
                        stage('重启容器') {
                            when{
                                expression  {env.region_group == "test" && (package_type == "param-remote" || package_type == "ssr-remote")}
                            }
                            steps {
                                wrap([$class: 'BuildUser']) {
                                    println('restart_container')
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} restart_container ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                        stage('小程序upload') {
                            when{
                                expression  {env.region_group =="test" && package_type=="mini-program"}
                            }
                            steps {
                                wrap([$class: 'BuildUser']) {
                                   println('upload_mini_program')
                                   sh '${PYTHON_CMD} ${SCRIPT_PATH} upload_mini_program ${WORKSPACE} ${jenkins_id} ${params}'
                                }
                            }
                        }
                    }
                }

            }
        }
        stage('结束') {
            steps {
                wrap([$class: 'BuildUser']) {
                    println('end_pipeline')
                    sh '${PYTHON_CMD} ${SCRIPT_PATH} end_pipeline ${WORKSPACE} ${jenkins_id} ${params}'
                }
            }
        }
    }
}