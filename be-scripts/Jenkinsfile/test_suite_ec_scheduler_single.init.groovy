#!groovy
pipeline {
    agent any
    environment {
        ONLINE_BRANCH="2.8.0"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/scheduler"
        PARAM_LIST="${SUIT_CODE}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        // timestamps()
        retry(1)
    }
    parameters {

        string(
            name: 'SUIT_CODE',
            defaultValue: 'tms19',
            description: "环境"
        )

    }
    stages {
        stage('初始化howbuy_quartz') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/scheduler_db_init_quartz.py ${PARAM_LIST}"
            }
        }
          stage('刷新howbuy_quartz') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/scheduler_db_refresh_quartz.py ${PARAM_LIST}"
            }
        }

    }
}