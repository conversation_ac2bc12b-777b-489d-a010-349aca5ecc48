#!groovy

def generatePipeline(String step_names) {
  def stages = step_names.split(",")
  def pipelineScript = """
    pipeline {
      agent any
      stages {
  """
  for (def stage in stages) {
    pipelineScript += """
        stage("${stage.trim()}") {
          steps {
            echo 1
          }
        }
    """
  }
  pipelineScript += """
      }
    }
  """
  return pipelineScript
}

pipeline {
    agent {
       label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/jenkins_mgt"
        PARAM_LIST="${BUILD_ID} ${schedule_config_id}"
    }
    parameters {
        string(
            name: 'schedule_config_id',
            defaultValue: "${schedule_config_id}",
            description: "定时配置ID"
        )
    }
    stages {
      stage('准备') {
        steps {
          script {
            def stages = '触发业务迭代自动化'.split(',')
             // 执行该阶段的命令
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/schedule_execute_biz_config.py ${BUILD_ID} $schedule_config_id parse"
            for (int i = 0; i < stages.size(); i++) {
              def stageName = stages[i].trim()
              stage(stageName) {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/schedule_execute_biz_config.py ${BUILD_ID} $schedule_config_id $stageName"
            }
          }
        }
      }
    }
     stage('结束') {
        steps {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/schedule_execute_biz_config.py ${BUILD_ID} $schedule_config_id end"
        }
     }
  }

   post {
        failure {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/schedule_execute_biz_config.py ${BUILD_ID} $schedule_config_id end"
        }
        aborted {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/schedule_execute_biz_config.py ${BUILD_ID} $schedule_config_id end"
        }
        unstable {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/schedule_execute_biz_config.py ${BUILD_ID} $schedule_config_id  end"
        }
    }

}
