#!groovy
pipeline {
    agent {
        label 'vmx'
    }
    environment {
        ONLINE_BRANCH="2.73.2"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/test_publish_aio"
        PARAM_LIST="${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} ${currentBuild.currentResult} ${PID}"
    }
    options {
        //timeout(time: 1, unit: 'HOURS')
        timeout(time: 90, unit: 'MINUTES')
        // timestamps()
        retry(1)
    }
    parameters {
        string(
            name: 'PID',
            defaultValue: '-1',
            description: "业务日志ID"
        )
        string(
            name: 'OPT_TYPE',
            defaultValue: 'CRON',
            description: "业务操作类型"
        )
        string(
            name: 'JACOCO_TYPE',
            defaultValue: '1',
            description: "覆盖率标识"
        )
        string(
            name: 'AREX_TYPE',
            defaultValue: '1',
            description: "流量回放agent标识"
        )
		string(
            name: 'PINPOINT_TYPE',
            defaultValue: '1',
            description: "pinpoint标识"
        )
        string(
            name: 'MOCK_TYPE',
            defaultValue: '1',
            description: "MOCK标识"
        )
         string(
            name: 'API_TYPE',
            defaultValue: '0',
            description: "API标识"
        )
//          string(
//             name: 'SHARDING_TYPE',
//             defaultValue: '0',
//             description: "SHARDING标识"
//         )
         string(
            name: 'JUST_INIT_DB',
            defaultValue: '0',
            description: "仅初始化数据库开关标识"
        )
    }
    stages {

        stage('解析') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} PARSE"
            }
        }

        stage('环境预检') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CHECK', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                    equals expected: 'UPDATE', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} PRE"
            }
        }

        stage('预处理') {
            failFast false

            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CHECK', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                    equals expected: 'UPDATE', actual: OPT_TYPE

                }
            }

            parallel {

                stage('static') {

                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'HISTORY', actual: OPT_TYPE
                            equals expected: 'TEST_PUSH', actual: OPT_TYPE
                            equals expected: 'CHECK', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                        }
                    }

                    stages {
                        stage('static分支拉取') {
                            steps {
                               sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} TSTATIC_BRANCH_CREATE"
                            }
                        }
                        stage('static配置更新') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} TSTATIC_CONF_UPD"
                            }
                        }
                    }
                }

                stage('vm') {

                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'HISTORY', actual: OPT_TYPE
                            equals expected: 'TEST_PUSH', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                        }
                    }

                    stages {
                        stage('虚机停止') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} BEFORE_VM_STOP"
                            }
                        }
                        stage('虚机基线处理') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} BEFORE_VM_BASE"
                            }
                        }
                    }
                }
            }
        }

        stage('初始化') {
            failFast false

            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CHECK', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                    equals expected: 'UPDATE', actual: OPT_TYPE
                }
            }

            parallel {
                stage('1-app') {

                    when {
                        expression { JUST_INIT_DB != '1' }
                        not {
                            equals expected: 'CHECK', actual: OPT_TYPE
                        }
                    }

                    stages {
                        stage('拉制品') {
                            when {
                                    anyOf {
                                        equals expected: 'TEST_PUSH', actual: OPT_TYPE
                                        equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                                    }
                                }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_PULL"
                            }
                        }
                        stage('推制品') {
                            steps {
                               sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_PUSH"
                            }
                        }
                    }
                }
                stage('2-config') {
                     when {
                            expression { JUST_INIT_DB != '1' }
                         }
                    stages {
                        stage('拉外移文件') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_PULL"
                            }
                        }
                        stage('外移文件替换') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_REPLACE"
                            }
                        }
                        stage('宙斯配置项替换') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_ZEUS"
                            }
                        }
                        stage('生成宙斯配置文件') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_ZEUS_CREATE"
                            }
                        }
                        stage('推外移文件') {
                            when {
                                not {
                                    equals expected: 'CHECK', actual: OPT_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_PUSH"
                            }
                        }
                        stage('配置分发') {
                            when {
                                anyOf {
                                    equals expected: 'INIT', actual: OPT_TYPE
                                    equals expected: 'CRON', actual: OPT_TYPE
                                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                                    equals expected: 'UPDATE', actual: OPT_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_DISPATCH"
                            }
                        }
                    }
                }
                stage('3-启动脚本处理') {
                    when {
                        expression { JUST_INIT_DB != '1' }
                        anyOf {
                            equals expected: 'TEST_PUSH', actual: OPT_TYPE
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                            equals expected: 'HISTORY', actual: OPT_TYPE
                            equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                            equals expected: 'UPDATE', actual: OPT_TYPE
                        }
                    }
                    stages {
                        stage('启动脚本预处理') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_PULL"
                            }
                        }
                        stage('追加 Agent') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_AGENT"
                            }
                        }
                        stage('推启动脚本') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_PUSH"
                            }
                        }
                    }
                }
                stage('4-db（2022全自动）') {
                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                            equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                            equals expected: 'UPDATE', actual: OPT_TYPE
                        }
                    }
                    stages {
                        stage('DB初始化') {
                            when {
                                not {
									equals expected: 'UPDATE', actual: OPT_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_INIT"
                            }
                        }
                        stage('SQL制品准备') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_PULL"
                            }
                        }
                        stage('SQL分拣') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_SORT"
                            }
                        }
                        stage('SQL执行') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_EXECUTE"
                            }
                        }
                    }
                }
                stage('5-agent处理') {
                    when {
                        expression { JUST_INIT_DB != '1' }
                        not {
                            equals expected: 'UPDATE', actual: OPT_TYPE
                        }
                    }
                    stages {
                        stage('拉agent制品') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} AGENT_PULL"
                            }
                        }
                        stage('推agent制品') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} AGENT_PUSH"
                            }
                        }
                    }
                }
            }
        }

        stage('清理缓存') {
            when {
                expression { JUST_INIT_DB != '1' }
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CLEAR_CACHE"
            }
        }

        stage('设置时间') {
            when {
                expression { JUST_INIT_DB != '1' }
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} SET_TIME"
            }
        }

        stage('第三方中间件重启') {
            when {
                expression { JUST_INIT_DB != '1' }
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} TPM_RESTART"
            }
        }

        stage('启动应用') {
            when {
                expression { JUST_INIT_DB != '1' }
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                    equals expected: 'UPDATE', actual: OPT_TYPE
                }
            }
             steps('启动应用') {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_RESTART"
             }
        }

        stage('验证') {
            when {
                expression { JUST_INIT_DB != '1' }
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                    equals expected: 'UPDATE', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} HEALTH_CHECK"
            }
        }

        stage('集成测试') {
            when {
                expression { JUST_INIT_DB != '1' }
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} INTEGRATION_TEST"
            }
        }

        stage('记录') {
            steps {
                echo "========== 构建『成功』执行！=========="
            }
        }
    }
    post {
        always {
            echo "========== 记录构建结果：=========="
        }
        aborted {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} ABORTED ${PID} RECORD"
        }
        success {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} SUCCESS ${PID} RECORD"
        }
        failure {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} FAILURE ${PID} RECORD"
        }
    }
}