#!groovy
/********************************
# Descript：
#   数据开发「初始化」需要跑的流水线。
# History:
# 	2024-09-18  Zt  增加「执行SQL」出错，数据导出导入实现。
********************************/

def param_exec_sql_err=false

pipeline {
    agent {
        label 'vm'
    }
    environment {
        EXEC_SQL_ERR = false
        PYTHON_CMD="python3.x"
        DUMP_SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev"
        PARAM_LIST="${JOB_NAME} ${WORKSPACE} ${biz_code} ${biz_branch_name} ${suite_code} ${business_id} ${BUILD_ID}"
    }
    stages{
        stage('数据记录')  {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_build_id ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} 'running' "
            }
        }
        stage('准备sql制品') {
            steps {
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/test_data_init.py "pull_sql_repo" ${JOB_NAME} ${WORKSPACE} ' + biz_code + ' ' + biz_branch_name + ' ' + suite_code + ' ' +  business_id +' '+'${BUILD_ID}'
            }
        }
        stage('分拣sql') {
            steps {
                println "==== PATH(os): ${env.PATH}"
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/test_data_init.py "sort_sql" ${JOB_NAME} ${WORKSPACE} ' + biz_code + ' ' + biz_branch_name + ' ' + suite_code + ' ' + business_id +' '+'${BUILD_ID}'
            }
        }
        stage('数据库恢复') {
            steps {
                println "==== PATH(os): ${env.PATH}"
                sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/test_data_init.py "restore_dump" ${JOB_NAME} ${WORKSPACE} ' + biz_code + ' ' + biz_branch_name + ' ' + suite_code + ' ' + business_id +' '+'${BUILD_ID}'
            }
        }
        stage('执行sql') {
            steps {
//                 sh label: '', script: 'python3.x /home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/test_data_init.py "execute_sql" ${JOB_NAME} ${WORKSPACE} ' + biz_code + ' ' + biz_branch_name + ' ' + suite_code + ' ' + business_id +' '+'${BUILD_ID}'
                script {
                    try {
                        sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py execute_sql ${PARAM_LIST}"
                    } catch(err) {
                        echo ">>>> 执行SQL出错，将进入尝试，错误信息：${err} - " + err.toString()
//                         param_exec_sql_err=true
                    } finally {
                        echo ">>>> 执行SQL完成（finally）"
                    }
                }
            }
        }
//         stage('手工出错') {
//             steps {
//                 script {
//                     env.EXEC_SQL_ERR=true
//                     println "==== env.EXEC_SQL_ERR(无法在代码中设置环境变量的值): ${env.EXEC_SQL_ERR}"
//                     println "==== def.param_exec_sql_err: ${param_exec_sql_err}"
//                     param_exec_sql_err=true
//                 }
//             }
//         }
        stage('dev-dump恢复') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) && false }
            }
            steps {
                script {
                    println "==== dev-dump恢复"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py restore_dump ${PARAM_LIST}"
                }
            }
        }
        stage('dev数据导出') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) }
            }
            steps {
                script {
                    println "==== dev数据导出"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py test_data_export ${PARAM_LIST}"
                }
            }
        }
        stage('dev数据清空') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) }
            }
            steps {
                script {
                    println "==== dev数据清空"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py db_clean ${PARAM_LIST}"
                }
            }
        }
        stage('SQL制品准备') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) }
            }
            steps {
                script {
                    println "==== SQL制品准备"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py pull_sql_repo ${PARAM_LIST}"
                }
            }
        }
        stage('SQL分拣') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) }
            }
            steps {
                script {
                    println "==== SQL分拣"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py sort_sql ${PARAM_LIST}"
                }
            }
        }
        stage('SQL执行') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) }
            }
            steps {
                script {
                    println "==== SQL执行"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py execute_sql ${PARAM_LIST}"
                }
            }
        }
        stage('dev数据灌入') {
            when {
                expression { Boolean.valueOf(param_exec_sql_err) }
            }
            steps {
                script {
                    println "==== dev数据灌入"
                    sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py test_data_import ${PARAM_LIST}"
                }
            }
        }
    }
    post{
        failure{
            echo 'failure'
            sh label: '', script: '${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_execute_result ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} "failure" '
        }
        aborted{
			echo 'aborted'
            sh label: '', script: '${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_execute_result ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} "aborted" '
        }
        success{
			echo 'success'
            sh label: '', script: '${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_execute_result ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} "success" '
        }
    }
}
