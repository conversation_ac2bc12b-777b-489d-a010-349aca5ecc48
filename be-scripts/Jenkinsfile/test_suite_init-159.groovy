#!groovy
pipeline {
    agent {
        label 'vm'
    }
    environment {
        ONLINE_BRANCH="2.8.0"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/test_publish_aio"
        PARAM_LIST="${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} ${currentBuild.currentResult} ${PID}"
    }
    options {
        //timeout(time: 1, unit: 'HOURS')
        timeout(time: 90, unit: 'MINUTES')
        // timestamps()
        retry(1)
    }
    parameters {
        string(
            name: 'PID', 
            defaultValue: '-159',
            description: "业务日志ID"
        )
        string(
            name: 'OPT_TYPE',
            defaultValue: 'CRON',
            description: "业务操作类型"
        )
        string(
            name: 'JACOCO_TYPE',
            defaultValue: '0',
            description: "覆盖率标识"
        )
        string(
            name: 'CCMS_TYPE',
            defaultValue: '1',
            description: "CCMS配置开关"
        )
        string(
            name: 'MOCK_TYPE',
            defaultValue: '1',
            description: "MOCK标识"
        )
         string(
            name: 'API_TYPE',
            defaultValue: '0',
            description: "API标识"
        )
         string(
            name: 'SHARDING_TYPE',
            defaultValue: '0',
            description: "SHARDING标识"
        )
    }
    stages {

        stage('解析') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} PARSE"
            }
        }

        stage('环境预检') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CHECK', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} PRE"
            }
        }

        stage('预处理') {
            failFast false

            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CHECK', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }

            parallel {

                stage('static') {

                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'HISTORY', actual: OPT_TYPE
                            equals expected: 'TEST_PUSH', actual: OPT_TYPE
                            equals expected: 'CHECK', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                        }
                    }

                    stages {
                        stage('static分支拉取') {
                            steps {
                               sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} TSTATIC_BRANCH_CREATE"
                            }
                        }
                        stage('static配置更新') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} TSTATIC_CONF_UPD"
                            }
                        }
                    }
                }

                stage('vm') {

                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'HISTORY', actual: OPT_TYPE
                            equals expected: 'TEST_PUSH', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                        }
                    }

                    stages {
                        stage('虚机停止') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} BEFORE_VM_STOP"
                            }
                        }
                        stage('虚机基线处理') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} BEFORE_VM_BASE"
                            }
                        }
                    }
                }
            }
        }

        stage('初始化') {
            failFast false

            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CHECK', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }

            parallel {
                stage('1-app') {

                    when {
                        not {
                            equals expected: 'CHECK', actual: OPT_TYPE
                        }
                    }

                    stages {
                        stage('拉制品') {
                            when {
                                    anyOf {
                                        equals expected: 'TEST_PUSH', actual: OPT_TYPE
                                        equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                                    }
                                }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_PULL"
                            }
                        }
                        stage('推制品') {
                            steps {
                               sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_PUSH"
                            }
                        }
                    }
                }
                stage('2-config（2021仅剩宙斯）') {

                    stages {
                        stage('拉外移文件') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_PULL"
                            }
                        }
                        stage('外移文件替换') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_REPLACE"
                            }
                        }
                        stage('宙斯配置项替换') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_ZEUS"
                            }
                        }
                        stage('生成宙斯配置文件') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_ZEUS_CREATE"
                            }
                        }
                        stage('配置校验') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_CHECK"
                            }
                        }
                        stage('推外移文件') {
                            when {
                                not {
                                    equals expected: 'CHECK', actual: OPT_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_PUSH"
                            }
                        }
                        stage('配置分发') {
                            when {
                                anyOf {
                                    equals expected: 'INIT', actual: OPT_TYPE
                                    equals expected: 'CRON', actual: OPT_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CONF_DISPATCH"
                            }
                        }
                    }
                }
                stage('3-启动脚本处理') {
                    when {
                        anyOf {
                            equals expected: 'TEST_PUSH', actual: OPT_TYPE
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                            equals expected: 'HISTORY', actual: OPT_TYPE
                            equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                        }
                    }
                    stages {
                        stage('启动脚本预处理') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_PULL"
                            }
                        }
                        stage('追加覆盖率 Agent') {
                            when {
                                allOf {
                                    equals expected: '1', actual: JACOCO_TYPE
                                    equals expected: '0', actual: API_TYPE
                                    equals expected: '0', actual: SHARDING_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_REPLACE"
                            }
                        }
                        stage('追加MOCK Agent') {
                            when {
                                allOf {
                                    equals expected: '1', actual: MOCK_TYPE
                                    equals expected: '0', actual: API_TYPE
                                    equals expected: '0', actual: SHARDING_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_REPLACE_FOR_MOCK"
                            }
                        }
                         stage('追加Api Agent') {
                            when {
                                allOf {
                                    equals expected: '1', actual: API_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_REPLACE_FOR_API"
                            }
                        }
                        stage('追加Sharding Agent') {

                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_REPLACE_FOR_SHARDING"
                            }
                        }
                        stage('推启动脚本') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_PUSH"
                            }
                        }
                    }
                }
                stage('4-ccms（2022下线）') {
                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'CHECK', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                            equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                        }
                    }
                    stages {
                        stage('下载CCMS') {
							when {
                                not {
                                    equals expected: '0', actual: CCMS_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CCMS_DOWNLOAD"
                            }
                        }
                        stage('CCMS配置替换') {
							when {
                                not {
                                    equals expected: '0', actual: CCMS_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CCMS_REPLACE"
                            }
                        }
                        stage('CCMS校验') {
							when {
								not {
									equals expected: '0', actual: CCMS_TYPE
								}
							}
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CCMS_CHECK"
                            }
                        }
                        stage('导入测试CCMS') {
                            when {
                                not {
									equals expected: '0', actual: CCMS_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CCMS_IMPORT"
                            }
                        }
                        stage('CCMS分发') {
                            when {
                                not {
									//equals expected: 'CHECK', actual: OPT_TYPE
									equals expected: '0', actual: CCMS_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CCMS_DISPATCH"
                            }
                        }
                    }
                }
                stage('5-db（2022全自动）') {
                    when {
                        anyOf {
                            equals expected: 'INIT', actual: OPT_TYPE
                            equals expected: 'CRON', actual: OPT_TYPE
                            equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                        }
                    }
                    stages {
                        stage('DB初始化') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_INIT"
                            }
                        }
                        stage('SQL制品准备') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_PULL"
                            }
                        }
                        stage('SQL分拣') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_SORT"
                            }
                        }
                        stage('SQL执行') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} DB_EXECUTE"
                            }
                        }
                    }
                }
                stage('6-sharding（规则推送）') {
                    when {
                        not {
                            equals expected: '1', actual: SHARDING_TYPE
                        }
                    }
                    stages {
                        stage('分片数据推送') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} PUSH_SHARDING_DATA"
                            }
                        }
                    }
                }
                stage('7-agent处理') {

                    stages {
                        stage('拉agent制品') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} AGENT_PULL"
                            }
                        }
                        stage('推agent制品') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} AGENT_PUSH"
                            }
                        }
                    }
                }
            }
        }

        stage('清理缓存') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} CLEAR_CACHE"
            }
        }

        stage('设置时间') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} SET_TIME"
            }
        }

        stage('第三方中间件重启') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} TPM_RESTART"
            }
        }

        stage('启动应用') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'OTHER', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
             steps('启动应用') {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_RESTART"
             }
        }

        stage('数据采集') {
            when {
                anyOf {
                    equals expected: '1', actual: API_TYPE
                    equals expected: '1', actual: SHARDING_TYPE
                }
            }
             parallel {
                    stage('api数据校验') {
                            when {
                                allOf {
                                    equals expected: '1', actual: API_TYPE
                                   }
                                }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_API_RESULT_CHECK"
                            }
                    }

                    stage('分片策略数据校验') {
                            when {
                                   allOf {
                                        equals expected: '1', actual: SHARDING_TYPE
                                   }
                                }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_SHARDING_RESULT_RESTART"
                            }
                    }

             }

        }

        stage('启动脚本后置处理') {
            when {
                anyOf {
                    equals expected: '1', actual: API_TYPE
                    equals expected: '1', actual: SHARDING_TYPE
                }
            }
              stages {
                        stage('启动脚本预处理') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_PULL"
                            }
                        }
                        stage('追加覆盖率 Agent') {
                            when {
                                allOf {
                                    equals expected: '1', actual: JACOCO_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_REPLACE"
                            }
                        }
                        stage('追加MOCK Agent') {
                            when {
                                allOf {
                                    equals expected: '1', actual: MOCK_TYPE
                                }
                            }
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_REPLACE_FOR_MOCK"
                            }
                        }
                        stage('推启动脚本') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} START_SCRIPT_PUSH"
                            }
                        }
                    }
        }

        stage('重启应用') {
            when {
                 anyOf {
                    equals expected: '1', actual: API_TYPE
                    equals expected: '1', actual: SHARDING_TYPE
                }
            }
             steps('重启应用') {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} APP_RESTART"
             }
        }

        stage('验证') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'TEST_PUSH', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} HEALTH_CHECK"
            }
        }

        stage('集成测试') {
            when {
                anyOf {
                    equals expected: 'INIT', actual: OPT_TYPE
                    equals expected: 'HISTORY', actual: OPT_TYPE
                    equals expected: 'CRON', actual: OPT_TYPE
                    equals expected: 'MULTI_PUSH', actual: OPT_TYPE
                }
            }
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${PARAM_LIST} INTEGRATION_TEST"
            }
        }

        stage('记录') {
            steps {
                echo "========== 构建『成功』执行！=========="
            }
        }
    }
    post {
        always {
            echo "========== 记录构建结果：=========="
        }
        aborted {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} ABORTED ${PID} RECORD"
        }
        success {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} SUCCESS ${PID} RECORD"
        }
        failure {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_suite_init_main.py ${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} FAILURE ${PID} RECORD"
        }
    }
}