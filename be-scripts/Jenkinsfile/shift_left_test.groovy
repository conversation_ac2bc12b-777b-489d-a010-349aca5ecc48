#!groovy
// 左移测试流水线定义
// 包含API文档处理、SQL处理和ES处理三条主要流程
pipeline {
    agent {
        label 'vm'
    }
    environment {
        ONLINE_BRANCH="2.73.2"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/es_mgt/es_business"
        PARAM_LIST="${BUILD_ID} ${WORKSPACE} ${currentBuild.duration} ${currentBuild.currentResult}"
    }
    options {
        timeout(time: 60, unit: 'MINUTES')
        retry(1)
    }
    parameters {
        string(
            name: 'iteration_id',
            defaultValue: '',
            description: "迭代ID"
        )
        string(
            name: 'app_name_str',
            defaultValue: '',
            description: "应用名称列表，以逗号分隔"
        )
        string(
            name: 'APIDOC_TYPE',
            defaultValue: '1',
            description: "API文档生成标识"
        )
        string(
            name: 'SQL_TYPE',
            defaultValue: '1',
            description: "SQL处理标识"
        )
        string(
            name: 'ES_TYPE',
            defaultValue: '1',
            description: "ES处理标识"
        )
    }
    stages {
        // 并行处理阶段
        stage('并行处理') {
            parallel {
                // API文档处理流程
                stage('APIDOC处理') {
                    when {
                        expression { APIDOC_TYPE == '1' }
                    }
                    stages {
                        stage('拉代码') {
                            when {
                                expression { APIDOC_TYPE == '1' }
                            }
                            steps {
                                println "拉代码"
                                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} PULL_CODE"
                            }
                        }
                        stage('生成API文档') {
                            steps {
                                println "生成API文档"
                                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} GENERATE_APIDOC"
                            }
                        }
                        stage('解析API文档') {
                            steps {
                                println "解析API文档"
                                // sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} PARSE_APIDOC"
                            }
                        }
                        stage('同步API文档') {
                            steps {
                                println "同步API文档"
                                // sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} SYNC_APIDOC"
                            }
                        }
                    }
                }

                // SQL处理流程
                stage('SQL处理') {
                    when {
                        expression { SQL_TYPE == '1' }
                    }
                    stages {
                        stage('拉SQL') {
                            steps {
                                println "拉SQL"
                                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} PULL_SQL"
                            }
                        }
                        stage('重命名SQL') {
                            steps {
                                println "重命名SQL"
                                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} RENAME_SQL"
                            }
                        }
                        stage('检测SQL') {
                            steps {
                                println "检测SQL"
                                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} CHECK_SQL"
                            }
                        }
                        stage('推送SQL') {
                            steps {
                                println "推送SQL"
                                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} PUSH_SQL"
                            }
                        }
                    }
                }

                // ES处理流程
                stage('ES处理') {
                    when {
                        expression { ES_TYPE == '1' }
                    }
                    stages {
                        stage('拉ES') {
                            steps {
                                println "拉ES"
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/create_es_index_from_vcs.py pull_es ${PARAM_LIST} ${iteration_id} ${app_name_str}"
                            }
                        }
                        stage('提取ES') {
                            steps {
                                println "提取ES"
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/create_es_index_from_vcs.py extract_es ${PARAM_LIST} ${iteration_id} ${app_name_str}"
                            }
                        }
                        stage('推送ES') {
                            steps {
                                println "推送ES"
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/create_es_index_from_vcs.py push_es ${PARAM_LIST} ${iteration_id} ${app_name_str}"
                            }
                        }
                    }
                }
            }
        }

        stage('结果通知') {
            steps {
                println "结果通知"
                //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} NOTIFY"
            }
        }
    }

    // 流水线执行后处理
    post {
        always {
            println "流水线执行后处理"
            //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} POST_ALWAYS"
        }
        success {
            println "流水线执行成功"
            //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} POST_SUCCESS"
        }
        failure {
            println "流水线执行失败"
            //sh "${PYTHON_CMD} ${SCRIPT_PATH}/shift_left_test_main.py ${PARAM_LIST} POST_FAILURE"
        }
    }
}