#!groovy
pipeline {
    agent any
    environment {
        ONLINE_BRANCH="2.8.0"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/scheduler"
        PARAM_LIST="${USERS}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        // timestamps()
        retry(1)
    }
    parameters {

        string(
            name: 'USERS',
            defaultValue: 'paTest1,paTest2',
            description: "用户名"
        )

    }
    stages {
        stage('重启ccms') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/test_ccms_addUser.py ${PARAM_LIST}"
            }
        }


    }
}