#!groovy
/********************************
# Descript：
#   mantis发布流水线
********************************/

pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/py_app_publish.py"

    }
    stages {
        stage('<生产阶段>') {
            failFast false
            stages {
                stage('物料准备') {
                    parallel{
                        stage('拉取代码') {
                            steps {
                                wrap([$class: 'BuildUser']) {
                                    println('pull_code')
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} pull_code ${module_name} ${br_name} ${suite_code} ${delete_image}'
                                }
                            }
                        }
                        stage("拉取宙斯配置" ){
                            steps {
                                wrap([$class: 'BuildUser']) {
                                    println('pull_zeus_config')
                                    sh '${PYTHON_CMD} ${SCRIPT_PATH} pull_zeus_config ${module_name} ${br_name} ${suite_code} ${delete_image}'
                                }
                            }

                        }
                    }
                }
                stage("启动脚本" ){
                    steps {
                        wrap([$class: 'BuildUser']) {
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} make_start_script ${module_name} ${br_name} ${suite_code} ${delete_image}'
                        }
                    }

                }
                stage('Dockerfile文件') {
                    steps {
                        wrap([$class: 'BuildUser']) {
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} create_dockerfile ${module_name} ${br_name} ${suite_code} ${delete_image}'
                        }
                    }
                }
            }
        }
        stage('<成品阶段>') {
            failFast false
            stages {
                stage('制作镜像') {
                    steps {
                        wrap([$class: 'BuildUser']) {
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} make_image ${module_name} ${br_name} ${suite_code} ${delete_image}'
                        }
                    }
                }
				stage('入库') {
                    parallel{
						stage('入代码制品库') {
							steps {
								wrap([$class: 'BuildUser']) {
									sh '${PYTHON_CMD} ${SCRIPT_PATH} push_product ${module_name} ${br_name} ${suite_code} ${delete_image}'
								}
							}
						}
						stage('入镜像制品库') {
							steps {
								wrap([$class: 'BuildUser']) {
									sh '${PYTHON_CMD} ${SCRIPT_PATH} push_image_product ${module_name} ${br_name} ${suite_code} ${delete_image}'
								}
							}
						}
					}
				}
            }
        }
        stage('<交付阶段>'){
            failFast false
            stages{
                // stage("推送外移配置"){
                //     steps {
                //         wrap([$class: 'BuildUser']) {
                //           sh '${PYTHON_CMD} ${SCRIPT_PATH} push_zeus_config ${module_name} ${br_name} ${suite_code}'
                //         }
                //     }
                // }
				stage('出库转运') {
                    parallel{
						stage('到代码中转库') {
							steps {
								wrap([$class: 'BuildUser']) {
								  sh '${PYTHON_CMD} ${SCRIPT_PATH} push_code_repo ${module_name} ${br_name} ${suite_code} ${delete_image}'
								}
							}
						}
						stage('到镜像中转库') {
							steps {
								wrap([$class: 'BuildUser']) {
								  sh '${PYTHON_CMD} ${SCRIPT_PATH} push_image_repo ${module_name} ${br_name} ${suite_code} ${delete_image}'
								}
							}
						}
					}
				}
            }
        }
        stage("<发布阶段>"){
            failFast false
            stages {
                stage('重启镜像') {
                    steps {
                        wrap([$class: 'BuildUser']) {
                            sh '${PYTHON_CMD} ${SCRIPT_PATH} publish_app ${module_name} ${br_name} ${suite_code} ${delete_image}'
                        }
                    }
                }

            }
        }
    }
}