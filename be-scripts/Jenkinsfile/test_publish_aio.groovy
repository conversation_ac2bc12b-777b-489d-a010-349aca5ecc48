#!groovy
pipeline {
    agent any
    environment {
        PYTHON_CMD="python37"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/test_publish_aio/test_publish_aio_script"
        PARAM_LIST="${BUILD_ID} \"${params}\" ${WORKSPACE} ${currentBuild.duration} ${currentBuild.result} ${PID} ${LOG_ID} ${OPERATOR} ${SUITE_CODE} \"${APP_NAME_LIST}\""
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        // timestamps()
        retry(1)
    }
    triggers {
        cron('H 8 * * 1-5')
        pollSCM('H * * * 1-5')
    }
    parameters {
        string(
            name: 'PID', 
            defaultValue: '1', 
            description: "业务日志ID"
        )

        string(
            name: 'LOG_ID', 
            defaultValue: '1', 
            description: "jenkins日志ID"
        )

        string(
            name: 'OPERATOR', 
            defaultValue: 'howbuyscm', 
            description: "操作人"
        )

        string(
            name: 'SUITE_CODE', 
            defaultValue: 'tms18', 
            description: "环境套（不能为空）"
        )

        string(
            name: 'APP_NAME_LIST', 
            defaultValue: 'adviser-batch-center-remote,asset-batch-center-remote,asset-center-remote,high-order-search-remote,order-center-remote,order-center-search-remote,acc-center-web,acc-console-web', 
            description: "应用名列表（不能为空）"
        )
    }
    stages {
        stage('解析') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/tp_aio_parse.py ${PARAM_LIST}"
            }
        }

        stage('前置检查') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/tp_aio_pre.py ${PARAM_LIST}"
            }
        }

        stage('初始化') {
            failFast true
            parallel {
                stage('1-app') {
                    stages {
                        stage('拉制品') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/tp_aio_app_pull.py ${PARAM_LIST}"
                            }
                        }
                        stage('tstatic替换') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('推制品') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/tp_aio_app_push.py ${PARAM_LIST}"
                            }
                        }
                        stage('打镜像') {
                            steps {
                                sh "${PYTHON_CMD} ${SCRIPT_PATH}/tp_aio_app_make_img.py ${PARAM_LIST}"
                            }
                        }
                    }
                }
                stage('2-config') {
                    stages {
                        stage('拉配置') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('外移文件替换') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('宙斯配置项替换') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('推配置') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('创建ConfigMap') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                    }
                }
                stage('3-ccms') {
                    stages {
                        stage('下载CCMS') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('CCMS配置替换') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                        stage('导入测试CCMS') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                    }
                }
                stage('4-tpm') {
                    stages {
                        stage('第三方中间件重启') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                    }
                }
                stage('5-db') {
                    stages {
                        stage('DB初始化') {
                            when {
                                branch 'master'
                            }
                            steps {
                                echo "buildId = " + BUILD_ID
                            }
                        }
                    }
                }
            } //parallel
        }
        
        stage('设置时间') {
            when {
                branch 'master'
            }
            steps {
                echo "buildId = " + BUILD_ID
            }
        }

        stage('清理缓存') {
            when {
                branch 'master'
            }
            steps {
                echo "buildId = " + BUILD_ID
            }
        }

        stage('应用重启') {
            when {
                branch 'master'
            }
            steps {
                echo "buildId = " + BUILD_ID
            }
        }

        stage('验证') {
            when {
                branch 'master'
            }
            steps {
                echo "buildId = " + BUILD_ID
            }
        }

        stage('测试') {
            when {
                branch 'master'
            }
            steps {
                echo "buildId = " + BUILD_ID
            }
        }

        stage('记录') {
            steps {
                echo ">>>>日志记录："
            }
        }
    }
    post {
        always {
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/tp_aio_record.py ${PARAM_LIST}"
        }
        aborted {
            echo '构建被中止'
        }
        success {
            echo '构建成功！'
        }
        failure {
            echo '构建被中止！'
        }
    }
}