#!groovy

def generatePipeline(String step_names) {
  def stages = step_names.split(",")
  def pipelineScript = """
    pipeline {
      agent any
      stages {
  """
  for (def stage in stages) {
    pipelineScript += """
        stage("${stage.trim()}") {
          steps {
            echo 1
          }
        }
    """
  }
  pipelineScript += """
      }
    }
  """
  return pipelineScript
}

pipeline {
    agent {
       label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/jenkins_mgt"
        PARAM_LIST="${BUILD_ID} ${jenkins_composition_id} ${step_names}"
    }
    parameters {
        string(
            name: 'jenkins_composition_id',
            description: "组合计划ID"
        )
         string(name: 'step_names', defaultValue: 'Build, Test, Deploy', description: 'Comma-separated names of pipeline stages')
    }
    stages {
      stage('组合计划准备') {
        steps {
          script {
            def stages = step_names.split(',')
             // 执行该阶段的命令
            sh "${PYTHON_CMD} ${SCRIPT_PATH}/jenkins_composition_mgt.py ${BUILD_ID} $jenkins_composition_id parse"
            for (int i = 0; i < stages.size(); i++) {
              def stageNames = stages[i].trim()
              def stageName = stageNames.split('_job_type_')[1]
              stage(stageName) {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/jenkins_composition_mgt.py ${BUILD_ID} $jenkins_composition_id $stageNames"
            }
          }
        }
      }
    }
     stage('组合计划结束') {
        steps {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/jenkins_composition_mgt.py ${BUILD_ID} $jenkins_composition_id end"
        }
     }
  }

   post {
        failure {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/jenkins_composition_mgt.py ${BUILD_ID} $jenkins_composition_id end"
        }
        aborted {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/jenkins_composition_mgt.py ${BUILD_ID} $jenkins_composition_id end"
        }
        unstable {
              // 执行该阶段的命令
               sh "${PYTHON_CMD} ${SCRIPT_PATH}/jenkins_composition_mgt.py ${BUILD_ID} $jenkins_composition_id  end"
        }
    }

}
