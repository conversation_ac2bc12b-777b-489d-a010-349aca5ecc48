pipeline {
    agent {
        label 'vm'
     }

    environment {
        USERNAME = 'log'
        NAMESPACE = "${环境套}"
        APP_NAME = "${应用名}"
    }

    stages {
        stage('Login and Fetch Pods') {
            steps {
                script {
                    def SERVER_IP = ''
                    def PASSWORD = ''
                    if (NAMESPACE.compareTo("it70") > 0) {
                        SERVER_IP = '**************'
                        PASSWORD = "^IMx&8B&ZryydoF\$*Gp1^"
                        echo "云上 ${NAMESPACE} ${APP_NAME}"}
                    else {
                        SERVER_IP = '***************'
                        PASSWORD = '3wNF9S4Zw5HHJavH'
                        echo "内网 ${NAMESPACE} ${APP_NAME}"}
                    // Step 1: Use SSH to login and find pods in the namespace
                    def findPodsCommand = """
                    sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "\
                      kubectl get pods -n ${NAMESPACE} --no-headers -o custom-columns=NAME:.metadata.name"
                    """

                    def podList = sh(script: findPodsCommand, returnStdout: true).trim().tokenize('\n')

                    //echo "Found Pods: ${podList.join(', ')}"
                    def finalString = '环境名,pod名,agents\n'

                    podList.each { pod ->
                        if (pod.contains("${APP_NAME}")) { // 如果pod名称中包含“abc”，才执行原来的逻辑，否则跳过
                            // Step 2: Fetch pod status
                            def podJavaAgentsCommand = """
                            sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "\
                            kubectl exec -n ${NAMESPACE} ${pod} -- /bin/sh -c \\"ps aux | grep java | grep -o 'javaagent:[^ ]*.jar' \\" "
                            """

                            try {
                                javaAgents = sh(script: podJavaAgentsCommand, returnStdout: true).trim()
                            } catch (Exception e) {
                                // 记录错误信息或者采取其他措施
                                javaAgents = '无'
                                echo "Command failed with exit code 1. Recording empty value."
                            }

                            javaAgents = javaAgents.replaceAll('\\n', '；')

                            finalString += "${NAMESPACE},${pod},${javaAgents}\n"

                        }
                    }

                    // Step 4: Write finalString to file
                    echo "${finalString}"
                    writeFile file: "${env.WORKSPACE}/${NAMESPACE}.csv", text: finalString
                }
            }
        }
    }
}
