pipeline {
    agent {
        label 'vm'
     }

    environment {
        USERNAME = 'log'
        NAMESPACE = "${环境套}"
        APP_NAME = "${应用名}"
    }

    stages {
        stage('Login and Fetch Pods') {
            steps {
                script {
                    def SERVER_IP = ''
                    def PASSWORD = ''
                    if (NAMESPACE.compareTo("it70") > 0) {
                        SERVER_IP = '**************'
                        PASSWORD = "^IMx&8B&ZryydoF\$*Gp1^"
                        echo "云上 ${NAMESPACE} ${APP_NAME}"}
                    else {
                        SERVER_IP = '***************'
                        PASSWORD = '3wNF9S4Zw5HHJavH'
                        echo "内网 ${NAMESPACE} ${APP_NAME}"}
                    // Step 1: Use SSH to login and find pods in the namespace
                    def findPodsCommand = """
                    sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "\
                      kubectl get pods -n ${NAMESPACE} --no-headers -o custom-columns=NAME:.metadata.name"
                    """

                    def podList = sh(script: findPodsCommand, returnStdout: true).trim().tokenize('\n')

                    //echo "Found Pods: ${podList.join(', ')}"
                    def finalString = '环境名,pod名,从调度开始,从启动开始,含重试耗时,节点名,节点ip,pod数\n'

                    podList.each { pod ->
                        if (pod.contains("${APP_NAME}")) { // 如果pod名称中包含“abc”，才执行原来的逻辑，否则跳过
                            // Step 2: Fetch pod status
                            def podStatusCommand = """
                            sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "\
                              kubectl get pod ${pod} -n ${NAMESPACE} -o jsonpath='{.status}'"
                            """

                            def podStatus = sh(script: podStatusCommand, returnStdout: true).trim()

                            def podNodeCommand = """
                            sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "\
                              kubectl describe pod ${pod} -n ${NAMESPACE} | grep 'Node:' | sed 's/Node://g' | xargs"
                            """

                            def podNode = sh(script: podNodeCommand, returnStdout: true).trim()
                            def nodeParts = podNode.split('/')
                            def nodeName = nodeParts[0]
                            def nodeIp = nodeParts[1]
                            //echo "Pod ${pod} status: ${podStatus}"

                            def nodePodsTotalCommand = """
                            sshpass -p '${PASSWORD}' ssh -o StrictHostKeyChecking=no ${USERNAME}@${SERVER_IP} "\
                              kubectl get pods --all-namespaces -o wide | grep ${nodeName} | wc -l"
                            """

                            def podsCountOfNode = sh(script: nodePodsTotalCommand, returnStdout: true).trim()

                            // Step 3: Parse and filter pod status
                            def jsonSlurper = new groovy.json.JsonSlurperClassic()
                            def podStatusJson = jsonSlurper.parseText(podStatus)

                            def initializedTime = null
                            def startedTime = null
                            def readyTime = null
                            def scheduledTime = null

                            podStatusJson.conditions.each { condition ->
                                if(condition.type == 'Initialized' && condition.status == 'True') {
                                    initializedTime = condition.lastTransitionTime
                                //    echo "Pod ${pod} was initialized at ${initializedTime}"
                                }
                                if(condition.type == 'Ready' && condition.status == 'True') {
                                    readyTime = condition.lastTransitionTime
                                    //echo "Pod ${pod} is ready at ${readyTime}"
                                }
                                if(condition.type == 'PodScheduled' && condition.status == 'True') {
                                    scheduledTime = condition.lastTransitionTime
                                    //echo "Pod ${pod} is ready at ${readyTime}"
                                }
                            }

                            podStatusJson.containerStatuses.each { containerStatus ->
                                if(containerStatus.ready == true) {
                                    startedTime = containerStatus.state.running.startedAt
                                //    echo "Pod ${pod} was startedTime at ${startedTime}"
                                }
                            }

                            if(initializedTime != null && readyTime != null && startedTime!=null) {
                                def format = new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")

                                def initializedDate = format.parse(initializedTime)
                                def scheduledDate = format.parse(scheduledTime)
                                def readyDate = format.parse(readyTime)
                                def startedDate = format.parse(startedTime)

                                def diffInit = readyDate.time - initializedDate.time
                                def diffStart = readyDate.time - startedDate.time
                                def diffScheduled = readyDate.time - scheduledDate.time

                                def diffInitSeconds = diffInit / 1000
                                def diffStartSeconds = diffStart / 1000
                                def diffScheduledSeconds = diffScheduled / 1000


                                finalString += "${NAMESPACE},${pod},${diffScheduledSeconds},${diffStartSeconds},${diffInitSeconds},${nodeName},${nodeIp},${podsCountOfNode}\n"
                                //echo "Pod ${pod} took ${diffSeconds} seconds to initialize and become ready"
                            }
                        }
                    }

                    // Step 4: Write finalString to file
                    echo "${finalString}"
                    writeFile file: "${env.WORKSPACE}/${NAMESPACE}.csv", text: finalString
                }
            }
        }
    }
}
