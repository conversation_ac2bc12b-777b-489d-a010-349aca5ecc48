#!groovy
pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        DUMP_SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev"
        PARAM_LIST="${JOB_NAME} ${WORKSPACE} ${biz_code} ${biz_branch_name} ${suite_code} ${business_id} ${BUILD_ID}"
        DUMP_SQL_PARAM_LIST="${biz_code} 'master' ${suite_code} ${business_id}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        retry(1)
    }
    parameters {
        string(
            name: 'biz_code',
            description: "业务code"
        )
        string(
            name: 'biz_branch_name',
            description: "业务分支"
        )

        string(
            name: 'suite_code',
            description: "环境套名"
        )
        string(
            name: 'business_id',
            description: "业务ID"
        )
    }
    stages {
        stage('数据记录')  {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_build_id ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} 'running' "
            }
        }
        stage('dev-dump恢复') {
            steps {
                echo "${PARAM_LIST}"
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py restore_dump ${PARAM_LIST}"
            }
        }
        stage('dev数据导出') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py test_data_export ${PARAM_LIST}"
            }
        }
        stage('master-dump恢复') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py restore_dump ${JOB_NAME} ${WORKSPACE} ${biz_code} 'master' ${suite_code} ${business_id} ${BUILD_ID}"
            }
        }
        stage('master数据清空') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py truncate_data ${PARAM_LIST}"
            }
        }
        stage('SQL制品准备') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py pull_sql_repo ${PARAM_LIST}"
            }
        }
        stage('SQL分拣') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py sort_sql ${PARAM_LIST}"
            }
        }
        stage('SQL执行') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py execute_sql ${PARAM_LIST}"
            }
        }
        stage('dev数据灌入') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py test_data_import ${PARAM_LIST}"
            }
        }
        stage('制作dump') {
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/make_dump_service.py make_dump ${DUMP_SQL_PARAM_LIST}"
            }
        }
    }
    post{
    failure{
        echo 'failure'
        sh label: '', script: '${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_execute_result ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} "failure" '
    }
    aborted{
        echo 'aborted'
        sh label: '', script: '${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_execute_result ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} "aborted" '
    }
    success{
        echo 'success'
        sh label: '', script: '${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/run_handle_data.py record_execute_result ${JOB_NAME} ${business_id} ${biz_code} ${BUILD_ID} "success" '
    }
    }
}