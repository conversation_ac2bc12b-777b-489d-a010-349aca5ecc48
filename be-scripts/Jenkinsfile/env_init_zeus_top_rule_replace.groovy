#!groovy
pipeline {
    agent any
    environment {
        ONLINE_BRANCH="2.8.0"
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/scheduler"
        PARAM_LIST="${SUIT_CODE} ${SOURCE_CODE}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        // timestamps()
        retry(1)
    }
    parameters {

        string(
            name: 'SUIT_CODE',
            defaultValue: 'tms19',
            description: "环境"
        )

        string(
            name: 'SOURCE_CODE',
            defaultValue: '19',
            description: "基准环境"
        )

    }
    stages {
        stage('初始化**************-zeus_nacos数据库config_replace_toppriority_rule环境数据') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/env_init_zeus_replace_rule.py ${PARAM_LIST}"
            }
        }

    }
}