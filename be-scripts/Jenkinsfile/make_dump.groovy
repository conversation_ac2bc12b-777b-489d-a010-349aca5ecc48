#!groovy
pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        DUMP_SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev"
        PARAM_LIST="${JOB_NAME} ${WORKSPACE} ${bis_pipeline_id} ${suite_code} ${newest_flag} ${business_id} ${BUILD_ID} ${opt_type} ${biz_base_db_code} 'master' "
        DUMP_CHECK_PARAM_LIST="${JOB_NAME} ${WORKSPACE} ${bis_pipeline_id} ${suite_code} ${newest_flag} ${business_id} ${BUILD_ID} 3 ${biz_base_db_code} 'master' "
        DUMP_SQL_PARAM_LIST="${bis_pipeline_id} ${suite_code} ${opt_type} ${business_id}"
        UPDATE_DUMP_PARAM="${bis_pipeline_id} ${suite_code} 3 ${business_id}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        retry(1)
    }
    parameters {
        string(
            name: 'bis_pipeline_id',
            description: "业务迭代id"
        )

        string(
            name: 'suite_code',
            description: "环境套名"
        )
        booleanParam(
            name: 'newest_flag',
            defaultValue: false,
            description: "是否取最新归档的应用迭代SQL"
        )
        string(
            name: 'business_id',
            description: "业务ID"
        )
        choice(
            name:'opt_type',
            choices:[1, 2, 3],
            description:'1:测试环境数据初始化，2、执行make_dump, 3、dump_check'
        )
        string(
            name: 'biz_base_db_code',
            description: "基础库code"
        )
    }
    stages {
        stage('SQL制品准备') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                echo "${PARAM_LIST}"
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py pull_sql_repo ${PARAM_LIST}"
            }
        }
        stage('SQL分拣') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py sort_sql ${PARAM_LIST}"
            }
        }
        stage('DB初始化') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py restore_dump ${PARAM_LIST}"
            }
        }
        stage('SQL执行') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py execute_sql ${PARAM_LIST}"
            }
        }
        stage('make_dump') {
            when {
                anyOf {
                    equals expected: '1', actual: opt_type
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/make_dump_service.py ${DUMP_SQL_PARAM_LIST}"
            }
        }
        stage('SQL制品准备-dump检查') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py pull_sql_repo ${DUMP_CHECK_PARAM_LIST}"
            }
        }
        stage('SQL分拣-dump检查') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py sort_sql ${DUMP_CHECK_PARAM_LIST}"
            }
        }
        stage('DB初始化-dump检查') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py restore_dump ${DUMP_CHECK_PARAM_LIST}"
            }
        }
        stage('SQL执行-dump检查') {
            when {
                anyOf {
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/test_data_init.py execute_sql ${DUMP_CHECK_PARAM_LIST}"
            }
        }
        stage('更新dump状态') {
            when {
                anyOf {
                    equals expected: '1', actual: opt_type
                    equals expected: '2', actual: opt_type
                }
            }
            steps {
                sh "${PYTHON_CMD} ${DUMP_SCRIPT_PATH}/make_dump_service.py ${UPDATE_DUMP_PARAM}"
            }
        }
    }
}