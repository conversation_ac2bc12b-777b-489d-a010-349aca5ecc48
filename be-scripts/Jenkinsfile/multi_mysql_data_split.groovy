#!groovy
pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/service"
        PARAM_LIST="${db_name} ${it100_db_name} ${it300_db_name}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        retry(1)
    }
    parameters {

        string(
            name: 'db_name',
            description: "数据库名"
        )
         string(
            name: 'it100_db_name',
            description: "it100业务库数据库名"
        )
       string(
            name: 'it300_db_name',
            description: "it300业务库数据库名"
        )
    }
    stages {
        stage('执行') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/multi_biz_data_split_service.py ${PARAM_LIST}"
            }
        }

    }
}