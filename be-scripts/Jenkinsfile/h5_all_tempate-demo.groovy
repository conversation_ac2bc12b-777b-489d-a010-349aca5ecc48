#!groovy
/********************************
# Descript：
#   Node相关多种模板汇总--h5模板。（示例）
# History:
# 	2025-04-27  Zt  First Release。
********************************/
def getDateStr() {
    return new Date().format('yyyyMMdd')
}

pipeline {
    agent {
        label 'nfs1'
    }
    environment {
        PYTHON_CMD="python3.x"
        //同步「根」目录（同步目录汇总，但本身并不会同步）
        SYNC_PATH="/data/devops_sync"
        //同步「环境」目录（具体的一个同步目录）
        SYNC_ENV_PATH="$SYNC_PATH/devops_sync_$PUBLISH_SUITE"
    }
    options {
        timeout(time: 90, unit: 'MINUTES')
        retry(1)
    }

    parameters {
        booleanParam(
            name: 'IS_DEBUG', 
            defaultValue: false, 
            description: '调试模式：打印环境信息。'
        )
    }
    
    stages {

        stage('format') {
            steps {
                script {
                    manager.addShortText("${env.BUILD_USER}",'black','lightgreen','5px','yellow')
                    currentBuild.description = "IS_DEBUG: ${params.IS_DEBUG}"
                }
            }
        }

        stage('Debug-->') {
            when {
                expression { params.IS_DEBUG }
            }
            failFast false
            stages {
                stage('节点Debug') {
                    when {
                        expression { params.IS_DEBUG }
                    }
                    failFast false
                    parallel {
                        stage('01-node') {
                            steps {
                                println "==== NODE_NAME: ${env.NODE_NAME}"
                            }
                        }
                        stage('02-ssh') {
                            steps {
                                println "==== SSH_CONNECTION: ${env.SSH_CONNECTION}"
                            }
                        }
                    }
                }
                stage('环境Debug') {
                    when {
                        expression { params.IS_DEBUG }
                    }
                    failFast false
                    parallel {
                        stage('01-「PATH」') {
                            steps {
                                println "==== PATH(os): ${env.PATH}"
                            }
                        }
                        stage('02-环境变量') {
                            steps {
                                sh "printenv"
                            }
                        }
                        stage('03-全局变量') {
                            steps {
                                println "==== 同步「根」目录 SYNC_PATH: ${env.SYNC_PATH}"
                                println "==== 同步「环境」目录 SYNC_ENV_PATH: ${env.SYNC_ENV_PATH}"
                            }
                        }
                        stage('04-入参') {
                            steps {
                                println "==== 「应用名」 PUBLISH_APP: ${params.PUBLISH_APP}"
                                println "==== 「分支名」 PUBLISH_BRANCH: ${params.PUBLISH_BRANCH}"
                                println "==== 「发布环境」 PUBLISH_SUITE: ${params.PUBLISH_SUITE}"
                                println "==== 「调试模式」 IS_DEBUG: ${params.IS_DEBUG}"
                            }
                        }
                    }
                }
            }

        }

        stage('生产-->') {
            failFast false

            stages {
                stage('解析缓存数据') {
                    steps {
                        script {
                            println "==== 生产--> 解析缓存数据！ ===="
                        }
                            
                    }
                }

                stage('前置') {
                    parallel {
                        stage('前置一') {
                            stages {
                                stage ('前置一：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('前置一：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('前置一：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('前置二') {
                            stages {
                                stage ('前置二：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('前置二：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('前置二：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('前置三') {
                            stages {
                                stage ('前置三：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('前置三：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('前置三：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }

                stage('准备') {
                    parallel {
                        stage('代码准备') {
                            stages {
                                stage ('代码准备：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('代码准备：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('代码准备：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('配置准备') {
                            stages {
                                stage ('配置准备：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('配置准备：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('配置准备：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('其它准备') {
                            stages {
                                stage ('其它准备：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('其它准备：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('其它准备：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }

                stage('构建') {
                    parallel {
                        stage('代码构建三步') {
                            stages {
                                stage ('环境安装') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('代码编译') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('筛选制品') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
                
                stage('后置') {
                    parallel {
                        stage('后置一') {
                            stages {
                                stage ('后置一：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('后置一：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('后置一：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('后置二') {
                            stages {
                                stage ('后置二：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('后置二：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('后置二：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('后置三') {
                            stages {
                                stage ('后置三：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('后置三：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('后置三：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        stage('成品-->') {
            failFast false

            stages {

                stage('组装成品') {
                    parallel {
                        stage('组装一') {
                            stages {
                                stage ('组装一：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('组装一：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('组装一：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('组装二') {
                            stages {
                                stage ('组装二：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('组装二：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('组装二：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('组装三') {
                            stages {
                                stage ('组装三：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('组装三：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('组装三：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }

                stage('入库') {
                    parallel {
                        stage('制品入库') {
                            stages {
                                stage ('制品入库：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('制品入库：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('制品入库：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('配置入库') {
                            stages {
                                stage ('配置入库：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('配置入库：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('配置入库：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('其它入库') {
                            stages {
                                stage ('其它入库：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('其它入库：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('其它入库：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        stage('交付-->') {
            failFast false

            stages {

                stage('交付前准备') {
                    parallel {
                        stage('交付前--验证一') {
                            stages {
                                stage ('交付前验证一：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('交付前验证一：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('交付前验证一：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('交付前--验证二') {
                            stages {
                                stage ('交付前验证二：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('交付前验证二：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('交付前验证二：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }

                stage('交付') {
                    parallel {
                        stage('制品入库') {
                            stages {
                                stage ('制品入库：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('制品入库：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('制品入库：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置一：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('配置入库') {
                            stages {
                                stage ('配置入库：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('配置入库：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('配置入库：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置二：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                        stage('其它入库') {
                            stages {
                                stage ('其它入库：①') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：① ===="
                                        }
                                        
                                    }
                                }
                                stage ('其它入库：②') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：② ===="
                                        }
                                        
                                    }
                                }
                                stage ('其它入库：③') {
                                    steps {
                                        script {
                                            println "==== 生产--> 前置处理：前置三：③ ===="
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        stage('后置处理') {
            failFast false

            parallel {
                stage('01-step1') {
                    steps {
                        sh "echo '>>>> step1:' $PYTHON_CMD"
                    }
                }
                stage('02-step2') {
                    steps {
                        sh "echo '>>>> 后置处理2:' $PYTHON_CMD"
                    }
                }
                stage('03-step3') {
                    steps {
                        sh "echo '>>>> 后置处理3:' $PYTHON_CMD"
                    }
                }
            }
        }
    }
    /*
    post {
        always {
            echo "========== zt@2025-04-27 =========="
        }
    }
    */
}