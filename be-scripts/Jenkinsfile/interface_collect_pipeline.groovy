import groovy.json.JsonSlurper;
import groovy.json.JsonSlurperClassic;
import groovy.json.JsonOutput;

def interface_node_stages = ["env_check_per","pull_product", "scp_package", "make_img", "cover_config", "create_config_map", "get_start_file",
     "alert_start_file", "publish_start_file", "restart_app", "check_result"]

pipeline {
    agent any
     environment {
        SCRIPT_PATH='/home/<USER>/be-scripts/be-scripts/test_pipeline/test_pipeline_business/app_interface_collect_pipeline.py'
        PYTHON_CMD="python37"
        args = "{\"workspace\":\"${workspace}\",\"marking_path\":\"/home/<USER>/dump/${env.JOB_NAME}.txt\"}"
      
                      
    }
    stages{
        stage("判断是否需要解析"){

                steps{
                    echo "${env.JOB_NAME}"
                    script {
                       cache_data_path=workspace
                       flagPath="/home/<USER>/dump/${env.JOB_NAME}.txt"
                       analysis_flag = fileExists flagPath
                       testSkipPath="/home/<USER>/dump/${env.JOB_NAME}_test_skip.txt"
                       //bind_suite
                        suite_code = "tms18"
                       //bind_suite
                       analysis_test_skip = fileExists testSkipPath
                    }
                }
        }
         stage("解析"){
               when{
                        expression  {analysis_flag == false}
                }
                steps {
                   sh label: '', script: '${PYTHON_CMD} ${SCRIPT_PATH} ${pipeline_params} ${args} "create_pipeline"'
                }
         }

         //interface_collect
stage("多应用并发"){
                  when{
                        expression  {analysis_flag == true}
                    }      
                    parallel {
           stage("acc-center-web发布阶段"){
                when{
                    expression  {analysis_flag == true}
                }          
               steps{
               
        script{
           for (interface_node_name in interface_node_stages){
                stage(interface_node_name) { 
            if (interface_node_name=="cover_config" || interface_node_name=="alert_start_file")
            {
            def argsMap = new JsonSlurperClassic().parseText(args)
                                argsMap["src_suite_code"] = "test02"
                                argsMap["package_type"] = "war"
                                argsMap["build_jdk_version"] = "1.8"
                                argsMap["app_name"] = "acc-center-web"
                                argsMap["br_name"] = "2.0.27"
                                println argsMap
                                env.newargs_test02 = new JsonOutput().toJson(argsMap)
                                env.newargs_test02 = env.newargs_test02.trim().replaceAll("\n","")
                                println env.newargs_test02
            
       
            sh label: '', script: '''${PYTHON_CMD} ${SCRIPT_PATH} ${newargs_test02} ${pipeline_params} '''+interface_node_name               
            }
            else{           
                    sh "python37 ~/be-scripts/be-scripts/ci_pipeline/test_env_publish/test_env_publish_main.py ${cache_data_path} ${interface_node_name} acc-center-web 2.0.27 publish_test_data_20211124_150356_115100 it03"
                    }
                }
           }
        }
    

        script{
           for (interface_node_name in interface_node_stages){
                stage(interface_node_name) { 
            if (interface_node_name=="cover_config" || interface_node_name=="alert_start_file")
            {
            def argsMap = new JsonSlurperClassic().parseText(args)
                                argsMap["src_suite_code"] = "it03"
                                argsMap["package_type"] = "war"
                                argsMap["build_jdk_version"] = "1.8"
                                argsMap["app_name"] = "acc-center-web"
                                argsMap["br_name"] = "2.0.29"
                                println argsMap
                                env.newargs_it03 = new JsonOutput().toJson(argsMap)
                                env.newargs_it03 = env.newargs_it03.trim().replaceAll("\n","")
                                println env.newargs_it03
            
       
            sh label: '', script: '''${PYTHON_CMD} ${SCRIPT_PATH} ${newargs_it03} ${pipeline_params} '''+interface_node_name               
            }
            else{           
                    sh "python37 ~/be-scripts/be-scripts/ci_pipeline/test_env_publish/test_env_publish_main.py ${cache_data_path} ${interface_node_name} acc-center-web 2.0.29 publish_test_data_20211124_150356_173701 it03"
                    }
                }
           }
        }
    
               }
           }
           

           stage("acc-console-web发布阶段"){
                when{
                    expression  {analysis_flag == true}
                }          
               steps{
               
        script{
           for (interface_node_name in interface_node_stages){
                stage(interface_node_name) { 
            if (interface_node_name=="cover_config" || interface_node_name=="alert_start_file")
            {
            def argsMap = new JsonSlurperClassic().parseText(args)
                                argsMap["src_suite_code"] = "test02"
                                argsMap["package_type"] = "war"
                                argsMap["build_jdk_version"] = "1.8"
                                argsMap["app_name"] = "acc-console-web"
                                argsMap["br_name"] = "2.0.27"
                                println argsMap
                                env.newargs_test02 = new JsonOutput().toJson(argsMap)
                                env.newargs_test02 = env.newargs_test02.trim().replaceAll("\n","")
                                println env.newargs_test02
            
       
            sh label: '', script: '''${PYTHON_CMD} ${SCRIPT_PATH} ${newargs_test02} ${pipeline_params} '''+interface_node_name               
            }
            else{           
                    sh "python37 ~/be-scripts/be-scripts/ci_pipeline/test_env_publish/test_env_publish_main.py ${cache_data_path} ${interface_node_name} acc-console-web 2.0.27 publish_test_data_20211124_150356_311379 it03"
                    }
                }
           }
        }
    

        script{
           for (interface_node_name in interface_node_stages){
                stage(interface_node_name) { 
            if (interface_node_name=="cover_config" || interface_node_name=="alert_start_file")
            {
            def argsMap = new JsonSlurperClassic().parseText(args)
                                argsMap["src_suite_code"] = "it03"
                                argsMap["package_type"] = "war"
                                argsMap["build_jdk_version"] = "1.8"
                                argsMap["app_name"] = "acc-console-web"
                                argsMap["br_name"] = "2.0.29"
                                println argsMap
                                env.newargs_it03 = new JsonOutput().toJson(argsMap)
                                env.newargs_it03 = env.newargs_it03.trim().replaceAll("\n","")
                                println env.newargs_it03
            
       
            sh label: '', script: '''${PYTHON_CMD} ${SCRIPT_PATH} ${newargs_it03} ${pipeline_params} '''+interface_node_name               
            }
            else{           
                    sh "python37 ~/be-scripts/be-scripts/ci_pipeline/test_env_publish/test_env_publish_main.py ${cache_data_path} ${interface_node_name} acc-console-web 2.0.29 publish_test_data_20211124_150358_450753 it03"
                    }
                }
           }
        }
    
               }
           }
           }
                    }
//interface_collect
stage("环境恢复"){
               when{
                        expression  {analysis_flag == true}
                }
                steps {
                   sh label: '', script: '${PYTHON_CMD} ${SCRIPT_PATH} ${pipeline_params} ${args} "env_recover"'
                }
         }
    }
    
    post{
        always {
            script {
                sh 'echo ' + analysis_flag
                sh 'echo ' + analysis_test_skip
                if ( analysis_flag && analysis_test_skip ) {
                    sh 'rm -f ' + testSkipPath
                }
                if (analysis_flag){
                    sh 'rm -rf '+flagPath
                }
            }
        }
        failure{
            echo '运行失败'
            sh label: '', script: '${PYTHON_CMD} ${SCRIPT_PATH} ${pipeline_params} ${args} "env_recover"'
            sh label: '', script: 'rm -rf '+flagPath
        }
        aborted {
            echo '取消运行'
            sh label: '', script: '${PYTHON_CMD} ${SCRIPT_PATH} ${pipeline_params} ${args} "env_recover"'
            sh label: '', script: 'rm -rf '+flagPath
        }
        success{
            echo "运行成功"
        }
    }
}