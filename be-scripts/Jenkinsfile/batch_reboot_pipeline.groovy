import groovy.json.JsonSlurper
import groovy.json.JsonOutput
pipeline {
    agent {
        label 'vm'
    }
    environment{
        BEP="/home/<USER>/be-scripts/be-scripts"
	    job_url="${currentBuild.absoluteUrl}"
	    build_id="${currentBuild.id}"
    }
    //parameters
    stages {
        stage("数据处理") {
            steps {
                script {
                    echo "数据处理"
                    def defaultValue = new JsonSlurper().parseText(params.DATA_PARAM)
                    defaultValue = new HashMap(defaultValue)
                    defaultValue.build_id = currentBuild.id
                    defaultValue.job_name = env.JOB_NAME
                    default_exec_id = defaultValue.exec_id
                    manager.addShortText(defaultValue.username,'black','lightgreen','5px','yellow')
                    currentBuild.description = "发布版本: " + defaultValue.iteration_id +"\n"
                    def updatedValue = JsonOutput.toJson(defaultValue)

                    try {
                        sh 'python3.x /home/<USER>/be-scripts/be-scripts/publish_tool/publish/publish_jenkins_pipeline.py reboot_handle_data \'' + updatedValue + '\' ' + build_id
                    } catch(Exception e) {
                        // verifyLogs()
                        throw e
                    }

                }
            }
        }
        //stages

    }

    post{
        failure{
            echo 'failure'
            sh label: '', script: 'python3.x ${BEP}/publish_tool/publish/record_publish_pipeline_status.py batch_reboot_record '+ env.JOB_NAME +' "failure" '+build_id

        }
        aborted {
    		script {
    			echo 'aborted'
    			sh label: '', script: 'python3.x ${BEP}/publish_tool/publish/record_publish_pipeline_status.py batch_reboot_record '+ env.JOB_NAME +' "aborted" '+build_id
            }
        }
        success{
    		script {
    			echo 'success'
    			sh label: '', script: 'python3.x ${BEP}/publish_tool/publish/record_publish_pipeline_status.py batch_reboot_record '+ env.JOB_NAME +' "success" '+build_id
            }
        }
    }

}

def verifyLogs(prompt, content) {
    def logContent = currentBuild.rawBuild.getLog(100)
    logContent = prompt + "\n\n" + logContent
    def params = [
        "content": content,
        "question": logContent
    ]
    def response = httpRequest(
        contentType: 'APPLICATION_JSON',
        httpMode: 'POST',
        requestBody: JsonOutput.toJson(params),
        url: 'http://mantis.howbuy.pa/mantis/assistant/question_answer/question_store/'
    )
    def json = readJSON text: response.content
    def key = json.key
    println "看不懂日志？移步这里试试：http://mantis.howbuy.pa/mantis/assistant/question_answer/question_answer/?key=$key"
}