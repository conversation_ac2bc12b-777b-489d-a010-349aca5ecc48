#!groovy
pipeline {
    agent {
        label 'vm2'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/service"
        PARAM_LIST="${db_name} ${biz_db_name} ${basic_db_name} ${biz}  ${bis_branch_name} ${sql_file_path}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        retry(1)
    }
    parameters {

        string(
            name: 'db_name',
            description: "数据库名"
        )
         string(
            name: 'biz_db_name',
            description: "业务库数据库名"
        )
       string(
            name: 'basic_db_name',
            description: "基准数据库名"
        )
        string(
            name: 'biz',
            description: "业务名"
        )
       string(
            name: 'bis_branch_name',
            description: "业务分支"
        )
       string(
            name: 'sql_file_path',
            description: "sql_file_path"
        )

    }
    stages {
        stage('执行') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/oracle_biz_data_split_service.py ${PARAM_LIST}"
            }
        }

    }
}