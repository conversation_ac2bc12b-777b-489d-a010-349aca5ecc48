#!groovy
pipeline {
    agent {
        label 'vm'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/scheduler"
        PARAM_LIST="${SUIT_CODE} ${SOURCE_CODE}"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        retry(1)
    }
    stages {
        stage('执行') {

            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/scheduler_agent.py"
            }
        }

    }
}