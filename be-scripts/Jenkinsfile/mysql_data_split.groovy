#!groovy
pipeline {
    agent {
        label 'vm3'
    }
    environment {
        PYTHON_CMD="python3.x"
        SCRIPT_PATH="/home/<USER>/be-scripts/be-scripts/job/jenkins/test_data_dev/service"
        DUMP_SQL_PARAM_LIST="${env_name} ${db_name} ${db_user} ${db_pwd} ${db_srv_ip}"
        SUBMIT_SQL_PARAM_LIST="${biz_db_code} ${biz_branch} ${sql_file_path} ${archive_path} ${local_dml_path} biz None"
    }
    options {
        timeout(time: 1, unit: 'HOURS')
        retry(1)
    }
    parameters {
        string(
            name: 'env_name',
            description: "环境"
        )
        string(
            name: 'db_name',
            description: "数据库名"
        )
        string(
            name: 'db_user',
            description: "用户名"
        )
       string(
            name: 'db_pwd',
            description: "密码",
            defaultValue: 'howbuy2015'
        )
        string(
            name: 'db_srv_ip',
            description: "数据库IP"
        )
       string(
            name: 'biz_db_code',
            description: "it102"
        )
         string(
            name: 'biz_branch',
            description: "master",
            defaultValue: 'master'
        )
        string(
            name: 'sql_file_path',
            description: "OTCMOCK/otcmock/DML",
        )
        string(
            name: 'archive_path',
            description: "/archive/0.0.1/",
            defaultValue: '/archive/0.0.1/'
        )
        string(
            name: 'local_dml_path',
            description: "/data/biz-sql-split/DML",
            defaultValue: '/data/biz-sql-split/DML'
        )
    }
    stages {
        stage('dump_sql') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/dump_sql_service.py ${DUMP_SQL_PARAM_LIST}"
            }
        }
        stage('make_sql') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/make_sql_service.py"
            }
        }
        stage('submit_sql') {
            steps {
                sh "${PYTHON_CMD} ${SCRIPT_PATH}/biz_data_split_service.py ${SUBMIT_SQL_PARAM_LIST}"
            }
        }
    }
}